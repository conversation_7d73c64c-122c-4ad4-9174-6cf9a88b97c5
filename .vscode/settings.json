{"editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "npm.packageManager": "pnpm", "typescript.updateImportsOnFileMove.enabled": "always", "typescript.tsdk": "tools/eslint-config/node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "eslint.nodePath": "tools/eslint-config/node_modules/eslint", "eslint.workingDirectories": [{"mode": "auto"}], "editor.formatOnSave": true, "prettier.ignorePath": ".prettieri<PERSON>re", "prettier.configPath": ".prettierrc.js"}