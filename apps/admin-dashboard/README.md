# 恺望数据管理平台

## 项目简介

该项目为恺望标注工具平台，主要为标注人员提供以下功能：

1. 图片与点云渲染
2. 点、线、体等图形绘制
3. 标注数据的提交与审核
4. 自动化标注与校准
5. ...

## 目录

- components 抽离通用组件
- config 项目通用配置内容
- features 各模块内容
  - auth 登录注册模块
    - api 该模块下所使用的 api（对 service 进行前端封装，不直接使用 service 类型文件）
    - components 该模块的相关子组件
    - routes 该模块所代表的页面组件
  - dashboard 仪表盘模块
  - tasks 任务管理模块
  - teams 团队管理模块
  - users 用户管理模块
- hooks 自定义高阶函数，用于复用代码中通用逻辑
- providers 封装全局统一的逻辑处理
  - auth 个人认证的信息
  - error 全局错误信息处理
- routes 路由管理
- service 服务端接口文档生成的类型文件（前端只是引用，不做修改）
- utils 工具函数

## 技术架构

1. 脚手架：[CRACO](https://github.com/dilanx/craco)
2. 组件库：[chakra-ui](https://chakra-ui.com/docs/components) + @forest/simple-ui
3. 表单方案：[Formily](https://formilyjs.org/)
4. 接口层：[Rxjs](https://rxjs.dev/)

## 开发部署

### 准备工作

在当前项目根目录下添加 `.env.local` 文件，内容如下
PS：建议【不要】将两个证书文件放至 forest 项目目录下（例如，可以放到你本地的/etc/ssl/certs/目录下）

```
SSL_CRT_FILE=${your crt file path}
SSL_KEY_FILE=${your key file path}
```

### 本地运行

1. `rush update` 更新依赖
2. 在当前项目根目录下运行 `rushx start`

## 学习参考
