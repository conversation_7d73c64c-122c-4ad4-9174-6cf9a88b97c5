import { lazyImport } from '@forest/utils';
import { Suspense } from 'react';
import { Navigate, Outlet } from 'react-router-dom';

import { AdminLayout } from '@/components/layout';

const { Dashboard } = lazyImport(() => import('@/features/dashboard'), 'Dashboard');
const { TeamRoutes } = lazyImport(() => import('@/features/teams'), 'TeamRoutes');
const { UserRoutes } = lazyImport(() => import('@/features/users'), 'UserRoutes');
const { TaskRoutes } = lazyImport(() => import('@/features/tasks'), 'TaskRoutes');
const { OrderRoutes } = lazyImport(() => import('@/features/orders'), 'OrderRoutes');

// TODO: Do I need a skeleton placeholder here?
const AdminWrapper = () => {
  return (
    <AdminLayout>
      <Suspense>
        <Outlet />
      </Suspense>
    </AdminLayout>
  );
};

export const protectedRoutes = [
  {
    path: '/admin',
    element: <AdminWrapper />,
    children: [
      { path: 'home', element: <Dashboard /> },
      { path: 'teams/*', element: <TeamRoutes /> },
      { path: 'users/*', element: <UserRoutes /> },
      { path: 'tasks/*', element: <TaskRoutes /> },
      { path: 'orders/*', element: <OrderRoutes /> },
    ],
  },
  { path: '*', element: <Navigate to="/admin/home" /> },
];
