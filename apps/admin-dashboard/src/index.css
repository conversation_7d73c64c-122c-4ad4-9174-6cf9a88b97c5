body {
  margin: 0;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell',
    'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 100%;
  overflow: hidden;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background: transparent;
}
*::-webkit-scrollbar-thumb {
  background: #dedede;
  border-radius: 8px;
}
*::-webkit-scrollbar-thumb:hover {
  background: #cccccc;
}
*::-webkit-scrollbar-button:vertical:start:increment,
*::-webkit-scrollbar-button:vertical:end:increment {
  display: block;
  height: 5px;
  background: transparent;
}
*::-webkit-scrollbar-button:horizontal:start:increment,
*::-webkit-scrollbar-button:horizontal:end:increment {
  display: block;
  width: 5px;
  background: transparent;
}
