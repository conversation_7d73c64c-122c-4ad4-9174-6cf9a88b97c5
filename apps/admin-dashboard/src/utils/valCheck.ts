/**
 * 用于 ArrayTable 校验空行
 * @param object
 * @returns boolean
 */
export const objHasValue: (obj: object) => boolean = (obj) => {
  return Object.values(obj).some((value: any) => {
    if (typeof value === 'number' || typeof value === 'boolean') {
      return true;
    }
    if (value === undefined || value === null) {
      return false;
    }
    if (typeof value === 'string' && value === 'unspecified') {
      return false;
    }
    if (typeof value === 'object') {
      return Array.isArray(value) ? value.length > 0 : objHasValue(value);
    }
    return true;
  });
};
