export interface RequestConfig {
  timeout?: number;
}

export const getUpdatedFields = <T extends object>(
  origin: T,
  current: Partial<T>,
  fields?: Array<string>
): Array<keyof T> => {
  if (Array.isArray(fields)) {
    return fields.filter(
      (key) => current.hasOwnProperty(key) && origin[key as keyof T] !== current[key as keyof T]
    ) as Array<keyof T>;
  } else {
    const updatedFields = [];
    for (const key in current) {
      if (origin[key] !== current[key]) {
        updatedFields.push(key);
      }
    }
    return updatedFields;
  }
};
