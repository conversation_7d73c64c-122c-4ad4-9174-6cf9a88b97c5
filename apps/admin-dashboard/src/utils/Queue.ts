export class Queue<T> {
  private _list: T[];
  constructor() {
    this._list = [];
  }

  enqueue(value: T) {
    this._list.push(value);
  }
  dequeue() {
    // 数组长度大于16时, 采用reverse+pop来代替shift
    if (this._list.length === 0) {
      return undefined;
    } else if (this._list.length < 16) {
      return this._list.shift();
    } else {
      this._list.reverse();
      const temp = this._list.pop();
      this._list.reverse();
      return temp;
    }
  }
  get length() {
    return this._list.length;
  }
  get peek() {
    return this._list[0];
  }
}
