/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_AttrAndValues } from './anno_v1_AttrAndValues';
import type { anno_v1_Object } from './anno_v1_Object';
import type { anno_v1_RawdataAnno_Metadata } from './anno_v1_RawdataAnno_Metadata';
import type { anno_v1_Segmentation } from './anno_v1_Segmentation';

/**
 * annotations of a rawdata
 */
export type anno_v1_RawdataAnno = {
  /**
   * name of the rawdata
   */
  name: string;
  objects: Array<anno_v1_Object>;
  attrs: Array<anno_v1_AttrAndValues>;
  /**
   * segmentation result of the rawdata; null if the task is not a segmentation task
   */
  segmentation?: anno_v1_Segmentation;
  /**
   * original pathname of the rawdata
   */
  orig_name?: string;
  /**
   * url of the rawdata, only available during exporting anno results
   */
  url?: string;
  /**
   * metadata
   */
  metadata?: anno_v1_RawdataAnno_Metadata;
};
