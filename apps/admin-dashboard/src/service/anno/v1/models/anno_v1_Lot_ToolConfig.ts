/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_Lot_Range } from './anno_v1_Lot_Range';
import type { anno_v1_ToolConfig_PreBox } from './anno_v1_ToolConfig_PreBox';
import type { anno_v1_ToolConfig_Ruler } from './anno_v1_ToolConfig_Ruler';

export type anno_v1_Lot_ToolConfig = {
  /**
   * work range indicators
   */
  ranges?: Array<anno_v1_Lot_Range>;
  /**
   * if projected objects are editable in a 23D fusion task
   */
  projected_editable?: boolean;
  /**
   * if to regenerate 2D projected objects according to 3D objects
   */
  redo_projection?: boolean;
  /**
   * 定义各种类型和大小的量尺，方便比对标志物的尺寸是否合规
   */
  rulers?: Array<anno_v1_ToolConfig_Ruler>;
  /**
   * whether to save 2D projected objects
   */
  save_projected?: boolean;
  /**
   * the order of images in a single frame, specified with images' rawdata.meta.image.camera
   */
  image_order?: Array<string>;
  segmentation_3d_enabled?: boolean;
  pre_box?: Array<anno_v1_ToolConfig_PreBox>;
};
