/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/**
 * contains number of jobs in various phases
 */
export type anno_v1_GetLotSummaryReply = {
  /**
   * total number of jobs
   */
  total_jobs: number;
  /**
   * jobs at each phase 1~n, the last element means the finished jobs.
   * if the lot has n phases, the length of jobs_at_phase is n+1.
   */
  jobs_at_phase: Array<number>;
};
