/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_Lotphase_Execteam } from './anno_v1_Lotphase_Execteam';

/**
 * Lotphase describes an exeuction phase in a lot
 */
export type anno_v1_Lotphase = {
  /**
   * phase number, start from 1
   */
  number?: number;
  /**
   * name of this phase, e.g. label/review-1/review-2/acceptance/..., 标注/审核1/审核2/验收/...
   */
  name: string;
  /**
   * type of this phase
   */
  type: 'unspecified' | 'label' | 'review';
  /**
   * can reviewer edit the annotations
   */
  editable: boolean;
  /**
   * percent of the annotations to be reviewed
   */
  sample_percent: number;
  /**
   * minimum skill level required for executor eligible for this lot phase
   */
  min_skill_level?: number;
  /**
   * assignees should finish their assignments within this number of seconds
   */
  timeout: number;
  /**
   * whether to merge the annotation results of splitted jobs after this phase
   */
  merge?: boolean;
  /**
   * support multiple execution team-uids in this phase;
   */
  execteams: Array<anno_v1_Lotphase_Execteam>;
  claim_policy?: 'unspecified' | 'only_same_team' | 'only_other_teams';
};
