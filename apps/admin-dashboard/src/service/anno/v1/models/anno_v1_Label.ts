/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_AttrRefList } from './anno_v1_AttrRefList';
import type { anno_v1_Label_Compound } from './anno_v1_Label_Compound';
import type { anno_v1_Label_Widget } from './anno_v1_Label_Widget';

/**
 * Label used in a task config
 */
export type anno_v1_Label = {
  /**
   * key of the label
   */
  name: string;
  display_name: string;
  desc?: string;
  avatar?: string;
  /**
   * RGB value: #RRGGBBAA
   */
  color: string;
  /**
   * attributes to be attached
   */
  attrs?: anno_v1_AttrRefList;
  /**
   * muti-level classes: human -> adult, car -> mpv, ...
   */
  class?: Array<string>;
  widgets_v2?: Array<anno_v1_Label_Widget>;
  /**
   * a compounded object
   */
  compound?: anno_v1_Label_Compound;
  is_3d_segmentation?: boolean;
  has_instance?: boolean;
};
