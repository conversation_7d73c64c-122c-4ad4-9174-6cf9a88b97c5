/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_AttrValue } from './anno_v1_AttrValue';

export type anno_v1_Attr = {
  /**
   * key of the attribute
   */
  name: string;
  display_name: string;
  desc?: string;
  avatar?: string;
  type: 'input' | 'bool' | 'radiobox' | 'checkbox';
  /**
   * accepted value type if type is input
   */
  value_type: 'int' | 'float' | 'text' | 'color' | 'date' | 'time' | 'datetime' | 'bool';
  /**
   * if not empty, value should be chosen from the list
   */
  choices: Array<anno_v1_AttrValue>;
};
