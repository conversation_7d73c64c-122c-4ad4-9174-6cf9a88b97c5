/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_CommentReasonClass } from './anno_v1_CommentReasonClass';
import type { anno_v1_Error } from './anno_v1_Error';
import type { anno_v1_Lot_ToolConfig } from './anno_v1_Lot_ToolConfig';
import type { anno_v1_Lotontologies } from './anno_v1_Lotontologies';
import type { anno_v1_Lotphase } from './anno_v1_Lotphase';
import type { anno_v1_OutConfig } from './anno_v1_OutConfig';

export type anno_v1_Lot = {
  /**
   * lot UID
   */
  uid: string;
  /**
   * lot name
   */
  name: string;
  /**
   * lot description
   */
  desc?: string;
  /**
   * lot state
   */
  state: 'unspecified' | 'initializing' | 'unstart' | 'ongoing' | 'finished' | 'paused' | 'canceled';
  /**
   * lot type
   */
  type: 'unspecified' | 'annotate' | 'segment_instance' | 'segment_semantic' | 'segment_panoptic';
  /**
   * data type
   */
  data_type?: 'unspecified' | 'image' | 'pointcloud' | 'fusion2d' | 'fusion3d' | 'fusion4d';
  /**
   * if to treat job as consecutive frames
   */
  is_frame_series?: boolean;
  /**
   * larger number indicates higher priority
   */
  priority: number;
  /**
   * whether to automatically start
   */
  autostart?: boolean;
  /**
   * maximum number of elements in a job.
   * if it is 0, a job is created per subfolder
   */
  job_size: number;
  /**
   * lot ontologies
   */
  ontologies: anno_v1_Lotontologies;
  /**
   * execution phases; phase number starts from 1
   */
  phases: Array<anno_v1_Lotphase>;
  /**
   * execution instructions in format of Markdown?
   */
  instruction?: string;
  /**
   * creator UID
   */
  creator_uid?: string;
  /**
   * number of elements in this lot
   */
  readonly data_size?: number;
  /**
   * expected end time
   */
  exp_end_time?: string;
  readonly updated_at?: string;
  readonly created_at: string;
  /**
   * error info if state is ls_error
   */
  error?: anno_v1_Error;
  /**
   * UID of the organization which the lot belongs to
   */
  org_uid: string;
  /**
   * UID of the data associated with the lot
   */
  data_uid: string;
  /**
   * annotation result output config
   */
  out?: anno_v1_OutConfig;
  /**
   * number of jobs in this lot
   */
  job_count?: number;
  /**
   * if jobs are created
   */
  job_ready?: boolean;
  /**
   * order UID
   */
  order_uid?: string;
  /**
   * manually annotated objects count; only available after lot is finished
   */
  ins_cnt?: number;
  /**
   * annotated objects count (including interpolated ones); only available after lot is finished
   */
  ins_total?: number;
  /**
   * annotation tool configuration
   */
  tool_cfg?: anno_v1_Lot_ToolConfig;
  /**
   * anno result url
   */
  anno_result_url?: string;
  /**
   * indicate if the demander can export annos
   */
  can_export_annos?: boolean;
  /**
   * allowed comment reason list
   */
  comment_reasons?: Array<anno_v1_CommentReasonClass>;
  /**
   * tags attached to the lot
   */
  tags?: Array<string>;
};
