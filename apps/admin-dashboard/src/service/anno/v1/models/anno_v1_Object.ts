/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_Object_Compound } from './anno_v1_Object_Compound';
import type { anno_v1_Object_Label } from './anno_v1_Object_Label';

export type anno_v1_Object = {
  /**
   * UUID of the object
   */
  uuid: string;
  /**
   * 一个任务包中，相同的 track_id 表示同一个实体对象。
   * 命名可以采用 Job.Uid + Label.name + index 的模式，例如：“xxx-car-1”
   */
  track_id?: string;
  label: anno_v1_Object_Label;
  /**
   * 在连续帧标注中，表示该物体从下一帧开始将不复存在
   */
  vanish_later?: boolean;
  /**
   * it is a compounded object if not null
   */
  compound?: anno_v1_Object_Compound;
  /**
   * how the object is created
   */
  source?: 'unspecified' | 'manual' | 'prelabel' | 'interpolation' | 'projection';
};
