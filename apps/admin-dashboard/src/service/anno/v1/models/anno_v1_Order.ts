/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_DataValidationSummary } from './anno_v1_DataValidationSummary';
import type { anno_v1_Source } from './anno_v1_Source';

export type anno_v1_Order = {
  /**
   * order UID
   */
  uid: string;
  /**
   * order name
   */
  name: string;
  /**
   * UID of the organization which the order belongs to
   */
  org_uid: string;
  /**
   * files attached to the order
   */
  source?: anno_v1_Source;
  /**
   * UID of the data associated with the order
   */
  data_uid?: string;
  /**
   * creator UID
   */
  creator_uid?: string;
  /**
   * number of elements in the order
   */
  size?: number;
  /**
   * order state
   */
  state: 'unspecified' | 'initializing' | 'waiting' | 'ongoing' | 'finished' | 'canceled' | 'failed';
  /**
   * annotated object count (include interpolated ones); only available after lot is finished
   */
  ins_total?: number;
  /**
   * annotation result file URL
   */
  anno_result_url?: string;
  /**
   * when state is failed, this field will contain detailed error message
   */
  error?: string;
  /**
   * order creation time
   */
  created_at: string;
  /**
   * data validation summary
   */
  readonly data_summary?: anno_v1_DataValidationSummary;
  /**
   * whether the demander can export annos:
   * 1) if false, anno_result_url will be always empty and the demander cannot export annos;
   * 2) if true,
   * 2.1) if anno_result_url is not empty, the demander can use it to download anno result;
   * 2.2) otherwise, the demander can export annos;
   */
  readonly can_export_annos?: boolean;
};
