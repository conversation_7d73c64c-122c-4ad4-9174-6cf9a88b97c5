/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_Lotontologies } from './anno_v1_Lotontologies';
import type { anno_v1_Lotphase } from './anno_v1_Lotphase';
import type { anno_v1_OutConfig } from './anno_v1_OutConfig';

export type anno_v1_CreateLottplRequest = {
  /**
   * mandatory in update-requests
   */
  uid?: string;
  /**
   * mandatory in create-requests
   */
  name?: string;
  desc?: string;
  /**
   * mandatory in create-requests;
   * lot type
   */
  type?: 'unspecified' | 'annotate' | 'segment_instance' | 'segment_semantic' | 'segment_panoptic';
  /**
   * lot ontologies
   */
  ontologies?: anno_v1_Lotontologies;
  /**
   * execution phases; phase number starts from 1
   */
  phases?: Array<anno_v1_Lotphase>;
  /**
   * execution instructions in format of HTML or Markdown
   */
  instruction?: string;
  /**
   * annotation result output config
   */
  out?: anno_v1_OutConfig;
  /**
   * number of elements in a job
   */
  job_size?: number;
  /**
   * make annotations within the radius, unit is meter
   */
  work_range?: number;
};
