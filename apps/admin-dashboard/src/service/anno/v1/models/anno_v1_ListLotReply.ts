/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_Lot } from './anno_v1_Lot';
import type { iam_v1_BaseUser } from './iam_v1_BaseUser';

export type anno_v1_ListLotReply = {
  /**
   * total number of items found; valid only in the first page reply.
   */
  total?: number;
  lots: Array<anno_v1_Lot>;
  /**
   * the organization that the lot, at the corresponding position, belongs to.
   */
  orgs?: Array<iam_v1_BaseUser>;
};
