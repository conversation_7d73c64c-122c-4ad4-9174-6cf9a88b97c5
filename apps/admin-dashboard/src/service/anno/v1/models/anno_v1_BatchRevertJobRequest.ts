/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_BatchRevertJobRequest_Action } from './anno_v1_BatchRevertJobRequest_Action';
import type { anno_v1_BatchRevertJobRequest_Options } from './anno_v1_BatchRevertJobRequest_Options';
import type { anno_v1_ListJobFilter } from './anno_v1_ListJobFilter';

export type anno_v1_BatchRevertJobRequest = {
  /**
   * changes to be made to the jobs
   */
  action: anno_v1_BatchRevertJobRequest_Action;
  /**
   * options to the action
   */
  options: anno_v1_BatchRevertJobRequest_Options;
  /**
   * job filter
   */
  filter: anno_v1_ListJobFilter;
};
