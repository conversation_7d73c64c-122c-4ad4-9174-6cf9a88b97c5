/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_AnnoComment } from './anno_v1_AnnoComment';
import type { anno_v1_JobAnno } from './anno_v1_JobAnno';
import type { anno_v1_ResolveAnnoComment } from './anno_v1_ResolveAnnoComment';

export type anno_v1_ReviewJobRequest = {
  /**
   * job uid
   */
  uid: string;
  /**
   * if it is null, this review makes no change to previous job annotations;
   * otherwise, it is a complete result to replace previous job annotations.
   */
  annotations?: anno_v1_JobAnno;
  /**
   * "accept" will put the job to the next phase; annotations and resolve_comments can be set.
   * "reject" will put the job to the previous phase, and assign it to the previous executor at that phase;
   * annotations, comments and resolve_comments can be set.
   * "recycle" will put the job to its initial phase; previous annotations and comments will be cleared;
   * other fields will be ignored.
   */
  decision: 'unspecified' | 'accept' | 'reject' | 'recycle';
  /**
   * new comments to the annotations when decision is "reject"
   */
  comments?: Array<anno_v1_AnnoComment>;
  /**
   * comments to resolve
   */
  resolves?: Array<anno_v1_ResolveAnnoComment>;
  /**
   * comments to update
   */
  updated_comments?: Array<anno_v1_AnnoComment>;
  /**
   * comments to delete
   */
  deleted_comments?: Array<anno_v1_ResolveAnnoComment>;
};
