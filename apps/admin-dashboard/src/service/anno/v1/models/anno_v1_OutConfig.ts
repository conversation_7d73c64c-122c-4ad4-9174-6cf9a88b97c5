/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_DataConverter } from './anno_v1_DataConverter';
import type { anno_v1_OutConfig_Exporter } from './anno_v1_OutConfig_Exporter';

export type anno_v1_OutConfig = {
  /**
   * exporter configuration
   */
  exporter: anno_v1_OutConfig_Exporter;
  /**
   * encoding type
   */
  encoder: 'json';
  /**
   * files layout
   */
  layout: string;
  /**
   * file style
   */
  style: string;
  /**
   * converter is a piece of script to convert the annos from platform format to customer format
   */
  converter?: anno_v1_DataConverter;
};
