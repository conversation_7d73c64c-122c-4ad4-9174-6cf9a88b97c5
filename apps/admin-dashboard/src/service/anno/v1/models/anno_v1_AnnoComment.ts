/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_AnnoComment_ExtraInfo } from './anno_v1_AnnoComment_ExtraInfo';
import type { anno_v1_AnnoCommentReason_Reasons } from './anno_v1_AnnoCommentReason_Reasons';
import type { iam_v1_BaseUser } from './iam_v1_BaseUser';

/**
 * comment to an annotation
 */
export type anno_v1_AnnoComment = {
  /**
   * element index within the job
   */
  elem_idx: number;
  /**
   * details or additional information
   */
  content?: string;
  /**
   * the user who added the comment;
   * the field is ignored in review requests
   */
  commenter: iam_v1_BaseUser;
  /**
   * number of the phase in which the comment is added;
   * the field is ignored in review requests
   */
  add_phase: number;
  /**
   * number of the highest phase in which the comment is marked as resolved
   * in frontend, only executors in phases within (resolve_phase, add_phase] can see the comment and react to it;
   * the field is ignored in review requests
   */
  resolve_phase: number;
  /**
   * rawdata index within the element; should not be omitted if the comment is missed（漏标）
   */
  rd_idx?: number;
  /**
   * extra info associated with the comment
   */
  extra_info?: anno_v1_AnnoComment_ExtraInfo;
  /**
   * UUID of the comment; it is obj_uuid when omitted. Must not be empty if reason is missed（漏标）.
   */
  uuid: string;
  /**
   * UUID of the associated objects; it may be empty if the comment is missed(漏标), or assicated with the element or job
   */
  obj_uuids: Array<string>;
  /**
   * comment reasons
   */
  reasons: anno_v1_AnnoCommentReason_Reasons;
  /**
   * scope of the comment
   */
  scope?: 'unspecified' | 'object' | 'element' | 'job';
  /**
   * the field is ignored in review requests
   */
  created_at: string;
};
