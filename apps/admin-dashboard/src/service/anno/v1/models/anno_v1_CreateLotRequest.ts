/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_CommentReasonClass } from './anno_v1_CommentReasonClass';
import type { anno_v1_Lot_ToolConfig } from './anno_v1_Lot_ToolConfig';
import type { anno_v1_Lotontologies } from './anno_v1_Lotontologies';
import type { anno_v1_Lotphase } from './anno_v1_Lotphase';
import type { anno_v1_OutConfig } from './anno_v1_OutConfig';

export type anno_v1_CreateLotRequest = {
  /**
   * mandatory in update-requests
   */
  uid?: string;
  /**
   * mandatory in create-requests
   */
  name?: string;
  desc?: string;
  /**
   * mandatory in create-requests
   */
  project_uid?: string;
  /**
   * task type; mandatory in create-requests
   */
  type?: 'unspecified' | 'annotate' | 'segment_instance' | 'segment_semantic' | 'segment_panoptic';
  /**
   * larger number indicates higher priority
   */
  priority?: number;
  /**
   * whether to automatically start
   */
  autostart?: boolean;
  /**
   * number of elements in a job
   */
  job_size?: number;
  /**
   * lot ontologies
   */
  ontologies?: anno_v1_Lotontologies;
  /**
   * execution phases; phase number starts from 1
   */
  phases?: Array<anno_v1_Lotphase>;
  /**
   * execution instructions in format of HTML or Markdown
   */
  instruction?: string;
  org_uid?: string;
  /**
   * mandatory in create-requests
   */
  data_uid?: string;
  /**
   * annotation result output config
   */
  out?: anno_v1_OutConfig;
  /**
   * expected end time
   */
  exp_end_time?: string;
  /**
   * if to treat job as consecutive frames
   */
  is_frame_series?: boolean;
  /**
   * order UID
   */
  order_uid?: string;
  /**
   * annotation tool configuration
   */
  tool_cfg?: anno_v1_Lot_ToolConfig;
  /**
   * allowed comment reason list
   */
  comment_reasons?: Array<anno_v1_CommentReasonClass>;
};
