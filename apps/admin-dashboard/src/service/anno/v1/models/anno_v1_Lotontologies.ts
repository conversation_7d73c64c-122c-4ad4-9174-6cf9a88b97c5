/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_Attr } from './anno_v1_Attr';
import type { anno_v1_AttrRefList } from './anno_v1_AttrRefList';
import type { anno_v1_Lotontologies_Group } from './anno_v1_Lotontologies_Group';

export type anno_v1_Lotontologies = {
  /**
   * definitions for all attributes
   */
  attrs: Record<string, anno_v1_Attr>;
  /**
   * global attribute names for an element(frame)
   */
  elem_attrs?: anno_v1_AttrRefList;
  /**
   * global attribute names for a rawdata(image/file)
   */
  rawdata_attrs?: anno_v1_AttrRefList;
  /**
   * when there are mutiple groups, the lot will be splitted according to groups
   */
  groups: Array<anno_v1_Lotontologies_Group>;
  /**
   * global attribute names for a job
   */
  job_attrs?: anno_v1_AttrRefList;
};
