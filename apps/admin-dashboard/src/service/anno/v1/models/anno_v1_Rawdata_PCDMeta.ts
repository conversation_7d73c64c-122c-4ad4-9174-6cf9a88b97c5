/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_RawdataParam } from './anno_v1_RawdataParam';

export type anno_v1_Rawdata_PCDMeta = {
  /**
   * number of points in the pcd file
   */
  points: number;
  /**
   * viewpoint of the point cloud in the point cloud's coordinate system
   */
  viewpoint?: anno_v1_RawdataParam;
  /**
   * origin point of the point cloud in world coordinate system when point cloud is not in world coordinate system,
   * in the form of [x,y,z,qx,qy,qz,qw]
   */
  pose?: Array<number>;
};
