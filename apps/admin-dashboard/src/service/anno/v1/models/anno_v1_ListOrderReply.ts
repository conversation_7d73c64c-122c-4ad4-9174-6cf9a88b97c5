/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_Order } from './anno_v1_Order';
import type { iam_v1_BaseUser } from './iam_v1_BaseUser';

export type anno_v1_ListOrderReply = {
  /**
   * total number of items found; valid only in the first page reply.
   */
  total?: number;
  orders?: Array<anno_v1_Order>;
  /**
   * the organization that the order, at the corresponding position, belongs to.
   */
  orgs?: Array<iam_v1_BaseUser>;
};
