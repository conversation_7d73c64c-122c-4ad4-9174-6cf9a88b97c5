/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_DataConverter } from './anno_v1_DataConverter';
import type { anno_v1_Source_ParseErrorHandler } from './anno_v1_Source_ParseErrorHandler';
import type { anno_v1_Source_Proprietary } from './anno_v1_Source_Proprietary';

export type anno_v1_Source = {
  /**
   * package file (.zip) URIs
   */
  uris: Array<string>;
  /**
   * access config when the files are hosted in a third-party platform
   */
  proprietary?: anno_v1_Source_Proprietary;
  /**
   * folder layout style within package files if not conform to Konvery standard
   */
  style?: string;
  /**
   * element type
   */
  elem_type?: 'unspecified' | 'image' | 'pointcloud' | 'fusion2d' | 'fusion3d' | 'fusion4d';
  /**
   * if source contains consecutive frames
   */
  is_frame_series?: boolean;
  /**
   * size of the unpacked data in GB
   */
  plain_size_gb?: number;
  /**
   * define parser error handlers; it will fail the parser if no handler is specified.
   * max size is count(RawdataErrorAction.Error) x count(Rawdata.Type).
   */
  error_handlers?: Array<anno_v1_Source_ParseErrorHandler>;
  /**
   * whether to automatically parse data in annofeed service
   */
  auto_parse?: boolean;
  /**
   * define single file names and their corresponding expected names;
   */
  named_uris?: Record<string, string>;
  /**
   * converter is a piece of script to convert the source data to the platform accepted format
   */
  converter?: anno_v1_DataConverter;
  /**
   * metadata about original data, which might be used in parsing data and exporting annos
   */
  metadata?: Record<string, string>;
};
