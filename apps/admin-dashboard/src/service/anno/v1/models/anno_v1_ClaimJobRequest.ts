/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type anno_v1_ClaimJobRequest = {
  /**
   * which lot the job is claimed from; if not specified, the job is claimed from any assigned lot
   */
  lot_uid: string;
  /**
   * for a splitted lot, this is to specify the sub-lot.
   */
  subtype?: string;
  /**
   * when lot_uid/subtype is specified and no available job can be claimed, this controls if it
   * should retry to claim in disregard of lot_uid/subtype
   */
  fallback?: boolean;
  /**
   * claim preference
   */
  prefer?: 'unspecified' | 'rejected_first';
  /**
   * true to reset the claim countdown timer; the response will not contain the job info.
   */
  renew?: boolean;
  /**
   * required if renew is true
   */
  job_uid?: string;
};
