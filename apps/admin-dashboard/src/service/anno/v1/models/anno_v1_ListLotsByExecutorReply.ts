/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_ListLotsByExecutorReply_Extra } from './anno_v1_ListLotsByExecutorReply_Extra';
import type { anno_v1_Lot } from './anno_v1_Lot';

export type anno_v1_ListLotsByExecutorReply = {
  /**
   * total number of items found; valid only in the first page reply.
   */
  total?: number;
  lots: Array<anno_v1_Lot>;
  /**
   * the extra info that the lot has
   */
  extras?: Record<string, anno_v1_ListLotsByExecutorReply_Extra>;
};
