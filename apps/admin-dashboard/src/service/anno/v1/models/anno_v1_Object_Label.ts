/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_AttrAndValues } from './anno_v1_AttrAndValues';
import type { anno_v1_Object_Widget } from './anno_v1_Object_Widget';

export type anno_v1_Object_Label = {
  /**
   * label name
   */
  name: string;
  /**
   * widget will be null if compound is not null
   */
  widget?: anno_v1_Object_Widget;
  /**
   * attributes associated with the object
   */
  attrs?: Array<anno_v1_AttrAndValues>;
};
