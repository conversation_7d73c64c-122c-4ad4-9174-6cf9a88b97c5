/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_Log_Details } from './anno_v1_Log_Details';
import type { iam_v1_BaseUser } from './iam_v1_BaseUser';

export type anno_v1_GetJoblogReply_Log = {
  operator: iam_v1_BaseUser;
  action:
    | 'unspecified'
    | 'accept'
    | 'reject'
    | 'recycle'
    | 'submit'
    | 'claim'
    | 'giveup'
    | 'assign'
    | 'timeout'
    | 'end';
  /**
   * detailed information if any
   */
  details?: anno_v1_Log_Details;
  /**
   * phase number at this event
   */
  from_phase?: number;
  /**
   * phase number after this event
   */
  to_phase?: number;
  /**
   * created_at timestamp
   */
  created_at?: string;
};
