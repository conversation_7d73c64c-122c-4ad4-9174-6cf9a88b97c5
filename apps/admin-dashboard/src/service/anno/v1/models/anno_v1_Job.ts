/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_AnnoComment } from './anno_v1_AnnoComment';
import type { anno_v1_AttrAndValues } from './anno_v1_AttrAndValues';
import type { anno_v1_Element } from './anno_v1_Element';
import type { anno_v1_ElementAnno } from './anno_v1_ElementAnno';
import type { anno_v1_Job_CamParam } from './anno_v1_Job_CamParam';
import type { iam_v1_BaseUser } from './iam_v1_BaseUser';

export type anno_v1_Job = {
  /**
   * job UID
   */
  uid: string;
  /**
   * lot UID
   */
  lot_uid: string;
  /**
   * job index in the lot
   */
  idx_in_lot: number;
  /**
   * identify a subjob in a splitted lot.
   */
  subtype: string;
  /**
   * 待标注的内容
   */
  elements: Array<anno_v1_Element>;
  /**
   * 标注结果
   */
  annotations: Array<anno_v1_ElementAnno>;
  /**
   * unresolved comments (a comment can only be resolved at the same phase when it is added)
   */
  comments: Array<anno_v1_AnnoComment>;
  state: 'unspecified' | 'unstart' | 'doing' | 'finished';
  /**
   * why job is in the state
   */
  cause: string;
  /**
   * phase number, starts from 1
   */
  phase: number;
  /**
   * manually annotated objects count in the job
   */
  ins_cnt: number;
  /**
   * annotated objects count in the job (including the interpolated ones)
   */
  ins_total?: number;
  /**
   * current executor; valid for privileged requestors
   */
  executor_uid?: string;
  /**
   * if it is true, the annotations contain only the key element results,
   * and interpolation is needed to get the final result
   */
  need_interpolation?: boolean;
  /**
   * current executor or last submitter; valid for privileged requestors
   */
  last_executor?: iam_v1_BaseUser;
  /**
   * team of last_executor; valid for privileged requestors
   */
  last_execteam?: iam_v1_BaseUser;
  /**
   * number of elements in this job
   */
  elems_cnt?: number;
  /**
   * job attributes
   */
  job_attrs?: Array<anno_v1_AttrAndValues>;
  /**
   * 相机映射参数，其中 key is rawdata.meta.image_meta.camera
   */
  cam_params?: Record<string, anno_v1_Job_CamParam>;
  /**
   * 保存了 elements 等数据, 其结构参考 Job_ElementData;
   * 如果有多个 url，完整的内容将是这些 url 所指文件的合集，cam_params 只会放在第一个文件里
   */
  elements_urls?: Array<string>;
  /**
   * 保存了 annotations 等数据, 其结构参考 Job_AnnotationData
   */
  annotations_url?: string;
  /**
   * 保存了 comments 等数据，其结构参考 Job_CommentData
   */
  comments_url?: string;
  job_elem_clip?: string;
  readonly updated_at?: string;
  readonly created_at: string;
};
