/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_AttrAndValues } from './anno_v1_AttrAndValues';
import type { anno_v1_ElementAnno } from './anno_v1_ElementAnno';

/**
 * annotations of a job
 */
export type anno_v1_JobAnno = {
  /**
   * 相应位置的元素对应 Job 的 Elements 相应位置的结果
   */
  element_annos: Array<anno_v1_ElementAnno>;
  /**
   * if it is true, the annotations contain only the key element results,
   * and interpolation is needed to get the final result
   */
  need_interpolation: boolean;
  /**
   * number of objects annotated in the job (including the interpolated objects)
   */
  ins_cnt: number;
  /**
   * job index in the lot
   */
  job_index?: number;
  /**
   * job attributes
   */
  attrs?: Array<anno_v1_AttrAndValues>;
};
