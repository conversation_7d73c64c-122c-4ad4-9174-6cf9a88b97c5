/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { types_Range } from './types_Range';

export type anno_v1_Lot_Range = {
  /**
   * shape type
   */
  shape: 'unspecified' | 'circle' | 'rectangle';
  /**
   * the params of the shape; specific to shape type.
   * circle: [radius], the unit is meter (in pointclouds), or pixel (in images);
   * rectangle: [width, height], the unit is meter (in pointclouds), or pixel (in images);
   */
  data: Array<number>;
  /**
   * range on the z-axis, the unit is meter
   */
  zrange?: types_Range;
  /**
   * which rawdata types the range is applicable to
   */
  rawdata_types?: Array<'unspecified' | 'image' | 'pointcloud'>;
};
