/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_AnnoComment } from './anno_v1_AnnoComment';
import type { anno_v1_Log_GiveupReason } from './anno_v1_Log_GiveupReason';
import type { anno_v1_ResolveAnnoComment } from './anno_v1_ResolveAnnoComment';

export type anno_v1_Log_Details = {
  /**
   * added comments in this log
   */
  add_comments: Array<anno_v1_AnnoComment>;
  /**
   * resolved comments in this log
   */
  resolves: Array<anno_v1_ResolveAnnoComment>;
  /**
   * job giveup reason
   */
  giveup_reason?: anno_v1_Log_GiveupReason;
};
