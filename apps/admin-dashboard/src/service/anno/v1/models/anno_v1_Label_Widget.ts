/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { types_Range } from './types_Range';

export type anno_v1_Label_Widget = {
  /**
   * widget name
   */
  name:
    | 'unspecified'
    | 'box2d'
    | 'pscuboid'
    | 'cuboid'
    | 'poly2d'
    | 'poly3d'
    | 'line2d'
    | 'line3d'
    | 'point2d'
    | 'point3d'
    | 'bitmap';
  /**
   * rawdata type the widget is applicable to
   */
  rawdata_type: 'unspecified' | 'image' | 'pointcloud';
  /**
   * preset scale: it is
   * [width, height] for box2d in pixel;
   * [width, height, depth (distance between two center-points)] for pscuboid in pixel;
   * [sx, sy, sz] for cuboid in meter;
   */
  preset?: Array<number>;
  /**
   * define the minimum and maximum scales the widget must conform to; see comments on the preset field for its format
   */
  scale?: types_Range;
  /**
   * line type used to draw the widget
   */
  line_type?: 'line' | 'crspline';
};
