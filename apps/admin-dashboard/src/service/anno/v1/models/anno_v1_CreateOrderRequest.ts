/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_Source } from './anno_v1_Source';

export type anno_v1_CreateOrderRequest = {
  /**
   * mandatory in update-requests
   */
  uid?: string;
  /**
   * mandatory in create-requests;
   * pattern: ^[\\p{Han}\\w\\d_-]{0,256}$
   */
  name?: string;
  /**
   * order's organization; if empty, this is the requestor's organization
   */
  org_uid?: string;
  /**
   * files attached to the order
   */
  source?: anno_v1_Source;
};
