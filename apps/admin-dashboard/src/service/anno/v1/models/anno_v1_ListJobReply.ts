/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_ElementAnno_Metadata } from './anno_v1_ElementAnno_Metadata';
import type { anno_v1_Job } from './anno_v1_Job';

export type anno_v1_ListJobReply = {
  /**
   * total number of items found; valid only in the first page reply.
   */
  total?: number;
  jobs?: Array<anno_v1_Job>;
  /**
   * jobs' executors in each phase. the key is job uid.
   */
  executors?: Record<string, anno_v1_ElementAnno_Metadata>;
};
