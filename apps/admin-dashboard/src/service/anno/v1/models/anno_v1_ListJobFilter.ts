/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type anno_v1_ListJobFilter = {
  /**
   * filter by job uids; max length is 100
   */
  uids?: Array<string>;
  /**
   * filter by belonging lot
   */
  lot_uid: string;
  /**
   * filter by jobs current phase; starts from 1
   */
  phase?: number;
  /**
   * filter by jobs current state
   */
  state?: 'unspecified' | 'unstart' | 'doing' | 'finished';
  /**
   * query jobs by their last executors; max length is 100
   */
  last_executors?: Array<string>;
  /**
   * query jobs by the last executor's belonging team
   */
  last_execteam?: string;
  phases?: Array<number>;
  jobclip?: string;
};
