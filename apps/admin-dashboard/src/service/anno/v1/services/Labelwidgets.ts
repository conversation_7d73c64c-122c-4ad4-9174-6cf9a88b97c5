/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { anno_v1_ListLabelwidgetReply } from '../models/anno_v1_ListLabelwidgetReply';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Labelwidgets {
  /**
   * @returns anno_v1_ListLabelwidgetReply OK
   * @throws ApiError
   */
  public static labelwidgetsListLabelwidget(): CancelablePromise<anno_v1_ListLabelwidgetReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/labelwidgets',
    });
  }
}
