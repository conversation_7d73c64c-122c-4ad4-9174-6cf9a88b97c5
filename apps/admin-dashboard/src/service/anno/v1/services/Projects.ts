/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { anno_v1_CreateProjectRequest } from '../models/anno_v1_CreateProjectRequest';
import type { anno_v1_ListProjectReply } from '../models/anno_v1_ListProjectReply';
import type { anno_v1_Project } from '../models/anno_v1_Project';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Projects {
  /**
   * @returns anno_v1_ListProjectReply OK
   * @throws ApiError
   */
  public static projectsListProject({
    page,
    pagesz,
    orgUid,
    creatorUid,
    namePattern,
  }: {
    page?: number;
    pagesz?: number;
    /**
     * filter by orgnization
     */
    orgUid?: string;
    /**
     * filter by creator
     */
    creatorUid?: string;
    /**
     * filter by name pattern
     */
    namePattern?: string;
  }): CancelablePromise<anno_v1_ListProjectReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/projects',
      query: {
        page: page,
        pagesz: pagesz,
        org_uid: orgUid,
        creator_uid: creatorUid,
        name_pattern: namePattern,
      },
    });
  }

  /**
   * @returns anno_v1_Project OK
   * @throws ApiError
   */
  public static projectsCreateProject({
    requestBody,
  }: {
    requestBody: anno_v1_CreateProjectRequest;
  }): CancelablePromise<anno_v1_Project> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/anno/v1/projects',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns anno_v1_Project OK
   * @throws ApiError
   */
  public static projectsUpdateProject({
    projectUid,
    requestBody,
    fields,
  }: {
    projectUid: string;
    requestBody: anno_v1_CreateProjectRequest;
    fields?: Array<string>;
  }): CancelablePromise<anno_v1_Project> {
    return __request(APIConfig, {
      method: 'PATCH',
      url: '/anno/v1/projects/{project.uid}',
      path: {
        'project.uid': projectUid,
      },
      query: {
        fields: fields,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns anno_v1_Project OK
   * @throws ApiError
   */
  public static projectsGetProject({ uid }: { uid: string }): CancelablePromise<anno_v1_Project> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/projects/{uid}',
      path: {
        uid: uid,
      },
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static projectsDeleteProject({ uid }: { uid: string }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/anno/v1/projects/{uid}',
      path: {
        uid: uid,
      },
    });
  }
}
