/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { anno_v1_AssignJobRequest } from '../models/anno_v1_AssignJobRequest';
import type { anno_v1_BatchRevertJobReply } from '../models/anno_v1_BatchRevertJobReply';
import type { anno_v1_BatchRevertJobRequest } from '../models/anno_v1_BatchRevertJobRequest';
import type { anno_v1_ClaimJobRequest } from '../models/anno_v1_ClaimJobRequest';
import type { anno_v1_ClaimJobResponse } from '../models/anno_v1_ClaimJobResponse';
import type { anno_v1_GetJobDraftReply } from '../models/anno_v1_GetJobDraftReply';
import type { anno_v1_GetJobLastCommitLogReply } from '../models/anno_v1_GetJobLastCommitLogReply';
import type { anno_v1_GetJoblogReply } from '../models/anno_v1_GetJoblogReply';
import type { anno_v1_GetRawJoblogReply } from '../models/anno_v1_GetRawJoblogReply';
import type { anno_v1_GetSkipAnnotationReply } from '../models/anno_v1_GetSkipAnnotationReply';
import type { anno_v1_GiveupJobRequest } from '../models/anno_v1_GiveupJobRequest';
import type { anno_v1_HasHoldingJobsReply } from '../models/anno_v1_HasHoldingJobsReply';
import type { anno_v1_Job } from '../models/anno_v1_Job';
import type { anno_v1_ListJobReply } from '../models/anno_v1_ListJobReply';
import type { anno_v1_ReviewJobRequest } from '../models/anno_v1_ReviewJobRequest';
import type { anno_v1_SaveJobDraftReply } from '../models/anno_v1_SaveJobDraftReply';
import type { anno_v1_SaveJobDraftRequest } from '../models/anno_v1_SaveJobDraftRequest';
import type { anno_v1_SetRawdataEmbeddingRequest } from '../models/anno_v1_SetRawdataEmbeddingRequest';
import type { anno_v1_SkipAnnotationReply } from '../models/anno_v1_SkipAnnotationReply';
import type { anno_v1_SkipAnnotationRequest } from '../models/anno_v1_SkipAnnotationRequest';
import type { anno_v1_SubmitJobRequest } from '../models/anno_v1_SubmitJobRequest';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Jobs {
  /**
   * @returns anno_v1_ListJobReply OK
   * @throws ApiError
   */
  public static jobsListJob({
    page,
    pagesz,
    filterUids,
    filterLotUid,
    filterPhase,
    filterState,
    filterLastExecutors,
    filterLastExecteam,
    filterPhases,
    filterJobclip,
    showLastExecutor,
    fullJob,
    showExecutors,
    elemNamePattern,
  }: {
    page?: number;
    pagesz?: number;
    /**
     * filter by job uids; max length is 100
     */
    filterUids?: Array<string>;
    /**
     * filter by belonging lot
     */
    filterLotUid?: string;
    /**
     * filter by jobs current phase; starts from 1
     */
    filterPhase?: number;
    /**
     * filter by jobs current state
     */
    filterState?: 'unspecified' | 'unstart' | 'doing' | 'finished';
    /**
     * query jobs by their last executors; max length is 100
     */
    filterLastExecutors?: Array<string>;
    /**
     * query jobs by the last executor's belonging team
     */
    filterLastExecteam?: string;
    filterPhases?: Array<number>;
    filterJobclip?: string;
    /**
     * if to return jobs' last_executor and last_execteam in the response
     */
    showLastExecutor?: boolean;
    /**
     * [PRIVILEGED] show full job info, including annotations, comments, etc.
     */
    fullJob?: boolean;
    /**
     * whether to return jobs' executor in each phase
     */
    showExecutors?: boolean;
    /**
     * query jobs by their containing elements' name pattern
     */
    elemNamePattern?: string;
  }): CancelablePromise<anno_v1_ListJobReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/jobs',
      query: {
        page: page,
        pagesz: pagesz,
        'filter.uids': filterUids,
        'filter.lot_uid': filterLotUid,
        'filter.phase': filterPhase,
        'filter.state': filterState,
        'filter.last_executors': filterLastExecutors,
        'filter.last_execteam': filterLastExecteam,
        'filter.phases': filterPhases,
        'filter.jobclip': filterJobclip,
        show_last_executor: showLastExecutor,
        full_job: fullJob,
        show_executors: showExecutors,
        elem_name_pattern: elemNamePattern,
      },
    });
  }

  /**
   * check if there is any holding jobs for users/orgs
   * @returns anno_v1_HasHoldingJobsReply OK
   * @throws ApiError
   */
  public static jobsHasHoldingJobs({
    orgUid,
    userUids,
  }: {
    /**
     * if available, only query by org_uid; otherwise, query by user_uids
     */
    orgUid?: string;
    userUids?: Array<string>;
  }): CancelablePromise<anno_v1_HasHoldingJobsReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/jobs/-/holding',
      query: {
        org_uid: orgUid,
        user_uids: userUids,
      },
    });
  }

  /**
   * Claim a job
   * @returns anno_v1_ClaimJobResponse OK
   * @throws ApiError
   */
  public static jobsClaimJob({
    requestBody,
  }: {
    requestBody: anno_v1_ClaimJobRequest;
  }): CancelablePromise<anno_v1_ClaimJobResponse> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/anno/v1/jobs/claim',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Revert multiple jobs to a previous phase.
   * @returns anno_v1_BatchRevertJobReply OK
   * @throws ApiError
   */
  public static jobsBatchRevertJob({
    requestBody,
  }: {
    requestBody: anno_v1_BatchRevertJobRequest;
  }): CancelablePromise<anno_v1_BatchRevertJobReply> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/anno/v1/jobs/revert',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns anno_v1_Job OK
   * @throws ApiError
   */
  public static jobsGetJob({
    uid,
    expand,
  }: {
    uid: string;
    /**
     * [PRIVILEGED] if true, the job data (e.g. elements, annotations) will be expanded
     */
    expand?: boolean;
  }): CancelablePromise<anno_v1_Job> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/jobs/{uid}',
      path: {
        uid: uid,
      },
      query: {
        expand: expand,
      },
    });
  }

  /**
   * Assign a job
   * @returns any OK
   * @throws ApiError
   */
  public static jobsAssignJob({
    uid,
    requestBody,
  }: {
    uid: string;
    requestBody: anno_v1_AssignJobRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/anno/v1/jobs/{uid}/assign',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get draft annos/comments saved in the server.
   * @returns anno_v1_GetJobDraftReply OK
   * @throws ApiError
   */
  public static jobsGetJobDraft({ uid }: { uid: string }): CancelablePromise<anno_v1_GetJobDraftReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/jobs/{uid}/draft',
      path: {
        uid: uid,
      },
    });
  }

  /**
   * Save draft annos/comments in the server.
   * @returns anno_v1_SaveJobDraftReply OK
   * @throws ApiError
   */
  public static jobsSaveJobDraft({
    uid,
    requestBody,
  }: {
    /**
     * job uid
     */
    uid: string;
    requestBody: anno_v1_SaveJobDraftRequest;
  }): CancelablePromise<anno_v1_SaveJobDraftReply> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/anno/v1/jobs/{uid}/draft',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Set rawdata embedding.
   * @returns any OK
   * @throws ApiError
   */
  public static jobsSetRawdataEmbedding({
    uid,
    elemIdx,
    rawdataIdx,
    requestBody,
  }: {
    /**
     * job UID
     */
    uid: string;
    /**
     * element index within job
     */
    elemIdx: number;
    /**
     * rawdata index within element
     */
    rawdataIdx: number;
    requestBody: anno_v1_SetRawdataEmbeddingRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/anno/v1/jobs/{uid}/elems/{elem_idx}/rawdatas/{rawdata_idx}/embedding',
      path: {
        uid: uid,
        elem_idx: elemIdx,
        rawdata_idx: rawdataIdx,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns anno_v1_GetSkipAnnotationReply OK
   * @throws ApiError
   */
  public static jobsGetSkipAnnotation({
    uid,
  }: {
    /**
     * if available, only query by org_uid; otherwise, query by user_uids
     */
    uid: string;
  }): CancelablePromise<anno_v1_GetSkipAnnotationReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/jobs/{uid}/get-skip-annotation',
      path: {
        uid: uid,
      },
    });
  }

  /**
   * giveup a job
   * @returns any OK
   * @throws ApiError
   */
  public static jobsGiveupJob({
    uid,
    requestBody,
  }: {
    uid: string;
    requestBody: anno_v1_GiveupJobRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/anno/v1/jobs/{uid}/giveup',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns anno_v1_GetJoblogReply OK
   * @throws ApiError
   */
  public static jobsGetJoblog({
    uid,
    page,
    pagesz,
  }: {
    uid: string;
    page?: number;
    pagesz?: number;
  }): CancelablePromise<anno_v1_GetJoblogReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/jobs/{uid}/log',
      path: {
        uid: uid,
      },
      query: {
        page: page,
        pagesz: pagesz,
      },
    });
  }

  /**
   * get the last commit joblog of a phase
   * @returns anno_v1_GetJobLastCommitLogReply OK
   * @throws ApiError
   */
  public static jobsGetJobLastCommitLog({
    uid,
    phase,
    direction,
  }: {
    /**
     * job uid
     */
    uid: string;
    /**
     * phase number: 0 means all the phases
     */
    phase: number;
    /**
     * commit direction
     */
    direction?: 'unspecified' | 'up' | 'down';
  }): CancelablePromise<anno_v1_GetJobLastCommitLogReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/jobs/{uid}/phases/{phase}/last-commit-log',
      path: {
        uid: uid,
        phase: phase,
      },
      query: {
        direction: direction,
      },
    });
  }

  /**
   * @returns anno_v1_GetRawJoblogReply OK
   * @throws ApiError
   */
  public static jobsGetRawJoblog({
    uid,
    page,
    pagesz,
  }: {
    uid: string;
    page?: number;
    pagesz?: number;
  }): CancelablePromise<anno_v1_GetRawJoblogReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/jobs/{uid}/rawlog',
      path: {
        uid: uid,
      },
      query: {
        page: page,
        pagesz: pagesz,
      },
    });
  }

  /**
   * Review job annotations; used in 2nd phase onwards.
   * @returns any OK
   * @throws ApiError
   */
  public static jobsReviewJob({
    uid,
    requestBody,
  }: {
    /**
     * job uid
     */
    uid: string;
    requestBody: anno_v1_ReviewJobRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/anno/v1/jobs/{uid}/review',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns anno_v1_SkipAnnotationReply OK
   * @throws ApiError
   */
  public static jobsSkipAnnotation({
    uid,
    requestBody,
  }: {
    /**
     * if available, only query by org_uid; otherwise, query by user_uids
     */
    uid: string;
    requestBody: anno_v1_SkipAnnotationRequest;
  }): CancelablePromise<anno_v1_SkipAnnotationReply> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/anno/v1/jobs/{uid}/skip-annotation',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Submit job annotations; used in the 1st phase only.
   * @returns any OK
   * @throws ApiError
   */
  public static jobsSubmitJob({
    uid,
    requestBody,
  }: {
    uid: string;
    requestBody: anno_v1_SubmitJobRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/anno/v1/jobs/{uid}/submit',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }
}
