/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { anno_v1_Bizgrant } from '../models/anno_v1_Bizgrant';
import type { anno_v1_CreateBizgrantRequest } from '../models/anno_v1_CreateBizgrantRequest';
import type { anno_v1_ListBizgrantReply } from '../models/anno_v1_ListBizgrantReply';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Bizgrants {
  /**
   * @returns anno_v1_ListBizgrantReply OK
   * @throws ApiError
   */
  public static bizgrantsListBizgrant({
    pagesz,
    pageToken,
    filterGrantorUid,
    filterGranteeUid,
    filterOrgUid,
  }: {
    pagesz?: number;
    /**
     * An opaque pagination token returned from a previous call.
     * An empty token denotes the first page.
     */
    pageToken?: string;
    /**
     * grantor uid
     */
    filterGrantorUid?: string;
    /**
     * grantee uid
     */
    filterGranteeUid?: string;
    /**
     * uid of the organization whose business permissions are granted
     */
    filterOrgUid?: string;
  }): CancelablePromise<anno_v1_ListBizgrantReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/bizgrants',
      query: {
        pagesz: pagesz,
        page_token: pageToken,
        'filter.grantor_uid': filterGrantorUid,
        'filter.grantee_uid': filterGranteeUid,
        'filter.org_uid': filterOrgUid,
      },
    });
  }

  /**
   * @returns anno_v1_Bizgrant OK
   * @throws ApiError
   */
  public static bizgrantsCreateBizgrant({
    requestBody,
  }: {
    requestBody: anno_v1_CreateBizgrantRequest;
  }): CancelablePromise<anno_v1_Bizgrant> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/anno/v1/bizgrants',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static bizgrantsDeleteBizgrant({
    filterGrantorUid,
    filterGranteeUid,
    filterOrgUid,
  }: {
    /**
     * grantor uid
     */
    filterGrantorUid?: string;
    /**
     * grantee uid
     */
    filterGranteeUid?: string;
    /**
     * uid of the organization whose business permissions are granted
     */
    filterOrgUid?: string;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/anno/v1/bizgrants',
      query: {
        'filter.grantor_uid': filterGrantorUid,
        'filter.grantee_uid': filterGranteeUid,
        'filter.org_uid': filterOrgUid,
      },
    });
  }
}
