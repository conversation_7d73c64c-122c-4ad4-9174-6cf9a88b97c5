/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { anno_v1_CreateOrderRequest } from '../models/anno_v1_CreateOrderRequest';
import type { anno_v1_ExportOrderAnnosRequest } from '../models/anno_v1_ExportOrderAnnosRequest';
import type { anno_v1_GetOrderAnnoResultReply } from '../models/anno_v1_GetOrderAnnoResultReply';
import type { anno_v1_GetOrderRequest } from '../models/anno_v1_GetOrderRequest';
import type { anno_v1_ListOrderReply } from '../models/anno_v1_ListOrderReply';
import type { anno_v1_Order } from '../models/anno_v1_Order';
import type { anno_v1_SetOrderAnnoResultRequest } from '../models/anno_v1_SetOrderAnnoResultRequest';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Orders {
  /**
   * @returns anno_v1_ListOrderReply OK
   * @throws ApiError
   */
  public static ordersListOrder({
    page,
    pagesz,
    orgUid,
    creatorUid,
    namePattern,
    states,
    withOrg,
  }: {
    page?: number;
    pagesz?: number;
    /**
     * filter by orgnization
     */
    orgUid?: string;
    /**
     * filter by creator
     */
    creatorUid?: string;
    /**
     * filter by name pattern
     */
    namePattern?: string;
    /**
     * filter by order state
     */
    states?: Array<'unspecified' | 'initializing' | 'waiting' | 'ongoing' | 'finished' | 'canceled' | 'failed'>;
    /**
     * include order's organization in the reply
     */
    withOrg?: boolean;
  }): CancelablePromise<anno_v1_ListOrderReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/orders',
      query: {
        page: page,
        pagesz: pagesz,
        org_uid: orgUid,
        creator_uid: creatorUid,
        name_pattern: namePattern,
        states: states,
        with_org: withOrg,
      },
    });
  }

  /**
   * @returns anno_v1_Order OK
   * @throws ApiError
   */
  public static ordersCreateOrder({
    requestBody,
  }: {
    requestBody: anno_v1_CreateOrderRequest;
  }): CancelablePromise<anno_v1_Order> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/anno/v1/orders',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns anno_v1_Order OK
   * @throws ApiError
   */
  public static ordersUpdateOrder({
    orderUid,
    requestBody,
    fields,
  }: {
    orderUid: string;
    requestBody: anno_v1_Order;
    /**
     * name of fileds to be updated
     */
    fields?: Array<string>;
  }): CancelablePromise<anno_v1_Order> {
    return __request(APIConfig, {
      method: 'PATCH',
      url: '/anno/v1/orders/{order.uid}',
      path: {
        'order.uid': orderUid,
      },
      query: {
        fields: fields,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns anno_v1_Order OK
   * @throws ApiError
   */
  public static ordersGetOrder({
    uid,
  }: {
    /**
     * order UID
     */
    uid: string;
  }): CancelablePromise<anno_v1_Order> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/orders/{uid}',
      path: {
        uid: uid,
      },
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static ordersDeleteOrder({
    uid,
  }: {
    /**
     * order UID
     */
    uid: string;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/anno/v1/orders/{uid}',
      path: {
        uid: uid,
      },
    });
  }

  /**
   * @returns anno_v1_GetOrderAnnoResultReply OK
   * @throws ApiError
   */
  public static ordersGetAnnoResult({
    uid,
  }: {
    /**
     * order UID
     */
    uid: string;
  }): CancelablePromise<anno_v1_GetOrderAnnoResultReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/orders/{uid}/anno-result',
      path: {
        uid: uid,
      },
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static ordersSetAnnoResult({
    uid,
    requestBody,
  }: {
    /**
     * order UID
     */
    uid: string;
    requestBody: anno_v1_SetOrderAnnoResultRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/anno/v1/orders/{uid}/anno-result',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * only platform admin can cancel orders
   * @returns any OK
   * @throws ApiError
   */
  public static ordersCancelOrder({
    uid,
    requestBody,
  }: {
    /**
     * order UID
     */
    uid: string;
    requestBody: anno_v1_GetOrderRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/anno/v1/orders/{uid}/cancel',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static ordersExportOrderAnnos({
    uid,
    requestBody,
  }: {
    /**
     * order UID
     */
    uid: string;
    requestBody: anno_v1_ExportOrderAnnosRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/anno/v1/orders/{uid}/export-annos',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }
}
