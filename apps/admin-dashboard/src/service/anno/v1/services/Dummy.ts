/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { anno_v1_DummyReply } from '../models/anno_v1_DummyReply';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Dummy {
  /**
   * @returns anno_v1_DummyReply OK
   * @throws ApiError
   */
  public static dummyDummy(): CancelablePromise<anno_v1_DummyReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/dummy',
    });
  }
}
