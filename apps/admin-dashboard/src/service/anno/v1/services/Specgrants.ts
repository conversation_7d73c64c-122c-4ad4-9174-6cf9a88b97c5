/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { anno_v1_CreateSpecgrantRequest } from '../models/anno_v1_CreateSpecgrantRequest';
import type { anno_v1_ListSpecgrantReply } from '../models/anno_v1_ListSpecgrantReply';
import type { anno_v1_Specgrant } from '../models/anno_v1_Specgrant';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Specgrants {
  /**
   * @returns anno_v1_ListSpecgrantReply OK
   * @throws ApiError
   */
  public static specgrantsListSpecgrant({
    pagesz,
    pageToken,
    filterGrantorUid,
    filterGranteeUid,
    filterOrgUid,
    filterItemType,
    filterItemUids,
  }: {
    pagesz?: number;
    /**
     * An opaque pagination token returned from a previous call.
     * An empty token denotes the first page.
     */
    pageToken?: string;
    /**
     * grantor uid
     */
    filterGrantorUid?: string;
    /**
     * grantee uid
     */
    filterGranteeUid?: string;
    /**
     * item owner orgnization uid
     */
    filterOrgUid?: string;
    /**
     * item type
     */
    filterItemType?: 'unspecified' | 'AnnoLot';
    /**
     * item uid list
     */
    filterItemUids?: Array<string>;
  }): CancelablePromise<anno_v1_ListSpecgrantReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/specgrants',
      query: {
        pagesz: pagesz,
        page_token: pageToken,
        'filter.grantor_uid': filterGrantorUid,
        'filter.grantee_uid': filterGranteeUid,
        'filter.org_uid': filterOrgUid,
        'filter.item_type': filterItemType,
        'filter.item_uids': filterItemUids,
      },
    });
  }

  /**
   * @returns anno_v1_Specgrant OK
   * @throws ApiError
   */
  public static specgrantsCreateSpecgrant({
    requestBody,
  }: {
    requestBody: anno_v1_CreateSpecgrantRequest;
  }): CancelablePromise<anno_v1_Specgrant> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/anno/v1/specgrants',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static specgrantsDeleteSpecgrant({
    filterGrantorUid,
    filterGranteeUid,
    filterOrgUid,
    filterItemType,
    filterItemUids,
  }: {
    /**
     * grantor uid
     */
    filterGrantorUid?: string;
    /**
     * grantee uid
     */
    filterGranteeUid?: string;
    /**
     * item owner orgnization uid
     */
    filterOrgUid?: string;
    /**
     * item type
     */
    filterItemType?: 'unspecified' | 'AnnoLot';
    /**
     * item uid list
     */
    filterItemUids?: Array<string>;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/anno/v1/specgrants',
      query: {
        'filter.grantor_uid': filterGrantorUid,
        'filter.grantee_uid': filterGranteeUid,
        'filter.org_uid': filterOrgUid,
        'filter.item_type': filterItemType,
        'filter.item_uids': filterItemUids,
      },
    });
  }
}
