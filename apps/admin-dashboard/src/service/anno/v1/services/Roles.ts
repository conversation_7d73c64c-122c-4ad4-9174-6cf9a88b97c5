/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { anno_v1_GetUserRoleReply } from '../models/anno_v1_GetUserRoleReply';
import type { anno_v1_ListRoleReply } from '../models/anno_v1_ListRoleReply';
import type { anno_v1_ListUsersRoleReply } from '../models/anno_v1_ListUsersRoleReply';
import type { anno_v1_Role } from '../models/anno_v1_Role';
import type { anno_v1_SetUsersRoleRequest } from '../models/anno_v1_SetUsersRoleRequest';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Roles {
  /**
   * @returns anno_v1_ListRoleReply OK
   * @throws ApiError
   */
  public static rolesListRole({
    page,
    pagesz,
    namePattern,
  }: {
    page?: number;
    pagesz?: number;
    namePattern?: string;
  }): CancelablePromise<anno_v1_ListRoleReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/roles',
      query: {
        page: page,
        pagesz: pagesz,
        name_pattern: namePattern,
      },
    });
  }

  /**
   * @returns anno_v1_Role OK
   * @throws ApiError
   */
  public static rolesCreateRole({ requestBody }: { requestBody: anno_v1_Role }): CancelablePromise<anno_v1_Role> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/anno/v1/roles',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns anno_v1_ListUsersRoleReply OK
   * @throws ApiError
   */
  public static rolesListUsersRole({
    page,
    pagesz,
    teamUid,
    userUids,
    role,
  }: {
    page?: number;
    pagesz?: number;
    /**
     * Scope scope = 3;
     */
    teamUid?: string;
    userUids?: Array<string>;
    role?: Array<string>;
  }): CancelablePromise<anno_v1_ListUsersRoleReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/roles/users',
      query: {
        page: page,
        pagesz: pagesz,
        team_uid: teamUid,
        user_uids: userUids,
        role: role,
      },
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static rolesSetUsersRole({
    requestBody,
  }: {
    requestBody: anno_v1_SetUsersRoleRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/anno/v1/roles/users',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static rolesDeleteUsersRole({
    scopeType,
    scopeUid,
    userIds,
  }: {
    /**
     * global/project/lot
     */
    scopeType?: string;
    /**
     * project-uid/lot-uid
     */
    scopeUid?: string;
    userIds?: Array<string>;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/anno/v1/roles/users',
      query: {
        'scope.type': scopeType,
        'scope.uid': scopeUid,
        user_ids: userIds,
      },
    });
  }

  /**
   * @returns anno_v1_GetUserRoleReply OK
   * @throws ApiError
   */
  public static rolesGetUserRole({
    uid,
    scopeType,
    scopeUid,
  }: {
    /**
     * user uid
     */
    uid: string;
    /**
     * global/project/lot
     */
    scopeType?: string;
    /**
     * project-uid/lot-uid
     */
    scopeUid?: string;
  }): CancelablePromise<anno_v1_GetUserRoleReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/roles/users/{uid}',
      path: {
        uid: uid,
      },
      query: {
        'scope.type': scopeType,
        'scope.uid': scopeUid,
      },
    });
  }

  /**
   * @returns anno_v1_Role OK
   * @throws ApiError
   */
  public static rolesGetRole({ name }: { name: string }): CancelablePromise<anno_v1_Role> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/roles/{name}',
      path: {
        name: name,
      },
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static rolesDeleteRole({ name }: { name: string }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/anno/v1/roles/{name}',
      path: {
        name: name,
      },
    });
  }

  /**
   * @returns anno_v1_Role OK
   * @throws ApiError
   */
  public static rolesUpdateRole({
    roleName,
    requestBody,
    fields,
  }: {
    roleName: string;
    requestBody: anno_v1_Role;
    fields?: Array<string>;
  }): CancelablePromise<anno_v1_Role> {
    return __request(APIConfig, {
      method: 'PATCH',
      url: '/anno/v1/roles/{role.name}',
      path: {
        'role.name': roleName,
      },
      query: {
        fields: fields,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }
}
