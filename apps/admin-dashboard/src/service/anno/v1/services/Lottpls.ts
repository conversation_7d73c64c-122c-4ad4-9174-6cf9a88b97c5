/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { anno_v1_CreateLottplRequest } from '../models/anno_v1_CreateLottplRequest';
import type { anno_v1_ListLottplReply } from '../models/anno_v1_ListLottplReply';
import type { anno_v1_Lottpl } from '../models/anno_v1_Lottpl';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Lottpls {
  /**
   * @returns anno_v1_ListLottplReply OK
   * @throws ApiError
   */
  public static lottplsListLottpl({
    page,
    pagesz,
    orgUid,
    creatorUid,
    namePattern,
    type,
  }: {
    page?: number;
    pagesz?: number;
    /**
     * filter by organization
     */
    orgUid?: string;
    /**
     * filter by creator
     */
    creatorUid?: string;
    /**
     * filter by name pattern
     */
    namePattern?: string;
    /**
     * filter by lot type
     */
    type?: 'unspecified' | 'annotate' | 'segment_instance' | 'segment_semantic' | 'segment_panoptic';
  }): CancelablePromise<anno_v1_ListLottplReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/lottpls',
      query: {
        page: page,
        pagesz: pagesz,
        org_uid: orgUid,
        creator_uid: creatorUid,
        name_pattern: namePattern,
        type: type,
      },
    });
  }

  /**
   * @returns anno_v1_Lottpl OK
   * @throws ApiError
   */
  public static lottplsCreateLottpl({
    requestBody,
  }: {
    requestBody: anno_v1_CreateLottplRequest;
  }): CancelablePromise<anno_v1_Lottpl> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/anno/v1/lottpls',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns anno_v1_Lottpl OK
   * @throws ApiError
   */
  public static lottplsUpdateLottpl({
    lottplUid,
    requestBody,
    fields,
  }: {
    lottplUid: string;
    requestBody: anno_v1_CreateLottplRequest;
    /**
     * name of fields to be updated
     */
    fields?: Array<string>;
  }): CancelablePromise<anno_v1_Lottpl> {
    return __request(APIConfig, {
      method: 'PATCH',
      url: '/anno/v1/lottpls/{lottpl.uid}',
      path: {
        'lottpl.uid': lottplUid,
      },
      query: {
        fields: fields,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns anno_v1_Lottpl OK
   * @throws ApiError
   */
  public static lottplsGetLottpl({ uid }: { uid: string }): CancelablePromise<anno_v1_Lottpl> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/lottpls/{uid}',
      path: {
        uid: uid,
      },
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static lottplsDeleteLottpl({ uid }: { uid: string }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/anno/v1/lottpls/{uid}',
      path: {
        uid: uid,
      },
    });
  }
}
