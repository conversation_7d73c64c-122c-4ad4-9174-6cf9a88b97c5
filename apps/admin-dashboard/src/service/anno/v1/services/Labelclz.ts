/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { anno_v1_Labelcls } from '../models/anno_v1_Labelcls';
import type { anno_v1_ListLabelclsReply } from '../models/anno_v1_ListLabelclsReply';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Labelclz {
  /**
   * @returns anno_v1_ListLabelclsReply OK
   * @throws ApiError
   */
  public static labelclzListLabelcls(): CancelablePromise<anno_v1_ListLabelclsReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/labelcls',
    });
  }

  /**
   * @returns anno_v1_Labelcls OK
   * @throws ApiError
   */
  public static labelclzCreateLabelcls({
    requestBody,
  }: {
    requestBody: anno_v1_Labelcls;
  }): CancelablePromise<anno_v1_Labelcls> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/anno/v1/labelcls',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns anno_v1_Labelcls OK
   * @throws ApiError
   */
  public static labelclzGetLabelcls({ name }: { name: string }): CancelablePromise<anno_v1_Labelcls> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/labelcls/{name}',
      path: {
        name: name,
      },
    });
  }

  /**
   * @returns anno_v1_Labelcls OK
   * @throws ApiError
   */
  public static labelclzUpdateLabelcls({
    name,
    requestBody,
  }: {
    name: string;
    requestBody: anno_v1_Labelcls;
  }): CancelablePromise<anno_v1_Labelcls> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/anno/v1/labelcls/{name}',
      path: {
        name: name,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static labelclzDeleteLabelcls({ name }: { name: string }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/anno/v1/labelcls/{name}',
      path: {
        name: name,
      },
    });
  }
}
