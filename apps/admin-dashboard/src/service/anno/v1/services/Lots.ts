/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { anno_v1_AllowDownloadAnnosRequest } from '../models/anno_v1_AllowDownloadAnnosRequest';
import type { anno_v1_AssignExecteamRequest } from '../models/anno_v1_AssignExecteamRequest';
import type { anno_v1_CloneLotRequest } from '../models/anno_v1_CloneLotRequest';
import type { anno_v1_CreateLotRequest } from '../models/anno_v1_CreateLotRequest';
import type { anno_v1_ExportLotAnnosRequest } from '../models/anno_v1_ExportLotAnnosRequest';
import type { anno_v1_GetLotRequest } from '../models/anno_v1_GetLotRequest';
import type { anno_v1_GetLotSummaryReply } from '../models/anno_v1_GetLotSummaryReply';
import type { anno_v1_JobCountByLotidsReply } from '../models/anno_v1_JobCountByLotidsReply';
import type { anno_v1_ListExecteamsReply } from '../models/anno_v1_ListExecteamsReply';
import type { anno_v1_ListExecutorsReply } from '../models/anno_v1_ListExecutorsReply';
import type { anno_v1_ListLotReply } from '../models/anno_v1_ListLotReply';
import type { anno_v1_ListLotsByExecutorReply } from '../models/anno_v1_ListLotsByExecutorReply';
import type { anno_v1_Lot } from '../models/anno_v1_Lot';
import type { anno_v1_ManageExecutorsRequest } from '../models/anno_v1_ManageExecutorsRequest';
import type { types_TagList } from '../models/types_TagList';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Lots {
  /**
   * @returns anno_v1_JobCountByLotidsReply OK
   * @throws ApiError
   */
  public static lotsJobCountByLots({ ids }: { ids?: Array<string> }): CancelablePromise<anno_v1_JobCountByLotidsReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/job-count-by-lots',
      query: {
        ids: ids,
      },
    });
  }

  /**
   * @returns anno_v1_ListLotReply OK
   * @throws ApiError
   */
  public static lotsListLot({
    page,
    pagesz,
    orgUid,
    creatorUid,
    namePattern,
    states,
    type,
    orderUid,
    tags,
    withOrg,
  }: {
    page?: number;
    pagesz?: number;
    /**
     * filter by orgnization
     */
    orgUid?: string;
    /**
     * filter by creator
     */
    creatorUid?: string;
    /**
     * filter by name pattern
     */
    namePattern?: string;
    /**
     * filter by lot state
     */
    states?: Array<'unspecified' | 'initializing' | 'unstart' | 'ongoing' | 'finished' | 'paused' | 'canceled'>;
    /**
     * filter by lot type
     */
    type?: 'unspecified' | 'annotate' | 'segment_instance' | 'segment_semantic' | 'segment_panoptic';
    /**
     * filter by order
     */
    orderUid?: string;
    /**
     * find by attached tags
     */
    tags?: Array<string>;
    /**
     * include lot's orgnization in the reply
     */
    withOrg?: boolean;
  }): CancelablePromise<anno_v1_ListLotReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/lots',
      query: {
        page: page,
        pagesz: pagesz,
        org_uid: orgUid,
        creator_uid: creatorUid,
        name_pattern: namePattern,
        states: states,
        type: type,
        order_uid: orderUid,
        tags: tags,
        with_org: withOrg,
      },
    });
  }

  /**
   * @returns anno_v1_Lot OK
   * @throws ApiError
   */
  public static lotsCreateLot({
    requestBody,
  }: {
    requestBody: anno_v1_CreateLotRequest;
  }): CancelablePromise<anno_v1_Lot> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/anno/v1/lots',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * get lots assigned to a user or a team.
   * if the phase's execteam is empty, the phase is not assigned to the user or team.
   * @returns anno_v1_ListLotsByExecutorReply OK
   * @throws ApiError
   */
  public static lotsListLotsByExecutor({
    page,
    pagesz,
    userUid,
    teamUid,
    orgUid,
    states,
    type,
    namePattern,
    claimable,
  }: {
    page?: number;
    pagesz?: number;
    /**
     * list lots assigned to the user;
     * requestor should have manager role in the user's organization, if it is not oneself.
     * if omitted, default to the requestor.
     */
    userUid?: string;
    /**
     * list lots assigned to the team;
     * requestor should have manager role in this team.
     */
    teamUid?: string;
    /**
     * list lots assigned to teams within this organization;
     * requestor should have IamGroup.list permission in this organization, e.g. a manager,.
     */
    orgUid?: string;
    /**
     * filter by lot state
     */
    states?: Array<'unspecified' | 'initializing' | 'unstart' | 'ongoing' | 'finished' | 'paused' | 'canceled'>;
    /**
     * filter by lot type
     */
    type?: 'unspecified' | 'annotate' | 'segment_instance' | 'segment_semantic' | 'segment_panoptic';
    /**
     * filter by name pattern
     */
    namePattern?: string;
    /**
     * if to skip lots that a claim request will return no job for an executor; available only to executor query.
     */
    claimable?: boolean;
  }): CancelablePromise<anno_v1_ListLotsByExecutorReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/lots/by-executor',
      query: {
        page: page,
        pagesz: pagesz,
        user_uid: userUid,
        team_uid: teamUid,
        org_uid: orgUid,
        states: states,
        type: type,
        name_pattern: namePattern,
        claimable: claimable,
      },
    });
  }

  /**
   * update lot. only simple information like name or desc update are allowed.
   * @returns anno_v1_Lot OK
   * @throws ApiError
   */
  public static lotsUpdateLot({
    lotUid,
    requestBody,
    fields,
  }: {
    lotUid: string;
    requestBody: anno_v1_CreateLotRequest;
    /**
     * name of fields to be updated
     */
    fields?: Array<string>;
  }): CancelablePromise<anno_v1_Lot> {
    return __request(APIConfig, {
      method: 'PATCH',
      url: '/anno/v1/lots/{lot.uid}',
      path: {
        'lot.uid': lotUid,
      },
      query: {
        fields: fields,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns anno_v1_Lot OK
   * @throws ApiError
   */
  public static lotsGetLot({ uid }: { uid: string }): CancelablePromise<anno_v1_Lot> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/lots/{uid}',
      path: {
        uid: uid,
      },
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static lotsDeleteLot({ uid }: { uid: string }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/anno/v1/lots/{uid}',
      path: {
        uid: uid,
      },
    });
  }

  /**
   * allows demander to download annos
   * @returns any OK
   * @throws ApiError
   */
  public static lotsAllowDownloadAnnos({
    uid,
    requestBody,
  }: {
    /**
     * lot UID
     */
    uid: string;
    requestBody: anno_v1_AllowDownloadAnnosRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/anno/v1/lots/{uid}/allow-download-annos',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static lotsCancelLot({
    uid,
    requestBody,
  }: {
    uid: string;
    requestBody: anno_v1_GetLotRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/anno/v1/lots/{uid}/cancel',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * create a new lot by cloning an existing lot.
   * @returns anno_v1_Lot OK
   * @throws ApiError
   */
  public static lotsCloneLot({
    uid,
    requestBody,
  }: {
    /**
     * source lot uid
     */
    uid: string;
    requestBody: anno_v1_CloneLotRequest;
  }): CancelablePromise<anno_v1_Lot> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/anno/v1/lots/{uid}/clone',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * assign execution team, update or delete an execution team
   * @returns any OK
   * @throws ApiError
   */
  public static lotsAssignExecteam({
    uid,
    requestBody,
  }: {
    /**
     * lot uid
     */
    uid: string;
    requestBody: anno_v1_AssignExecteamRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/anno/v1/lots/{uid}/execteam',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * list assigned execution teams and executors
   * @returns anno_v1_ListExecteamsReply OK
   * @throws ApiError
   */
  public static lotsListExecteams({
    uid,
    withExecteams,
    withExecutors,
  }: {
    /**
     * lot uid
     */
    uid: string;
    /**
     * if to include team info in the reply
     */
    withExecteams?: boolean;
    /**
     * if to include executors in the reply
     */
    withExecutors?: boolean;
  }): CancelablePromise<anno_v1_ListExecteamsReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/lots/{uid}/execteams',
      path: {
        uid: uid,
      },
      query: {
        with_execteams: withExecteams,
        with_executors: withExecutors,
      },
    });
  }

  /**
   * list executors at a phase
   * @returns anno_v1_ListExecutorsReply OK
   * @throws ApiError
   */
  public static lotsListExecutors({
    uid,
    phase,
    subtype,
    teamUid,
    page,
    pagesz,
  }: {
    /**
     * lot uid
     */
    uid: string;
    /**
     * phase number, starts from 1
     */
    phase?: number;
    subtype?: string;
    /**
     * mandatory in a multi-team configuration
     */
    teamUid?: string;
    page?: number;
    pagesz?: number;
  }): CancelablePromise<anno_v1_ListExecutorsReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/lots/{uid}/executors',
      path: {
        uid: uid,
      },
      query: {
        phase: phase,
        subtype: subtype,
        team_uid: teamUid,
        page: page,
        pagesz: pagesz,
      },
    });
  }

  /**
   * add or remove executors
   * @returns any OK
   * @throws ApiError
   */
  public static lotsManageExecutors({
    uid,
    requestBody,
  }: {
    /**
     * lot uid
     */
    uid: string;
    requestBody: anno_v1_ManageExecutorsRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/anno/v1/lots/{uid}/executors',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * ExportLotAnnos exports lot annotations
   * @returns any OK
   * @throws ApiError
   */
  public static lotsExportLotAnnos({
    uid,
    requestBody,
  }: {
    /**
     * lot UID
     */
    uid: string;
    requestBody: anno_v1_ExportLotAnnosRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/anno/v1/lots/{uid}/export-annos',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static lotsPauseLot({
    uid,
    requestBody,
  }: {
    uid: string;
    requestBody: anno_v1_GetLotRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/anno/v1/lots/{uid}/pause',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static lotsStartLot({
    uid,
    requestBody,
  }: {
    uid: string;
    requestBody: anno_v1_GetLotRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/anno/v1/lots/{uid}/start',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get summary of a lot
   * @returns anno_v1_GetLotSummaryReply OK
   * @throws ApiError
   */
  public static lotsGetSummary({ uid }: { uid: string }): CancelablePromise<anno_v1_GetLotSummaryReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/lots/{uid}/summary',
      path: {
        uid: uid,
      },
    });
  }

  /**
   * @returns types_TagList OK
   * @throws ApiError
   */
  public static lotsAddTag({
    uid,
    requestBody,
  }: {
    uid: string;
    requestBody: string;
  }): CancelablePromise<types_TagList> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/anno/v1/lots/{uid}/tag',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns types_TagList OK
   * @throws ApiError
   */
  public static lotsDeleteTag({ uid, tags }: { uid: string; tags?: Array<string> }): CancelablePromise<types_TagList> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/anno/v1/lots/{uid}/tag',
      path: {
        uid: uid,
      },
      query: {
        tags: tags,
      },
    });
  }
}
