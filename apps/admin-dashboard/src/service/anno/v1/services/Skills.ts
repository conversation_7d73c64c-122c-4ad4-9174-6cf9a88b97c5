/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { anno_v1_AddTeamSkillRequest } from '../models/anno_v1_AddTeamSkillRequest';
import type { anno_v1_AddUsersSkillRequest } from '../models/anno_v1_AddUsersSkillRequest';
import type { anno_v1_GetUserSkillReply } from '../models/anno_v1_GetUserSkillReply';
import type { anno_v1_ListSkillReply } from '../models/anno_v1_ListSkillReply';
import type { anno_v1_ListUsersSkillReply } from '../models/anno_v1_ListUsersSkillReply';
import type { anno_v1_Skill } from '../models/anno_v1_Skill';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Skills {
  /**
   * @returns anno_v1_ListSkillReply OK
   * @throws ApiError
   */
  public static skillsListSkill({
    page,
    pagesz,
    type,
    namePattern,
  }: {
    page?: number;
    pagesz?: number;
    type?: 'unspecified' | 'task_type' | 'label_class' | 'widget';
    namePattern?: string;
  }): CancelablePromise<anno_v1_ListSkillReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/skills',
      query: {
        page: page,
        pagesz: pagesz,
        type: type,
        name_pattern: namePattern,
      },
    });
  }

  /**
   * @returns anno_v1_Skill OK
   * @throws ApiError
   */
  public static skillsCreateSkill({ requestBody }: { requestBody: anno_v1_Skill }): CancelablePromise<anno_v1_Skill> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/anno/v1/skills',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * add skills to users in a team
   * @returns any OK
   * @throws ApiError
   */
  public static skillsAddTeamSkill({
    uid,
    requestBody,
  }: {
    /**
     * team uid
     */
    uid: string;
    requestBody: anno_v1_AddTeamSkillRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/anno/v1/skills/teams/{uid}',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * delete skills from users in a team
   * @returns any OK
   * @throws ApiError
   */
  public static skillsDeleteTeamSkill({
    uid,
    scopeType,
    scopeUid,
  }: {
    /**
     * team uid
     */
    uid: string;
    /**
     * global/project/lot
     */
    scopeType?: string;
    /**
     * project-uid/lot-uid
     */
    scopeUid?: string;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/anno/v1/skills/teams/{uid}',
      path: {
        uid: uid,
      },
      query: {
        'scope.type': scopeType,
        'scope.uid': scopeUid,
      },
    });
  }

  /**
   * @returns anno_v1_ListUsersSkillReply OK
   * @throws ApiError
   */
  public static skillsListUsersSkill({
    scopeType,
    scopeUid,
    teamUid,
    userUids,
  }: {
    /**
     * global/project/lot
     */
    scopeType?: string;
    /**
     * project-uid/lot-uid
     */
    scopeUid?: string;
    teamUid?: string;
    userUids?: Array<string>;
  }): CancelablePromise<anno_v1_ListUsersSkillReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/skills/users',
      query: {
        'scope.type': scopeType,
        'scope.uid': scopeUid,
        team_uid: teamUid,
        user_uids: userUids,
      },
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static skillsAddUsersSkill({
    requestBody,
  }: {
    requestBody: anno_v1_AddUsersSkillRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/anno/v1/skills/users',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static skillsDeleteUsersSkill({
    scopeType,
    scopeUid,
  }: {
    /**
     * global/project/lot
     */
    scopeType?: string;
    /**
     * project-uid/lot-uid
     */
    scopeUid?: string;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/anno/v1/skills/users',
      query: {
        'scope.type': scopeType,
        'scope.uid': scopeUid,
      },
    });
  }

  /**
   * users skills
   * @returns anno_v1_GetUserSkillReply OK
   * @throws ApiError
   */
  public static skillsGetUserSkill({
    uid,
    scopeType,
    scopeUid,
  }: {
    /**
     * user uid
     */
    uid: string;
    /**
     * global/project/lot
     */
    scopeType?: string;
    /**
     * project-uid/lot-uid
     */
    scopeUid?: string;
  }): CancelablePromise<anno_v1_GetUserSkillReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/skills/users/{uid}',
      path: {
        uid: uid,
      },
      query: {
        'scope.type': scopeType,
        'scope.uid': scopeUid,
      },
    });
  }

  /**
   * @returns anno_v1_Skill OK
   * @throws ApiError
   */
  public static skillsGetSkill({ name }: { name: string }): CancelablePromise<anno_v1_Skill> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/skills/{name}',
      path: {
        name: name,
      },
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static skillsDeleteSkill({ name }: { name: string }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/anno/v1/skills/{name}',
      path: {
        name: name,
      },
    });
  }

  /**
   * @returns anno_v1_Skill OK
   * @throws ApiError
   */
  public static skillsUpdateSkill({
    skillName,
    requestBody,
    fields,
  }: {
    skillName: string;
    requestBody: anno_v1_Skill;
    fields?: Array<string>;
  }): CancelablePromise<anno_v1_Skill> {
    return __request(APIConfig, {
      method: 'PATCH',
      url: '/anno/v1/skills/{skill.name}',
      path: {
        'skill.name': skillName,
      },
      query: {
        fields: fields,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }
}
