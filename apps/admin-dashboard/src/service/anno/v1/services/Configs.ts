/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { anno_v1_Errors } from '../models/anno_v1_Errors';
import type { anno_v1_GetVersionReply } from '../models/anno_v1_GetVersionReply';
import type { anno_v1_ListCommentReasonsReply } from '../models/anno_v1_ListCommentReasonsReply';
import type { anno_v1_PutCommentReasonsRequest } from '../models/anno_v1_PutCommentReasonsRequest';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Configs {
  /**
   * List comment reasons
   * @returns anno_v1_ListCommentReasonsReply OK
   * @throws ApiError
   */
  public static configsListCommentReasons(): CancelablePromise<anno_v1_ListCommentReasonsReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/commentreasons',
    });
  }

  /**
   * Modify comment reasons
   * @returns any OK
   * @throws ApiError
   */
  public static configsPutCommentReasons({
    requestBody,
  }: {
    requestBody: anno_v1_PutCommentReasonsRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/anno/v1/commentreasons',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * List Errors info
   * @returns anno_v1_Errors OK
   * @throws ApiError
   */
  public static configsListErrors(): CancelablePromise<anno_v1_Errors> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/errors',
    });
  }

  /**
   * @returns anno_v1_GetVersionReply OK
   * @throws ApiError
   */
  public static configsGetVersion(): CancelablePromise<anno_v1_GetVersionReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/anno/v1/version',
    });
  }
}
