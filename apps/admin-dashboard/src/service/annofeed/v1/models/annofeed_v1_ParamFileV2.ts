/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { annofeed_v1_CameraParamV2 } from './annofeed_v1_CameraParamV2';
import type { annofeed_v1_LidarParamV2 } from './annofeed_v1_LidarParamV2';
import type { annofeed_v1_ParamFileMeta } from './annofeed_v1_ParamFileMeta';

export type annofeed_v1_ParamFileV2 = {
  /**
   * version = "v2"
   */
  meta?: annofeed_v1_ParamFileMeta;
  /**
   * define elem param
   */
  lidar?: annofeed_v1_LidarParamV2;
  /**
   * define clip param
   */
  lidars?: Array<annofeed_v1_LidarParamV2>;
  cameras?: Record<string, annofeed_v1_CameraParamV2>;
};
