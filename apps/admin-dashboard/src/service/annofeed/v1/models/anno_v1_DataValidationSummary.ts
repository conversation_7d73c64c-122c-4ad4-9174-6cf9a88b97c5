/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_DataValidationSummary_Error } from './anno_v1_DataValidationSummary_Error';

export type anno_v1_DataValidationSummary = {
  /**
   * total number of validation errors found, includes those unsaved errors
   */
  total_errors?: number;
  /**
   * validation errors; only the first 10 errors are saved
   */
  errors?: Array<anno_v1_DataValidationSummary_Error>;
};
