/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_ElementAnno } from './anno_v1_ElementAnno';
import type { anno_v1_Rawdata } from './anno_v1_Rawdata';

/**
 * 一帧数据，包含一个或多个待标注文件
 */
export type anno_v1_Element = {
  /**
   * element index in the lot dataset
   */
  index: number;
  /**
   * name of the element: innermost-folder/frame-index
   */
  name: string;
  type: 'unspecified' | 'image' | 'pointcloud' | 'fusion2d' | 'fusion3d' | 'fusion4d';
  datas: Array<anno_v1_Rawdata>;
  /**
   * 标注结果；
   * 仅用于结果导出，或在导入时用于传输已经有标注信息的数据；
   * 标注过程中，请使用 Job 里的相关字段
   */
  anno?: anno_v1_ElementAnno;
};
