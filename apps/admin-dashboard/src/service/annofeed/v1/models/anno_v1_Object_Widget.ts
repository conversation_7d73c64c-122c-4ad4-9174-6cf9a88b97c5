/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_Direction } from './anno_v1_Direction';

export type anno_v1_Object_Widget = {
  /**
   * widget name
   */
  name:
    | 'unspecified'
    | 'box2d'
    | 'pscuboid'
    | 'cuboid'
    | 'poly2d'
    | 'poly3d'
    | 'line2d'
    | 'line3d'
    | 'point2d'
    | 'point3d'
    | 'bitmap';
  /**
   * characteristic values of geometric shapes, or bitmap origin (left, top)
   */
  data: Array<number>;
  /**
   * gaps within the widget if any
   */
  gaps?: Array<anno_v1_Object_Widget>;
  /**
   * bitmap file URL or data URI, e.g. data:image/png;base64,<base64-encoded-file-content>
   * pointcloud file URL or data URI, e.g. data:application/pcd;base64,<base64-encoded-file-content>
   */
  uri?: string;
  /**
   * forward direction. if origin is null, then the center point of the widget is implied.
   */
  forward?: anno_v1_Direction;
  /**
   * number of points within the object (in 3D segmentation tasks)
   */
  point_cnt?: number;
  /**
   * class ID in RawdataAnno.Segmentation; zero if the task is not a segmentation task
   */
  seg_class_id?: number;
  /**
   * line type used to draw the widget
   */
  line_type?: 'line' | 'crspline';
};
