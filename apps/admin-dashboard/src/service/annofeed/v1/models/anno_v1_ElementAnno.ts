/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_AttrAndValues } from './anno_v1_AttrAndValues';
import type { anno_v1_ElementAnno_Metadata } from './anno_v1_ElementAnno_Metadata';
import type { anno_v1_RawdataAnno } from './anno_v1_RawdataAnno';
import type { anno_v1_Segmentation3d } from './anno_v1_Segmentation3d';

/**
 * annotations of an element (frame)
 */
export type anno_v1_ElementAnno = {
  /**
   * element index in the lot dataset
   */
  index: number;
  /**
   * name of the element: innermost-folder/frame-index
   */
  name: string;
  /**
   * 相应位置的元素对应 Element 的 datas 相应位置的结果
   */
  rawdata_annos: Array<anno_v1_RawdataAnno>;
  attrs: Array<anno_v1_AttrAndValues>;
  /**
   * number of objects annotated in the element
   */
  ins_cnt: number;
  /**
   * metadata of the element annos
   */
  metadata?: anno_v1_ElementAnno_Metadata;
  segmentation3d?: anno_v1_Segmentation3d;
};
