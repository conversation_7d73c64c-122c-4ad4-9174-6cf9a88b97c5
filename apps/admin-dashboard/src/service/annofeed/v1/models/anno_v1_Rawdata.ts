/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_Object } from './anno_v1_Object';
import type { anno_v1_Rawdata_Embedding } from './anno_v1_Rawdata_Embedding';
import type { anno_v1_Rawdata_Meta } from './anno_v1_Rawdata_Meta';
import type { anno_v1_RawdataParam } from './anno_v1_RawdataParam';

/**
 * 待标注文件，比如一张图片、点云的一帧
 */
export type anno_v1_Rawdata = {
  /**
   * file pathname
   */
  name: string;
  /**
   * data type
   */
  type: 'unspecified' | 'image' | 'pointcloud';
  /**
   * data format
   */
  format: 'unspecified' | 'json' | 'png' | 'jpg' | 'pcd' | 'filelist' | 'webp';
  url: string;
  /**
   * number of bytes in the file
   */
  size: number;
  /**
   * SHA-256 of the file
   */
  sha256: string;
  /**
   * display name (sensor name)
   */
  title: string;
  /**
   * 变换参数，可以将世界坐标系下的点映射到图片上
   */
  transform: Array<anno_v1_RawdataParam>;
  /**
   * metadata of rawdata
   */
  meta: anno_v1_Rawdata_Meta;
  /**
   * 识别出的物体列表；
   * 仅用于结果导出，或在导入时用于传输已经有标注信息的数据；
   * 标注过程中，请使用 Job 里的相关字段
   */
  ins: Array<anno_v1_Object>;
  /**
   * original pathname of the rawdata
   */
  orig_name?: string;
  /**
   * file embedding
   */
  embedding?: anno_v1_Rawdata_Embedding;
};
