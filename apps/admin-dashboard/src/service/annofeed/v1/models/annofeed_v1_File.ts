/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type annofeed_v1_File = {
  /**
   * file UID
   */
  uid: string;
  /**
   * file URI
   */
  uri: string;
  /**
   * file name
   */
  name: string;
  /**
   * file size in number of bytes
   */
  size: number;
  /**
   * file MIME type
   */
  mime?: string;
  /**
   * SHA256/MD5 digest of the file
   */
  sha256: string;
  /**
   * UID of the organization which the file belongs to
   */
  org_uid: string;
  /**
   * file state
   */
  state: 'unspecified' | 'uploading' | 'uploaded';
  /**
   * UID of the creator
   */
  creator_uid?: string;
  /**
   * file creation time
   */
  created_at: string;
};
