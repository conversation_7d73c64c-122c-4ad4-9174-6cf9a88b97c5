/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_Source } from './anno_v1_Source';

export type annofeed_v1_CreateDataRequest = {
  /**
   * data UID (only needed in update-requests)
   */
  uid?: string;
  /**
   * data name (mandatory in create-requests)
   */
  name?: string;
  /**
   * data description
   */
  desc?: string;
  /**
   * data type
   */
  type: 'unspecified' | 'image' | 'pointcloud' | 'fusion2d' | 'fusion3d' | 'fusion4d';
  /**
   * based on data UID
   */
  base_on_uid?: string;
  /**
   * source files which the data originates from
   */
  source: anno_v1_Source;
  /**
   * UID of the organization which the data belongs to
   */
  org_uid: string;
  /**
   * UID of the order which caused the data
   */
  order_uid?: string;
};
