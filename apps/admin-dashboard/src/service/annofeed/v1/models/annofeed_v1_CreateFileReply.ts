/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { annofeed_v1_File } from './annofeed_v1_File';

export type annofeed_v1_CreateFileReply = {
  file: annofeed_v1_File;
  /**
   * if len(upload_urls) > 1, file should be uploaded as multi-parts
   */
  upload_urls: Array<string>;
  /**
   * presigned URL expire time. upload should be done before that
   */
  url_expires_at: string;
  /**
   * if true, a previously created file is returned due to they have the same hash and size
   */
  pre_existing?: boolean;
};
