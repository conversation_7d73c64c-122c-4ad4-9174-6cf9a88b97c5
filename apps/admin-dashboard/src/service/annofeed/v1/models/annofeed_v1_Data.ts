/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_DataValidationSummary } from './anno_v1_DataValidationSummary';
import type { anno_v1_Source } from './anno_v1_Source';

export type annofeed_v1_Data = {
  /**
   * data UID
   */
  readonly uid: string;
  /**
   * data name
   */
  name: string;
  /**
   * data description
   */
  desc?: string;
  /**
   * data type
   */
  type: 'unspecified' | 'image' | 'pointcloud' | 'fusion2d' | 'fusion3d' | 'fusion4d';
  /**
   * based on data UID
   */
  base_on_uid?: string;
  /**
   * source files which the data originates from
   */
  source: anno_v1_Source;
  /**
   * UID of the organization which the data belongs to
   */
  org_uid: string;
  /**
   * UID of the order which caused the data
   */
  order_uid?: string;
  /**
   * number of elements in the data
   */
  readonly size?: number;
  /**
   * data state: fetching, processing, ready, failed
   */
  readonly state: 'unspecified' | 'raw' | 'fetching' | 'processing' | 'ready' | 'abandoned' | 'disabled' | 'failed';
  /**
   * when state is failed, this field will contain the error message
   */
  error?: string;
  /**
   * UID of the creator
   */
  creator_uid?: string;
  /**
   * data creation time
   */
  readonly created_at: string;
  /**
   * data validation summary
   */
  readonly summary?: anno_v1_DataValidationSummary;
};
