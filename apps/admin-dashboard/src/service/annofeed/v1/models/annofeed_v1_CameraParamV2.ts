/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type annofeed_v1_CameraParamV2 = {
  /**
   * name of the camera
   */
  name?: string;
  /**
   * extrinsic, either in the form of position and quaternion ([x,y,z,qx,qy,qz,qw]), or
   * in the form of 4x4 row-major matrix ([r00,r01,r02,r03,...,r30,r31,r32,r33]).
   * In the 1st format, it is the pose of the camera in the point cloud's coordinate system;
   * in the 2nd format, it is a matrix to convert point cloud into camera's coordinate system.
   * <2nd format> = inverse(to_matrix(<1st format>))
   */
  extrinsic?: Array<number>;
  /**
   * intrinsic, either in the form of [fx,fy,cx,cy], or
   * in the form of 3x3 row-major matrix ([fx,0,cx,0,fy,cy,0,0,1])
   */
  intrinsic?: Array<number>;
  /**
   * distortion params: distortion_type,k1,k2,...
   */
  distortion?: Array<number>;
  /**
   * used to display in anno platform if not empty, preferably in local language
   */
  title?: string;
};
