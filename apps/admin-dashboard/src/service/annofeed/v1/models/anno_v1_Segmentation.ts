/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { anno_v1_Segmentation_Class } from './anno_v1_Segmentation_Class';
import type { anno_v1_Segmentation_RLE } from './anno_v1_Segmentation_RLE';

export type anno_v1_Segmentation = {
  /**
   * classes definition; if empty, class name is RawdataAnno.Object.uuid with the matching seg_class_id
   */
  classes?: Array<anno_v1_Segmentation_Class>;
  /**
   * unpacked RLE; either rle or rle_pack is set, not both
   */
  rle?: anno_v1_Segmentation_RLE;
  /**
   * packed RLE; serialize RLE using JSON, then compress using gzip, then encode in base64;
   * format: data:application/json;gzip;base64,[base64-encoded-content]
   * or http://packed-RLE-file-url (file format: RLE;json;gzip\ngzip-encoded-content)
   */
  rle_pack?: string;
};
