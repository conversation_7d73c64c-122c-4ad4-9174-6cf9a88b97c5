/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type annofeed_v1_LidarParamV2 = {
  /**
   * point cloud viewpoint (in the pointcloud's coordinate system), in the form of position and quaternion ([x,y,z,qx,qy,qz,qw])
   */
  viewpoint?: Array<number>;
  /**
   * origin point of the point cloud in world coordinate system when point cloud is not in world coordinate system,
   * either in the form of position and quaternion ([x,y,z,qx,qy,qz,qw]), or
   * in the form of 4x4 row-major matrix ([r00,r01,r02,r03,...,r30,r31,r32,r33]).
   * In the 1st format, it is the pose of the point cloud's origin point in world coordinate system;
   * in the 2nd format, it is a matrix to convert point cloud into world coordinate system.
   * <2nd format> = to_matrix(<1st format>)
   */
  pose?: Array<number>;
  /**
   * UNIX timestamp in seconds
   */
  timestamp?: number;
  /**
   * transforms the point cloud into lidar coordinate system when the point cloud is not in lidar coordinate system,
   * either in the form of position and quaternion ([x,y,z,qx,qy,qz,qw]), or
   * in the form of 4x4 row-major matrix ([r00,r01,r02,r03,...,r30,r31,r32,r33]).
   * In the 1st format, it is the position of the lidar in the point cloud's coordinate system;
   * in the 2nd format, it is a matrix to convert point cloud into lidar coordinate system.
   * <2nd format> = inverse(to_matrix(<1st format>))
   */
  transform?: Array<number>;
};
