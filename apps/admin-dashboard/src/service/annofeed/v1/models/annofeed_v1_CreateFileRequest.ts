/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type annofeed_v1_CreateFileRequest = {
  /**
   * name the file to be created
   */
  name: string;
  /**
   * number of bytes of the file
   */
  size: number;
  /**
   * set number of parts in a multi-part upload;
   * if unspecified, which is 0, server will choose a proper number based on file size.
   * Part size should be between 5 MiB to 5 GiB. There is no minimum size limit on the
   * last part of your multipart upload.
   */
  parts: number;
  /**
   * MIME type of the file: e.g. "application/zip",
   * or file name extention: e.g. ".zip"
   */
  mime: string;
  /**
   * SHA256 digest of the file; it also accepts MD5 digest in the format: md5:xxx
   */
  sha256?: string;
  /**
   * organization that the file belongs to; if omitted, it is the user's organization
   */
  org_uid?: string;
};
