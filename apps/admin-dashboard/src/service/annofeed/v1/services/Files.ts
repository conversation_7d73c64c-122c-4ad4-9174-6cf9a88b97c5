/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { annofeed_v1_CreateFileReply } from '../models/annofeed_v1_CreateFileReply';
import type { annofeed_v1_CreateFileRequest } from '../models/annofeed_v1_CreateFileRequest';
import type { annofeed_v1_File } from '../models/annofeed_v1_File';
import type { annofeed_v1_FinishFileUploadRequest } from '../models/annofeed_v1_FinishFileUploadRequest';
import type { annofeed_v1_GetFileUploadURLsReply } from '../models/annofeed_v1_GetFileUploadURLsReply';
import type { annofeed_v1_ListFileReply } from '../models/annofeed_v1_ListFileReply';
import type { annofeed_v1_ShareFileReply } from '../models/annofeed_v1_ShareFileReply';
import type { annofeed_v1_ShareFileRequest } from '../models/annofeed_v1_ShareFileRequest';
import type { UploadFileReply } from '../models/UploadFileReply';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Files {
  /**
   * @returns annofeed_v1_ListFileReply OK
   * @throws ApiError
   */
  public static filesListFile({
    pagesz,
    pageToken,
    orgUid,
    creatorUid,
    namePattern,
    uris,
  }: {
    pagesz?: number;
    pageToken?: string;
    /**
     * filter by organization
     */
    orgUid?: string;
    /**
     * filter by creator
     */
    creatorUid?: string;
    /**
     * filter by name pattern
     */
    namePattern?: string;
    /**
     * filter by URI
     */
    uris?: Array<string>;
  }): CancelablePromise<annofeed_v1_ListFileReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/annofeed/v1/files',
      query: {
        pagesz: pagesz,
        page_token: pageToken,
        org_uid: orgUid,
        creator_uid: creatorUid,
        name_pattern: namePattern,
        uris: uris,
      },
    });
  }

  /**
   * create a file record and presigned URLs for upload
   * @returns annofeed_v1_CreateFileReply OK
   * @throws ApiError
   */
  public static filesCreateFile({
    requestBody,
  }: {
    requestBody: annofeed_v1_CreateFileRequest;
  }): CancelablePromise<annofeed_v1_CreateFileReply> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/annofeed/v1/files',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns annofeed_v1_File OK
   * @throws ApiError
   */
  public static filesUpdateFile({
    fileUid,
    requestBody,
    fields,
  }: {
    fileUid: string;
    requestBody: annofeed_v1_File;
    /**
     * name of fields to update
     */
    fields?: Array<string>;
  }): CancelablePromise<annofeed_v1_File> {
    return __request(APIConfig, {
      method: 'PATCH',
      url: '/annofeed/v1/files/{file.uid}',
      path: {
        'file.uid': fileUid,
      },
      query: {
        fields: fields,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns annofeed_v1_File OK
   * @throws ApiError
   */
  public static filesGetFile({
    uid,
  }: {
    /**
     * file UID
     */
    uid: string;
  }): CancelablePromise<annofeed_v1_File> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/annofeed/v1/files/{uid}',
      path: {
        uid: uid,
      },
    });
  }

  /**
   * delete a file record. Ongoing multipart upload will be aborted.
   * @returns any OK
   * @throws ApiError
   */
  public static filesDeleteFile({
    uid,
  }: {
    /**
     * file UID
     */
    uid: string;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/annofeed/v1/files/{uid}',
      path: {
        uid: uid,
      },
    });
  }

  /**
   * mark the file upload as completed
   * @returns any OK
   * @throws ApiError
   */
  public static filesFinishFileUpload({
    uid,
    requestBody,
  }: {
    /**
     * file uid
     */
    uid: string;
    requestBody: annofeed_v1_FinishFileUploadRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/annofeed/v1/files/{uid}/finish-upload',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * get a presigned URL to download the file
   * @returns annofeed_v1_ShareFileReply OK
   * @throws ApiError
   */
  public static filesShareFile({
    uid,
    requestBody,
  }: {
    /**
     * file uid
     */
    uid: string;
    requestBody: annofeed_v1_ShareFileRequest;
  }): CancelablePromise<annofeed_v1_ShareFileReply> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/annofeed/v1/files/{uid}/share',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns annofeed_v1_GetFileUploadURLsReply OK
   * @throws ApiError
   */
  public static filesGetFileUploadUrLs({
    uid,
    parts,
  }: {
    /**
     * file uid
     */
    uid: string;
    /**
     * set number of parts in a multi-part upload
     */
    parts?: number;
  }): CancelablePromise<annofeed_v1_GetFileUploadURLsReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/annofeed/v1/files/{uid}/upload-urls',
      path: {
        uid: uid,
      },
      query: {
        parts: parts,
      },
    });
  }

  /**
   * @returns UploadFileReply OK
   * @throws ApiError
   */
  public static filesUpload({
    requestBody,
    name,
  }: {
    requestBody: Blob;
    name?: string;
  }): CancelablePromise<UploadFileReply> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/annofeed/v1/upload',
      query: {
        name: name,
      },
      body: requestBody,
      mediaType: 'application/octet-stream',
    });
  }

  /**
   * @returns UploadFileReply OK
   * @throws ApiError
   */
  public static filesUploadAvatar({
    requestBody,
    name,
    type,
    _class,
  }: {
    requestBody: Blob;
    name?: string;
    type?: 'user' | 'prj' | 'res';
    _class?: string;
  }): CancelablePromise<UploadFileReply> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/annofeed/v1/avatar',
      query: {
        name: name,
        type: type,
        class: _class,
      },
      body: requestBody,
      mediaType: 'application/octet-stream',
    });
  }

  /**
   * @returns void
   * @throws ApiError
   */
  public static filesGetRawdata({ uid }: { uid: string }): CancelablePromise<void> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/annofeed/v1/rawdata/{uid}',
      path: {
        uid: uid,
      },
      errors: {
        302: `Redirect to the actual URL.`,
      },
    });
  }

  /**
   * get access URL to a shared file; authorization is required
   * @returns void
   * @throws ApiError
   */
  public static filesGetPfile({ uid }: { uid: string }): CancelablePromise<void> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/annofeed/v1/pfile/{share_id}',
      path: {
        uid: uid,
      },
      errors: {
        302: `Redirect to the actual URL.`,
      },
    });
  }
}
