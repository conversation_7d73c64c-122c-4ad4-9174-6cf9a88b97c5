/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { annofeed_v1_DummyReply } from '../models/annofeed_v1_DummyReply';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Dummy {
  /**
   * @returns annofeed_v1_DummyReply OK
   * @throws ApiError
   */
  public static dummyDummy(): CancelablePromise<annofeed_v1_DummyReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/annofeed/v1/dummy',
    });
  }
}
