/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { annofeed_v1_CreateDataRequest } from '../models/annofeed_v1_CreateDataRequest';
import type { annofeed_v1_Data } from '../models/annofeed_v1_Data';
import type { annofeed_v1_GetDataElementsReply } from '../models/annofeed_v1_GetDataElementsReply';
import type { annofeed_v1_GetDataMetaReply } from '../models/annofeed_v1_GetDataMetaReply';
import type { annofeed_v1_ListDataReply } from '../models/annofeed_v1_ListDataReply';
import type { annofeed_v1_ParseDataRequest } from '../models/annofeed_v1_ParseDataRequest';
import type { annofeed_v1_SetRawdataEmbeddingReply } from '../models/annofeed_v1_SetRawdataEmbeddingReply';
import type { annofeed_v1_SetRawdataEmbeddingRequest } from '../models/annofeed_v1_SetRawdataEmbeddingRequest';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Datas {
  /**
   * @returns annofeed_v1_ListDataReply OK
   * @throws ApiError
   */
  public static datasListData({
    page,
    pagesz,
    orgUid,
    creatorUid,
    namePattern,
    orderUid,
    states,
  }: {
    page?: number;
    pagesz?: number;
    /**
     * filter by organization
     */
    orgUid?: string;
    /**
     * filter by creator
     */
    creatorUid?: string;
    /**
     * filter by name pattern
     */
    namePattern?: string;
    /**
     * filter by order
     */
    orderUid?: string;
    /**
     * filter by data state
     */
    states?: Array<'unspecified' | 'raw' | 'fetching' | 'processing' | 'ready' | 'abandoned' | 'disabled' | 'failed'>;
  }): CancelablePromise<annofeed_v1_ListDataReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/annofeed/v1/datas',
      query: {
        page: page,
        pagesz: pagesz,
        org_uid: orgUid,
        creator_uid: creatorUid,
        name_pattern: namePattern,
        order_uid: orderUid,
        states: states,
      },
    });
  }

  /**
   * @returns annofeed_v1_Data OK
   * @throws ApiError
   */
  public static datasCreateData({
    requestBody,
  }: {
    requestBody: annofeed_v1_CreateDataRequest;
  }): CancelablePromise<annofeed_v1_Data> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/annofeed/v1/datas',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns annofeed_v1_Data OK
   * @throws ApiError
   */
  public static datasUpdateData({
    dataUid,
    requestBody,
    fields,
  }: {
    dataUid: string;
    requestBody: annofeed_v1_CreateDataRequest;
    /**
     * name of fields to be updated
     */
    fields?: Array<string>;
  }): CancelablePromise<annofeed_v1_Data> {
    return __request(APIConfig, {
      method: 'PATCH',
      url: '/annofeed/v1/datas/{data.uid}',
      path: {
        'data.uid': dataUid,
      },
      query: {
        fields: fields,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns annofeed_v1_Data OK
   * @throws ApiError
   */
  public static datasGetData({
    uid,
    simple,
  }: {
    uid: string;
    /**
     * if true, only return the simple info: name, desc, state, etc.
     */
    simple?: boolean;
  }): CancelablePromise<annofeed_v1_Data> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/annofeed/v1/datas/{uid}',
      path: {
        uid: uid,
      },
      query: {
        simple: simple,
      },
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static datasDeleteData({ uid }: { uid: string }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/annofeed/v1/datas/{uid}',
      path: {
        uid: uid,
      },
    });
  }

  /**
   * Fetch a list of elements
   * @returns annofeed_v1_GetDataElementsReply OK
   * @throws ApiError
   */
  public static datasGetDataElements({
    uid,
    startIdx,
    count,
  }: {
    /**
     * data UID
     */
    uid: string;
    /**
     * element start index
     */
    startIdx?: number;
    /**
     * element count
     */
    count?: number;
  }): CancelablePromise<annofeed_v1_GetDataElementsReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/annofeed/v1/datas/{uid}/elements',
      path: {
        uid: uid,
      },
      query: {
        start_idx: startIdx,
        count: count,
      },
    });
  }

  /**
   * Find a list of elements (only names and indexes are returned)
   * @returns annofeed_v1_GetDataElementsReply OK
   * @throws ApiError
   */
  public static datasFindDataElements({
    uid,
    startIdx,
    count,
    namePattern,
  }: {
    /**
     * data UID
     */
    uid: string;
    /**
     * element start index
     */
    startIdx?: number;
    /**
     * element count
     */
    count?: number;
    /**
     * search elements by the pattern of their names
     */
    namePattern?: string;
  }): CancelablePromise<annofeed_v1_GetDataElementsReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/annofeed/v1/datas/{uid}/find-elements',
      path: {
        uid: uid,
      },
      query: {
        start_idx: startIdx,
        count: count,
        name_pattern: namePattern,
      },
    });
  }

  /**
   * Get data meta
   * @returns annofeed_v1_GetDataMetaReply OK
   * @throws ApiError
   */
  public static datasGetDataMeta({
    uid,
    withMetafiles,
  }: {
    /**
     * data UID
     */
    uid: string;
    /**
     * whether to return metafiles in response
     */
    withMetafiles?: boolean;
  }): CancelablePromise<annofeed_v1_GetDataMetaReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/annofeed/v1/datas/{uid}/meta',
      path: {
        uid: uid,
      },
      query: {
        with_metafiles: withMetafiles,
      },
    });
  }

  /**
   * Parse data
   * @returns any OK
   * @throws ApiError
   */
  public static datasParseData({
    uid,
    requestBody,
  }: {
    /**
     * data UID
     */
    uid: string;
    requestBody: annofeed_v1_ParseDataRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/annofeed/v1/datas/{uid}/parse',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Set rawdata embedding.
   * @returns annofeed_v1_SetRawdataEmbeddingReply OK
   * @throws ApiError
   */
  public static datasSetRawdataEmbedding({
    uid,
    requestBody,
  }: {
    /**
     * data UID
     */
    uid: string;
    requestBody: annofeed_v1_SetRawdataEmbeddingRequest;
  }): CancelablePromise<annofeed_v1_SetRawdataEmbeddingReply> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/annofeed/v1/datas/{uid}/rawdata-embedding',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }
}
