/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { annofeed_v1_GetVersionReply } from '../models/annofeed_v1_GetVersionReply';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Configs {
  /**
   * @returns annofeed_v1_GetVersionReply OK
   * @throws ApiError
   */
  public static configsGetVersion(): CancelablePromise<annofeed_v1_GetVersionReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/annofeed/v1/version',
    });
  }
}
