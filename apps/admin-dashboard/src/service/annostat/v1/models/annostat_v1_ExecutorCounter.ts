/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { iam_v1_BaseUser } from './iam_v1_BaseUser';

export type annostat_v1_ExecutorCounter = {
  /**
   * executor can be a user or a team
   */
  executor: iam_v1_BaseUser;
  /**
   * number of jobs submitted
   */
  jobs: number;
  /**
   * number of elements submitted
   */
  elems: number;
  /**
   * number of annotations submitted
   */
  ins: number;
  /**
   * ins by widget: anno2d/anno3d/...
   */
  ins_by_widget: Record<string, number>;
  /**
   * number of jobs submitted per hour
   */
  jobs_per_hour: number;
  /**
   * number of elements submitted per hour
   */
  elems_per_hour: number;
  /**
   * element accuracy: [0, 1]
   */
  elem_accuracy: number;
  /**
   * ins accuracy: [0, 1]
   */
  ins_accuracy: number;
};
