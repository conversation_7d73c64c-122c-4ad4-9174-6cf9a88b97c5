/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { annostat_v1_GetOngoingLotsReply_Phase } from './annostat_v1_GetOngoingLotsReply_Phase';

export type annostat_v1_GetOngoingLotsReply_Lot = {
  uid: string;
  name: string;
  data_type: 'unspecified' | 'image' | 'pointcloud' | 'fusion2d' | 'fusion3d';
  /**
   * number of elements in the lot
   */
  data_size: number;
  phases: Array<annostat_v1_GetOngoingLotsReply_Phase>;
  /**
   * expected end time
   */
  exp_end_time: string;
  created_at: string;
};
