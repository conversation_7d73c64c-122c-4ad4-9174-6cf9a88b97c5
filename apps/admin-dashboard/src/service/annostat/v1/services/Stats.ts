/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { annostat_v1_GetLotCountReply } from '../models/annostat_v1_GetLotCountReply';
import type { annostat_v1_GetLotLabelStatReply } from '../models/annostat_v1_GetLotLabelStatReply';
import type { annostat_v1_GetLotStatByExecutorReply } from '../models/annostat_v1_GetLotStatByExecutorReply';
import type { annostat_v1_GetLotStatusReply } from '../models/annostat_v1_GetLotStatusReply';
import type { annostat_v1_GetOngoingLotsReply } from '../models/annostat_v1_GetOngoingLotsReply';
import type { annostat_v1_GetOrderConversionReply } from '../models/annostat_v1_GetOrderConversionReply';
import type { annostat_v1_GetOrderCountReply } from '../models/annostat_v1_GetOrderCountReply';
import type { annostat_v1_ProductionByTimeReply } from '../models/annostat_v1_ProductionByTimeReply';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Stats {
  /**
   * count lots
   * @returns annostat_v1_GetLotCountReply OK
   * @throws ApiError
   */
  public static statsGetLotCount({
    filterTimeRangeFrom,
    filterTimeRangeTo,
    filterLotTypes,
    filterDataTypes,
    filterLotUids,
    filterOwnerOrgs,
    filterExecutorOrgs,
    filterExecutorUids,
  }: {
    /**
     * time is within [from, to)
     */
    filterTimeRangeFrom?: string;
    filterTimeRangeTo?: string;
    /**
     * filter lot by type
     */
    filterLotTypes?: Array<'unspecified' | 'annotate' | 'segment'>;
    /**
     * filter lot by data type
     */
    filterDataTypes?: Array<'unspecified' | 'image' | 'pointcloud' | 'fusion2d' | 'fusion3d'>;
    /**
     * filter lot by uid
     */
    filterLotUids?: Array<string>;
    /**
     * filter lot by owner organization
     */
    filterOwnerOrgs?: Array<string>;
    /**
     * filter lot by executor organization
     */
    filterExecutorOrgs?: Array<string>;
    /**
     * filter lot by executor uid
     */
    filterExecutorUids?: Array<string>;
  }): CancelablePromise<annostat_v1_GetLotCountReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/annostat/v1/lots/count',
      query: {
        'filter.time_range.from': filterTimeRangeFrom,
        'filter.time_range.to': filterTimeRangeTo,
        'filter.lot_types': filterLotTypes,
        'filter.data_types': filterDataTypes,
        'filter.lot_uids': filterLotUids,
        'filter.owner_orgs': filterOwnerOrgs,
        'filter.executor_orgs': filterExecutorOrgs,
        'filter.executor_uids': filterExecutorUids,
      },
    });
  }

  /**
   * get ongoing lots
   * @returns annostat_v1_GetOngoingLotsReply OK
   * @throws ApiError
   */
  public static statsGetOngoingLots({
    page,
    pagesz,
  }: {
    page?: number;
    pagesz?: number;
  }): CancelablePromise<annostat_v1_GetOngoingLotsReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/annostat/v1/lots/ongoing',
      query: {
        page: page,
        pagesz: pagesz,
      },
    });
  }

  /**
   * lot statistics by label
   * @returns annostat_v1_GetLotLabelStatReply OK
   * @throws ApiError
   */
  public static statsGetLotLabelStat({ uid }: { uid: string }): CancelablePromise<annostat_v1_GetLotLabelStatReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/annostat/v1/lots/{uid}/labels',
      path: {
        uid: uid,
      },
    });
  }

  /**
   * lot statistics by executor
   * @returns annostat_v1_GetLotStatByExecutorReply OK
   * @throws ApiError
   */
  public static statsGetLotStatByExecutor({
    uid,
    phase,
    byExecteam,
    timeRangeFrom,
    timeRangeTo,
    page,
    pagesz,
  }: {
    /**
     * lot uid
     */
    uid: string;
    /**
     * phase number, start from 1
     */
    phase: number;
    /**
     * true to stats by execution teams, false to stats by individuals
     */
    byExecteam?: boolean;
    /**
     * time is within [from, to)
     */
    timeRangeFrom?: string;
    timeRangeTo?: string;
    page?: number;
    pagesz?: number;
  }): CancelablePromise<annostat_v1_GetLotStatByExecutorReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/annostat/v1/lots/{uid}/phases/{phase}/stat-by-exec',
      path: {
        uid: uid,
        phase: phase,
      },
      query: {
        by_execteam: byExecteam,
        'time_range.from': timeRangeFrom,
        'time_range.to': timeRangeTo,
        page: page,
        pagesz: pagesz,
      },
    });
  }

  /**
   * lot statistics by phase
   * @returns annostat_v1_GetLotStatusReply OK
   * @throws ApiError
   */
  public static statsGetLotStatus({ uid }: { uid: string }): CancelablePromise<annostat_v1_GetLotStatusReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/annostat/v1/lots/{uid}/status',
      path: {
        uid: uid,
      },
    });
  }

  /**
   * order-to-lot conversion statistics
   * @returns annostat_v1_GetOrderConversionReply OK
   * @throws ApiError
   */
  public static statsGetOrderConversion({
    filterTimeRangeFrom,
    filterTimeRangeTo,
    filterOwnerOrgs,
  }: {
    /**
     * time is within [from, to)
     */
    filterTimeRangeFrom?: string;
    filterTimeRangeTo?: string;
    /**
     * filter by owner organization
     */
    filterOwnerOrgs?: Array<string>;
  }): CancelablePromise<annostat_v1_GetOrderConversionReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/annostat/v1/orders/conversion',
      query: {
        'filter.time_range.from': filterTimeRangeFrom,
        'filter.time_range.to': filterTimeRangeTo,
        'filter.owner_orgs': filterOwnerOrgs,
      },
    });
  }

  /**
   * count orders
   * @returns annostat_v1_GetOrderCountReply OK
   * @throws ApiError
   */
  public static statsGetOrderCount({
    filterTimeRangeFrom,
    filterTimeRangeTo,
    filterOwnerOrgs,
  }: {
    /**
     * time is within [from, to)
     */
    filterTimeRangeFrom?: string;
    filterTimeRangeTo?: string;
    /**
     * filter by owner organization
     */
    filterOwnerOrgs?: Array<string>;
  }): CancelablePromise<annostat_v1_GetOrderCountReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/annostat/v1/orders/count',
      query: {
        'filter.time_range.from': filterTimeRangeFrom,
        'filter.time_range.to': filterTimeRangeTo,
        'filter.owner_orgs': filterOwnerOrgs,
      },
    });
  }

  /**
   * get production by time
   * @returns annostat_v1_ProductionByTimeReply OK
   * @throws ApiError
   */
  public static statsProductionByTime({
    filterTimeRangeFrom,
    filterTimeRangeTo,
    filterLotTypes,
    filterDataTypes,
    filterLotUids,
    filterOwnerOrgs,
    filterExecutorOrgs,
    filterExecutorUids,
    timeUnit,
    items,
  }: {
    /**
     * time is within [from, to)
     */
    filterTimeRangeFrom?: string;
    filterTimeRangeTo?: string;
    /**
     * filter lot by type
     */
    filterLotTypes?: Array<'unspecified' | 'annotate' | 'segment'>;
    /**
     * filter lot by data type
     */
    filterDataTypes?: Array<'unspecified' | 'image' | 'pointcloud' | 'fusion2d' | 'fusion3d'>;
    /**
     * filter lot by uid
     */
    filterLotUids?: Array<string>;
    /**
     * filter lot by owner organization
     */
    filterOwnerOrgs?: Array<string>;
    /**
     * filter lot by executor organization
     */
    filterExecutorOrgs?: Array<string>;
    /**
     * filter lot by executor uid
     */
    filterExecutorUids?: Array<string>;
    /**
     * group results by time_unit (in seconds)
     */
    timeUnit?: number;
    /**
     * items to count: anno2d, anno3d, elem, job
     */
    items?: Array<string>;
  }): CancelablePromise<annostat_v1_ProductionByTimeReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/annostat/v1/prod-by-time',
      query: {
        'filter.time_range.from': filterTimeRangeFrom,
        'filter.time_range.to': filterTimeRangeTo,
        'filter.lot_types': filterLotTypes,
        'filter.data_types': filterDataTypes,
        'filter.lot_uids': filterLotUids,
        'filter.owner_orgs': filterOwnerOrgs,
        'filter.executor_orgs': filterExecutorOrgs,
        'filter.executor_uids': filterExecutorUids,
        time_unit: timeUnit,
        items: items,
      },
    });
  }
}
