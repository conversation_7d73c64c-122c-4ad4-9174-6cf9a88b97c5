/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { annostat_v1_GetVersionReply } from '../models/annostat_v1_GetVersionReply';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Configs {
  /**
   * @returns annostat_v1_GetVersionReply OK
   * @throws ApiError
   */
  public static configsGetVersion(): CancelablePromise<annostat_v1_GetVersionReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/annostat/v1/version',
    });
  }
}
