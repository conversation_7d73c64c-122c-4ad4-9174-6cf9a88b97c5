/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { iam_v1_GetRoleFepermReply } from '../models/iam_v1_GetRoleFepermReply';
import type { iam_v1_GetRoleReply } from '../models/iam_v1_GetRoleReply';
import type { iam_v1_ListRoleReply } from '../models/iam_v1_ListRoleReply';
import type { iam_v1_Role } from '../models/iam_v1_Role';
import type { iam_v1_SetRoleFepermRequest } from '../models/iam_v1_SetRoleFepermRequest';
import type { iam_v1_UpdateRoleRequest } from '../models/iam_v1_UpdateRoleRequest';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Roles {
  /**
   * @returns iam_v1_ListRoleReply OK
   * @throws ApiError
   */
  public static rolesListRole({
    pagesz,
    pageToken,
    orgUid,
    namePattern,
  }: {
    pagesz?: number;
    /**
     * An opaque pagination token returned from a previous call.
     * An empty token denotes the first page.
     */
    pageToken?: string;
    /**
     * if not empty, only list roles created by this org
     */
    orgUid?: string;
    namePattern?: string;
  }): CancelablePromise<iam_v1_ListRoleReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/roles',
      query: {
        pagesz: pagesz,
        page_token: pageToken,
        org_uid: orgUid,
        name_pattern: namePattern,
      },
    });
  }

  /**
   * @returns iam_v1_Role OK
   * @throws ApiError
   */
  public static rolesCreateRole({ requestBody }: { requestBody: iam_v1_Role }): CancelablePromise<iam_v1_Role> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/iam/v1/roles',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns iam_v1_GetRoleReply OK
   * @throws ApiError
   */
  public static rolesGetRole({ name }: { name: string }): CancelablePromise<iam_v1_GetRoleReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/roles/{name}',
      path: {
        name: name,
      },
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static rolesDeleteRole({ name }: { name: string }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/iam/v1/roles/{name}',
      path: {
        name: name,
      },
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static rolesUpdateRole({
    name,
    requestBody,
  }: {
    name: string;
    requestBody: iam_v1_UpdateRoleRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'PATCH',
      url: '/iam/v1/roles/{name}',
      path: {
        name: name,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns iam_v1_GetRoleFepermReply OK
   * @throws ApiError
   */
  public static rolesGetRoleFeperm({ name }: { name: string }): CancelablePromise<iam_v1_GetRoleFepermReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/roles/{name}/feperm',
      path: {
        name: name,
      },
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static rolesSetRoleFeperm({
    name,
    requestBody,
  }: {
    /**
     * role name
     */
    name: string;
    requestBody: iam_v1_SetRoleFepermRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/iam/v1/roles/{name}/feperm',
      path: {
        name: name,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }
}
