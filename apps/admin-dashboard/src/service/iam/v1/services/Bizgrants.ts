/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { iam_v1_CreateBizgrantReply } from '../models/iam_v1_CreateBizgrantReply';
import type { iam_v1_CreateBizgrantRequest } from '../models/iam_v1_CreateBizgrantRequest';
import type { iam_v1_ListBizgrantReply } from '../models/iam_v1_ListBizgrantReply';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Bizgrants {
  /**
   * @returns iam_v1_ListBizgrantReply OK
   * @throws ApiError
   */
  public static bizgrantsListBizgrant({
    pagesz,
    pageToken,
    filterGrantorUid,
    filterGranteeUid,
    filterOrgUid,
    filterBiz,
  }: {
    pagesz?: number;
    /**
     * An opaque pagination token returned from a previous call.
     * An empty token denotes the first page.
     */
    pageToken?: string;
    /**
     * grantor uid
     */
    filterGrantorUid?: string;
    /**
     * grantee uid
     */
    filterGranteeUid?: string;
    /**
     * uid of the orgnization whose business permissions are granted
     */
    filterOrgUid?: string;
    /**
     * business scope granted
     */
    filterBiz?: 'unspecified' | 'anno';
  }): CancelablePromise<iam_v1_ListBizgrantReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/bizgrants',
      query: {
        pagesz: pagesz,
        page_token: pageToken,
        'filter.grantor_uid': filterGrantorUid,
        'filter.grantee_uid': filterGranteeUid,
        'filter.org_uid': filterOrgUid,
        'filter.biz': filterBiz,
      },
    });
  }

  /**
   * @returns iam_v1_CreateBizgrantReply OK
   * @throws ApiError
   */
  public static bizgrantsCreateBizgrant({
    requestBody,
  }: {
    requestBody: iam_v1_CreateBizgrantRequest;
  }): CancelablePromise<iam_v1_CreateBizgrantReply> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/iam/v1/bizgrants',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static bizgrantsDeleteBizgrant({
    filterGrantorUid,
    filterGranteeUid,
    filterOrgUid,
    filterBiz,
  }: {
    /**
     * grantor uid
     */
    filterGrantorUid?: string;
    /**
     * grantee uid
     */
    filterGranteeUid?: string;
    /**
     * uid of the orgnization whose business permissions are granted
     */
    filterOrgUid?: string;
    /**
     * business scope granted
     */
    filterBiz?: 'unspecified' | 'anno';
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/iam/v1/bizgrants',
      query: {
        'filter.grantor_uid': filterGrantorUid,
        'filter.grantee_uid': filterGranteeUid,
        'filter.org_uid': filterOrgUid,
        'filter.biz': filterBiz,
      },
    });
  }
}
