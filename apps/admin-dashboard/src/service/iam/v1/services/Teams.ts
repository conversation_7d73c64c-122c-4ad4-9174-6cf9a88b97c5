/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { iam_v1_AddMembersRequest } from '../models/iam_v1_AddMembersRequest';
import type { iam_v1_CreateTeamRequest } from '../models/iam_v1_CreateTeamRequest';
import type { iam_v1_ListMembersReply } from '../models/iam_v1_ListMembersReply';
import type { iam_v1_ListTeamReply } from '../models/iam_v1_ListTeamReply';
import type { iam_v1_SetMembersRoleRequest } from '../models/iam_v1_SetMembersRoleRequest';
import type { iam_v1_Team } from '../models/iam_v1_Team';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Teams {
  /**
   * @returns iam_v1_ListTeamReply OK
   * @throws ApiError
   */
  public static teamsListTeam({
    page,
    pagesz,
    parentUid,
    namePattern,
    uids,
    teamType,
  }: {
    page?: number;
    pagesz?: number;
    /**
     * parent team uid; if not specified, only query organizations
     */
    parentUid?: string;
    /**
     * find teams by name pattern
     */
    namePattern?: string;
    uids?: Array<string>;
    teamType?: 'unspecified' | 'demander' | 'operator' | 'supplier';
  }): CancelablePromise<iam_v1_ListTeamReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/teams',
      query: {
        page: page,
        pagesz: pagesz,
        parent_uid: parentUid,
        name_pattern: namePattern,
        uids: uids,
        team_type: teamType,
      },
    });
  }

  /**
   * @returns iam_v1_Team OK
   * @throws ApiError
   */
  public static teamsCreateTeam({
    requestBody,
  }: {
    requestBody: iam_v1_CreateTeamRequest;
  }): CancelablePromise<iam_v1_Team> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/iam/v1/teams',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns iam_v1_ListTeamReply OK
   * @throws ApiError
   */
  public static teamsGetTeamsRoot({ uids }: { uids?: Array<string> }): CancelablePromise<iam_v1_ListTeamReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/teams/root',
      query: {
        uids: uids,
      },
    });
  }

  /**
   * @returns iam_v1_Team OK
   * @throws ApiError
   */
  public static teamsUpdateTeam({
    teamUid,
    requestBody,
    fields,
  }: {
    teamUid: string;
    requestBody: iam_v1_CreateTeamRequest;
    fields?: Array<string>;
  }): CancelablePromise<iam_v1_Team> {
    return __request(APIConfig, {
      method: 'PATCH',
      url: '/iam/v1/teams/{team.uid}',
      path: {
        'team.uid': teamUid,
      },
      query: {
        fields: fields,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns iam_v1_Team OK
   * @throws ApiError
   */
  public static teamsGetTeam({ uid }: { uid: string }): CancelablePromise<iam_v1_Team> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/teams/{uid}',
      path: {
        uid: uid,
      },
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static teamsDeleteTeam({ uid }: { uid: string }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/iam/v1/teams/{uid}',
      path: {
        uid: uid,
      },
    });
  }

  /**
   * @returns iam_v1_ListMembersReply OK
   * @throws ApiError
   */
  public static teamsListMembers({
    uid,
    page,
    pagesz,
    namePattern,
    role,
  }: {
    /**
     * team uid
     */
    uid: string;
    page?: number;
    pagesz?: number;
    /**
     * list only members matching the name pattern
     */
    namePattern?: string;
    /**
     * list only members with the specified role
     */
    role?: string;
  }): CancelablePromise<iam_v1_ListMembersReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/teams/{uid}/members',
      path: {
        uid: uid,
      },
      query: {
        page: page,
        pagesz: pagesz,
        name_pattern: namePattern,
        role: role,
      },
    });
  }

  /**
   * send join-team invitation to mentioned users
   * @returns any OK
   * @throws ApiError
   */
  public static teamsAddMembers({
    uid,
    requestBody,
  }: {
    /**
     * team uid
     */
    uid: string;
    requestBody: iam_v1_AddMembersRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/iam/v1/teams/{uid}/members',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static teamsDeleteMembers({
    uid,
    userUids,
  }: {
    /**
     * team uid
     */
    uid: string;
    userUids?: Array<string>;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/iam/v1/teams/{uid}/members',
      path: {
        uid: uid,
      },
      query: {
        user_uids: userUids,
      },
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static teamsSetMembersRole({
    uid,
    requestBody,
  }: {
    /**
     * uid of the team
     */
    uid: string;
    requestBody: iam_v1_SetMembersRoleRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/iam/v1/teams/{uid}/role',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }
}
