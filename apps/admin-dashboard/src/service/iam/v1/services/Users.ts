/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { iam_v1_BatchCreateUsersRequest } from '../models/iam_v1_BatchCreateUsersRequest';
import type { iam_v1_CreateUserRequest } from '../models/iam_v1_CreateUserRequest';
import type { iam_v1_Feperm } from '../models/iam_v1_Feperm';
import type { iam_v1_GetPermsReply } from '../models/iam_v1_GetPermsReply';
import type { iam_v1_IsAllowedReply } from '../models/iam_v1_IsAllowedReply';
import type { iam_v1_ListUserReply } from '../models/iam_v1_ListUserReply';
import type { iam_v1_LoginReply } from '../models/iam_v1_LoginReply';
import type { iam_v1_LoginRequest } from '../models/iam_v1_LoginRequest';
import type { iam_v1_SendAuthCodeReply } from '../models/iam_v1_SendAuthCodeReply';
import type { iam_v1_SendAuthCodeRequest } from '../models/iam_v1_SendAuthCodeRequest';
import type { iam_v1_User } from '../models/iam_v1_User';
import type { iam_v1_UserContext } from '../models/iam_v1_UserContext';
import type { types_TagList } from '../models/types_TagList';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Users {
  /**
   * @returns iam_v1_ListUserReply OK
   * @throws ApiError
   */
  public static usersListUser({
    page,
    pagesz,
    namePattern,
    uids,
    phones,
    emails,
    withOrg,
    tags,
    orgUid,
    roles,
  }: {
    page?: number;
    pagesz?: number;
    /**
     * find by name pattern
     */
    namePattern?: string;
    /**
     * find by uid
     */
    uids?: Array<string>;
    /**
     * find by phone number
     */
    phones?: Array<string>;
    /**
     * find by emails
     */
    emails?: Array<string>;
    /**
     * include user's organization in the reply
     */
    withOrg?: boolean;
    /**
     * find by attached tags
     */
    tags?: Array<string>;
    /**
     * find by org_uid
     */
    orgUid?: string;
    /**
     * find by roles
     */
    roles?: Array<string>;
  }): CancelablePromise<iam_v1_ListUserReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/users',
      query: {
        page: page,
        pagesz: pagesz,
        name_pattern: namePattern,
        uids: uids,
        phones: phones,
        emails: emails,
        with_org: withOrg,
        tags: tags,
        org_uid: orgUid,
        roles: roles,
      },
    });
  }

  /**
   * @returns iam_v1_User OK
   * @throws ApiError
   */
  public static usersCreateUser({
    requestBody,
  }: {
    requestBody: iam_v1_CreateUserRequest;
  }): CancelablePromise<iam_v1_User> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/iam/v1/users',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns iam_v1_ListUserReply OK
   * @throws ApiError
   */
  public static usersBatchCreateUsers({
    requestBody,
  }: {
    requestBody: iam_v1_BatchCreateUsersRequest;
  }): CancelablePromise<iam_v1_ListUserReply> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/iam/v1/users/batch',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns iam_v1_LoginReply OK
   * @throws ApiError
   */
  public static usersLogin({
    requestBody,
  }: {
    requestBody: iam_v1_LoginRequest;
  }): CancelablePromise<iam_v1_LoginReply> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/iam/v1/users/login',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * clear HTTP cookies
   * @returns any OK
   * @throws ApiError
   */
  public static usersLogout({ requestBody }: { requestBody: any }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/iam/v1/users/logout',
      body: requestBody,
    });
  }

  /**
   * retrieve my info
   * @returns iam_v1_User OK
   * @throws ApiError
   */
  public static usersGetMe(): CancelablePromise<iam_v1_User> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/users/me',
    });
  }

  /**
   * get my front-end permissions
   * @returns iam_v1_Feperm OK
   * @throws ApiError
   */
  public static usersGetMyFeperm(): CancelablePromise<iam_v1_Feperm> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/users/me/feperm',
    });
  }

  /**
   * retrieve my info; also returns the real user in an assume context
   * @returns iam_v1_UserContext OK
   * @throws ApiError
   */
  public static usersGetMe2(): CancelablePromise<iam_v1_UserContext> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/users/me2',
    });
  }

  /**
   * @returns iam_v1_SendAuthCodeReply OK
   * @throws ApiError
   */
  public static usersSendAuthCode({
    requestBody,
  }: {
    requestBody: iam_v1_SendAuthCodeRequest;
  }): CancelablePromise<iam_v1_SendAuthCodeReply> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/iam/v1/users/send-auth-code',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns iam_v1_LoginReply OK
   * @throws ApiError
   */
  public static usersRefreshToken(): CancelablePromise<iam_v1_LoginReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/users/token/refresh',
    });
  }

  /**
   * @returns iam_v1_User OK
   * @throws ApiError
   */
  public static usersGetUser({ uid }: { uid: string }): CancelablePromise<iam_v1_User> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/users/{uid}',
      path: {
        uid: uid,
      },
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static usersDeleteUser({ uid }: { uid: string }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/iam/v1/users/{uid}',
      path: {
        uid: uid,
      },
    });
  }

  /**
   * @returns iam_v1_LoginReply OK
   * @throws ApiError
   */
  public static usersAssumeUser({
    uid,
    identity,
  }: {
    /**
     * uid of the user to assume; use "me" to unassume;
     * use "identity" to specify the user by his/her phone or email
     */
    uid: string;
    /**
     * when uid is "identity", this holds the phone or email of the user to assume
     */
    identity?: string;
  }): CancelablePromise<iam_v1_LoginReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/users/{uid}/assume',
      path: {
        uid: uid,
      },
      query: {
        identity: identity,
      },
    });
  }

  /**
   * check if the user is allowed to perform the actions
   * @returns iam_v1_IsAllowedReply OK
   * @throws ApiError
   */
  public static usersIsAllowed({
    uid,
    actionResource,
    actionPerm,
  }: {
    uid: string;
    /**
     * resource name. format: type:(uid|IamGroup:team-uid)
     * pattern: "^\\w+:([\\w:.-]+)*$"
     */
    actionResource?: string;
    actionPerm?: string;
  }): CancelablePromise<iam_v1_IsAllowedReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/users/{uid}/is-allowed',
      path: {
        uid: uid,
      },
      query: {
        'action.resource': actionResource,
        'action.perm': actionPerm,
      },
    });
  }

  /**
   * get the permissions granted on a resource
   * @returns iam_v1_GetPermsReply OK
   * @throws ApiError
   */
  public static usersGetPerms({
    uid,
    resource,
    scope,
    perms,
  }: {
    uid: string;
    /**
     * resource name in format: type:uid; when scope is specified, uid may be omitted
     * pattern: "^\\w+:([\\w.-]+)?$"
     */
    resource?: string;
    /**
     * check if the user can create/list resources within an organization/team
     * format: IamGroup:uid, pattern: "^(IamGroup:[\\w-]+)?$"
     */
    scope?: string;
    /**
     * if perms is not empty, only check for the permissions in the list
     * if it is empty, check all permissions valid to the resource type.
     * pattern: "^\\w+\\.\\w+$"
     */
    perms?: Array<string>;
  }): CancelablePromise<iam_v1_GetPermsReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/users/{uid}/perms',
      path: {
        uid: uid,
      },
      query: {
        resource: resource,
        scope: scope,
        perms: perms,
      },
    });
  }

  /**
   * @returns types_TagList OK
   * @throws ApiError
   */
  public static usersAddTag({
    uid,
    requestBody,
  }: {
    uid: string;
    requestBody: string;
  }): CancelablePromise<types_TagList> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/iam/v1/users/{uid}/tag',
      path: {
        uid: uid,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns types_TagList OK
   * @throws ApiError
   */
  public static usersDeleteTag({ uid, tags }: { uid: string; tags?: Array<string> }): CancelablePromise<types_TagList> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/iam/v1/users/{uid}/tag',
      path: {
        uid: uid,
      },
      query: {
        tags: tags,
      },
    });
  }

  /**
   * @returns iam_v1_User OK
   * @throws ApiError
   */
  public static usersUpdateUser({
    userUid,
    requestBody,
    fields,
  }: {
    userUid: string;
    requestBody: iam_v1_CreateUserRequest;
    fields?: Array<string>;
  }): CancelablePromise<iam_v1_User> {
    return __request(APIConfig, {
      method: 'PATCH',
      url: '/iam/v1/users/{user.uid}',
      path: {
        'user.uid': userUid,
      },
      query: {
        fields: fields,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }
}
