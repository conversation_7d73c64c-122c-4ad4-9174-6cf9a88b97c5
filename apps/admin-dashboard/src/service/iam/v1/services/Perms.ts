/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { iam_v1_EditPermsRequest } from '../models/iam_v1_EditPermsRequest';
import type { iam_v1_ListPermClassReply } from '../models/iam_v1_ListPermClassReply';
import type { iam_v1_ListPermReply } from '../models/iam_v1_ListPermReply';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Perms {
  /**
   * get permission list
   * @returns iam_v1_ListPermReply OK
   * @throws ApiError
   */
  public static permsListPerm({
    pagesz,
    pageToken,
    _class,
    namePattern,
  }: {
    pagesz?: number;
    /**
     * An opaque pagination token returned from a previous call.
     * An empty token denotes the first page.
     */
    pageToken?: string;
    /**
     * resource type: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...
     */
    _class?: string;
    namePattern?: string;
  }): CancelablePromise<iam_v1_ListPermReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/perms',
      query: {
        pagesz: pagesz,
        page_token: pageToken,
        class: _class,
        name_pattern: namePattern,
      },
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static permsEditPerms({ requestBody }: { requestBody: iam_v1_EditPermsRequest }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/iam/v1/perms',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * get permission class (resource type) list: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...
   * @returns iam_v1_ListPermClassReply OK
   * @throws ApiError
   */
  public static permsListPermClass(): CancelablePromise<iam_v1_ListPermClassReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/perms/classes',
    });
  }
}
