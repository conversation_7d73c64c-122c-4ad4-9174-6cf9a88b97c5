/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { iam_v1_CreatePolicyRequest } from '../models/iam_v1_CreatePolicyRequest';
import type { iam_v1_GetAttachedPoliciesReply } from '../models/iam_v1_GetAttachedPoliciesReply';
import type { iam_v1_Policy } from '../models/iam_v1_Policy';
import type { iam_v1_UpdatePolicyRequest } from '../models/iam_v1_UpdatePolicyRequest';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Policies {
  /**
   * Create and attach a policy to a resource
   * @returns iam_v1_Policy OK
   * @throws ApiError
   */
  public static policiesCreatePolicy({
    requestBody,
  }: {
    requestBody: iam_v1_CreatePolicyRequest;
  }): CancelablePromise<iam_v1_Policy> {
    return __request(APIConfig, {
      method: 'POST',
      url: '/iam/v1/policies',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * @returns iam_v1_Policy OK
   * @throws ApiError
   */
  public static policiesGetPolicy({ name }: { name: string }): CancelablePromise<iam_v1_Policy> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/policies/{name}',
      path: {
        name: name,
      },
    });
  }

  /**
   * detach and delete the policy
   * @returns any OK
   * @throws ApiError
   */
  public static policiesDeletePolicy({ name }: { name: string }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'DELETE',
      url: '/iam/v1/policies/{name}',
      path: {
        name: name,
      },
    });
  }

  /**
   * @returns any OK
   * @throws ApiError
   */
  public static policiesUpdatePolicy({
    name,
    requestBody,
  }: {
    name: string;
    requestBody: iam_v1_UpdatePolicyRequest;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'PATCH',
      url: '/iam/v1/policies/{name}',
      path: {
        name: name,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * get policies attached to a resource
   * @returns iam_v1_GetAttachedPoliciesReply OK
   * @throws ApiError
   */
  public static policiesGetAttachedPolicies({
    name,
    role,
  }: {
    /**
     * resource name in the format: type:(uid|name)
     * type: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...
     */
    name: string;
    /**
     * get attached policy binding the role
     */
    role?: string;
  }): CancelablePromise<iam_v1_GetAttachedPoliciesReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/resources/{name}/policies',
      path: {
        name: name,
      },
      query: {
        role: role,
      },
    });
  }
}
