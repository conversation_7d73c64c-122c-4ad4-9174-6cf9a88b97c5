/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { iam_v1_Errors } from '../models/iam_v1_Errors';
import type { iam_v1_GetVersionReply } from '../models/iam_v1_GetVersionReply';
import type { types_NameValue } from '../models/types_NameValue';

import { CancelablePromise } from '@forest/fetch';
import { request as __request } from '@forest/fetch';
import { APIConfig } from '@/service/config';

export class Configs {
  /**
   * get a configuration item
   * @returns types_NameValue OK
   * @throws ApiError
   */
  public static configsGetConf({ name }: { name: string }): CancelablePromise<types_NameValue> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/conf/{name}',
      path: {
        name: name,
      },
    });
  }

  /**
   * set/change a configuration item
   * @returns any OK
   * @throws ApiError
   */
  public static configsSetConf({
    name,
    requestBody,
  }: {
    name: string;
    requestBody: types_NameValue;
  }): CancelablePromise<any> {
    return __request(APIConfig, {
      method: 'PUT',
      url: '/iam/v1/conf/{name}',
      path: {
        name: name,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * List Errors info
   * @returns iam_v1_Errors OK
   * @throws ApiError
   */
  public static configsListErrors(): CancelablePromise<iam_v1_Errors> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/errors',
    });
  }

  /**
   * @returns iam_v1_GetVersionReply OK
   * @throws ApiError
   */
  public static configsGetVersion(): CancelablePromise<iam_v1_GetVersionReply> {
    return __request(APIConfig, {
      method: 'GET',
      url: '/iam/v1/version',
    });
  }
}
