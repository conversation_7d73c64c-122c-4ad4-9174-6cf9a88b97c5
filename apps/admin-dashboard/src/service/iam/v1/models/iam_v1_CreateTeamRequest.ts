/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type iam_v1_CreateTeamRequest = {
  uid?: string;
  /**
   * mandatory in create-team request
   */
  name?: string;
  desc?: string;
  avatar?: string;
  province?: string;
  city?: string;
  /**
   * parent team uid
   */
  parent_uid?: string;
  /**
   * mandatory in create-team request
   */
  type?: 'unspecified' | 'demander' | 'operator' | 'supplier';
  /**
   * specify the team owner.
   * supported formats: uid:xxx, phone:+8612345678901 or email:<EMAIL>
   */
  owner?: string;
};
