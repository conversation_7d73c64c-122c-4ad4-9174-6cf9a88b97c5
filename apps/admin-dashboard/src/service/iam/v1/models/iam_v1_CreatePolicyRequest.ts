/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type iam_v1_CreatePolicyRequest = {
  role: string;
  /**
   * uid of users (IamUser:xxx) or groups(IamGroup:xxx)
   * pattern: "^(IamUser|IamGroup):[\\w-]+(:[\\w-]+)?$"
   */
  users: Array<string>;
  /**
   * the name of the resource to be attached to; pattern: "^([\\w-]+:){1,2}([\\w-]+(.|/))+$"
   */
  resource: string;
};
