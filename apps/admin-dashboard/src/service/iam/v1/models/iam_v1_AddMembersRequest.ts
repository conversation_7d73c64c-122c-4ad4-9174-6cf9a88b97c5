/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type iam_v1_AddMembersRequest = {
  /**
   * team uid
   */
  uid: string;
  /**
   * type of user identities: uid/email/phone/...
   */
  id_type: 'unspecified' | 'uid' | 'phone' | 'email';
  /**
   * max number of user identities is 100
   */
  identities: Array<string>;
  /**
   * one of owner/manager/member
   */
  role: string;
};
