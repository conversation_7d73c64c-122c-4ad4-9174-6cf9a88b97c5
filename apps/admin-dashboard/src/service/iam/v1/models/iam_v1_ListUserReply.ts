/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { iam_v1_BaseUser } from './iam_v1_BaseUser';
import type { iam_v1_User } from './iam_v1_User';

export type iam_v1_ListUserReply = {
  /**
   * total number of items found; valid only in the first page reply.
   */
  total?: number;
  users: Array<iam_v1_User>;
  /**
   * the organizations that the user, at the corresponding position, belongs to.
   */
  orgs?: Array<iam_v1_BaseUser>;
};
