/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type iam_v1_User = {
  uid: string;
  name: string;
  /**
   * phone number: +8613412345678
   */
  phone?: string;
  /**
   * email: <EMAIL>
   */
  email?: string;
  avatar: string;
  /**
   * user's role: admin/manager/member
   */
  role: string;
  /**
   * user's gender
   */
  gender: 'unspecified' | 'male' | 'female';
  /**
   * user's birthday: 2010-06-07
   */
  birthday?: string;
  province: string;
  city: string;
  /**
   * top level team the user belongs to
   */
  org_uid?: string;
  /**
   * type of the user's organization
   */
  org_type?: 'unspecified' | 'demander' | 'operator' | 'supplier';
  /**
   * string hierarchy = 13;
   * indicates if the user's information should be improved
   */
  readonly imperfect: boolean;
  readonly created_at: string;
  /**
   * tags attached to the user
   */
  tags?: Array<string>;
};
