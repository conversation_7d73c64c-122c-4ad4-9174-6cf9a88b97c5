/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type iam_v1_CreateUserRequest = {
  uid?: string;
  /**
   * mandatory in create-user requests
   */
  name?: string;
  /**
   * phone number: +8613412345678; mandatory in create-user requests
   */
  phone?: string;
  /**
   * email: <EMAIL>
   */
  email?: string;
  avatar?: string;
  /**
   * user's system role: member/admin/root
   */
  role?: string;
  /**
   * user's gender
   */
  gender?: 'unspecified' | 'male' | 'female';
  /**
   * user's birthday in RFC339 format: 2010-06-07T00:00:00Z
   * use a string type to allow an empty birthday
   */
  birthday?: string;
  province?: string;
  city?: string;
};
