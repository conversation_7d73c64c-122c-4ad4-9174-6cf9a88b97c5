/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type iam_v1_Role = {
  /**
   * name of the role
   * admins can create global roles; others can only create org-scope roles
   * role names should be unique within their scopes.
   * name of org-scope roles are in the format: org-uid.role-name
   * pattern: "^[\\w-]+(\\.[\\w-]+)?$"; length-in-bytes: [3, 30]
   */
  name: string;
  /**
   * name displayed in UI; length-in-bytes: [3, 30]
   */
  display_name?: string;
  /**
   * list of roles (IamRole:xxx) and permissions
   */
  perms: Array<string>;
};
