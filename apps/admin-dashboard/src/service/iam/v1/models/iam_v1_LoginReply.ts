/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { iam_v1_Feperm } from './iam_v1_Feperm';
import type { iam_v1_User } from './iam_v1_User';

export type iam_v1_LoginReply = {
  /**
   * user info; it may be an assumed user in an assume request
   */
  user: iam_v1_User;
  /**
   * JWT token
   */
  token: string;
  /**
   * token expire time in RFC3339 format: 2016-01-01T00:00:00+08:00
   */
  expire_time: string;
  /**
   * front-end permissions
   */
  feperm: iam_v1_Feperm;
  /**
   * the real user in an assume request
   */
  assume_by?: iam_v1_User;
};
