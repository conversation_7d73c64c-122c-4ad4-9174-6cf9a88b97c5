/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type iam_v1_LoginRequest = {
  /**
   * type of user's identity
   */
  id_type: 'unspecified' | 'uid' | 'phone' | 'email';
  /**
   * user's phone number-number/email/...
   */
  identity: string;
  /**
   * type of the authentication
   */
  auth_type: 'unspecified' | 'authcode' | 'password' | 'otp';
  /**
   * authentication credential according to auth_type
   */
  credential: string;
  /**
   * signed version of user agreement; 0 means no update
   */
  agreement?: number;
};
