import { Box, Breadcrumb, BreadcrumbItem, BreadcrumbLink, Flex } from '@chakra-ui/react';
import { TipButton } from '@forest/simple-ui';
import React, { ReactElement, ReactNode } from 'react';
import { RiAddLine } from 'react-icons/ri';
import { Link as RouterLink } from 'react-router-dom';

interface HeaderProps {
  title: string;
  pathList: Array<{
    name: string;
    path?: string;
  }>;
  children?: ReactNode;
  rightButton?: {
    tip: string;
    icon?: ReactElement;
    onClick?: () => void;
    isLoading?: boolean;
    isDisabled?: boolean;
  };
}

export const Header: React.FC<HeaderProps> = ({ title, pathList, rightButton, children }) => {
  return (
    <Box
      w="100%"
      boxSizing="content-box"
      pos="sticky"
      top="2.5"
      mb="5"
      ml="-3"
      py="2.5"
      px="3"
      borderRadius="lg"
      // bg="none"
      backgroundColor="whiteAlpha.500"
      backdropFilter="blur(20px)"
      display="flex"
      alignItems="center"
      justifyContent="space-between"
      zIndex="overlay"
    >
      <Box>
        <Breadcrumb color="gray.600" _hover={{ color: 'gray.800' }}>
          <BreadcrumbItem>
            <Box>恺望数据</Box>
          </BreadcrumbItem>
          {pathList.map(({ name, path }, i) => (
            <BreadcrumbItem key={i}>
              {path && path.length > 0 ? (
                <BreadcrumbLink as={RouterLink} to={path} color={i + 1 === pathList.length ? 'gray.800' : ''}>
                  {name}
                </BreadcrumbLink>
              ) : (
                <Box>{name}</Box>
              )}
            </BreadcrumbItem>
          ))}
        </Breadcrumb>
        <Box fontSize="1.8rem" fontWeight="bold" color="brand.800">
          {title}
        </Box>
      </Box>
      <Box p="2.5" background="white" borderRadius="xl" boxShadow="xl">
        <Flex align="center">
          {children}
          {rightButton ? (
            <TipButton
              aria-label={rightButton.tip}
              colorScheme="brand"
              isRound={true}
              fontSize="18px"
              icon={rightButton.icon || <RiAddLine />}
              ml="2"
              isLoading={rightButton.isLoading}
              isDisabled={rightButton.isDisabled}
              onClick={rightButton.onClick}
            ></TipButton>
          ) : (
            <div></div>
          )}
        </Flex>
      </Box>
    </Box>
  );
};
