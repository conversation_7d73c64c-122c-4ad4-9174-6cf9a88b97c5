import type { IconType } from 'react-icons';
import {
  RiBarChart2Line,
  RiContactsBook2Line,
  RiHammerLine,
  RiHomeSmileLine,
  RiStackLine,
  RiUserSettingsLine,
} from 'react-icons/ri';

export interface INavItem {
  name: string;
  text: string;
  icon: IconType;
}
export const navRouters: Record<string, INavItem> = {
  '/admin/home': {
    name: 'dashboard',
    text: '仪表盘',
    icon: RiBarChart2Line,
  },
  '/admin/tasks/manage': {
    name: 'tasks',
    text: '任务管理',
    icon: RiStackLine,
  },
  '/admin/tasks/produce': {
    name: 'taskProduce',
    text: '任务生产',
    icon: RiHammerLine,
  },
  '/admin/tasks/hall': {
    name: 'taskHall',
    text: '任务大厅',
    icon: RiHomeSmileLine,
  },
  '/admin/orders/manage': {
    name: 'orders',
    text: '订单管理',
    icon: RiStackLine,
  },
  '/admin/teams/manage': {
    name: 'teams',
    text: '组织管理',
    icon: RiContactsBook2Line,
  },
  '/admin/teams/members': {
    name: 'members',
    text: '成员管理',
    icon: RiUserSettingsLine,
  },
  '/admin/users/manage': {
    name: 'users',
    text: '用户管理',
    icon: RiUserSettingsLine,
  },
};
