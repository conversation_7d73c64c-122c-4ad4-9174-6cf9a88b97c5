import { Box, Flex, Icon, Link, VStack } from '@chakra-ui/react';
import { FC } from 'react';
import { Link as RouterLink, useMatch } from 'react-router-dom';

import { useAuth } from '@/providers/auth';

import { INavItem, navRouters } from './config';

const NavButton: FC<INavItem & { path: string }> = ({ icon, text, path }) => {
  const selected = useMatch({
    path,
    caseSensitive: false,
    end: true,
  });
  const defaultCase = window.location.pathname === '/admin' && path === '/admin/home';

  const selectedSX =
    selected === null && !defaultCase
      ? {}
      : {
          color: 'gray.600',
          '> svg': { color: '#1481E3' },
        };

  return (
    <Flex
      align="center"
      py="3"
      pl="2.75rem"
      cursor="pointer"
      fontSize="16"
      role="group"
      fontWeight="bold"
      letterSpacing="1px"
      sx={selectedSX}
    >
      {icon ? (
        <Icon
          as={icon}
          mr="4"
          fontSize="22"
          verticalAlign="bottom"
          _groupHover={{
            color: '#1481E3',
          }}
        />
      ) : (
        <Box mr="3" w="22" display="inline-block"></Box>
      )}
      {text}
    </Flex>
  );
};
export const NavList = () => {
  const { permission } = useAuth();

  if (!permission) return <></>;

  return (
    <VStack mt="8" spacing="2.5" flex="1">
      {permission.visible_pages.map((path) =>
        navRouters[path] ? (
          <Link as={RouterLink} key={navRouters[path].name} w="100%" to={path || '#'} variant="text" colorScheme="gray">
            <NavButton path={path} {...navRouters[path]}></NavButton>
          </Link>
        ) : (
          <></>
        )
      )}
    </VStack>
  );
};
