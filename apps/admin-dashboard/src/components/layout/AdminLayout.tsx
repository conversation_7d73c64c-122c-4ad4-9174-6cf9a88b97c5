import { Box, BoxProps, Flex, Text } from '@chakra-ui/react';
import React from 'react';

import { NavList } from './NavList';
import { Sidebar } from './Sidebar';

const Footer: React.FC = () => {
  return (
    <Text color="gray.300" px="2" pt="2" mb="1" fontSize="xs">
      京ICP备 2022018306号 京公网安备 11010802039684 北京恺望数据科技有限公司 © 2023 Konvery. All rights reserved.
    </Text>
  );
};

export const AdminLayout: React.FC<BoxProps> = ({ children }) => {
  return (
    <Box bg="gray.50">
      <Flex>
        <Box w="14rem">
          <Sidebar w="14rem" h="100vh" left="0" py="2">
            <NavList />
          </Sidebar>
        </Box>
        <Flex direction="column" flex="1" mr="3px" px="6" h="100vh" overflow="overlay" overflowX="hidden">
          <Box flex="1" w="100%">
            {children}
          </Box>
          <Footer></Footer>
        </Flex>
      </Flex>
    </Box>
  );
};
