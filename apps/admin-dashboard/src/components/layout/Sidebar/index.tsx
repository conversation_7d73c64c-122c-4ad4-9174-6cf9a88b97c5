import { Box, BoxProps, Container, Flex, Image, Show } from '@chakra-ui/react';
import { FC } from 'react';

import { ProfileMenu } from './ProfileMenu';

export const Sidebar: FC<BoxProps> = ({ children, ...props }) => (
  <Show above="md">
    <Box
      bg="white"
      pos="fixed"
      zIndex="docked"
      overflowX="hidden"
      overflowY="scroll"
      css={{
        '&::-webkit-scrollbar': {
          display: 'none',
        },
      }}
      {...props}
    >
      <Flex minH="100%" direction="column">
        <Flex
          h="96px"
          pt="1"
          alignItems="center"
          justifyContent="center"
          borderBottom="1px"
          borderBottomColor="blackAlpha.200"
        >
          <Image src="/logo-sider.png" w="60%" alt="konvery" objectFit="contain" objectPosition="center" />
        </Flex>
        {children}
        <Container
          w="80%"
          my="3"
          bgRepeat="no-repeat"
          bgGradient="linear(to-br, brand.50, brand.600)"
          color="white"
          textAlign="center"
        ></Container>
        <Box w="100%" py="4" position="relative" textAlign="center">
          <ProfileMenu />
        </Box>
      </Flex>
    </Box>
  </Show>
);
