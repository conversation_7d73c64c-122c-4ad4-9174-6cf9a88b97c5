import { Avatar, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Menu<PERSON><PERSON>, MenuList, Text } from '@chakra-ui/react';
import React from 'react';

import { logout } from '@/features/auth';
import { useAuth } from '@/providers/auth';

const greet = (hour: number) => {
  if (hour < 12) {
    return '上午好 ☀️';
  } else if (hour < 18) {
    return '下午好 ☕️';
  } else {
    return '晚上好 ☕️';
  }
};

export const ProfileMenu = () => {
  const { user } = useAuth();

  if (!user) {
    return <></>;
  }

  return (
    <Menu placement="right" strategy="fixed" offset={[0, 28]}>
      <MenuButton cursor="pointer">
        <Avatar w="12" h="12" mr="3" name={user.name} src={user.avatar} />
        <Box display="inline-block" color="brand.800" textAlign="left">
          <Text fontWeight="bold" noOfLines={1} maxWidth="32">
            {`${user.name} - ${user.team?.name || '独行者'}`}
          </Text>
          <Text>{user.role}</Text>
        </Box>
      </MenuButton>
      <MenuList pt="0" pb="2" fontSize="sm" zIndex="dropdown" textAlign="left">
        <Box pt="4" pb="3" pl="5" fontWeight="bold">
          <Box>Hey, {greet(new Date().getHours())}</Box>
          <Box mt={2} fontWeight="normal">
            ID: {user.uid}
          </Box>
        </Box>
        <MenuDivider m="0" color="gray.200" />
        <MenuItem
          onClick={() =>
            logout().subscribe(() => {
              window.location.assign(window.location.origin);
            })
          }
        >
          登出
        </MenuItem>
      </MenuList>
    </Menu>
  );
};
