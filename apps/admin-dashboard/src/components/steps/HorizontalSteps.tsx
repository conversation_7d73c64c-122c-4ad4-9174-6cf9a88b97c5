import { Box, Divider, Flex, Icon } from '@chakra-ui/react';
import { RiCheckboxCircleFill, RiCloseCircleFill, RiRecordCircleLine } from 'react-icons/ri';

export type StepState = 'todo' | 'doing' | 'done' | 'failed';
const getStepIconConfig = (state: StepState, primaryColor: string, todoColor: string, failedColor: string) =>
  ({
    todo: {
      icon: RiRecordCircleLine,
      iconColor: todoColor,
      lineLeftColor: todoColor,
      lineRightColor: todoColor,
    },
    doing: {
      icon: RiRecordCircleLine,
      iconColor: primaryColor,
      lineLeftColor: todoColor,
      lineRightColor: todoColor,
    },
    done: {
      icon: RiCheckboxCircleFill,
      iconColor: primaryColor,
      lineLeftColor: todoColor,
      lineRightColor: todoColor,
    },
    failed: {
      icon: RiCloseCircleFill,
      iconColor: failedColor,
      lineLeftColor: todoColor,
      lineRightColor: todoColor,
    },
  }[state]);
export interface StepIconProps {
  state: StepState;
  color?: string;
  iconSize?: string;
  todoColor?: string;
  failedColor?: string;
  isFirst?: boolean;
  isLast?: boolean;
}
export const StepIcon: React.FC<StepIconProps> = ({
  state,
  color: primaryColor = 'blue.500',
  iconSize = '32px',
  todoColor = 'gray.400',
  failedColor = 'red.400',
  isFirst = false,
  isLast = false,
}) => {
  const { icon, iconColor, lineLeftColor, lineRightColor } = getStepIconConfig(
    state,
    primaryColor,
    todoColor,
    failedColor
  );

  return (
    <Flex align="center">
      <Divider borderColor={isFirst ? 'transparent' : lineLeftColor} borderWidth="1px" />
      <Icon boxSize={iconSize} as={icon} color={iconColor} />
      <Divider borderColor={isLast ? 'transparent' : lineRightColor} borderWidth="1px" />
    </Flex>
  );
};

export interface HorizontalStepsProps<T extends {}> {
  steps: Array<T & { id: number; state: StepState }>;
  color?: string;
  TopElement?: (props: T) => JSX.Element;
  BottomElement?: (props: T) => JSX.Element;
}
export const HorizontalSteps = <T extends {}>({ steps, color, TopElement, BottomElement }: HorizontalStepsProps<T>) => {
  return (
    <Flex>
      {steps.map((step, index) => (
        <Box key={step.id} flex={1}>
          {TopElement ? <TopElement {...step} /> : <></>}
          <StepIcon state={step.state} color={color} isFirst={index === 0} isLast={index === steps.length - 1} />
          {BottomElement ? <BottomElement {...step} /> : <></>}
        </Box>
      ))}
    </Flex>
  );
};

export default HorizontalSteps;
