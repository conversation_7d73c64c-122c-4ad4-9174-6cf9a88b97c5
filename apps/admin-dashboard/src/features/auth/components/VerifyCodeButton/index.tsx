/* eslint-disable react-hooks/exhaustive-deps */
import { Button, Input, InputGroup, InputRightElement, useToast } from '@chakra-ui/react';
import React, { useEffect, useMemo, useState } from 'react';
import { catchError, EMPTY, interval, map, Subject, switchMap, take, tap } from 'rxjs';

import { sendVerifyCode } from '@/features/auth/api';
import { useAuth } from '@/providers/auth';

interface LoginFormProps {
  phoneNumberData: string;
  verifyCodeData?: string;
  onChange: React.ChangeEventHandler<HTMLInputElement>;
  styles?: React.CSSProperties;
}

export const VerifyCodeButton: React.FC<LoginFormProps> = ({ phoneNumberData, verifyCodeData, onChange, ...props }) => {
  const [codeSecond, setCodeSecond] = useState(0);

  const codeButtonClick$ = useMemo(() => new Subject<React.MouseEvent<HTMLButtonElement, MouseEvent>>(), []);

  const toast = useToast({ position: 'top' });
  const { user } = useAuth();

  useEffect(() => {
    const sendFlow = (phoneNumber: string) =>
      sendVerifyCode(phoneNumber.startsWith('+') ? phoneNumber : `+86${phoneNumber}`).pipe(
        tap((data) => {
          localStorage.setItem(`${user?.uid}_agreement`, JSON.stringify(data.agreement));
          toast({
            title: '验证码已发送',
            status: 'success',
            duration: 3000,
          });
        }),
        catchError(() => {
          toast({
            title: '验证码发送失败，请稍后再试',
            status: 'error',
          });
          setCodeSecond(0);
          return EMPTY;
        })
      );
    const countDown = () =>
      interval(1000).pipe(
        take(60),
        tap((num: number) => {
          setCodeSecond(59 - num);
        })
      );
    const clickSub = codeButtonClick$
      .pipe(
        map(() => {
          if (phoneNumberData.length === 0) {
            toast({
              title: '请正确填写手机号',
              status: 'error',
            });
            throw new Error('未正确填写手机号');
          } else {
            setCodeSecond(-1);
            return phoneNumberData;
          }
        }),
        switchMap(sendFlow),
        switchMap(countDown)
      )
      .subscribe({
        next: () => {},
        error: (err) => console.log(err),
      });
    return () => clickSub.unsubscribe();
  }, [phoneNumberData, verifyCodeData]);

  return (
    <InputGroup>
      <Input pr="6.4rem" type="tel" placeholder="请输入验证码" value={verifyCodeData} onChange={onChange} />
      <InputRightElement width="6.5rem">
        <Button
          colorScheme="blue"
          variant="text"
          size="sm"
          loadingText="发送中"
          isLoading={codeSecond < 0}
          isDisabled={codeSecond > 0}
          onClick={(e) => codeButtonClick$.next(e)}
        >
          {codeSecond > 0 ? `${codeSecond}秒后可重试` : '获取验证码'}
        </Button>
      </InputRightElement>
    </InputGroup>
  );
};

export default VerifyCodeButton;
