import {
  Box,
  Button,
  Checkbox,
  Container,
  Flex,
  Image,
  Link,
  Modal,
  ModalBody,
  ModalContent,
  ModalFooter,
  ModalOverlay,
  Stack,
  Text,
} from '@chakra-ui/react';
import { createForm } from '@formily/core';
import { createSchemaField, FormProvider } from '@formily/react';
import React, { useMemo, useState } from 'react';

import { agreementVersion } from '@/config';
import type { UpdateUserProps, User } from '@/features/users';
import { useAuth } from '@/providers/auth';

import type { LoginRequest } from '../api';
import { loginInfoSchema, registerInfoSchema } from './forms';

interface LoginFormProps {
  phoneNumber: string;
  verifyCode: string;
}

export const LoginForm: React.FC<{
  onSubmitLogin: (params: LoginRequest) => void;
}> = ({ onSubmitLogin }) => {
  const [isOpen, setIsOpen] = useState(false);

  // 分别表示：第一个选项值，第二个选项值，第一个是否为用户手动勾选，第二个是否为用户手动勾选
  const [agreementItems, setAgreementItems] = useState([false, false, false, false]);
  const LoginSchemaField = useMemo(() => createSchemaField(loginInfoSchema.config), []);
  const loginForm = useMemo(() => {
    const form = createForm<LoginFormProps>({
      initialValues: {
        phoneNumber: '',
        verifyCode: '',
      },
    });
    return form;
  }, []);

  const { user } = useAuth();
  const onClose = () => {
    setIsOpen(false);
  };

  const onSubmit = async () => {
    loginForm.submit((values: LoginFormProps) => {
      const userAgreement = Number(localStorage.getItem(`${user?.uid}_agreement`));
      if (agreementVersion > userAgreement && (!agreementItems[2] || !agreementItems[3])) {
        setAgreementItems([agreementItems[2], agreementItems[3], agreementItems[2], agreementItems[3]]);
        setIsOpen(true);
        return;
      }
      if (agreementItems.slice(0, 2).filter(Boolean).length < 2) {
        setIsOpen(true);
        return;
      }
      localStorage.setItem(`${user?.uid}_agreement`, JSON.stringify(agreementVersion));
      const { phoneNumber, verifyCode } = values;
      const identity = phoneNumber.startsWith('+') ? phoneNumber : `+86${phoneNumber}`;

      return onSubmitLogin({
        id_type: 'phone',
        identity,
        auth_type: 'authcode',
        credential: verifyCode,
        agreement: agreementVersion,
      });
    });
  };

  return (
    <Flex w="100%" h="100vh" direction="column" align="center" justify="center">
      <Box w="md" h="18%" textAlign="center" mt="-12">
        <Image src="/login-banner.png" alt="konvery" objectFit="contain" objectPosition="top" />
      </Box>
      <Container w="lg" bg="white" p="12">
        <FormProvider form={loginForm}>
          <LoginSchemaField schema={loginInfoSchema.schema} />
          <Button
            type="submit"
            // isLoading={isSubmitting}
            loadingText="登录中"
            colorScheme="brand"
            w="full"
            h="50px"
            mt="4"
            mb="2"
            letterSpacing="1px"
            onClick={onSubmit}
          >
            登录 / 注册
          </Button>
          {/* <Box fontSize="sm" color="gray.500" textAlign="center">
            没有账户？
            <Link color="blue.600">点击创建</Link>
          </Box> */}
        </FormProvider>
      </Container>

      <Flex marginTop={10}>
        <Stack pl={6} mt={1} spacing={1}>
          <Checkbox
            isChecked={agreementItems[0]}
            onChange={(e) => {
              setAgreementItems([e.target.checked, agreementItems[1], e.target.checked, agreementItems[3]]);
            }}
          >
            本人确认为中国国籍人士，非外籍人士
          </Checkbox>
          <Checkbox
            isChecked={agreementItems[1]}
            onChange={(e) =>
              setAgreementItems([agreementItems[0], e.target.checked, agreementItems[2], e.target.checked])
            }
          >
            阅读并接受
            <Link color="blue.500" href="/user-agreement.pdf" isExternal>
              《恺望用户协议》
            </Link>
            ，
            <Link color="blue.500" href="/data-privacy-agreement.pdf" isExternal>
              《恺望数据隐私协议》
            </Link>
          </Checkbox>
        </Stack>
      </Flex>

      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalBody>
            您好，应国家相关政策要求，本平台只提供中国国籍人士使用，如您为中国国籍人士，请在下面勾选，表示确认。
            同时，仔细阅读《恺望用户协议》和《恺望数据隐私协议》，并勾选表示接受协议内容。
          </ModalBody>

          <ModalFooter>
            <Button colorScheme="blue" mr={3} onClick={onClose} size="sm">
              知道了
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Flex>
  );
};

interface RegisterFormProps extends Record<string, string> {
  name: string;
  city: string;
  province: string;
}
interface RegisterProps {
  user?: User;
  onUpdateUser: (params: UpdateUserProps) => void;
}
export const RegisterForm: React.FC<RegisterProps> = ({ user, onUpdateUser }) => {
  const RegisterSchemaField = createSchemaField(registerInfoSchema.config);
  const registerForm = useMemo(() => {
    const form = createForm<RegisterFormProps>({
      initialValues: {
        name: '',
        province: '',
        city: '',
      },
    });
    return form;
  }, []);

  const onSubmit = async () => {
    registerForm.submit((values: RegisterFormProps) => {
      return onUpdateUser({
        origin: user!,
        current: values,
        formFields: ['name', 'city'],
      });
    });
  };

  return (
    <Flex w="100%" h="100vh" direction="column" align="center" justify="center">
      <Container w="lg" bg="white" p="12">
        <Text fontSize={32} fontWeight="bold" color="gray.800" mb="1">
          欢迎您
        </Text>
        <Text fontSize={16} color="gray.400" letterSpacing="0.5px">
          简单三步，快速完善账户信息 ↓{' '}
        </Text>
        <FormProvider form={registerForm}>
          <RegisterSchemaField schema={registerInfoSchema.schema} />
          <Button
            type="submit"
            // isLoading={isSubmitting}
            loadingText="完善中"
            colorScheme="teal"
            w="full"
            h="50px"
            mt="4"
            mb="2"
            letterSpacing="1px"
            onClick={onSubmit}
          >
            开启数据之旅
          </Button>
          {/* <Box fontSize="sm" color="gray.500" textAlign="center">
            没有账户？
            <Link color="blue.600">点击创建</Link>
          </Box> */}
        </FormProvider>
      </Container>
    </Flex>
  );
};
