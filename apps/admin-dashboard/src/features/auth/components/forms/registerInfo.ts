import { FormItem, FormLayout, Input, Select } from '@forest/formily';
import type { ISchema } from '@formily/react';

import { locations, provinces } from '@/config/locations';

const provinceCities = (function (loc) {
  const data: Record<
    string,
    {
      label: string;
      value: string;
    }[]
  > = {};
  const entries = Object.entries(loc);
  for (const [prov, cities] of entries) {
    data[prov] = cities.map((city) => ({
      label: city,
      value: city,
    }));
  }
  return data;
})(locations);

const config = {
  components: {
    FormLayout,
    FormItem,
    Input,
    Select,
  },
  scope: {
    locations,
    provinceCities,
  },
};

const schema: ISchema = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        size: 'sm',
        layout: 'vertical',
      },
      properties: {
        name: {
          type: 'string',
          title: '用户名',
          required: true,
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请填写您的姓名',
            type: 'text',
            color: 'gray.600',
          },
          pattern: '^[\u4E00-\u9FA5A-Za-z0-9]+$',
        },
        province: {
          type: 'string',
          title: '省份',
          required: true,
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            labelWidth: '80px',
            labelStyle: {
              size: 'sm',
            },
          },
          'x-component': 'Select',
          'x-component-props': {
            size: 'sm',
            width: 280,
          },
          enum: provinces,
          'x-reactions': {
            target: 'city',
            effects: ['onFieldMount', 'onFieldValueChange'],
            when: '{{$self.value !== undefined}}',
            fulfill: {
              state: {
                dataSource: '{{provinceCities[$self.value]}}',
                value:
                  '{{Array.isArray(locations) ? (locations[$self.value].includes($target.value) ? $target.value : locations[$self.value][0]}) : undefined}',
              },
            },
          },
        },
        city: {
          type: 'string',
          title: '城市',
          required: true,
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            labelWidth: '80px',
            labelStyle: {
              size: 'sm',
            },
          },
          enum: ['请先选择省份'],
          'x-component': 'Select',
          'x-component-props': {
            size: 'sm',
            width: 280,
          },
        },
      },
    },
  },
};

export const registerInfoSchema = { config, schema };
