import { FormItem, FormLayout, Input } from '@forest/formily';
import type { ISchema } from '@formily/react';

import { VerifyCodeButton } from '../VerifyCodeButton';

const config = {
  components: {
    FormLayout,
    FormItem,
    Input,
    VerifyCodeButton,
  },
};

const schema: ISchema = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        size: 'sm',
        layout: 'vertical',
      },
      properties: {
        phoneNumber: {
          type: 'string',
          title: '手机号',
          required: true,
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入您的手机号',
            color: 'gray.600',
          },
          pattern: /^(?:\+?\d{2})?1\d{10}$|^\+?\d{0,11}$/,
        },
        verifyCode: {
          type: 'string',
          title: '验证码',
          required: true,
          'x-decorator': 'FormItem',
          'x-component': 'VerifyCodeButton',
          'x-reactions': [
            {
              dependencies: ['.phoneNumber#value', '.verifyCode#value'],
              fulfill: {
                state: {
                  'component[1].phoneNumberData': '{{$deps[0]}}',
                  'component[1].verifyCodeData': '{{$deps[1]}}',
                },
              },
            },
          ],
        },
      },
    },
  },
};

export const loginInfoSchema = { config, schema };
