import { forkJoin, from, map, mergeMap, of } from 'rxjs';

import { getTeam } from '@/features/teams';
import type {
  iam_v1_Feperm as FePermission,
  iam_v1_LoginRequest as LoginRequest,
  iam_v1_SendAuthCodeReply as <PERSON><PERSON>uthCodeReply,
  iam_v1_SendAuthCodeRequest as SendAuthCodeRequest,
} from '@/service/iam/v1';
import { Configs as ConfigService, Users as UserService } from '@/service/iam/v1';

export type { FePermission, LoginRequest, SendAuthCodeReply };

export const getErrors = () => from(ConfigService.configsListErrors());

export const getMe = () =>
  forkJoin([
    from(UserService.usersGetMe()).pipe(
      mergeMap((user) => (user.org_uid ? getTeam(user.org_uid).pipe(map((team) => ({ ...user, team }))) : of(user)))
    ),
    from(UserService.usersGetMyFeperm()),
  ]);
export const login = ({ params }: { params: LoginRequest }) => {
  return from(UserService.usersLogin({ requestBody: params })).pipe(
    mergeMap(({ user, feperm }) =>
      user.org_uid
        ? getTeam(user.org_uid).pipe(map((team) => ({ user: { ...user, team }, permission: feperm })))
        : of({ user, permission: feperm })
    )
  );
};
export const logout = () => {
  return from(UserService.usersLogout({ requestBody: {} }));
};

export const sendVerifyCode = (phoneNumber: string) => {
  const requestBody: SendAuthCodeRequest = {
    purpose: 'login',
    channel: 'sms',
    locale: 'zh-Hans',
    receiver: phoneNumber,
  };
  return from(UserService.usersSendAuthCode({ requestBody }));
};
