import { Box, Collapse } from '@chakra-ui/react';

import { updateUserInfo, UpdateUserProps } from '@/features/users';
import { useAuth } from '@/providers/auth';

import { login, LoginRequest } from '../api';
import { LoginForm, RegisterForm } from '../components';

export const Login: React.FC = () => {
  const { user, setUser, setPermission } = useAuth();

  const onSubmitLogin = (params: LoginRequest) => {
    login({ params }).subscribe({
      next: ({ user, permission }) => {
        setUser(user);
        setPermission(permission);
      },
    });
  };
  const onUpdateUser = (params: UpdateUserProps) => {
    updateUserInfo(params).subscribe({
      next: ({ user }) => {
        setUser(user);
      },
    });
  };

  return (
    <Box w="100%" h="100vh" bgSize="100% 50%" bgRepeat="no-repeat" bgGradient="linear(to-r, #80d0c7, #003A70)">
      <Collapse in={user === undefined} animateOpacity>
        <LoginForm onSubmitLogin={onSubmitLogin}></LoginForm>
      </Collapse>
      <Collapse in={user?.imperfect} animateOpacity>
        <RegisterForm user={user} onUpdateUser={onUpdateUser}></RegisterForm>
      </Collapse>
    </Box>
  );
};
