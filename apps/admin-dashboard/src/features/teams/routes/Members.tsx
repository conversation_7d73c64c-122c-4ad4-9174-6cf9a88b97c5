import { Box, Container, Flex, Input, InputGroup, InputLeftElement, useToast } from '@chakra-ui/react';
import { SimpleDialog } from '@forest/simple-ui';
import { createForm } from '@formily/core';
import { createSchemaField, FormProvider } from '@formily/react';
import { debounce } from 'lodash';
import { FC, useEffect, useMemo, useRef, useState } from 'react';
import { RiSearchLine, RiUserAddLine } from 'react-icons/ri';
import { useParams } from 'react-router-dom';

import { Header } from '@/components/layout';
import { UserCard } from '@/features/users';
import { useRequest } from '@/hooks/use-request';
import type { IServicePagination } from '@/types';

import { delTeamMember, getTeamMembers, setMembersRole } from '../api';
import { MemberModal, MembersTable } from '../components';
import { memberEditSchema } from '../components/forms';

const MemberEditSchemaField = createSchemaField(memberEditSchema.config);
const memberEditForm = createForm({
  readPretty: true,
});
export interface MembersProps {
  teamUid?: string;
}
export const Members: FC<MembersProps> = ({ teamUid = '' }) => {
  const [currentMemberId, setCurrentMemberId] = useState(0);
  const [openAddModal, setOpenAddModal] = useState(false);
  const [openDelDialog, setOpenDelDialog] = useState(false);
  const [pagination, setPagination] = useState<IServicePagination>({ page: 0, pagesz: 8, total: 0 });
  const namePattern = useRef<string>('');

  const urlParams = useParams();

  const {
    data,
    error,
    isLoading,
    refetch: updateMembers,
  } = useRequest(getTeamMembers, {
    params: { uid: urlParams.teamUid ?? teamUid, ...pagination },
    enabled: true,
    onSuccess: ({ total }) => {
      if (total !== undefined && total !== 0) {
        setPagination((prev) => ({ ...prev, total }));
      }
    },
  });

  const debounceUpdateList = debounce((e) => {
    namePattern.current = e.target.value;

    updateMembers({
      params: {
        uid: urlParams.teamUid ?? teamUid,
        ...pagination,
        namePattern: namePattern.current,
        page: 0,
        pagesz: pagination.pagesz,
      },
    });

    const newParams = { ...pagination, page: 0 };
    setPagination(newParams);
  }, 500);

  const { refetch: setRole } = useRequest(setMembersRole, {
    onSuccess: (data, _, showToast) => {
      showToast({ title: '已成功修改用户角色' });
      updateMembers({
        params: {
          uid: urlParams.teamUid ?? teamUid,
          ...pagination,
          namePattern: namePattern.current,
        },
      });
    },
  });

  const pathList = useMemo(
    () =>
      urlParams.teamUid
        ? [
            { name: '组织管理', path: '/admin/teams/manage' },
            { name: '成员管理', path: '#' },
          ]
        : [{ name: '成员管理', path: '#' }],
    [urlParams]
  );

  const toast = useToast({ position: 'top' });

  useEffect(() => {
    if (data?.members?.[currentMemberId]) {
      memberEditForm.setValues({ role: data?.members?.[currentMemberId].role });
      memberEditForm.readPretty = true;
    }
  }, [data?.members, currentMemberId]);

  const onClickAdd = () => {
    if (error || isLoading) {
      toast({
        title: '未获取到组织信息',
        status: 'warning',
      });
      return;
    }
    setOpenAddModal(true);
  };
  const onChangeRole = () => {
    if (data?.members?.[currentMemberId]) {
      memberEditForm.submit(({ role }) => {
        const { role: oldRole } = data?.members?.[currentMemberId];
        if (role !== oldRole) {
          setRole({
            params: {
              uid: urlParams.teamUid ?? teamUid,
              user_uids: [data?.members?.[currentMemberId].uid],
              role,
            },
          });
        }
      });
    }
    memberEditForm.readPretty = true;
  };

  const onCloseAddMember = (signal?: string) => {
    if (signal === 'UPDATE') {
      updateMembers({
        params: {
          uid: urlParams.teamUid ?? teamUid,
          ...pagination,
          namePattern: namePattern.current,
        },
      });
    }
    setOpenAddModal(false);
  };
  const onDelMember = () => {
    delTeamMember({
      uid: urlParams.teamUid ?? teamUid,
      userUids: [data?.members?.[currentMemberId].uid!],
    }).subscribe({
      next: () => {
        setOpenDelDialog(false);
        setCurrentMemberId(currentMemberId + 1 === data?.members?.length ? 0 : currentMemberId);
        toast({
          title: '已移除该成员',
          status: 'success',
        });
        updateMembers({
          params: {
            uid: urlParams.teamUid ?? teamUid,
            ...pagination,
            namePattern: namePattern.current,
          },
        });
      },
    });
  };

  const onChangePage = (currentPage: number) => {
    const newParams = { ...pagination, page: currentPage - 1 };
    updateMembers({
      params: {
        ...newParams,
        uid: urlParams.teamUid ?? teamUid,
        namePattern: namePattern.current,
      },
    });
    setPagination(newParams);
  };

  return (
    <Flex direction="column" minH="100%">
      <Header
        title={data?.team?.name ?? ''}
        pathList={pathList}
        rightButton={{
          tip: '添加组织成员',
          icon: <RiUserAddLine />,
          onClick: onClickAdd,
        }}
      >
        <InputGroup h="2.5rem" size="sm">
          <InputLeftElement pointerEvents="none" children={<RiSearchLine />} />
          <Input variant="filled" borderRadius="lg" placeholder="请输入成员名称" onChange={debounceUpdateList} />
        </InputGroup>
      </Header>
      <Flex align="center" justify="center" flexGrow={1}>
        <Flex width="100%" alignSelf="stretch">
          <Container px="6" py="5">
            <MembersTable
              members={data?.members}
              pagination={{
                total: pagination.total,
                defaultPageSize: pagination.pagesz,
                onChange: onChangePage,
              }}
              selectMember={(id) => setCurrentMemberId(id)}
            />
          </Container>
          <Container width="30rem" ml="4">
            <UserCard
              user={data?.members?.[currentMemberId]}
              onDeleteUser={() => setOpenDelDialog(true)}
              onEditUser={() => (memberEditForm.readPretty = false)}
              onSaveUser={onChangeRole}
              onUpdateUser={updateMembers}
            >
              <Box textAlign="left" px="0.5" pt="4">
                <FormProvider form={memberEditForm}>
                  <MemberEditSchemaField schema={memberEditSchema.schema}></MemberEditSchemaField>
                </FormProvider>
              </Box>
            </UserCard>
          </Container>
        </Flex>
      </Flex>
      <MemberModal teamUid={urlParams.teamUid ?? teamUid} isOpen={openAddModal} onClose={onCloseAddMember} />
      <SimpleDialog
        title="移除成员"
        content={`您希望从当前组织移除成员【${data?.members?.[currentMemberId]?.name}】吗？`}
        confirmText="移除"
        isOpen={openDelDialog}
        onCancel={() => setOpenDelDialog(false)}
        onConfirm={onDelMember}
      ></SimpleDialog>
    </Flex>
  );
};

export default Members;
