import { Navigate, Route, Routes } from 'react-router-dom';

import { useAuth } from '@/providers/auth';

import { Members } from './Members';
import { TeamManage } from './TeamManage';

export const TeamRoutes = () => {
  const { user } = useAuth();

  return (
    <Routes>
      <Route path="manage" element={<TeamManage />}></Route>
      <Route path="members" element={<Members teamUid={user?.team?.uid} />}>
        <Route path=":teamUid" element={<Members />}></Route>
      </Route>

      <Route path="*" element={<Navigate to="/admin/home" />}></Route>
    </Routes>
  );
};
