import { Box, Input, InputGroup, InputLeftElement } from '@chakra-ui/react';
import { CollapseContainer, SimpleDialog } from '@forest/simple-ui';
import { useState } from 'react';
import { RiGroupLine, RiSearchLine } from 'react-icons/ri';
import { useNavigate } from 'react-router-dom';

import { Header } from '@/components/layout/Header';
import { useRequest } from '@/hooks/use-request';

import { deleteTeam, getTeamList, TeamListProps } from '../api';
import { TeamModal, TeamsTable, TeamsTableProps } from '../components';
import type { Team } from '../types';

type ExcludeTeamsTableProps = Omit<TeamsTableProps, 'pagination'>;

const pathList = [{ name: '组织管理', path: '#' }];

export const TeamManage: React.FC = () => {
  const [editTeam, setEditTeam] = useState<Team | undefined>(undefined);
  const [delTeamInfo, setDelTeamInfo] = useState<Team | undefined>(undefined);
  const [demandParams, setDemandParams] = useState<TeamListProps & { total: number }>({
    page: 0,
    teamType: 'demander',
    pagesz: 5,
    total: 0,
  });
  const [supplyParams, setSupplyParams] = useState<TeamListProps & { total: number }>({
    page: 0,
    teamType: 'supplier',
    pagesz: 5,
    total: 0,
  });

  const [openModal, setOpenModal] = useState(false);

  const { data: demandTeamList, refetch: updateDemands } = useRequest(getTeamList, {
    params: demandParams,
    enabled: true,
    onSuccess: ({ total }) => {
      if (total) {
        setDemandParams((prev) => ({ ...prev, total }));
      }
    },
  });
  const { data: supplyTeamList, refetch: updateSupplies } = useRequest(getTeamList, {
    params: supplyParams,
    enabled: true,
    onSuccess: ({ total }) => {
      if (total) {
        setSupplyParams((prev) => ({ ...prev, total }));
      }
    },
  });

  const { refetch: delTeam, isLoading: deleting } = useRequest(deleteTeam, {
    onSuccess: (_, { meta }, showToast) => {
      showToast({ title: '删除成功' });
      setDelTeamInfo(undefined);

      const { teamType } = meta || {};
      if (teamType === 'demander') {
        updateTeams(teamType);
        setDemandParams((prev) => ({ ...prev, total: prev.total - 1 }));
      } else if (teamType === 'supplier') {
        updateTeams(teamType);
        setSupplyParams((prev) => ({ ...prev, total: prev.total - 1 }));
      }
    },
  });

  const navigate = useNavigate();

  const updateTeams = (type?: string) => {
    if (type === 'demander') {
      updateDemands({ params: demandParams });
    } else if (type === 'supplier') {
      updateSupplies({ params: supplyParams });
    }
  };

  const onCloseModal = (signal?: string) => {
    if (typeof signal === 'string') {
      const type = signal.split('::').pop();
      updateTeams(type);
    }
    setOpenModal(false);
  };

  const onDelTeam = () => {
    const { uid, type: teamType } = delTeamInfo!;
    delTeam({
      params: {
        uid,
      },
      meta: { teamType },
    });
  };

  const onClickAdd = () => {
    setEditTeam(undefined);
    setOpenModal(true);
  };

  const teamsTableEvent: ExcludeTeamsTableProps = {
    onListMembers: (uid) => {
      navigate(`/admin/teams/members/${uid}`);
    },
    onEditTeam: (team) => {
      setEditTeam(team);
      setOpenModal(true);
    },
    onDeleteTeam: (team) => {
      setDelTeamInfo(team);
    },
  };

  const onPageChange = (teamType: Team['type']) => (currentPage: number) => {
    const newParams = {
      ...(teamType === 'demander' ? demandParams : supplyParams),
      page: currentPage - 1,
    };
    if (teamType === 'demander') {
      updateDemands({ params: newParams });
      setDemandParams(newParams);
    } else {
      updateSupplies({ params: newParams });
      setSupplyParams(newParams);
    }
  };

  return (
    <Box minH="100%">
      <Header
        title="组织管理"
        pathList={pathList}
        rightButton={{
          tip: '新建组织',
          icon: <RiGroupLine />,
          onClick: onClickAdd,
        }}
      >
        <InputGroup h="2.5rem" size="sm">
          <InputLeftElement pointerEvents="none" children={<RiSearchLine />} />
          <Input variant="filled" borderRadius="lg" placeholder="请输入组织名称" />
        </InputGroup>
      </Header>
      <CollapseContainer title="需求方" startingHeight="16rem" mb="4">
        <TeamsTable
          teams={demandTeamList?.teams}
          pagination={{
            total: demandParams.total,
            defaultPageSize: demandParams.pagesz,
            onChange: onPageChange('demander'),
          }}
          {...teamsTableEvent}
        />
      </CollapseContainer>
      <CollapseContainer title="供应方" startingHeight="16rem">
        <TeamsTable
          teams={supplyTeamList?.teams}
          pagination={{
            total: supplyParams.total,
            defaultPageSize: supplyParams.pagesz,
            onChange: onPageChange('supplier'),
          }}
          {...teamsTableEvent}
        />
      </CollapseContainer>
      <TeamModal teamData={editTeam} isOpen={openModal} onClose={onCloseModal} />
      <SimpleDialog
        title="删除组织"
        content={`您希望删除组织【${delTeamInfo?.name}】吗？删除后无法恢复`}
        confirmText="删除"
        confirmButtonProps={{
          isLoading: deleting,
        }}
        isOpen={delTeamInfo !== undefined}
        onCancel={() => setDelTeamInfo(undefined)}
        onConfirm={onDelTeam}
      ></SimpleDialog>
    </Box>
  );
};

export default TeamManage;
