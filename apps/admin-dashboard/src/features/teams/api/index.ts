import { from } from 'rxjs';

import {
  iam_v1_CreateTeamRequest as CreateTeamProps,
  iam_v1_ListTeamReply as ListTeamResult,
  iam_v1_SetMembersRoleRequest,
  Teams as TeamService,
} from '@/service/iam/v1';

export type { CreateTeamProps, ListTeamResult };

/** Team */
export type TeamListProps = Parameters<typeof TeamService.teamsListTeam>[0];
export const getTeamList = (params: TeamListProps) =>
  from(
    TeamService.teamsListTeam({
      ...params,
    })
  );
export const getTeam = (uid: string) => from(TeamService.teamsGetTeam({ uid }));

export const createTeam = (params: CreateTeamProps) =>
  TeamService.teamsCreateTeam({
    requestBody: params,
  });
export const updateTeam = (params: { teamUid: string; requestBody: CreateTeamProps; fields?: string[] | undefined }) =>
  TeamService.teamsUpdateTeam(params);
export const deleteTeam = ({ uid }: { uid: string }) => from(TeamService.teamsDeleteTeam({ uid }));

/** Team Members */
export type TeamMembersProps = Parameters<typeof TeamService.teamsListMembers>[0];
export const getTeamMembers = (params: TeamMembersProps) =>
  from(
    TeamService.teamsListMembers({
      pagesz: 50,
      ...params,
    })
  );

export const delTeamMember = (params: { uid: string; userUids?: string[] | undefined }) =>
  from(TeamService.teamsDeleteMembers(params));

export const addTeamMember = TeamService.teamsAddMembers;

export const setMembersRole = (requestBody: iam_v1_SetMembersRoleRequest) =>
  from(
    TeamService.teamsSetMembersRole({
      uid: requestBody.uid,
      requestBody,
    })
  );
