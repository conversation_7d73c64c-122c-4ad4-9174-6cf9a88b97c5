import { Box, HStack, Text } from '@chakra-ui/react';
import { Column, IPagination, Table, TipButton } from '@forest/simple-ui';
import { useMemo } from 'react';
import { RiDeleteBinLine, RiEditLine, RiUserLine } from 'react-icons/ri';

import type { Team } from '../types';

export interface TeamsTableProps {
  teams?: Team[];
  pagination: IPagination;
  onListMembers: (uid: string) => void;
  onEditTeam: (team: Team) => void;
  onDeleteTeam: (team: Team) => void;
}
const iconButtonStyles = {
  variant: 'outline',
  isRound: true,
  fontSize: '16px',
  size: 'sm',
};
export const TeamsTable = ({ teams, pagination, onListMembers, onEditTeam, onDeleteTeam }: TeamsTableProps) => {
  const columns: Column<Team>[] = useMemo(
    () => [
      {
        Header: '组织名称',
        accessor: 'name',
        Cell: ({ row }) => {
          const { name, uid } = row.original;
          return (
            <Box width="12rem">
              <Text color="brand.500" fontWeight="bold">
                {name}
              </Text>
              <Text color="gray.500" fontSize="xs">
                ID: {uid}
              </Text>
            </Box>
          );
        },
      },
      {
        Header: '创建时间',
        id: 'created_at',
        accessor: (originalRow) => originalRow.created_at?.split('T')[0],
      },
      {
        Header: '地区',
        accessor: 'province',
        Cell: ({ row }) => {
          const { province, city } = row.original;
          return <Box width="20rem">{`${province}${city ? '-' : ''}${city}`}</Box>;
        },
      },
      {
        Header: '',
        accessor: 'uid',
        Cell: ({ row }) => {
          const { original: originalRow } = row;
          return (
            <HStack justify="right" spacing="2.5">
              <TipButton
                aria-label="查看成员"
                colorScheme="twitter"
                icon={<RiUserLine />}
                {...iconButtonStyles}
                onClick={() => onListMembers(originalRow.uid)}
              ></TipButton>
              <TipButton
                aria-label="编辑"
                colorScheme="purple"
                icon={<RiEditLine />}
                {...iconButtonStyles}
                onClick={() => onEditTeam(originalRow)}
              ></TipButton>
              <TipButton
                aria-label="删除"
                colorScheme="red"
                icon={<RiDeleteBinLine />}
                {...iconButtonStyles}
                onClick={() => onDeleteTeam(originalRow)}
              ></TipButton>
            </HStack>
          );
        },
      },
    ],
    [onDeleteTeam, onEditTeam, onListMembers]
  );
  const data = useMemo(() => (Array.isArray(teams) ? teams : []), [teams]);

  return <Table data={data} columns={columns} isLoading={!Array.isArray(teams)} pagination={pagination} />;
};
