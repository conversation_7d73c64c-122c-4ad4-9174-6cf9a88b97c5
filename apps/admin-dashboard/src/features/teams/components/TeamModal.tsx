/* eslint-disable react-hooks/exhaustive-deps */
import {
  <PERSON>ton,
  Modal,
  ModalBody,
  ModalCloseButton,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalOverlay,
  useToast,
} from '@chakra-ui/react';
import { createForm } from '@formily/core';
import { createSchemaField, FormProvider } from '@formily/react';
import { useEffect } from 'react';

import { createTeam, CreateTeamProps, updateTeam } from '../api';
import type { Team } from '../types';
import { teamAddSchema } from './forms';

interface TeamModalProps {
  isOpen: boolean;
  teamData?: Team;
  onClose: (signal?: string) => void;
}

const TeamAddSchemaField = createSchemaField(teamAddSchema.config);
const TeamNewFormFields = Object.keys(teamAddSchema.schema.properties as object) as Array<keyof CreateTeamProps>;
const teamNewForm = createForm<CreateTeamProps>();

export const TeamModal = ({ teamData, isOpen, onClose }: TeamModalProps) => {
  const toast = useToast({
    position: 'top',
  });

  useEffect(() => {
    if (isOpen) {
      teamNewForm.setValues({ ...teamData }, 'overwrite');
    } else {
      teamNewForm.setValues({}, 'overwrite');
    }
  }, [isOpen]);

  const onSubmit = () => {
    teamNewForm.submit((values: CreateTeamProps) => {
      if (teamData) {
        const requestBody: CreateTeamProps = {};
        const fields = TeamNewFormFields.filter((field) => {
          // 判断原有数据中是否有该字段
          const oldFieldExist = typeof (teamData as any)[field] !== 'undefined';
          // WARN: 仅适用所有表单字段值为string类型
          if ((!oldFieldExist && values[field]) || (oldFieldExist && (teamData as any)[field] !== values[field])) {
            requestBody[field] = values[field] as any;
            return true;
          }
          return false;
        });
        if (fields.length === 0) {
          onClose();
          return;
        } else {
          requestBody.type = teamData.type;
          return updateTeam({
            teamUid: teamData.uid,
            requestBody,
            fields,
          }).then(() => {
            toast({
              status: 'success',
              title: '已更新组织数据',
            });
            onClose(`UPDATE::${values.type}`);
          });
        }
      } else {
        const { owner = '' } = values;
        values.owner = owner.startsWith('+') ? `phone:${owner}` : `phone:+86${values.owner}`;

        return createTeam(values).then(() => {
          toast({
            status: 'success',
            title: '已成功新建组织',
          });
          onClose(`UPDATE::${values.type}`);
        });
      }
    });
  };

  return (
    <Modal closeOnOverlayClick={false} isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>{teamData ? '编辑' : '新建'}组织</ModalHeader>
        <ModalCloseButton />
        <ModalBody mt="2">
          <FormProvider form={teamNewForm}>
            <TeamAddSchemaField schema={teamAddSchema.schema}></TeamAddSchemaField>
          </FormProvider>
        </ModalBody>
        <ModalFooter mt="-2">
          <Button variant="ghost" mr={3} onClick={() => onClose()}>
            取消
          </Button>
          <Button colorScheme="logo" isLoading={teamNewForm.submitting} onClick={onSubmit}>
            提交
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
