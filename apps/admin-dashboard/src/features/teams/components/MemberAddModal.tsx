import {
  <PERSON>ton,
  Modal,
  Modal<PERSON>ody,
  <PERSON>dal<PERSON><PERSON>utton,
  <PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>eader,
  ModalOverlay,
  useToast,
} from '@chakra-ui/react';
import { createForm } from '@formily/core';
import { createSchemaField, FormProvider } from '@formily/react';
import { useEffect, useMemo } from 'react';

import { addTeamMember } from '../api';
import { memberAddSchema } from './forms';

interface MemberModalProps {
  isOpen: boolean;
  teamUid: string;
  onClose: (signal?: string) => void;
}
interface MemberAddFormProps {
  idList: string;
}
const MemberNewSchemaField = createSchemaField(memberAddSchema.config);

export const MemberModal = ({ teamUid, isOpen, onClose }: MemberModalProps) => {
  const memberAddForm = useMemo(() => {
    const form = createForm<MemberAddFormProps>();
    return form;
  }, []);

  const toast = useToast({
    position: 'top',
  });

  useEffect(() => {
    if (!isOpen) {
      memberAddForm.setValues({ idList: '' });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  const onSubmit = () => {
    memberAddForm.submit((values: MemberAddFormProps) => {
      return addTeamMember({
        uid: teamUid,
        requestBody: {
          uid: teamUid,
          id_type: 'phone',
          identities: values.idList.trim().split('/'),
          role: 'member',
        },
      }).then(() => {
        toast({
          status: 'success',
          title: '已成功添加该成员',
        });
        onClose('UPDATE');
      });
    });
  };

  return (
    <Modal closeOnOverlayClick={false} isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>添加成员</ModalHeader>
        <ModalCloseButton />
        <ModalBody mt="-3" pb="6">
          <FormProvider form={memberAddForm}>
            <MemberNewSchemaField schema={memberAddSchema.schema}></MemberNewSchemaField>
          </FormProvider>
        </ModalBody>
        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={() => onClose()}>
            取消
          </Button>
          <Button colorScheme="logo" isLoading={memberAddForm.submitting} onClick={onSubmit}>
            提交
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
