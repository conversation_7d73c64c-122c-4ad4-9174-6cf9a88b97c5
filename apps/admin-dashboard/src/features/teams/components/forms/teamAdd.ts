import { FormItem, Input, Radio, Select } from '@forest/formily';
import type { ISchema } from '@formily/react';

import { locations, provinces } from '@/config/locations';

const provinceCities = (function (loc) {
  const data: Record<
    string,
    {
      label: string;
      value: string;
    }[]
  > = {};
  const entries = Object.entries(loc);
  for (const [prov, cities] of entries) {
    data[prov] = cities.map((city) => ({
      label: city,
      value: city,
    }));
  }
  return data;
})(locations);

/** 新建 / 编辑组织信息表 */
const config = {
  components: {
    FormItem,
    Input,
    Radio,
    Select,
  },
  scope: {
    locations,
    provinceCities,
  },
};
const schema: ISchema = {
  type: 'object',
  properties: {
    name: {
      type: 'string',
      title: '名称',
      required: true,
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        labelWidth: '80px',
        labelStyle: {
          size: 'sm',
        },
      },
      'x-component': 'Input',
      'x-component-props': {
        size: 'sm',
        width: 280,
        placeholder: '请输入中英文或数字，不超过10个字符',
      },
      pattern: '^[\u4E00-\u9FA5A-Za-z0-9]+$',
      maxLength: 10,
      'x-reactions': [
        {
          target: 'type',
          effects: ['onFieldInit'],
          fulfill: {
            schema: {
              'x-disabled': '{{Boolean($self.value)}}',
            },
          },
        },
        {
          target: 'owner',
          effects: ['onFieldInit'],
          fulfill: {
            schema: {
              'x-visible': '{{!Boolean($self.value)}}',
            },
          },
        },
      ],
    },
    province: {
      type: 'string',
      title: '省份',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        labelWidth: '80px',
        labelStyle: {
          size: 'sm',
        },
      },
      'x-component': 'Select',
      'x-component-props': {
        size: 'sm',
        width: 280,
      },
      enum: provinces,
      'x-reactions': {
        target: 'city',
        effects: ['onFieldMount', 'onFieldValueChange'],
        when: '{{$self.value}}',
        fulfill: {
          state: {
            dataSource: '{{provinceCities[$self.value]}}',
            value: '{{locations[$self.value].includes($target.value) ? $target.value : locations[$self.value][0]}}',
          },
        },
      },
    },
    city: {
      type: 'string',
      title: '城市',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        labelWidth: '80px',
        labelStyle: {
          size: 'sm',
        },
      },
      enum: ['请先选择省份'],
      'x-component': 'Select',
      'x-component-props': {
        size: 'sm',
        width: 280,
      },
    },
    owner: {
      type: 'string',
      title: '负责人',
      required: true,
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        labelWidth: '80px',
        labelStyle: {
          size: 'sm',
        },
      },
      'x-component': 'Input',
      'x-component-props': {
        size: 'sm',
        width: 280,
        placeholder: '请输入组织负责人的手机号',
      },
    },
    type: {
      type: 'string',
      title: '类型',
      required: true,
      enum: [
        {
          label: '需求方',
          value: 'demander',
        },
        {
          label: '供应方',
          value: 'supplier',
        },
      ],
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        labelWidth: '80px',
        labelStyle: {
          size: 'sm',
        },
      },
      'x-component': 'Radio.Group',
      'x-component-props': {
        layout: 'horizontal',
        lineHeight: '40px',
      },
    },
  },
};

export const teamAddSchema = { config, schema };
