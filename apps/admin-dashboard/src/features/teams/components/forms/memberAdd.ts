import { FormItem, Input } from '@forest/formily';
import type { ISchema } from '@formily/react';

/** 添加成员表 */
const config = {
  components: {
    FormItem,
    Input,
  },
};
const schema: ISchema = {
  type: 'object',
  properties: {
    idList: {
      type: 'string',
      title: '',
      description: '请确认输入【已注册】的成员手机号，否则会导致添加失败',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        layout: 'vertical',
        labelStyle: {
          height: 0,
        },
        colon: false,
      },
      'x-component': 'Input.TextArea',
      'x-component-props': {
        size: 'sm',
        style: {
          width: '100%',
          height: 260,
        },
        resize: 'none',
        placeholder: '示例输入：\n单个号码\n+8613412341234\n\n多个号码以斜杠【/】分割\n+8613412341234/+8613412341235',
      },
      // TODO: 添加正则校验
      // pattern: '^[+]861[0-9]{10}(/[+]861[0-9]{10})*$',
    },
  },
};

export const memberAddSchema = { config, schema };
