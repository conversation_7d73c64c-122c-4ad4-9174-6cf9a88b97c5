import { FormItem, Select } from '@forest/formily';
import { RiAwardLine } from 'react-icons/ri';

const config = {
  components: {
    FormItem,
    Select,
  },
  scope: {
    RiAwardLine,
  },
};

const schema = {
  type: 'object',
  properties: {
    role: {
      type: 'string',
      required: true,
      'x-decorator': 'FormItem',
      'x-component': 'Select',
      enum: ['member', 'manager'],
      'x-component-props': {
        width: '200px',
        size: 'sm',
        prefix: '{{RiAwardLine}}',
      },
    },
  },
};

export const memberEditSchema = { schema, config };
