import { Avatar, Box, Flex, Tag, Text } from '@chakra-ui/react';
import { Column, IPagination, Row, Table } from '@forest/simple-ui';
import { useMemo } from 'react';

import { GENDER_ZH } from '@/features/users';

import type { Member } from '../types';

export interface MembersTableProps {
  members?: Member[];
  pagination: IPagination;
  selectMember: (id: number) => void;
}

export const MembersTable = ({ members, pagination, selectMember }: MembersTableProps) => {
  const columns: Column<Member>[] = useMemo(
    () => [
      {
        Header: '姓名',
        id: 'name',
        accessor: ({ name, avatar, uid }) => {
          return (
            <Flex align="center">
              <Avatar name={name} src={avatar} size="sm"></Avatar>
              <Box ml="2.5" width="8rem">
                <Text width="100%" size="sm" fontWeight="bold" color="brand.500" noOfLines={1}>
                  {name}
                </Text>
                <Box color="gray.600" fontSize="0.8rem" mt="0.5">
                  ID: {uid}
                </Box>
              </Box>
            </Flex>
          );
        },
      },
      {
        Header: '角色',
        accessor: 'role',
      },
      {
        Header: '手机号',
        accessor: 'phone',
      },
      {
        Header: '性别',
        accessor: 'gender',
        Cell: ({ value }) => {
          return <Tag variant="subtle">{GENDER_ZH[value]}</Tag>;
        },
      },
    ],
    []
  );
  const data = useMemo(() => (Array.isArray(members) ? members : []), [members]);

  const onClickRow = (row: Row<Member>) => selectMember(row?.index);

  return (
    <Table
      data={data}
      columns={columns}
      pagination={pagination}
      isLoading={!Array.isArray(members)}
      onRow={(row) => ({ onClick: () => onClickRow(row) })}
    />
  );
};
