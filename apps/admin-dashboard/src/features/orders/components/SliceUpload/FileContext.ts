import { createContext } from 'react';

import type { UploadFile } from '../../types';

export const FileContext = createContext<{
  fileList: Array<UploadFile>;
  removeUpload: (fileIndex: number) => void;
  retryUpload: (fileIndex: number, uid: string) => void;
  cancelUpload: (fileIndex: number) => void;
}>({
  fileList: [],
  removeUpload: () => {},
  retryUpload: () => {},
  cancelUpload: () => {},
});
