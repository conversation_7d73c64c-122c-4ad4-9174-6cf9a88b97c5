/**
 * @description: MD5加密，支持全量哈希/抽样哈希
 * 单位换算采用二进制值
 * 说明：https://konverydata.feishu.cn/wiki/wikcnmBixgQHpQmexc6p7w1eCsp
 * @param {*
 * file:文件对象
 * }
 * @return {string}
 */
import SparkMD5 from 'spark-md5';

import { Queue } from '@/utils';

export const hash = {
  md5(
    file: File | Blob,

    // 抽样间隔
    interval = 100 * 1024 * 1024,

    //抽样大小
    size = 1024
  ) {
    // 追加数组缓冲区。
    const spark = new SparkMD5.ArrayBuffer();
    const fileReader = new FileReader();
    const catchQueue = new Queue<Blob>();

    return new Promise<string>((resolve, rejects) => {
      fileReader.onerror = (e) => {
        rejects(e);
      };
      fileReader.onload = (e) => {
        spark.append(e?.target?.result as ArrayBuffer);
        catchQueue.dequeue();
        if (catchQueue.length > 0) {
          fileReader.readAsArrayBuffer(catchQueue.peek);
        } else if (catchQueue.length === 0) {
          // 完成md5的计算，返回十六进制结果。
          // 注：d41d8cd98f00b204e9800998ecf8427e 为空字符串的md5
          const md5 = spark.end();
          resolve(`md5:${md5}`);
        }
      };

      if (file.size <= interval * 2) {
        // 全量哈希
        catchQueue.enqueue(file);
      } else {
        // 抽样哈希
        catchQueue.enqueue(file.slice(0, interval));
        for (let start = 1; (start + 1) * interval <= file.size; start++) {
          catchQueue.enqueue(file.slice(start * interval, start * interval + size));
        }
        catchQueue.enqueue(file.slice(file.size - interval, file.size));
        const blob = new Blob([file.size + ''], {
          type: 'text/plain',
        });
        catchQueue.enqueue(blob);
      }

      fileReader.readAsArrayBuffer(catchQueue.peek);
    });
  },
};

export default hash;
