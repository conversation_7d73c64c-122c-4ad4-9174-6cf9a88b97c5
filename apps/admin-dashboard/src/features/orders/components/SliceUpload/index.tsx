import { Box } from '@chakra-ui/react';
import type { CancelablePromise } from '@forest/fetch';
import { useEffect, useRef, useState } from 'react';
import { catchError, EMPTY, from, map, mergeMap, Subject, takeUntil, tap } from 'rxjs';

import { createFile, finishUpload, uploadFromUrl } from '../../api';
import type { CreateFileRequest, FailData, UploadFile } from '../../types';
import { FileList } from '../FileList';
import { FileContext } from './FileContext';
import hash from './hash';

export interface UploadProps {
  data: { orgUid: string };
  accept?: string;
  disabled?: boolean;
  children: any;

  // 受控，外部传入的优先
  fileListData?: UploadFile[];

  // 返回最新的文件列表
  onChange?: (fileList: UploadFile[]) => void;
  beforeUpload?: (uid: string, uri: string) => void;
  onRemove?: (data: string, state?: 'uploaded') => void;
}

const config = {
  // 以100MB为一个分片上传
  defaultSize: 100 * 1024 * 1024,

  // 抽样间隔100MB
  sampleInterval: 100 * 1024 * 1024,

  // 抽样大小1KB
  sampleSize: 1024,
};

const progress = (etags: Array<string>) => {
  let count = 0;
  for (let i = 0; i < etags.length; i++) {
    if (typeof etags[i] === 'string') count++;
  }
  const result = Math.round((count / etags.length) * 100);
  return result;
};

export const Upload: React.FC<UploadProps> = ({
  data,
  accept,
  disabled,
  fileListData,
  children,
  beforeUpload,
  onChange,
  onRemove,
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [failMap, setFailMap] = useState<Record<string, FailData>>({});
  const newFileList = useRef<Array<UploadFile>>([]);
  const inputRef = useRef<HTMLInputElement>(null);

  const failUpload = (data: FailData) => {
    const { message, uid, fileIndex } = data;
    setFailMap((failMap) => {
      if (uid.length) {
        failMap[uid] = data;
      } else if (fileIndex) {
        failMap[fileIndex] = data;
      }
      return failMap;
    });
    newFileList.current[fileIndex].complete = -1;
    newFileList.current[fileIndex].uid = uid;
    newFileList.current[fileIndex].failReason = message;
    setFileList([...newFileList.current]);
  };

  const retryUpload = (fileIndex: number, uid: string) => {
    const data = failMap[uid] || failMap[fileIndex];
    if (!data) return;
    const { originFile, upload_urls, preEtags, sha256 } = data;

    // 需及时响应重试
    newFileList.current[fileIndex].complete = progress(preEtags) === 100 ? 99 : progress(preEtags);
    setFileList([...newFileList.current]);

    if (upload_urls.length) {
      // 断点续传
      silceUpload(originFile, upload_urls, uid, fileIndex, preEtags);
    } else {
      // 全部重传
      createUrl(originFile, fileIndex, sha256);
    }
  };

  const silceUpload = (
    originFile: File,
    upload_urls: Array<string>,
    uid: string,
    fileIndex: number,
    preEtags?: Array<string>
  ) => {
    const defaultSize = config.defaultSize;
    const etags = preEtags || new Array(upload_urls.length);
    const uploadPromiseCatch: CancelablePromise<unknown>[] = [];
    const cancelUploadFile$ = new Subject<string>();
    newFileList.current[fileIndex].cancelSubject = cancelUploadFile$;

    return from(upload_urls)
      .pipe(
        mergeMap((url, i) => {
          if (etags[i]) {
            return [i];
          } else {
            const chunk = originFile.slice(i * defaultSize, (i + 1) * defaultSize);
            const uploadPromise = uploadFromUrl(url, chunk);
            uploadPromiseCatch.push(uploadPromise);

            return from(uploadPromise).pipe(
              map((value) => ({ etag: value as string, url })),
              tap(({ etag, url }) => {
                const index = upload_urls.indexOf(url);
                etags[index] = etag.replace(/"/g, '');
              })
            );
          }
        }, 6),
        tap((index) => {
          const complete = progress(etags);
          if (complete < 100) {
            // 当前分片上传成功
            newFileList.current[fileIndex].complete = complete;
            setFileList([...newFileList.current]);
          } else if (progress(etags) === 100) {
            if (typeof index === 'number' && index < upload_urls.length - 1) {
              return;
            }
            // 所有分片上传完毕
            finishUpload({
              uid,
              requestBody: {
                etags,
              },
            })
              .pipe(
                tap(() => {
                  newFileList.current[fileIndex].complete = 100;
                  setFileList([...newFileList.current]);
                }),
                catchError((error) => {
                  newFileList.current[fileIndex].complete = -1;
                  setFileList([...newFileList.current]);
                  failUpload({
                    uid,
                    message: `传输失败，${error.body.message}`,
                    originFile,
                    upload_urls,
                    fileIndex,
                    preEtags: etags,
                  });
                  return EMPTY;
                })
              )
              .subscribe();
          }
        }),
        takeUntil(cancelUploadFile$),
        catchError((error) => {
          failUpload({
            uid,
            message: `传输失败，${error.body.message}`,
            originFile,
            upload_urls,
            fileIndex,
            preEtags: etags,
          });
          return EMPTY;
        })
      )
      .subscribe({
        complete() {
          if (progress(etags) < 100) {
            failUpload({
              uid,
              message: '手动取消',
              originFile,
              upload_urls,
              fileIndex,
              preEtags: etags,
            });
            uploadPromiseCatch.forEach((promise) => promise.cancel());
          }
        },
      });
  };

  const createUrl = async (originFile: File, fileIndex: number, preSha256?: string) => {
    const sha256 = preSha256 || (await hash.md5(originFile, config.sampleInterval, config.sampleSize));
    const { name, size, type } = originFile;
    const parts = Math.ceil(size / config.defaultSize);
    const reqData: CreateFileRequest = {
      name,
      size,
      parts,
      mime: type,
      org_uid: data.orgUid,
      sha256,
    };
    createFile(reqData)
      .pipe(
        map(({ upload_urls, file }) => {
          return { upload_urls, resFile: file };
        }),
        tap(({ upload_urls, resFile }) => {
          const { uri, uid, state } = resFile;
          newFileList.current[fileIndex].uid = uid;

          if (upload_urls.length === 0) {
            // TODO: 重新获取URL,getUrls({ uid, parts }).subscribe((res) => { });
            if (state === 'uploaded') {
              beforeUpload?.(uid, uri);
              newFileList.current[fileIndex].complete = 100;
              newFileList.current[fileIndex].state = 'uploaded';
              setFileList([...newFileList.current]);
            } else {
              failUpload({
                uid,
                originFile,
                message: '传输失败',
                upload_urls,
                sha256,
                fileIndex,
                preEtags: [],
              });
            }
          } else if (upload_urls.length === parts) {
            beforeUpload?.(uid, uri);
            silceUpload(originFile, upload_urls, uid, fileIndex);
          }
        })
      )
      .subscribe({
        error() {
          newFileList.current[fileIndex].complete = -1;
          newFileList.current[fileIndex].failReason = '传输失败';
          setFileList([...newFileList.current]);
        },
      });
  };

  const onUpload: React.ChangeEventHandler<HTMLInputElement> = async (e) => {
    if (!e.target.files?.[0]) return;
    const originFile = e.target.files?.[0];
    const file: UploadFile = {
      name: originFile.name as string,
      uid: '',
      complete: 0,
    };
    newFileList.current.push(file);
    setFileList([...newFileList.current]);
    onChange?.(newFileList.current);
    e.target.value = '';
    const sha256 = await hash.md5(originFile, config.sampleInterval, config.sampleSize);
    createUrl(originFile, newFileList.current.length - 1, sha256);
  };

  const removeUpload = (fileIndex: number) => {
    newFileList.current[fileIndex].cancelSubject?.next('cancel');
    const uid = newFileList.current[fileIndex].uid;
    const state = newFileList.current[fileIndex].state;
    newFileList.current.splice(fileIndex, 1);
    setFileList([...newFileList.current]);
    onChange?.(newFileList.current);
    if (uid && fileList.filter((item) => item.uid === uid).length === 1) {
      onRemove?.(uid, state);
    }
  };

  const FileValue = {
    fileList,
    removeUpload,
    retryUpload,
    cancelUpload: (fileIndex: number) => newFileList.current[fileIndex].cancelSubject?.next('cancel'),
  };

  useEffect(() => {
    if (fileListData?.length === 0) {
      for (let i = newFileList.current.length - 1; i >= 0; i--) {
        removeUpload(i);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fileListData]);

  return (
    <Box marginTop={4}>
      <FileContext.Provider value={FileValue}>
        <Box onClick={() => inputRef.current?.click()} w="fit-content">
          {children}
        </Box>

        <input ref={inputRef} type="file" hidden accept={accept} onChange={onUpload} disabled={disabled}></input>

        <FileList />
      </FileContext.Provider>
    </Box>
  );
};
