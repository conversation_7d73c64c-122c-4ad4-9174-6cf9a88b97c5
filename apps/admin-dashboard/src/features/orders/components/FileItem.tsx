import { Box, IconButton, Progress, Text, Tooltip } from '@chakra-ui/react';
import { useContext } from 'react';
import { BiCheck, BiFile } from 'react-icons/bi';
import { RiDeleteBin6Line, RiErrorWarningFill } from 'react-icons/ri';

import type { UploadFile } from '../types';
import { FileContext } from './SliceUpload/FileContext';

export interface FileItemProps {
  index: number;
  file: UploadFile;
}

export const FileItem: React.FC<FileItemProps> = ({ file, index }) => {
  const { removeUpload, retryUpload, cancelUpload } = useContext(FileContext);

  return (
    <Box display="flex" margin="4px 0">
      <Box display="flex" flexDirection="column" bg="#F7F8FA" w="95%" p="8px 10px" borderRadius="3px">
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <BiFile color="#4299E1" />
          <Box display="flex" flex={1} alignItems="center" justifyContent="flex-start" marginLeft={1}>
            <Text color={file.complete === -1 ? '#E53E3E' : '#000'}>{file.name}</Text>

            {file.complete === -1 && (
              <Tooltip hasArrow label={file?.failReason} bg="gray.500">
                <IconButton
                  aria-label="warn"
                  icon={<RiErrorWarningFill color="#E53E3E" />}
                  size="sm"
                  variant="unstyled"
                ></IconButton>
              </Tooltip>
            )}
          </Box>

          {file.complete === 100 && <BiCheck color="#00B42B" />}
          {file.complete === -1 && (
            <Text size="xs" cursor="pointer" color="blue.600" onClick={() => retryUpload(index, file.uid)}>
              点击重试
            </Text>
          )}
          {file.complete > -1 && file.complete < 100 && (
            <Text size="xs" cursor="pointer" color="blue.600" onClick={() => cancelUpload(index)}>
              取消上传
            </Text>
          )}
        </Box>
        {file.complete > -1 && file.complete < 100 && (
          <Box display="flex" justifyContent="space-between">
            <Box width="93%">
              <Progress value={file.complete} size="xs" colorScheme="blue" marginTop="10px" hasStripe isAnimated />
            </Box>
            <Text size="2xs" color="gray.500">
              {file.complete}%
            </Text>
          </Box>
        )}
      </Box>

      <Box display="flex" justifyContent="center" alignItems="center" flex="1">
        <IconButton
          variant="link"
          onClick={() => {
            removeUpload(index);
          }}
          icon={<RiDeleteBin6Line />}
          aria-label="delete"
        />
      </Box>
    </Box>
  );
};
