import { Box, I<PERSON><PERSON>utt<PERSON>, <PERSON>, Tag } from '@chakra-ui/react';
import { Column, IPagination, Table } from '@forest/simple-ui';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { RiDownloadLine } from 'react-icons/ri';

import { TASK_STATE_MAP } from '@/features/tasks';
import { filterValidDate } from '@/utils';

import type { Order, OrderShowState } from '../types';

export interface OrdersTableProps {
  name: OrderShowState;
  orders?: Order[];
  isLoading?: boolean;
  pagination: IPagination;
}

export const OrdersTable = ({ name, orders, isLoading, pagination }: OrdersTableProps) => {
  const columns: Column<Order>[] = useMemo(
    () => [
      {
        Header: '订单名称',
        accessor: 'name',
        Cell: ({ row }) => {
          const { original: task } = row;
          return (
            <Box>
              <Link color="brand.500" fontWeight="bold">
                {task.name}
              </Link>
              <Box color="gray.600" fontSize="0.8rem" mt="0.5">
                ID: {task.uid}
              </Box>
            </Box>
          );
        },
      },
      {
        Header: '创建时间',
        accessor: 'created_at',
        Cell: ({ value }) => (
          <Box whiteSpace="pre" color="gray.600" fontSize="0.9rem">
            {dayjs(value).format('YYYY / MM / DD\nHH:mm')}
          </Box>
        ),
      },
      {
        Header: '连续帧数据',
        accessor: 'source',
        Cell: ({ value }) => {
          return <Tag colorScheme="gray">{value?.is_frame_series ? '是' : '否'}</Tag>;
        },
      },
      {
        Header: '状态',
        accessor: 'state',
        Cell: ({ value }) => {
          const { color, text } = TASK_STATE_MAP[value];
          return (
            <Tag variant="solid" colorScheme={color}>
              {text}
            </Tag>
          );
        },
      },

      {
        Header: '操作',
        accessor: 'anno_result_url',
        Cell: ({ value, row }) => {
          const { original } = row;
          const { readyTime, state } = original;
          const displayTime = dayjs(readyTime).format('\nYYYY / MM / DD HH:mm');

          return (
            <Box width={20}>
              {value?.length ? (
                <IconButton
                  as="a"
                  variant="ghost"
                  isRound
                  href={value}
                  cursor="pointer"
                  icon={<RiDownloadLine color="#1481e3" />}
                  aria-label="delete"
                />
              ) : state === 'finished' && filterValidDate(displayTime) ? (
                <Box whiteSpace="pre" color="gray.600" fontSize="0.9rem">
                  预计可导出结果时间：<span style={{ fontWeight: 'bold' }}>{displayTime}</span>
                </Box>
              ) : (
                <></>
              )}
            </Box>
          );
        },
      },
    ],
    []
  );

  const data = useMemo(() => (Array.isArray(orders) ? orders : []), [orders]);

  return <Table data={data} columns={columns} isLoading={isLoading} pagination={pagination} />;
};
