import { useContext } from 'react';

import type { UploadFile } from '../types';
import { FileItem } from './FileItem';
import { FileContext } from './SliceUpload/FileContext';

export interface FileListProps {}

export const FileList: React.FC<FileListProps> = () => {
  const { fileList } = useContext(FileContext);
  return (
    <>
      {fileList.map((file: UploadFile, index) => (
        <FileItem file={file} index={index} key={index} />
      ))}
    </>
  );
};
