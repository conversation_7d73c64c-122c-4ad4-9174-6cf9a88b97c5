/* eslint-disable react-hooks/exhaustive-deps */
import {
  AlertDialog,
  AlertDialogBody,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogOverlay,
  Box,
  Button,
  FormLabel,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  <PERSON>dal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
  useToast,
} from '@chakra-ui/react';
import { createForm } from '@formily/core';
import { createSchemaField, FormProvider } from '@formily/react';
import React, { useEffect, useMemo, useRef, useState } from 'react';

import { useRequest } from '@/hooks/use-request';
import { useAuth } from '@/providers/auth';

import { creatOrder, deleteFile } from '../api';
import { ElemType, UploadFile } from '../types';
import { addOrderSchema } from './forms';
import { Upload } from './SliceUpload';

export interface AddModalProps {
  onOpen: boolean;
  closeModal: () => void;
  onAddOrder: () => void;
}
interface AddFormProps {
  name: string;
  is_frame_series: string;
  auto_parse: boolean;
  elem_type: ElemType;
}
// TODO: const managerConfig = ['root', 'manager', 'owner'];
const toastConfig = {
  notUpload: {
    title: '还未上传文件,文件上传完才能新建订单',
  },
  uploading: {
    title: '所有数据上传完，才能完成订单新建。不需要的数据请删除，保证所有数据为上传完成状态。',
  },
};
const beforeLeave = (e: BeforeUnloadEvent) => {
  const message = '确认离开？';
  e.preventDefault();
  e.returnValue = message;
  return message;
};

export const AddModal: React.FC<AddModalProps> = ({ onOpen, closeModal, onAddOrder }) => {
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [dialogState, setDialogState] = useState<'notUpload' | 'uploading'>('notUpload');
  const [fileListData, setFileListData] = useState<UploadFile[]>();
  const uriMap = useRef<Map<string, string>>(new Map());
  const cancelDialogRef = useRef<HTMLButtonElement | null>(null);
  const fileListRef = useRef<UploadFile[]>([]);
  const { user } = useAuth();
  const showToast = useToast({ position: 'top', duration: 2000 });

  const initialValues = {
    name: '',
    is_frame_series: 'false',
    auto_parse: true,
  };
  const addForm = useMemo(() => {
    const form = createForm<AddFormProps>({
      initialValues,
    });
    return form;
  }, []);

  const LoginSchemaField = createSchemaField(addOrderSchema.config);
  const orgUid = user?.team?.uid || '';

  const checkFileList = () => {
    if (fileListRef.current.length === 0) {
      setDialogState('notUpload');
      return 'notUpload';
    } else if (fileListRef.current.find((file) => file.complete < 100)) {
      setDialogState('uploading');
      return 'uploading';
    } else {
      return 'uploaded';
    }
  };
  const resetModal = () => {
    setFileListData(undefined);
    fileListRef.current = [];
    closeModal();
    uriMap.current.clear();
    setTimeout(() => {
      addForm.setValues(initialValues);
    }, 200);
  };

  const onClose = () => {
    if (checkFileList() === 'notUpload') {
      resetModal();
    } else {
      setConfirmOpen(true);
    }
  };
  const onConfirmClose = () => {
    setConfirmOpen(false);
  };

  const { refetch: addOrder } = useRequest(creatOrder, {
    onSuccess: () => {
      showToast({
        title: '订单新建成功',
        status: 'success',
      });
      resetModal();
      onAddOrder();
    },
    onError: () => {
      showToast({
        title: '订单创建失败',
        status: 'error',
      });
    },
  });

  const onSubmit = () => {
    if (checkFileList() === 'uploaded') {
      addForm.submit((value: AddFormProps) => {
        const { name, is_frame_series, auto_parse, elem_type } = value;
        const uris: Array<string> = [];
        uriMap.current.forEach((uri) => {
          uris.push(uri);
        });
        addOrder({
          params: {
            requestBody: {
              name,
              org_uid: orgUid,
              source: {
                uris,
                is_frame_series: is_frame_series === 'true' ? true : false,
                auto_parse,
                elem_type,
                error_handlers: [
                  {
                    handler: 'ignore',
                  },
                ],
              },
            },
          },
        });
      });
    } else {
      showToast({
        ...toastConfig[dialogState],
        status: 'warning',
        duration: 3000,
      });
    }
  };

  useEffect(() => {
    if (onOpen) {
      window.addEventListener('beforeunload', beforeLeave);
      return () => {
        window.removeEventListener('beforeunload', beforeLeave);
      };
    }
  }, [onOpen]);

  return (
    <>
      <Modal isOpen={onOpen} onClose={onClose} size="2xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>新建订单</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <FormProvider form={addForm}>
              <LoginSchemaField schema={addOrderSchema.schema}></LoginSchemaField>
            </FormProvider>
            <Box display="flex" alignItems="center" marginTop={3}>
              <FormLabel width={110} textAlign="right" marginInlineEnd={3} fontSize="sm" marginBottom={-1}>
                上传文件
                <span style={{ marginInline: '0.125rem' }}>:</span>
              </FormLabel>
              <Box width="100%">
                <Upload
                  accept=".doc,.docx,.zip,.pcd"
                  data={{ orgUid }}
                  fileListData={fileListData}
                  beforeUpload={(uid, uri: string) => uriMap.current.set(uid, uri)}
                  onChange={(fileList) => {
                    fileListRef.current = fileList;
                    if (fileListData?.length === 0) {
                      resetModal();
                    }
                  }}
                  onRemove={(uid, state) => {
                    if (!state) deleteFile({ uid });
                    uriMap.current.delete(uid);
                  }}
                >
                  <Button colorScheme="blue" marginBottom={3}>
                    点击上传
                  </Button>
                </Upload>
              </Box>
            </Box>
          </ModalBody>

          <ModalFooter>
            <Button colorScheme="blue" mr={3} onClick={onSubmit}>
              完成
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      <AlertDialog leastDestructiveRef={cancelDialogRef} onClose={onConfirmClose} isOpen={confirmOpen}>
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogBody>关闭界面后，已经上传/正在上传的数据将删除，同时将删除此订单，确认继续？</AlertDialogBody>
            <AlertDialogFooter>
              <Button variant="ghost" mr={3} onClick={onConfirmClose} size="sm">
                取消
              </Button>
              <Button
                colorScheme="blue"
                mr={3}
                size="sm"
                onClick={() => {
                  setFileListData([]);
                  onConfirmClose();
                }}
              >
                确认
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </>
  );
};
