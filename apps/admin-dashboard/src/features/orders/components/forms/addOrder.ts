import { Checkbox, FormItem, FormLayout, Input, Radio } from '@forest/formily';
import type { ISchema } from '@formily/react';

const config = {
  components: {
    FormItem,
    Input,
    Radio,
    Checkbox,
    FormLayout,
  },
};
const schema: ISchema = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        size: 'sm',
        labelWidth: '110px',
      },
      properties: {
        name: {
          type: 'string',
          title: '订单名',
          required: true,
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入订单名',
          },
          maxLength: 100,
          pattern: '^[\u4E00-\u9FA5A-Za-z0-9_-]+$',
        },
        // TODO: 如果选了 4D 任务类型，则只能选择离散帧数据
        is_frame_series: {
          type: 'string',
          title: '数据类型',
          'x-decorator': 'FormItem',
          'x-component': 'Radio.Group',
          required: true,
          enum: [
            {
              label: '离散帧数据',
              value: 'false',
            },
            {
              label: '连续帧数据',
              value: 'true',
            },
          ],
          'x-component-props': {
            layout: 'horizontal',
          },
          'x-decorator-props': {
            layoutStyle: {
              alignItems: 'center',
            },
          },
        },
        elem_type: {
          type: 'string',
          'x-decorator': 'FormItem',
          'x-component': 'Radio.Group',
          title: '帧类型',
          required: true,
          enum: [
            {
              label: '纯图片',
              value: 'image',
            },
            {
              label: '纯点云',
              value: 'pointcloud',
            },
            {
              label: '点云 + 图片（1 张）',
              value: 'fusion2d',
            },
            {
              label: '点云 + 图片（多张）',
              value: 'fusion3d',
            },
            {
              label: '车道线类型',
              value: 'fusion4d',
            },
          ],
          'x-decorator-props': {
            layoutStyle: {
              alignItems: 'center',
            },
          },
        },
        auto_parse: {
          type: 'boolean',
          title: '是否自动解析',
          'x-decorator': 'FormItem',
          'x-component': 'Checkbox',
          'x-component-props': {
            layout: 'horizontal',
            children: '自动解析',
          },
          'x-decorator-props': {
            layoutStyle: {
              alignItems: 'center',
            },
          },
        },
      },
    },
  },
};

export const addOrderSchema = { config, schema };
