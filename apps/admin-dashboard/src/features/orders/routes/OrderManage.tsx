import { Box, Input, InputGroup, InputLeftElement } from '@chakra-ui/react';
import { CollapseContainer } from '@forest/simple-ui';
import { useState } from 'react';
import { RiMenuAddLine, RiSearchLine } from 'react-icons/ri';

import { Header } from '@/components/layout/Header';
import { useRequest } from '@/hooks/use-request';
import { useAuth } from '@/providers/auth';
import type { IServicePagination } from '@/types';

import { formatOrdersRequest, getOrderList } from '../api';
import { AddModal, OrdersTable } from '../components';
import type { OrderShowState } from '../types';

const pathList = [{ name: '订单管理', path: '#' }];
const pagesz = 5;

export const OrderManage: React.FC = () => {
  const [onOpen, setOnOpen] = useState<boolean>(false);
  const { user } = useAuth();

  const [progressParams, setProgressParams] = useState<IServicePagination>({ total: 0, page: 0 });
  const [endParams, setEndParams] = useState<IServicePagination>({ total: 0, page: 0 });
  const {
    data: orderData,
    isLoading: listLoading,
    refetch: updateOrderData,
  } = useRequest(formatOrdersRequest, {
    params: {
      api: getOrderList,
      query: {
        page: [progressParams.page, endParams.page],
        pagesz,
        showStates: ['progress', 'end'],
        orgUid: user!.team?.uid,
      },
    },
    enabled: true,
    onSuccess: (data) => {
      const { progress, end } = data || {};
      if (progress?.total && progress.total !== 0) {
        const total = progress.total;
        setProgressParams((prev) => {
          return { ...prev, total };
        });
      }
      if (end?.total && end.total !== 0) {
        const total = end.total;
        setEndParams((prev) => {
          return { ...prev, total };
        });
      }
    },
  });

  const onClickAdd = () => {
    setOnOpen(true);
  };

  const onChangePage = (type: OrderShowState) => {
    return (currentPage: number) => {
      const page = currentPage - 1;
      const newParams = {
        api: getOrderList,
        query: {
          showStates: [type],
          page: [page],
          pagesz,
          orgUid: user!.team?.uid,
        },
      };
      updateOrderData({ params: newParams });
      if (type === 'progress') {
        setProgressParams((prev) => ({ ...prev, page }));
      } else {
        setEndParams((prev) => ({ ...prev, page }));
      }
    };
  };

  return (
    <Box minH="100%">
      <Header
        title="订单管理"
        pathList={pathList}
        rightButton={{
          tip: '新建订单',
          icon: <RiMenuAddLine />,
          onClick: onClickAdd,
        }}
      >
        <InputGroup h="2.5rem" size="sm">
          <InputLeftElement pointerEvents="none" children={<RiSearchLine />} />
          <Input variant="filled" borderRadius="lg" placeholder="请输入订单名称" />
        </InputGroup>
      </Header>
      <CollapseContainer title="处理中" startingHeight="12rem" mb="4">
        <OrdersTable
          name="progress"
          orders={orderData?.progress?.orders}
          pagination={{
            total: progressParams.total,
            defaultPageSize: pagesz,
            onChange: onChangePage('progress'),
          }}
        />
      </CollapseContainer>
      <CollapseContainer title="已完成" startingHeight="12rem">
        <OrdersTable
          name="end"
          orders={orderData?.end?.orders}
          isLoading={listLoading}
          pagination={{
            total: endParams.total,
            defaultPageSize: pagesz,
            onChange: onChangePage('end'),
          }}
        />
      </CollapseContainer>
      <AddModal onOpen={onOpen} closeModal={() => setOnOpen(false)} onAddOrder={() => updateOrderData()} />
    </Box>
  );
};

export default OrderManage;
