import type { Subject } from 'rxjs';

import type { anno_v1_Order as SOrder, anno_v1_Source } from '@/service/anno/v1';

export type { annofeed_v1_CreateFileRequest as CreateFileRequest } from '@/service/annofeed/v1';

export interface Order extends SOrder {
  readyTime?: string;
}

export type OrderShowState = 'progress' | 'end' | 'unknown';

export type UploadFile = {
  name: string;
  uid: string;
  complete: number;
  sha256?: string;
  failReason?: string;
  cancelSubject?: Subject<string>;
  state?: 'uploaded';
};

export type FailData = {
  uid: string;
  message: string;
  originFile: File;
  upload_urls: Array<string>;
  sha256?: string;
  fileIndex: number;
  preEtags: Array<string>;
};

export interface PaginationProps {
  total: number;
  page: number;
  pagesz: number;
}

export type ElemType = Exclude<anno_v1_Source['elem_type'], 'unspecified'>;
