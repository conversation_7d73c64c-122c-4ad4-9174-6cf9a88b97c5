import { request as __request } from '@forest/fetch';
import type { Observable } from 'rxjs';
import { defer, forkJoin, from, map, mergeMap, of, reduce } from 'rxjs';

import {
  anno_v1_CreateOrderRequest as CreateOrderRequest,
  anno_v1_ListOrderReply as ListOrderReply,
  Orders as OrderService,
} from '@/service/anno/v1';
import {
  annofeed_v1_CreateFileRequest as CreateFileRequest,
  annofeed_v1_FinishFileUploadRequest as FinishFileUploadRequest,
  Files as FileService,
} from '@/service/annofeed/v1';
import { APIConfig } from '@/service/config';

import { SHOW_STATE_MAP } from '../config';
import type { Order, OrderShowState } from '../types';

export interface OrderListRequest {
  page: number[];
  pagesz?: number;
  /**
   * filter by orgnization
   */
  orgUid?: string;
  /**
   * filter by creator
   */
  creatorUid?: string;
  /**
   * filter by name pattern
   */
  namePattern?: string;
  /**
   * filter by order state
   */
  states?: Array<Order['state']>;
  /**
   * include order's orgnization in the reply
   */
  withOrg?: boolean;

  showStates?: Array<OrderShowState>;
}
export type TransRequest<T extends OrderListRequest> = {
  page: number;
  pagesz?: number;
  states?: Array<Order['state']>;
} & Omit<T, 'showStates' | 'page' | 'pagesz' | 'states'>;

type EndListOrderReply = Omit<ListOrderReply, 'orders'> & {
  orders?: Order[];
};
export type OrderListResult = Partial<Record<OrderShowState, EndListOrderReply>>;

export const formatOrdersRequest = <T extends OrderListRequest>({
  api,
  query,
}: {
  api: (query: TransRequest<T>) => Observable<EndListOrderReply>;
  query: T;
}): Observable<OrderListResult> => {
  const { showStates, states, page, pagesz, ...rest } = query;

  if (Array.isArray(showStates)) {
    return from(showStates).pipe(
      mergeMap((showState, i) => {
        const states = SHOW_STATE_MAP[showState] as Array<Order['state']>;
        return api({
          page: Array.isArray(page) ? page[i] : page,
          pagesz,
          states,
          ...rest,
        }).pipe(
          mergeMap(({ total, orders = [] }) =>
            showState === 'end'
              ? forkJoin(
                orders.map((order) =>
                  order.state === 'finished' && !order.anno_result_url
                    ? defer(() =>
                      from(OrderService.ordersGetAnnoResult({ uid: order.uid })).pipe(
                        map(({ will_ready_at }) => ({ ...order, readyTime: will_ready_at }))
                      )
                    )
                    : of(order)
                )
              ).pipe(map((orders) => ({ [showState]: { total, orders } })))
              : of({ [showState]: { total, orders } })
          )
        );
      }),
      reduce((acc, value) => ({ ...acc, ...value }))
    );
  } else {
    return api({
      page: Array.isArray(page) ? page[0] : page,
      pagesz,
      states,
      ...rest,
    }).pipe(map((value) => ({ unknown: value })));
  }
};

export const getOrderList = (params: TransRequest<OrderListRequest>) => from(OrderService.ordersListOrder(params));

export const creatOrder = (params: { requestBody: CreateOrderRequest }) => {
  return from(OrderService.ordersCreateOrder(params));
};

/*
 * file
 */
export const createFile = (params: CreateFileRequest) => {
  return from(
    FileService.filesCreateFile({
      requestBody: params,
    })
  );
};

export const getUrls = (params: { uid: string; parts?: number }) => {
  return from(FileService.filesGetFileUploadUrLs(params));
};

export const uploadFromUrl = (url: string, chunk: Blob) => {
  return __request(
    {
      ...APIConfig,
      BASE: '',
      TOKEN: undefined,
    },
    {
      method: 'PUT',
      url,
      body: chunk,
      responseHeader: 'etag',
    }
  );
};

export const deleteFile = ({ uid }: { uid: string }) => {
  return from(
    FileService.filesDeleteFile({
      uid,
    })
  );
};

export const finishUpload = ({ uid, requestBody }: { uid: string; requestBody: FinishFileUploadRequest }) => {
  return from(
    FileService.filesFinishFileUpload({
      uid,
      requestBody,
    })
  );
};
