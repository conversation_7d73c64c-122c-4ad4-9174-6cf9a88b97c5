import {
  Box,
  Button,
  Card,
  CardBody,
  Container,
  Flex,
  Heading,
  SimpleGrid,
  Stack,
  StackDivider,
  Text,
} from '@chakra-ui/react';
import dayjs from 'dayjs';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';

import { Header } from '@/components/layout';
import { getTeamMembers } from '@/features/teams';
import type { User } from '@/features/users';
import { useRequest } from '@/hooks/use-request';
import { useAuth } from '@/providers/auth';
import type { IServicePagination } from '@/types';

import { getTask, getTaskExecutors, ListExecutorsRequest, ManageExecutorsRequest, manageTaskExecutors } from '../api';
import { ExecutorsTable } from '../components';
import { TASK_STATE_MAP, TASK_TYPE_MAP } from '../config';

export interface CardItemProps {
  title: string;
  content?: string | string[];
}
export const CardItem: React.FC<CardItemProps> = ({ title, content = '...' }) => {
  return (
    <Box>
      <Heading size="xs" color="gray.600">
        {title}
      </Heading>
      <Text pt="2" fontSize="sm">
        {Array.isArray(content) ? content.join(', ') || '...' : content}
      </Text>
    </Box>
  );
};

const pathList = [
  { name: '任务生产', path: '/admin/tasks/produce' },
  { name: '人员分配', path: '#' },
];

// WARN: 当前所有成员操作均认为是全数，未考虑分批次返回情况
export const TaskAssign = () => {
  const [phaseUserMap, setPhaseUserMap] = useState<Record<number, Record<string, boolean>>>({});
  const [currentPhase, setCurrentPhase] = useState(0);
  const [currentMembers, setCurrentMembers] = useState<Array<User & { isSelected: boolean }>>([]);
  const [pagination, setPagination] = useState<IServicePagination>({ page: 0, pagesz: 20, total: 0 });
  const currentPhaseRef = useRef(0);

  const { taskUid = '' } = useParams();
  const { user } = useAuth();

  const { data: task, error } = useRequest(getTask, {
    params: { uid: taskUid },
    enabled: true,
  });

  const { data: memberData, refetch: updateMembers } = useRequest(getTeamMembers, {
    params: { uid: user!.team?.uid || '', ...pagination },
    enabled: true,
    onSuccess: ({ total }) => {
      if (total !== undefined && total !== 0) {
        setPagination((prev) => ({ ...prev, total }));
      }
    },
  });
  const { data: originExecutors, refetch: updatePhaseUserMap } = useRequest(getTaskExecutors, {
    onSuccess: (data) => {
      const pum: Record<number, Record<string, boolean>> = {};
      for (const key in data) {
        pum[key] = data[key].reduce(
          (acc, user) => ({
            ...acc,
            [user.uid]: true,
          }),
          {}
        );
      }
      setPhaseUserMap(pum);
      setCurrentPhase((phaseId) => {
        const phase = phaseId === 0 ? parseInt(Object.keys(data)[0]) : phaseId;
        currentPhaseRef.current = phase;
        return phase;
      });
    },
  });
  const { isLoading: isSubmitting, refetch: updateTaskExecutors } = useRequest(manageTaskExecutors, {
    onSuccess: (_, opt, showToast) => {
      const { phases } = opt.meta as Pick<ListExecutorsRequest, 'phases'>;
      updatePhaseUserMap({
        params: {
          uid: taskUid,
          phases,
          pagesz: 500,
        },
      });
      showToast({ status: 'success', title: '提交成功' });
    },
  });

  const taskPhases = useMemo(() => {
    const ps = task
      ? task.phases
          .map((item, i) => ({
            ...item,
            id: i + 1,
          }))
          .filter((item) => item.execteams.some((info) => info.execteam === user?.team?.uid))
      : [];

    if (ps.length > 0) {
      updatePhaseUserMap({
        params: {
          uid: taskUid,
          phases: ps.map((item) => item.id),
          pagesz: 500,
        },
      });
    }

    return ps;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [task]);

  useEffect(() => {
    if (currentPhase > 0 && memberData) {
      const extraPhaseIds = taskPhases.filter((phase) => phase.id !== currentPhase).map((phase) => phase.id);

      const memberList: Array<User & { isSelected: boolean }> = [];
      memberData.members.forEach((member) => {
        // 判断当前用户没有被分配到其它环节
        if (extraPhaseIds.findIndex((phaseId) => Boolean(phaseUserMap[phaseId][member.uid])) === -1) {
          memberList.push({
            ...member,
            isSelected: Boolean(phaseUserMap[currentPhase][member.uid]),
          });
        }
      });

      setCurrentMembers(memberList);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPhase, memberData]);

  const onSelectMembersChange = (changedIds: number[], selected: boolean) => {
    if (changedIds.length > 1) {
      // change multiple members once
    } else if (changedIds.length === 1) {
      const cid = changedIds[0];
      setCurrentMembers((members) => {
        setPhaseUserMap((oldValue) => {
          const phaseMap = { ...oldValue };
          phaseMap[currentPhaseRef.current][members[cid].uid] = selected;
          return phaseMap;
        });
        return members.slice(0, cid).concat(
          [
            {
              ...members[cid],
              isSelected: selected,
            },
          ],
          members.slice(cid + 1)
        );
      });
    }
  };

  const onSubmitAssign = () => {
    if (originExecutors) {
      const phaseExecutors: ManageExecutorsRequest['phases'] = [];

      for (const phase in phaseUserMap) {
        const origin: Record<string, boolean> = {};
        const del: string[] = [];

        originExecutors[phase].forEach((execs) => {
          if (!phaseUserMap[phase][execs.uid]) {
            del.push(execs.uid);
          } else {
            origin[execs.uid] = true;
          }
        });

        const add = Object.keys(phaseUserMap[phase]).filter((key) => phaseUserMap[phase][key] && !origin[key]);

        if (add.length > 0 || del.length > 0) {
          phaseExecutors.push({
            phase: parseInt(phase),
            team_uid: user!.team?.uid || '',
            add,
            delete: del,
          });
        }
      }
      updateTaskExecutors({
        params: {
          uid: taskUid,
          phases: phaseExecutors,
        },
        meta: { phases: taskPhases.map((item) => item.id) },
      });
    }
  };

  const onClickPhase = (itemId: number) => {
    if (itemId !== currentPhase) {
      setCurrentPhase(itemId);
      currentPhaseRef.current = itemId;
    }
  };

  const onChangePage = (currentPage: number) => {
    const newParams = { ...pagination, page: currentPage - 1 };
    updateMembers({
      params: {
        ...newParams,
        uid: user!.team?.uid || '',
      },
    });
    setPagination(newParams);
  };

  if (error) {
    return <div>{error?.message || '未找到任务'}</div>;
  }

  return (
    <Box minH="100%">
      <Header title={task?.name ?? ''} pathList={pathList}></Header>
      <SimpleGrid columns={2} spacing={5}>
        <Card bg="white" px={1.5} py={1}>
          <CardBody>
            <Stack divider={<StackDivider />} spacing="4">
              <CardItem title="任务名称" content={task?.name} />
              <CardItem title="任务ID" content={task?.uid} />
              <CardItem title="任务职责" content={taskPhases.map((item) => item.name)} />
              <CardItem title="任务类型" content={task ? TASK_TYPE_MAP[task.type] : '...'} />
            </Stack>
          </CardBody>
        </Card>
        <Card bg="white" px={1.5} py={1}>
          <CardBody>
            <Stack divider={<StackDivider />} spacing="4">
              <CardItem title="创建时间" content={dayjs(task?.created_at).format('YYYY / MM / DD HH:mm')} />
              <CardItem title="预期完成时间" content={dayjs(task?.exp_end_time).format('YYYY / MM / DD HH:mm')} />
              <CardItem title="当前状态" content={task ? TASK_STATE_MAP[task.state].text : '...'} />
            </Stack>
          </CardBody>
        </Card>
      </SimpleGrid>
      <Container px="7" py="6" mt="5">
        <Flex justify="space-between">
          <Text size="md">
            <Text color="gray.700" as="span" fontWeight="bold">
              请为以下各环节分配人员
            </Text>
            <Text color="gray.500" as="span">
              （标注和审核人员不能重复）
            </Text>
          </Text>
          <Button
            colorScheme="green"
            onClick={onSubmitAssign}
            isDisabled={originExecutors === null}
            isLoading={isSubmitting}
          >
            提交分配
          </Button>
        </Flex>
        <Stack spacing={4} direction="row" mb="4">
          {taskPhases.map((item) => (
            <Button
              key={item.id}
              variant="ghost"
              colorScheme="brand"
              aria-selected={currentPhase === item.id}
              sx={{
                '&[aria-selected=true]': {
                  bg: 'brand.100',
                },
              }}
              onClick={() => onClickPhase(item.id)}
            >
              {item.name}
            </Button>
          ))}
        </Stack>
        <ExecutorsTable
          members={currentMembers}
          pagination={{
            total: pagination.total,
            defaultPageSize: pagination.pagesz,
            onChange: onChangePage,
          }}
          onSelectMembersChange={onSelectMembersChange}
        />
      </Container>
    </Box>
  );
};

export default TaskAssign;
