import { Box, Input, InputGroup, InputLeftElement } from '@chakra-ui/react';
import { CollapseContainer, SimpleDialog, SimpleDialogProps } from '@forest/simple-ui';
import { debounce } from 'lodash';
import { useCallback, useEffect, useRef, useState } from 'react';
import { RiSearchLine, RiUserSettingsLine } from 'react-icons/ri';
import { useNavigate } from 'react-router-dom';

import { Header } from '@/components/layout/Header';
import { useRequest } from '@/hooks/use-request';
import { useAuth } from '@/providers/auth';
import type { IServicePagination } from '@/types';

import { formatTasksRequest, getTasksByExecutor, TaskListResult } from '../api';
import { TasksExecTable } from '../components';
import type { Task, TaskShowState } from '../types';

const pathList = [{ name: '任务生产', path: '#' }];
const operations = [
  {
    name: 'assign',
    colorScheme: 'blue',
    icon: <RiUserSettingsLine />,
    label: '调整人员',
  },
];
const pagesz = 5;
export const TaskProduce: React.FC = () => {
  const [taskList, setTaskList] = useState<TaskListResult | null>(null);
  const [dialogConfig, setDialogConfig] = useState<Omit<SimpleDialogProps, 'isOpen' | 'onCancel'> & { task?: Task }>({
    title: '',
    content: '',
    onConfirm: () => {},
  });
  const [waitParams, setWaitParams] = useState<IServicePagination>({ page: 0, total: 0 });
  const [progressParams, setProgressParams] = useState<IServicePagination>({ page: 0, total: 0 });
  const [endParams, setEndParams] = useState<IServicePagination>({ page: 0, total: 0 });
  const namePattern = useRef<string>('');

  const { user } = useAuth();
  const navigate = useNavigate();

  const { data: taskData, refetch: updateTaskData } = useRequest(formatTasksRequest, {
    params: {
      api: getTasksByExecutor,
      query: {
        page: [waitParams.page, progressParams.page, endParams.page],
        pagesz,
        showStates: ['wait', 'progress', 'end'],
        orgUid: user!.team?.uid,
      },
    },
    enabled: true,
    onSuccess: (data: TaskListResult) => {
      const { end, progress, wait } = data || {};
      if (wait && wait?.total !== 0) {
        setWaitParams((pre) => ({ ...pre, total: wait.total }));
      }
      if (progress && progress?.total !== 0) {
        setProgressParams((pre) => ({ ...pre, total: progress.total }));
      }
      if (end && end?.total !== 0) {
        setEndParams((pre) => ({ ...pre, total: end.total }));
      }
    },
  });

  const debounceUpdateList = debounce((e) => {
    namePattern.current = e.target.value;
    updateTaskData({
      params: {
        api: getTasksByExecutor,
        query: {
          showStates: ['wait', 'progress', 'end'],
          page: [0, 0, 0],
          pagesz,
          orgUid: user!.team?.uid,
          namePattern: namePattern.current,
        },
      },
    });
  }, 500);

  const onChangePage = (type: TaskShowState) => {
    return (currentPage: number) => {
      const page = currentPage - 1;
      updateTaskData({
        params: {
          api: getTasksByExecutor,
          query: {
            showStates: [type],
            page: [page],
            pagesz,
            orgUid: user!.team?.uid,
            namePattern: namePattern.current,
          },
        },
      });

      if (type === 'wait') {
        setWaitParams((preState) => ({ ...preState, page }));
      } else if (type === 'progress') {
        setProgressParams((preState) => ({ ...preState, page }));
      } else {
        setEndParams((preState) => ({ ...preState, page }));
      }
    };
  };

  useEffect(() => {
    if (taskData) {
      if (taskList) {
        setTaskList({
          ...taskList,
          ...taskData,
        });
      } else {
        setTaskList(taskData);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [taskData]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const onCloseDialog = useCallback(() => setDialogConfig({ ...dialogConfig, task: undefined }), []);

  const onOperateTask = useCallback(
    (_: string, task: Task) => {
      navigate(`/admin/tasks/assign/${task.uid}`);
    },
    [navigate]
  );

  return (
    <Box minH="100%">
      <Header title="任务生产" pathList={pathList}>
        <InputGroup h="2.5rem" size="sm">
          <InputLeftElement pointerEvents="none" children={<RiSearchLine />} />
          <Input variant="filled" borderRadius="lg" placeholder="请输入任务名称" onChange={debounceUpdateList} />
        </InputGroup>
      </Header>
      <CollapseContainer title="待启动" startingHeight="16rem" mb="4">
        <TasksExecTable
          name="wait"
          tasks={taskList?.wait?.lots}
          pagination={{
            total: waitParams.total,
            defaultPageSize: pagesz,
            onChange: onChangePage('wait'),
          }}
          operations={operations}
          onOperateTask={onOperateTask}
        />
      </CollapseContainer>
      <CollapseContainer title="生产中" startingHeight="16rem" mb="4">
        <TasksExecTable
          name="progress"
          tasks={taskList?.progress?.lots}
          pagination={{
            total: progressParams.total,
            defaultPageSize: pagesz,
            onChange: onChangePage('progress'),
          }}
          operations={operations}
          onOperateTask={onOperateTask}
        />
      </CollapseContainer>
      <CollapseContainer title="已结束" startingHeight="16rem">
        <TasksExecTable
          name="end"
          tasks={taskList?.end?.lots}
          pagination={{
            total: endParams.total,
            defaultPageSize: pagesz,
            onChange: onChangePage('end'),
          }}
          onOperateTask={onOperateTask}
        />
      </CollapseContainer>
      <SimpleDialog isOpen={Boolean(dialogConfig?.task?.uid)} onCancel={onCloseDialog} {...dialogConfig}></SimpleDialog>
    </Box>
  );
};

export default TaskProduce;
