import { Navigate, Route, Routes } from 'react-router-dom';

import { TaskAdd } from './TaskAdd';
import { TaskAssign } from './TaskAssign';
import { TaskDetail } from './TaskDetail';
import { TaskHall } from './TaskHall';
import { TaskManage } from './TaskManage';
import { TaskProduce } from './TaskProduce';
import { TaskProgress } from './TaskProgress';

// TODO: 根据权限设置兜底页面导航
export const TaskRoutes = () => {
  return (
    <Routes>
      <Route path="manage" element={<TaskManage />}></Route>
      <Route path="add" element={<TaskAdd />}>
        <Route path=":taskUid" element={<TaskAdd />}></Route>
      </Route>
      <Route path="detail" element={<TaskDetail />}>
        <Route path=":taskUid" element={<TaskDetail />}></Route>
      </Route>
      <Route path="progress" element={<TaskProgress />}>
        <Route path=":taskUid" element={<TaskProgress />}></Route>
      </Route>

      <Route path="produce" element={<TaskProduce />}></Route>
      <Route path="hall" element={<TaskHall />}></Route>
      <Route path="assign" element={<TaskAssign />}>
        <Route path=":taskUid" element={<TaskAssign />}></Route>
      </Route>

      <Route path="*" element={<Navigate to="/admin/home" />}></Route>
    </Routes>
  );
};
