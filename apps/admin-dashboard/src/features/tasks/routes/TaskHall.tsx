import { Box, Container, Input, InputGroup, InputLeftElement, useToast } from '@chakra-ui/react';
import { debounce } from 'lodash';
import { useRef, useState } from 'react';
import { RiMarkPenLine, RiSearchLine } from 'react-icons/ri';

import { Header } from '@/components/layout/Header';
import { LABEL_ORIGIN } from '@/config';
import { useRequest } from '@/hooks/use-request';
import type { IServicePagination } from '@/types';

import { getTasksByExecutor } from '../api';
import { TasksHallTable } from '../components';
import type { Task } from '../types';

const pathList = [{ name: '任务大厅', path: '#' }];
const operations = [
  {
    name: 'label',
    colorScheme: 'green',
    icon: <RiMarkPenLine />,
    label: '开始标注',
  },
  {
    name: 'modify',
    colorScheme: 'gray',
    icon: <RiMarkPenLine />,
    label: '开始修改',
  },
];
export const TaskHall: React.FC = () => {
  const [queryParams, setQueryParams] = useState<
    Required<IServicePagination> & { claimable: boolean; states: Task['state'][] }
  >({
    page: 0,
    pagesz: 20,
    total: 0,
    claimable: true,
    states: ['ongoing', 'paused'],
  });
  const namePattern = useRef<string>('');

  const { data: taskList, refetch: updateTaskList } = useRequest(getTasksByExecutor, {
    params: {
      ...queryParams,
    },
    enabled: true,
    onSuccess: (data) => {
      const { total } = data || {};
      if (total && total !== 0) {
        setQueryParams((pre) => ({ ...pre, total }));
      }
    },
  });

  const debounceUpdateList = debounce((e) => {
    updateTaskList({
      params: {
        ...queryParams,
        page: 0,
        namePattern: e.target.value,
      },
    });
  }, 500);

  const showToast = useToast({ position: 'top', duration: 3000 });

  const onOperateTask = (type: string, task: Task) => {
    let url = `${LABEL_ORIGIN}/${task.type}/${task.data_type === 'fusion4d' ? 'lane' : 'frame'}?lid=${task.uid}`;

    if (type === 'modify' && taskList?.extras?.[task.uid].has_rejected_jobs) {
      url += `&prefer=1`;
    }

    window.open(url, '_self');
  };
  const onClickAdd = () => {
    if (taskList?.lots.length) {
      onOperateTask('', taskList.lots[0]);
    } else {
      showToast({
        title: '暂无合适任务',
        status: 'info',
      });
    }
  };

  const onChangePage = (currentPage: number, pageSize: number) => {
    updateTaskList({
      params: {
        ...queryParams,
        page: currentPage - 1,
        pagesz: pageSize,
        namePattern: namePattern.current,
      },
    });
  };

  return (
    <Box minH="100%">
      <Header
        title="任务大厅"
        pathList={pathList}
        rightButton={{
          tip: '开始标注',
          icon: <RiMarkPenLine />,
          onClick: onClickAdd,
        }}
      >
        <InputGroup h="2.5rem" size="sm">
          <InputLeftElement pointerEvents="none" children={<RiSearchLine />} />

          <Input
            variant="filled"
            borderRadius="lg"
            placeholder="请输入任务名称"
            onKeyDown={(event) => {
              if (event.key === 'Enter') {
                namePattern.current = (event.target as HTMLInputElement).value;
                updateTaskList({
                  params: {
                    ...queryParams,
                    page: 0,
                    namePattern: namePattern.current,
                  },
                });
              }
            }}
            onChange={debounceUpdateList}
          />
        </InputGroup>
      </Header>
      <Container px="7" py="6">
        <TasksHallTable
          name="wait"
          tasks={taskList?.lots}
          pagination={{
            total: queryParams.total,
            defaultPageSize: queryParams.pagesz,
            onChange: onChangePage,
          }}
          operations={operations}
          onOperateTask={onOperateTask}
        />
      </Container>
    </Box>
  );
};

export default TaskHall;
