/* eslint-disable react-hooks/exhaustive-deps */
import { <PERSON>, <PERSON><PERSON>, Collapse, Container, Flex, Text } from '@chakra-ui/react';
import { useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';

import { Header } from '@/components/layout';
import { HorizontalSteps, StepState } from '@/components/steps';
import { useRequest } from '@/hooks/use-request';

import { getTaskPhasesProgress, restartFinishedTask } from '../api';
import { JobsData, JobsDetail, StepInfo } from '../components';

const pathList = [
  { name: '任务管理', path: '/admin/tasks/manage' },
  { name: '任务数据', path: '#' },
];
type PanelType = 'detail' | 'stat';

export const TaskProgress: React.FC = () => {
  const { taskUid = '' } = useParams();

  const [phaseIndex, setPhaseIndex] = useState(0);
  const [panelType, setPanelType] = useState<PanelType>('stat');

  const { data: progressData, refetch: updateProgress } = useRequest(getTaskPhasesProgress, {
    params: taskUid,
    enabled: true,
  });

  const steps = useMemo(() => {
    if (progressData && Array.isArray(progressData.phasesProgress)) {
      return progressData.phasesProgress.map(({ id, name, finishRate, leftDayNum }, i) => {
        const value = Math.round(finishRate * 100);
        const state: StepState = value === 0 ? 'todo' : value === 100 ? 'done' : 'doing';
        return {
          id,
          name,
          value,
          state,
          leftDayNum,
          currentType: panelType,
          isSelected: i === phaseIndex,
          onClick: (type: PanelType) => {
            setPhaseIndex(i);
            setPanelType(type);
          },
        };
      });
    }
    return [];
  }, [progressData, phaseIndex, panelType]);

  const { refetch: restartTask, isLoading: isRestarting } = useRequest(restartFinishedTask, {
    onSuccess: (_, _opts, showToast) => {
      showToast({ status: 'success', title: `任务【${progressData?.taskName}】已恢复生产流程` });
      window.location.reload();
    },
    onError: (error, options, showToast) => {
      showToast({ status: 'error', title: `任务【${progressData?.taskName}】恢复失败` });
    },
  });

  const canRestart = progressData?.taskState === 'finished';

  return (
    <Box minH="100%">
      <Header title={progressData?.taskName ?? ''} pathList={pathList}></Header>
      {canRestart && (
        <Flex justifyContent={'center'} alignItems={'center'} marginBottom={10}>
          <Button
            colorScheme="yellow"
            disabled={isRestarting}
            size="lg"
            onClick={() =>
              restartTask({
                params: {
                  lotUid: taskUid,
                  toPhase: steps.length,
                },
              })
            }
          >
            重启任务
          </Button>
        </Flex>
      )}

      <Collapse in={steps.length > 0}>
        <Container px="7" py="6" mb="4">
          <HorizontalSteps
            steps={steps}
            TopElement={(steps) => (
              <Text textAlign="center" fontWeight="bold" color="gray.700" mb={0.5}>
                {steps.name}
              </Text>
            )}
            BottomElement={StepInfo}
          />
        </Container>
      </Collapse>

      <Container px="7" pt="6" pb="8">
        {panelType === 'detail' ? (
          <JobsDetail
            taskUid={taskUid}
            phaseIndex={phaseIndex}
            phasesProgress={progressData?.phasesProgress}
            updateProgress={updateProgress}
          />
        ) : (
          <JobsData
            taskUid={taskUid}
            phaseIndex={phaseIndex}
            phasesProgress={progressData?.phasesProgress}
            updateProgress={updateProgress}
          />
        )}
      </Container>
    </Box>
  );
};

export default TaskProgress;
