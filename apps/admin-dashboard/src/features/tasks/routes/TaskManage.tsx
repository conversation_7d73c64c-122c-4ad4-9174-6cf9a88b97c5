import { Box, Input, InputGroup, InputLeftElement } from '@chakra-ui/react';
import { CollapseContainer, SimpleDialog, SimpleDialogProps } from '@forest/simple-ui';
import { debounce } from 'lodash';
import { useCallback, useEffect, useRef, useState } from 'react';
import { RiMenuAddLine, RiSearchLine } from 'react-icons/ri';
import { useNavigate } from 'react-router-dom';

import { Header } from '@/components/layout/Header';
import { useRequest } from '@/hooks/use-request';
import type { IServicePagination } from '@/types';

import { cancelTask, deleteTask, formatTasksRequest, getTaskList, pauseTask, startTask, TaskListResult } from '../api';
import { TasksTable } from '../components';
import type { Task, TaskOperation, TaskShowState } from '../types';

const pathList = [{ name: '任务管理', path: '#' }];
const pagesz = 10;

export const TaskManage: React.FC = () => {
  const [taskList, setTaskList] = useState<TaskListResult | null>(null);
  const [dialogConfig, setDialogConfig] = useState<Omit<SimpleDialogProps, 'isOpen' | 'onCancel'> & { task?: Task }>({
    title: '',
    content: '',
    onConfirm: () => {},
  });
  const [waitParams, setWaitParams] = useState<IServicePagination>({ page: 0, total: 0 });
  const [progressParams, setProgressParams] = useState<IServicePagination>({ page: 0, total: 0 });
  const [endParams, setEndParams] = useState<IServicePagination>({ page: 0, total: 0 });
  const namePattern = useRef<string>('');

  const { data: taskData, refetch: updateTaskList } = useRequest(formatTasksRequest, {
    params: {
      api: getTaskList,
      query: {
        page: [waitParams.page, progressParams.page, endParams.page],
        pagesz,
        showStates: ['wait', 'progress', 'end'],
      },
    },
    enabled: true,
    onSuccess: (data, query: any) => {
      const { wait, progress, end } = data || {};
      const queryPage = query.params.query.page[0];
      if (wait && queryPage === 0) {
        setWaitParams((pre) => {
          return { ...pre, total: wait.total };
        });
      }
      if (progress && queryPage === 0) {
        setProgressParams((pre) => {
          return { ...pre, total: progress.total };
        });
      }
      if (end && queryPage === 0) {
        setEndParams((pre) => {
          return { ...pre, total: end.total };
        });
      }
    },
  });

  const debounceUpdateList = debounce((e) => {
    namePattern.current = e.target.value;
    updateTaskList({
      params: {
        api: getTaskList,
        query: {
          showStates: ['wait', 'progress', 'end'],
          page: [0],
          pagesz,
          namePattern: namePattern.current,
        },
      },
    });
  }, 500);

  const { refetch: onStartTask } = useRequest(startTask, {
    onSuccess: (_, options, showToast) => {
      const { showState = 'unknown', taskName = '' } = options.meta || {};
      const showStates: TaskShowState[] = showState === 'wait' ? ['wait', 'progress'] : [showState as TaskShowState];
      const title = showState === 'wait' ? `任务【${taskName}】已进入生产流程` : `任务【${taskName}】已恢复生产`;
      showToast({ status: 'success', title });
      updateTaskList({
        params: {
          api: getTaskList,
          query: {
            pagesz,
            page: [0, 0],
            showStates,
            namePattern: namePattern.current,
          },
        },
      });
    },
  });
  const { refetch: onPauseTask } = useRequest(pauseTask, {
    onSuccess: (_, options, showToast) => {
      showToast({ status: 'success', title: '已暂停' });
      const { showState = 'unknown' } = options.meta || {};
      updateTaskList({
        params: {
          api: getTaskList,
          query: {
            pagesz,
            page: [0],
            showStates: [showState as TaskShowState],
            namePattern: namePattern.current,
          },
        },
      });
    },
  });
  const { refetch: onCancelTask } = useRequest(cancelTask, {
    onSuccess: (_, options, showToast) => {
      showToast({ status: 'success', title: '已终止' });
      updateTaskList({
        params: {
          api: getTaskList,
          query: {
            pagesz,
            page: [0, 0],
            showStates: ['progress', 'end'],
            namePattern: namePattern.current,
          },
        },
      });
    },
  });
  const { refetch: onDeleteTask } = useRequest(deleteTask, {
    onSuccess: (_, options, showToast) => {
      showToast({ status: 'success', title: '已删除' });
      const { showState = 'unknown' } = options.meta || {};
      updateTaskList({
        params: {
          api: getTaskList,
          query: {
            pagesz,
            page: [0],
            showStates: [showState as TaskShowState],
            namePattern: namePattern.current,
          },
        },
      });
    },
  });
  const onChangePage = (type: TaskShowState) => {
    return (currentPage: number) => {
      const page = currentPage - 1;

      updateTaskList({
        params: {
          api: getTaskList,
          query: {
            showStates: [type],
            page: [page],
            pagesz,
            namePattern: namePattern.current,
          },
        },
      });
      if (type === 'wait') {
        setWaitParams((preState) => {
          return { ...preState, page };
        });
      } else if (type === 'progress') {
        setProgressParams((preState) => {
          return { ...preState, page };
        });
      } else {
        setEndParams((preState) => {
          return { ...preState, page };
        });
      }
    };
  };

  useEffect(() => {
    if (taskData) {
      if (taskList) {
        setTaskList({
          ...taskList,
          ...taskData,
        });
      } else {
        setTaskList(taskData);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [taskData]);

  const navigate = useNavigate();

  const onClickAdd = () => navigate('/admin/tasks/add');

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const onCloseDialog = useCallback(() => setDialogConfig({ ...dialogConfig, task: undefined }), []);

  const onOperateTask = useCallback(
    (type: TaskOperation, task: Task, showState: TaskShowState) => {
      switch (type) {
        case 'config':
          navigate(`/admin/tasks/detail/${task.uid}`);
          break;
        case 'progress':
          navigate(`/admin/tasks/progress/${task.uid}`);
          break;
        case 'start':
          onStartTask({ params: task.uid, meta: { showState, taskName: task.name } });
          break;
        case 'pause':
          setDialogConfig({
            task,
            title: '暂停任务',
            content: `您希望暂停任务【${task.name}】吗？暂停可能会影响任务完成时间`,
            confirmText: '暂停',
            confirmButtonProps: { colorScheme: 'orange' },
            onConfirm: () => {
              onPauseTask({ params: task.uid, meta: { showState } });
              onCloseDialog();
            },
          });
          break;
        case 'cancel':
          setDialogConfig({
            task,
            title: '终止任务',
            content: `您希望终止任务【${task.name}】吗？终止后无法恢复`,
            confirmText: '终止',
            onConfirm: () => {
              onCancelTask({ params: task.uid, meta: { showState } });
              onCloseDialog();
            },
          });
          break;
        case 'delete':
          setDialogConfig({
            task,
            title: '删除任务',
            content: `您希望删除任务【${task.name}】吗？删除后无法恢复`,
            confirmText: '删除',
            onConfirm: () => {
              onDeleteTask({ params: task.uid, meta: { showState } });
              onCloseDialog();
            },
          });
          break;
        case 'copy':
          navigate(`/admin/tasks/add/${task.uid}`);
          break;
        default:
          break;
      }
    },
    [navigate, onStartTask, onPauseTask, onCloseDialog, onCancelTask, onDeleteTask]
  );

  return (
    <Box minH="100%">
      <Header
        title="任务管理"
        pathList={pathList}
        rightButton={{
          tip: '新建任务',
          icon: <RiMenuAddLine />,
          onClick: onClickAdd,
        }}
      >
        <InputGroup h="2.5rem" size="sm">
          <InputLeftElement pointerEvents="none" children={<RiSearchLine />} />
          <Input
            variant="filled"
            borderRadius="lg"
            placeholder="请输入任务名称"
            onKeyDown={(event) => {
              if (event.key === 'Enter') {
                updateTaskList({
                  params: {
                    api: getTaskList,
                    query: {
                      showStates: ['wait', 'progress', 'end'],
                      page: [0],
                      pagesz,
                      namePattern: (event.target as HTMLInputElement).value,
                    },
                  },
                });
              }
            }}
            onChange={debounceUpdateList}
          />
        </InputGroup>
      </Header>
      <CollapseContainer title={`待启动（${waitParams.total}）`} startingHeight="6rem" mb="4">
        <TasksTable
          name="wait"
          tasks={taskList?.wait?.lots}
          pagination={{
            total: waitParams.total,
            defaultPageSize: pagesz,
            onChange: onChangePage('wait'),
          }}
          onOperateTask={onOperateTask}
        />
      </CollapseContainer>
      <CollapseContainer title={`生产中（${progressParams.total}）`} startingHeight="26rem" mb="4">
        <TasksTable
          name="progress"
          tasks={taskList?.progress?.lots}
          pagination={{
            total: progressParams.total,
            defaultPageSize: pagesz,
            onChange: onChangePage('progress'),
          }}
          onOperateTask={onOperateTask}
        />
      </CollapseContainer>
      <CollapseContainer title={`已结束（${endParams.total}）`} startingHeight="26rem">
        <TasksTable
          name="end"
          tasks={taskList?.end?.lots}
          pagination={{
            total: endParams.total,
            defaultPageSize: pagesz,
            onChange: onChangePage('end'),
          }}
          onOperateTask={onOperateTask}
        />
      </CollapseContainer>
      <SimpleDialog isOpen={Boolean(dialogConfig?.task?.uid)} onCancel={onCloseDialog} {...dialogConfig}></SimpleDialog>
    </Box>
  );
};

export default TaskManage;
