import { Box, Input, InputGroup, InputLeftElement, useToast } from '@chakra-ui/react';
import { useState } from 'react';
import { RiSaveLine, RiSearchLine } from 'react-icons/ri';
import { useParams } from 'react-router-dom';

import { Header } from '@/components/layout';
import { useRequest } from '@/hooks/use-request';

import { assignTaskExecteam, getTask } from '../api';
import { TaskConfig } from '../components';
import { taskStore } from '../stores';

const pathList = [
  { name: '任务管理', path: '/admin/tasks/manage' },
  { name: '任务配置', path: '#' },
];
export const TaskDetail = () => {
  const [isSubmitting, setSubmitting] = useState(false);

  const urlParams = useParams();
  const showToast = useToast({ position: 'top', duration: 1000 });

  const {
    refetch: flushTask,
    data: originTask,
    isLoading: isDisabled,
    error,
  } = useRequest(getTask, {
    params: { uid: urlParams.taskUid! },
    enabled: true,
    onSuccess: (data) => {
      taskStore.flush(data, 'edit');
    },
  });

  const { refetch: updateTaskExecteam } = useRequest(assignTaskExecteam, {
    onSuccess: (_, { meta }, showToast) => {
      flushTask();
      showToast({
        title: `已更新任务${meta?.name || ''}的执行团队`,
        status: 'success',
      });
    },
  });

  const onClickSave = () => {
    if (isSubmitting) {
      return;
    }
    setSubmitting(true);
    taskStore.startSubmit(
      (updatedTask) => {
        const { uid = '', phases = [] } = updatedTask;
        const execteams: {
          phase: number;
          execteams: Array<{
            execteam: string;
          }>;
        }[] = [];

        phases.forEach((phase, i) => {
          if (originTask?.phases[i].execteams[0].execteam !== phase.execteams[0].execteam) {
            execteams.push({
              phase: i + 1,
              execteams: phase.execteams,
            });
          }
        });

        if (execteams.length > 0) {
          updateTaskExecteam({
            params: {
              uid,
              execteams,
            },
          });
        } else {
          showToast({
            title: '未检测到需要更新的内容',
            status: 'info',
          });
        }
        setSubmitting(false);
      },
      { uid: originTask?.uid }
    );
  };

  if (error) {
    return <div>未找到任务</div>;
  }

  return (
    <Box minH="100%">
      <Header
        title={originTask?.name ?? ''}
        pathList={pathList}
        rightButton={{
          isDisabled,
          isLoading: isSubmitting,
          tip: '更新任务配置',
          icon: <RiSaveLine />,
          onClick: onClickSave,
        }}
      >
        <InputGroup h="2.5rem" size="sm">
          <InputLeftElement pointerEvents="none" children={<RiSearchLine />} />
          <Input variant="filled" borderRadius="lg" placeholder="请输入任务名称" />
        </InputGroup>
      </Header>
      <TaskConfig />
    </Box>
  );
};

export default TaskDetail;
