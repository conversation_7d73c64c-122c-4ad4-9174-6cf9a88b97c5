import { Box, Input, InputGroup, InputLeftElement } from '@chakra-ui/react';
import { SimpleDialog } from '@forest/simple-ui';
import { useEffect, useState } from 'react';
import { RiSaveLine, RiSearchLine } from 'react-icons/ri';
import { useNavigate, useParams } from 'react-router-dom';

import { Header } from '@/components/layout';
import { useRequest } from '@/hooks/use-request';

import { cloneTask, createTask, getTask } from '../api';
import { TaskConfig } from '../components';
import { labelFlowStore, phaseStore } from '../components/ProgressPhases/stores';
import { taskStore } from '../stores';
import { Task } from '../types';

const pathList = [
  { name: '任务管理', path: '/admin/tasks/manage' },
  { name: '新建任务', path: '#' },
];

const isSamePhaseExecteams = (oldPhases: Task['phases'], newPhases: Task['phases']) => {
  if (oldPhases.length !== newPhases.length) return false;
  for (let i = 0, len = newPhases.length; i < len; i++) {
    if (oldPhases[i].execteams[0].execteam !== newPhases[i].execteams[0].execteam) return false;
  }
  return true;
};

export const TaskAdd = () => {
  const [isShow, setIsShow] = useState<boolean>(false);
  const [isLoading, setLoading] = useState(false);
  const { taskUid } = useParams();

  const navigate = useNavigate();

  const {
    refetch: flushTask,
    isLoading: isDisabled,
    data: originTask,
  } = useRequest(getTask, {
    params: { uid: taskUid! },
    onSuccess: (data) => {
      taskStore.flush(data, 'new');
    },
  });

  useEffect(() => {
    if (taskUid) {
      flushTask();
    } else {
      taskStore.flush(null, 'new');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const { refetch: createRequest } = useRequest(createTask, {
    onSuccess: (_, { params }, showToast) => {
      showToast({
        title: `已成功创建任务${params?.name}，现为您跳转到任务管理页面`,
        status: 'success',
      });
      setTimeout(() => {
        navigate('/admin/tasks/manage');
      }, 1500);
    },
    onError: (error, _, showToast) => {
      showToast({ status: 'error', title: error.message });
      setLoading(false);
    },
  });

  const { refetch: cloneRequest } = useRequest(cloneTask, {
    onSuccess: (data, _, showToast) => {
      showToast({
        title: `已成功复制任务${data.name}，现为您跳转到任务管理页面`,
        status: 'success',
      });
      setTimeout(() => {
        navigate('/admin/tasks/manage');
      }, 1500);
    },
    onError: (error, _, showToast) => {
      showToast({ status: 'error', title: error.message });
      setLoading(false);
    },
  });

  const handleCloneTask = (isCloneAssign: boolean) => {
    // 更好的做法是弹出 复制人员配置 的弹窗应该在校验完成后再弹窗，重构后添加
    isShow && setIsShow(false);
    setLoading(true);
    // type 不用传给后端，type 值在复制任务的时候必须是一致的
    taskStore.startSubmit(({ type, ...newData }) => {
      cloneRequest({
        params: {
          sourceUid: taskUid!,
          updates: {
            lot: newData,
            fields: Object.keys(newData),
          },
          copy_executors: isCloneAssign,
        },
      });
    });
  };

  const onClickSave = () => {
    if (taskUid && originTask) {
      const labelFlow = labelFlowStore.exportData();
      const { phases: reviewPhases } = phaseStore.exportData();
      const newPhases = [...(labelFlow?.phases ?? []), ...reviewPhases];
      // 比较前后指定的团队，如果指定团队发生改变，则不用复制人员分配
      if (isSamePhaseExecteams(originTask.phases, newPhases)) return setIsShow(true);
      return handleCloneTask(false);
    } else {
      setLoading(true);
      taskStore.startSubmit((data) =>
        createRequest({
          params: data,
        })
      );
    }
  };

  return (
    <Box minH="100%">
      <Header
        title={taskUid ? '复制任务' : '新建任务'}
        pathList={pathList}
        rightButton={{
          isDisabled,
          isLoading,
          tip: '保存',
          icon: <RiSaveLine />,
          onClick: onClickSave,
        }}
      >
        <InputGroup h="2.5rem" size="sm">
          <InputLeftElement pointerEvents="none" children={<RiSearchLine />} />
          <Input variant="filled" borderRadius="lg" placeholder="请输入任务名称" />
        </InputGroup>
      </Header>
      <TaskConfig onError={() => setLoading(false)} />
      <SimpleDialog
        isOpen={isShow}
        title="复制任务"
        content="是否同时复制人员分配"
        cancelText="否"
        confirmText="是"
        onCancel={() => handleCloneTask(false)}
        onConfirm={() => handleCloneTask(true)}
      />
    </Box>
  );
};

export default TaskAdd;
