import type { anno_v1_Attr as SAttr, anno_v1_Lot as Lot, iam_v1_BaseUser as BaseUser } from '@/service/anno/v1';

export type {
  anno_v1_CloneLotRequest as CloneLotProps,
  anno_v1_CreateLotRequest as CreateLotProps,
  anno_v1_Job as Job,
  anno_v1_Label as Label,
  anno_v1_ListLotReply as ListLotResult,
  anno_v1_Lotphase as Taskphase,
} from '@/service/anno/v1';
export type { annofeed_v1_Data as Data } from '@/service/annofeed/v1';

export type { BaseUser };

export interface Attr extends SAttr {
  required?: boolean;
  choiceString?: string;
  default: string;
}
export interface Task extends Lot {
  demandTeams?: BaseUser[];
}

export type TaskShowState = 'wait' | 'progress' | 'end' | 'unknown';
export type TaskOperation = 'read' | 'edit' | 'start' | 'pause' | 'cancel' | 'delete' | 'config' | 'progress' | 'copy';

export type { anno_v1_Lot_Range as LotRange } from '@/service/anno/v1';
