import { Box, Button, CircularProgress, CircularProgressLabel } from '@chakra-ui/react';

export interface StepInfoProps {
  name: string;
  value: number;
  leftDayNum: number;
  currentType: string;
  isSelected?: boolean;
  onClick: (type: 'detail' | 'stat') => void;
}
const progressColor = ['red.400', 'yellow.300', 'green.400', 'green.400'];
export const StepInfo = ({ value, currentType, leftDayNum, isSelected = false, onClick }: StepInfoProps) => {
  const colorId = Math.max(0, Math.floor(value / 20) - 2);

  return (
    <Box textAlign="center" mt={2.5}>
      <CircularProgress value={value} color={progressColor[colorId]} size="110px" thickness="8px" capIsRound={true}>
        <CircularProgressLabel fontWeight="bold">
          <Box transform="scale(0.9)">
            <Box fontSize="sm">{value}%</Box>
            <Box fontSize="xs" color="gray.500">
              {leftDayNum < 0 ? '评估中' : leftDayNum === 0 ? '已完成' : `预计${leftDayNum}天完成`}
            </Box>
          </Box>
        </CircularProgressLabel>
      </CircularProgress>
      <Box mt={2}>
        <Button
          size="sm"
          variant="outline"
          colorScheme="logo"
          aria-selected={isSelected && currentType === 'detail'}
          mr={1}
          sx={{
            '&[aria-selected=true]': {
              bg: 'logo.500',
              color: 'white',
            },
          }}
          onClick={() => onClick('detail')}
        >
          详情
        </Button>
        <Button
          size="sm"
          variant="outline"
          colorScheme="logo"
          aria-selected={isSelected && currentType === 'stat'}
          sx={{
            '&[aria-selected=true]': {
              bg: 'logo.500',
              color: 'white',
            },
          }}
          onClick={() => onClick('stat')}
        >
          统计
        </Button>
      </Box>
    </Box>
  );
};

export default StepInfo;
