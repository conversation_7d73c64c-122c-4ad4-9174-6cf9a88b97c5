import { Box, Heading, HStack, Tag } from '@chakra-ui/react';
import { Column, IPagination, Table, TipButton } from '@forest/simple-ui';
import dayjs from 'dayjs';
import { useMemo } from 'react';

import { DATA_TYPE_MAP, TASK_TYPE_MAP } from '../config';
import type { Task, TaskShowState } from '../types';

const iconButtonStyles = {
  variant: 'outline',
  isRound: true,
  fontSize: '16px',
  size: 'sm',
};
const TASK_STATE_MAP: Record<
  Task['state'],
  {
    text: string;
    color: string;
  }
> = {
  initializing: {
    text: '处理中',
    color: 'blackAlpha',
  },
  unstart: {
    text: '待启动',
    color: 'green',
  },
  ongoing: {
    text: '生产中',
    color: 'logo',
  },
  paused: {
    text: '待恢复',
    color: 'cyan',
  },
  finished: {
    text: '已完成',
    color: 'teal',
  },
  canceled: {
    text: '已终止',
    color: 'yellow',
  },
  unspecified: {
    text: '未知',
    color: 'gray',
  },
};
export interface TasksHallTableProps<T> {
  name: TaskShowState;
  tasks?: Task[];
  pagination: IPagination;
  operations?: Array<{
    name: T;
    colorScheme: string;
    icon: JSX.Element;
    label: string;
  }>;
  onOperateTask: (type: T, task: Task, showState: TaskShowState) => void;
}
export const TasksHallTable = <T extends string>({
  name,
  tasks,
  pagination,
  operations = [],
  onOperateTask,
}: TasksHallTableProps<T>) => {
  const columns: Column<Task>[] = useMemo(
    () => [
      {
        Header: '任务名称',
        accessor: 'name',
        Cell: ({ row }) => {
          const {
            original: { name, uid },
          } = row;
          return (
            <Box width="12rem">
              <Heading size="sm" color="brand.500">
                {name}
              </Heading>
              <Box color="gray.600" fontSize="0.8rem" mt="0.5">
                ID: {uid}
              </Box>
            </Box>
          );
        },
      },
      {
        Header: '任务职责',
        accessor: 'phases',
        Cell: ({ value }) => {
          return (
            <Box fontSize="0.9rem" color="gray.600">
              {value.reduce((prev, phase) => prev + phase.name + ', ', '').slice(0, -2)}
            </Box>
          );
        },
      },
      {
        Header: '任务类型',
        accessor: 'type',
        Cell: ({ value }) => {
          return <Tag colorScheme="gray">{TASK_TYPE_MAP[value]}</Tag>;
        },
      },
      {
        Header: '数据类型',
        accessor: 'data_type',
        Cell: ({ value, row }) => {
          const {
            original: { is_frame_series = false },
          } = row;
          return value ? (
            <Box width="4rem">{DATA_TYPE_MAP[value] + (is_frame_series ? ' - 连续帧' : '')}</Box>
          ) : (
            <Box>无</Box>
          );
        },
      },
      {
        Header: '数据量',
        accessor: 'data_size',
        Cell: ({ value }) => {
          return (
            <Box fontSize="0.9rem" color="gray.600">
              {value}
            </Box>
          );
        },
      },
      {
        Header: '创建时间',
        accessor: 'created_at',
        Cell: ({ value }) => (
          <Box as="span" whiteSpace="pre" color="gray.600" fontSize="0.9rem">
            {dayjs(value).format('YYYY / MM / DD\nHH:mm')}
          </Box>
        ),
      },
      {
        Header: '预期完成时间',
        accessor: 'exp_end_time',
        Cell: ({ value }) => {
          const expired = dayjs().isAfter(dayjs(value));
          const mayExpire = dayjs().isAfter(dayjs(value).subtract(72, 'hour'));
          const status = expired
            ? { color: 'red.500', text: '（已过期）' }
            : mayExpire
            ? { color: 'orange.500', text: '（快过期）' }
            : { color: 'green.500', text: '' };
          return (
            <Box whiteSpace="pre" color={status.color} fontSize="0.9rem">
              {dayjs(value).format('YYYY / MM / DD\nHH:mm')}
              {status.text}
            </Box>
          );
        },
      },
      {
        Header: '任务状态',
        accessor: 'state',
        Cell: ({ value }) => {
          const { color, text } = TASK_STATE_MAP[value];
          return (
            <Tag variant="solid" colorScheme={color}>
              {text}
            </Tag>
          );
        },
      },
      {
        Header: '',
        accessor: 'uid',
        Cell: ({ row }) => {
          const { original: task } = row;
          return (
            <HStack justify="right" spacing="2.5">
              {operations.map(({ name: optName, icon, label, colorScheme }) => (
                <TipButton
                  key={optName}
                  {...iconButtonStyles}
                  colorScheme={colorScheme}
                  icon={icon}
                  aria-label={optName === 'label' && task.state !== 'ongoing' ? '当前任务已暂停' : label}
                  isDisabled={optName === 'label' && task.state !== 'ongoing'}
                  onClick={() => onOperateTask(optName, task, name)}
                ></TipButton>
              ))}
            </HStack>
          );
        },
      },
    ],
    [name, operations, onOperateTask]
  );
  const data = useMemo(() => (Array.isArray(tasks) ? tasks : []), [tasks]);

  return <Table data={data} columns={columns} isLoading={!Array.isArray(tasks)} pagination={pagination} />;
};
