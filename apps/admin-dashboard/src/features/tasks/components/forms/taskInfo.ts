import { ArrayTable, FormGrid, FormItem, FormLayout, Input, Select, Switch } from '@forest/formily';
import type { ISchema } from '@formily/react';

import { TASK_TYPE_MAP } from '../../config';
import { Task } from '../../types';

/** 任务基本信息 */
const config = {
  components: {
    FormGrid,
    FormLayout,
    FormItem,
    Input,
    Select,
    Switch,
    ArrayTable,
  },
};

const types: Array<Task['type']> = [
  'annotate',
  // 'segment_instance', 'segment_semantic', 'segment_panoptic'
];
const typeOptions: Array<{ label: string; value: Task['type'] }> = types.map((value) => ({
  label: TASK_TYPE_MAP[value],
  value,
}));

const componentStyle = {
  size: 'sm',
  width: '230px',
};
const schema: ISchema = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        size: 'sm',
        labelWidth: '110px',
      },
      properties: {
        grid: {
          type: 'void',
          'x-component': 'FormGrid',
          'x-component-props': {
            maxColumns: [1, 2],
            rowGap: 0,
          },
          properties: {
            name: {
              type: 'string',
              title: '当前任务名称',
              required: true,
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              'x-component-props': {
                ...componentStyle,
                placeholder: '请输入当前任务名称',
              },
            },
            type: {
              type: 'string',
              title: '任务类型',
              required: true,
              'x-decorator': 'FormItem',
              enum: typeOptions,
              'x-component': 'Select',
              'x-component-props': {
                ...componentStyle,
                placeholder: '请选择任务类型',
              },
            },
            exp_end_time: {
              type: 'string',
              title: '预计完成时间',
              required: true,
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              'x-component-props': {
                ...componentStyle,
                type: 'datetime-local',
                placeholder: '',
              },
            },
            projected_editable: {
              type: 'boolean',
              title: '编辑矩形',
              description: '23D融合任务中的映射矩形框能否被手动新建或修改',
              required: false,
              'x-decorator': 'FormItem',
              'x-component': 'Switch',
              'x-component-props': {
                ...componentStyle,
              },
            },
            segmentation_3d_enabled: {
              type: 'boolean',
              title: '3D点云分割',
              description: '是否激活3D点云语义分割',
              required: false,
              'x-decorator': 'FormItem',
              'x-component': 'Switch',
              'x-component-props': {
                ...componentStyle,
              },
            },
            ranges: {
              type: 'array',
              'x-decorator': 'FormItem',
              'x-decorator-props': {
                gridSpan: 2,
              },
              'x-component': 'ArrayTable',
              'x-component-props': {
                pagination: { pageSize: 10 },
                scroll: { x: '100%' },
              },
              items: {
                type: 'object',
                properties: {
                  column1: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': { Header: '标注范围' },
                    properties: {
                      shape: {
                        type: 'string',
                        required: true,
                        'x-decorator': 'FormItem',
                        'x-component': 'Select',
                        'x-component-props': {
                          ...componentStyle,
                          placeholder: '请选择形状',
                          options: [
                            {
                              label: '圆形',
                              value: 'circle',
                            },
                            {
                              label: '矩形',
                              value: 'rectangle',
                            },
                          ],
                        },
                      },
                    },
                  },
                  column2: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': { Header: '形状参数' },
                    properties: {
                      ranges_data: {
                        type: 'string',
                        description: '圆填半径，矩形填宽/高(米)',
                        // description: '填半径(米)',
                        required: true,
                        'x-decorator': 'FormItem',
                        'x-component': 'Input',
                        'x-component-props': {
                          ...componentStyle,
                          placeholder: '填数字，以/分割',
                          // placeholder: '填数字',
                        },
                        pattern: '^(\\d+(\\.\\d+)?(/\\d+(\\.\\d+)?)?)?$',
                      },
                    },
                  },
                  column3: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': { Header: '高度最大值' },
                    properties: {
                      zrange_max: {
                        type: 'number',
                        required: false,
                        format: 'number',
                        'x-decorator': 'FormItem',
                        'x-component': 'Input',
                        'x-component-props': {
                          ...componentStyle,
                          suffix: '米',
                          placeholder: '标注范围高度的最大值',
                        },
                        'x-reactions': [
                          {
                            dependencies: ['.zrange_min'],
                            fulfill: {
                              schema: {
                                required: '{{!!$deps[0]}}',
                              },
                            },
                          },
                        ],
                      },
                    },
                  },
                  column4: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': { Header: '高度最小值' },
                    properties: {
                      zrange_min: {
                        type: 'number',
                        required: false,
                        format: 'number',
                        'x-decorator': 'FormItem',
                        'x-component': 'Input',
                        'x-component-props': {
                          ...componentStyle,
                          suffix: '米',
                          placeholder: '标注范围高度的最小值',
                        },
                        'x-reactions': [
                          {
                            dependencies: ['.zrange_max'],
                            fulfill: {
                              schema: {
                                required: '{{!!$deps[0]}}',
                              },
                            },
                          },
                        ],
                      },
                    },
                  },
                  opt: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': {
                      Header: '操作',
                      fixed: 'right',
                    },
                    properties: {
                      item: {
                        type: 'void',
                        'x-component': 'FormItem',
                        properties: {
                          copy: {
                            type: 'void',
                            'x-component': 'ArrayTable.Copy',
                          },
                          remove: {
                            type: 'void',
                            'x-component': 'ArrayTable.Remove',
                          },
                        },
                      },
                    },
                  },
                },
              },
              properties: {
                add: {
                  type: 'void',
                  'x-component': 'ArrayTable.Addition',
                  'x-component-props': {
                    colorScheme: 'brand',
                  },
                  title: '添加标注范围',
                },
              },
            },
            pre_box: {
              type: 'array',
              'x-decorator': 'FormItem',
              'x-decorator-props': {
                gridSpan: 2,
              },
              'x-component': 'ArrayTable',
              'x-component-props': {
                pagination: { pageSize: 50 },
                scroll: { x: '100%' },
              },
              items: {
                type: 'object',
                properties: {
                  column1: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': { Header: '预置框名称' },
                    properties: {
                      name: {
                        type: 'string',
                        required: true,
                        'x-decorator': 'FormItem',
                        'x-component': 'Input',
                        'x-component-props': {
                          ...componentStyle,
                          placeholder: '请输入预置框名称',
                        },
                        pattern: '^[\u4e00-\u9fa5a-zA-Z0-9]+$', // 允许汉字、字母或数字，可自由组合，但不允许各种符号
                      },
                    },
                  },
                  column2: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': { Header: '长度(M)' },
                    properties: {
                      length: {
                        type: 'string',
                        required: true,
                        'x-decorator': 'FormItem',
                        'x-component': 'Input',
                        'x-component-props': {
                          ...componentStyle,
                          suffix: '米',
                          placeholder: '预置框长度',
                        },
                        'x-validator': [
                          {
                            pattern: '^\\d+(\\.\\d{1,4})?$',
                            message: '请输入最多四位小数的数字',
                          },
                        ],
                      },
                    },
                  },
                  column3: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': { Header: '宽度(M)' },
                    properties: {
                      width: {
                        type: 'number',
                        required: true,
                        'x-decorator': 'FormItem',
                        'x-component': 'Input',
                        'x-component-props': {
                          ...componentStyle,
                          suffix: '米',
                          placeholder: '预置框宽度',
                        },
                        'x-validator': [
                          {
                            pattern: '^\\d+(\\.\\d{1,4})?$',
                            message: '请输入最多四位小数的数字',
                          },
                        ],
                      },
                    },
                  },
                  column4: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': { Header: '高度(M)' },
                    properties: {
                      height: {
                        type: 'number',
                        required: true,
                        'x-decorator': 'FormItem',
                        'x-component': 'Input',
                        'x-component-props': {
                          ...componentStyle,
                          suffix: '米',
                          placeholder: '预置框高度',
                        },
                        'x-validator': [
                          {
                            pattern: '^\\d+(\\.\\d{1,4})?$',
                            message: '请输入最多四位小数的数字',
                          },
                        ],
                      },
                    },
                  },
                  opt: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': {
                      Header: '操作',
                      fixed: 'right',
                    },
                    properties: {
                      item: {
                        type: 'void',
                        'x-component': 'FormItem',
                        properties: {
                          copy: {
                            type: 'void',
                            'x-component': 'ArrayTable.Copy',
                          },
                          remove: {
                            type: 'void',
                            'x-component': 'ArrayTable.Remove',
                          },
                        },
                      },
                    },
                  },
                },
              },
              properties: {
                add: {
                  type: 'void',
                  'x-component': 'ArrayTable.Addition',
                  'x-component-props': {
                    colorScheme: 'brand',
                  },
                  title: '添加预置框',
                },
              },
            },
          },
        },
      },
    },
  },
};

export const taskInfoSchema = { config, schema };
