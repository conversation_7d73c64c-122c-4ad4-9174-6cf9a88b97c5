import { FormItem, FormLayout, Input, Select } from '@forest/formily';
import type { ISchema } from '@formily/react';

import type { Job } from '../../types';

export type { OptionProps } from '@forest/formily';

/** 任务包筛选信息 */
const config = {
  components: {
    FormLayout,
    FormItem,
    Input,
    Select,
  },
};

export const defaultExecteamOption = {
  label: '最近处理团队无指定',
  value: 'unspecified',
};
export const defaultExecutorOption = {
  label: '最近处理人无指定',
  value: 'unspecified',
};
export interface jobFilterFormProps {
  uid?: string;
  execteam?: string;
  executor?: string;
  filterState?: Job['state'];
}
const componentStyle = {
  size: 'sm',
  width: '190px',
  marginRight: '4px',
};
const schema: ISchema = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        size: 'sm',
        layout: 'inline',
      },
      properties: {
        uid: {
          type: 'string',
          title: '精准筛选【优先】',
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            labelStyle: {
              color: 'brand.500',
            },
          },
          'x-component': 'Input',
          'x-component-props': {
            ...componentStyle,
            placeholder: '请输入任务包ID',
          },
          pattern: '^[A-Za-z0-9]+$',
        },
        execteam: {
          type: 'string',
          title: '条件筛选',
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            labelStyle: {
              marginLeft: '16px',
              color: 'brand.500',
            },
          },
          enum: [defaultExecteamOption],
          'x-component': 'Select',
          'x-component-props': {
            ...componentStyle,
            placeholder: '最近处理团队无指定',
          },
        },
        executor: {
          type: 'string',
          'x-decorator': 'FormItem',
          enum: [defaultExecutorOption],
          'x-component': 'Select',
          'x-component-props': {
            ...componentStyle,
            placeholder: '最近处理人无指定',
          },
        },
        filterState: {
          type: 'string',
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            ...componentStyle,
            placeholder: '数据状态无指定',
          },
        },
      },
    },
  },
};

export const jobFilterSchema = { config, schema };
