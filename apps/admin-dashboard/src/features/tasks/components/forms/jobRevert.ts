import { Checkbox, FormItem, FormLayout, Select } from '@forest/formily';
import type { ISchema } from '@formily/react';

export type { OptionProps } from '@forest/formily';

/** 任务包打回配置 */
const config = {
  components: {
    FormLayout,
    FormItem,
    Checkbox,
    Select,
  },
};

export interface jobRevertFormProps {
  toPhase: number;
  keepAnnos: boolean;
  keepComments?: boolean;
  toPreviousExecutor: boolean;
}
const checkboxStyle = {
  fontWeight: 'bold',
  color: 'gray.600',
};
const schema: ISchema = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        size: 'sm',
      },
      properties: {
        toPhase: {
          type: 'number',
          title: '移动至',
          required: true,
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            labelStyle: {
              color: 'brand.500',
            },
            layoutStyle: {
              alignItems: 'center',
              marginBottom: 5,
            },
          },
          'x-component': 'Select',
          'x-component-props': {
            size: 'xs',
            width: '160px',
          },
          // 如果是打回到当前的阶段，则不能打回到指定人
          'x-reactions': {
            target: 'toPreviousExecutor',
            effects: ['onFieldMount', 'onFieldValueChange'],
            when: '{{Array.isArray($self.dataSource) && $self.value === $self.dataSource.length}}',
            fulfill: {
              state: {
                value: false,
                disabled: true,
              },
            },
            otherwise: {
              state: {
                disabled: false,
              },
            },
          },
        },
        keepAnnos: {
          type: 'boolean',
          default: true,
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            layoutStyle: {
              marginBottom: 4,
            },
          },
          'x-component': 'Checkbox',
          'x-component-props': {
            ...checkboxStyle,
            children: '保留标注结果',
          },
        },
        keepComments: {
          type: 'boolean',
          default: true,
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            layoutStyle: {
              marginBottom: 4,
              marginLeft: 2,
            },
          },
          'x-component': 'Checkbox',
          'x-component-props': {
            fontWeight: 'bold',
            color: 'gray.500',
            children: '保留批注结果',
          },
          'x-reactions': {
            dependencies: ['keepAnnos'],
            fulfill: {
              schema: {
                'x-visible': '{{$deps[0] === true}}',
              },
            },
          },
        },
        toPreviousExecutor: {
          type: 'boolean',
          default: true,
          'x-decorator': 'FormItem',
          'x-component': 'Checkbox',
          'x-component-props': {
            ...checkboxStyle,
            children: '原处理人',
          },
        },
      },
    },
  },
};

export const jobRevertSchema = { config, schema };
