import { FormItem, Input } from '@forest/formily';
import type { ISchema } from '@formily/react';

/** 任务导出配置 */
const config = {
  components: {
    FormItem,
    Input,
  },
};
const schema: ISchema = {
  type: 'object',
  properties: {
    out: {
      type: 'string',
      title: '',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        layout: 'vertical',
        labelStyle: {
          height: 0,
        },
        colon: false,
      },
      'x-component': 'Input.TextArea',
      'x-component-props': {
        size: 'sm',
        style: {
          width: '100%',
          height: 200,
        },
        resize: 'none',
      },
    },
  },
};

export const taskExportSchema = { config, schema };
