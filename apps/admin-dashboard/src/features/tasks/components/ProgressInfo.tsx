import { Box } from '@chakra-ui/react';
import { useObservable } from '@forest/hooks';
import { createForm, onFormValuesChange } from '@formily/core';
import { createSchemaField, FormProvider } from '@formily/react';
import dayjs from 'dayjs';
import { useEffect, useRef } from 'react';

import { objHasValue, strTransArr } from '@/utils';

import { taskStore } from '../stores';
import type { LotRange, Task } from '../types';
import { taskInfoSchema } from './forms';

const TaskInfoSchemaField = createSchemaField(taskInfoSchema.config);
const taskInfoForm = createForm<Pick<Task, 'name' | 'type' | 'exp_end_time'>>({
  effects: () => {
    onFormValuesChange((forms) => {
      taskInfoForm.setFieldState('ranges.add', (state) => {
        state.display = forms.getFormState().values.ranges?.length > 0 ? 'none' : 'visible';
      });
    });
  },
});

export const ProgressInfo = () => {
  const { task, state, mode } = useObservable(taskStore.subject, taskStore.subject.value);
  const toolCfgRef = useRef<Task['tool_cfg']>();

  const formatFormData = (formData: any) => {
    const {
      name,
      type,
      exp_end_time,
      projected_editable,
      segmentation_3d_enabled,
      ranges: originRanges,
      pre_box,
    } = formData;
    const ranges = (originRanges as any[])
      ?.map(({ shape, ranges_data, zrange_max, zrange_min }) => {
        const min = Number(zrange_min);
        const max = Number(zrange_max);
        const zrange =
          min || max
            ? {
                min: [min],
                max: [max],
              }
            : undefined;
        const data = strTransArr(ranges_data);

        const ret = {
          shape,
          data,
          zrange,
        };
        if (shape && data && objHasValue(ret)) {
          return ret;
        } else {
          return undefined;
        }
      })
      .filter((val) => Boolean(val)) as LotRange[];

    return {
      name,
      type,
      exp_end_time,
      tool_cfg: {
        ...toolCfgRef.current,
        ranges,
        projected_editable,
        segmentation_3d_enabled,
        pre_box,
      },
    };
  };

  const toolFormatForm = (tool_cfg?: Task['tool_cfg']) => {
    if (!tool_cfg) return {};
    const { ranges: originRanges, projected_editable, segmentation_3d_enabled, pre_box } = tool_cfg;
    const ranges = originRanges?.map(({ shape, data, zrange }) => {
      return {
        ranges_data: data?.join('/'),
        zrange_max: zrange?.max,
        zrange_min: zrange?.min,
        shape,
      };
    });
    toolCfgRef.current = tool_cfg;
    return {
      projected_editable,
      segmentation_3d_enabled,
      ranges,
      pre_box,
    };
  };

  useEffect(() => {
    if (task) {
      const { name, type, exp_end_time, tool_cfg } = task;
      const toolData = toolFormatForm(tool_cfg);
      taskInfoForm.setValues(
        {
          name,
          type,
          exp_end_time: dayjs(exp_end_time).format('YYYY-MM-DDTHH:mm'),

          ...toolData,
        },
        'overwrite'
      );
    } else {
      taskInfoForm.setValues({}, 'overwrite');
    }
  }, [task]);

  useEffect(() => {
    const disabled = mode === 'edit';
    taskInfoForm.disabled = disabled;
    taskInfoForm.setFieldState('ranges.add', (state) => {
      state.display = disabled ? 'none' : 'visible';
    });
  }, [mode]);

  useEffect(() => {
    if (state === 'info') {
      taskInfoForm
        .validate()
        .then(() => {
          taskInfoForm.submit((values) => {
            const data = formatFormData(values);
            taskStore.pushNewState('info', {
              ...data,
              exp_end_time: dayjs(values.exp_end_time).format(),
            });
          });
        })
        .catch(() => {
          taskStore.quitSubmit('未完成步骤2：信息填写');
        });
    }
  }, [state]);

  return (
    <Box pt="4" pr="20">
      <FormProvider form={taskInfoForm}>
        <TaskInfoSchemaField schema={taskInfoSchema.schema} />
      </FormProvider>
    </Box>
  );
};
