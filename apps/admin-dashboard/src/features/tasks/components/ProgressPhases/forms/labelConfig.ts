import {
  ArrayAccordion,
  ArrayTable,
  ColorPickerPanel as ColorPicker,
  FormGrid,
  FormItem,
  FormLayout,
  Input,
  Select,
  Switch,
} from '@forest/formily';
import type { Field } from '@formily/core';
import type { ISchema } from '@formily/react';

import { Attr } from '../../../types';
import { widgetsNameOpt } from '../config';

/** 标注环节基本信息 */
const config = {
  components: {
    ColorPicker,
    FormGrid,
    FormItem,
    FormLayout,
    Input,
    Select,
    Switch,
    ArrayAccordion,
    ArrayTable,
  },
  scope: {
    disableFirstRow: (field: Field) => {
      if (field.path.segments[1] === 0) {
        field.disabled = true;
      }
    },
  },
};
const componentStyle = {
  size: 'sm',
  width: '100%',
};
const secondFormStyle = {
  decorator: {
    labelWidth: '110px',
    labelStyle: {
      color: 'gray.500',
    },
  },
  component: {
    size: 'sm',
  },
};
const attrTypes: Array<{
  label: string;
  value: Attr['type'];
}> = [
  { label: '单选框（自定义值）', value: 'radiobox' },
  // { label: '单选框（是或否）', value: 'bool' },
  { label: '多选框（自定义值）', value: 'checkbox' },
  { label: '输入框', value: 'input' },
];
const schema: ISchema = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        size: 'sm',
        layout: 'vertical',
      },
      properties: {
        grid: {
          type: 'void',
          'x-component': 'FormGrid',
          'x-component-props': {
            maxColumns: [1, 2],
            columnGap: 24,
            rowGap: 0,
          },
          properties: {
            display_name: {
              type: 'string',
              title: '标签中文名',
              required: true,
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              'x-component-props': {
                ...componentStyle,
                placeholder: '支持中文、大小英文字母、数字、中文括号',
              },
              pattern: '^[\u4E00-\u9FA5A-Za-z0-9]+(\uFF08[\u4E00-\u9FA5A-Za-z0-9]+\uFF09)?$',
              maxLength: 10,
            },
            name: {
              type: 'string',
              title: '标签英文名',
              required: true,
              'x-decorator': 'FormItem',
              'x-decorator-props': {
                addonBefore: '（关联导出数据，当前任务中不可重复）',
              },
              'x-component': 'Input',
              'x-component-props': {
                ...componentStyle,
                placeholder: '支持大小写英文字母、数字和下划线、点、短横线符号',
              },
              pattern: '^[A-Za-z0-9]+([_.-]?[A-Za-z0-9]+)*$',
              maxLength: 50,
            },
            desc: {
              type: 'string',
              title: '简介',
              default: '',
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              'x-component-props': {
                ...componentStyle,
                placeholder: '请输入关于该配置的简要介绍',
              },
            },
            avatar: {
              type: 'string',
              title: '图像',
              default: '',
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              'x-component-props': {
                ...componentStyle,
                placeholder: '请上传',
              },
            },
            parts: {
              type: 'array',
              title: '组合标签',
              'x-decorator': 'FormItem',
              'x-component': 'Select',
              'x-component-props': {
                ...componentStyle,
                placeholder: '请选择组合标签的子标签',
                mode: 'multiple',
              },
              'x-reactions': {
                dependencies: ['widgets_v2'],
                fulfill: {
                  schema: {
                    'x-disabled':
                      '{{($deps[0] && $deps[0].length > 0) || ($self.dataSource && $self.dataSource.length === 0)}}',
                  },
                },
              },
            },
            is_3d_segmentation: {
              type: 'boolean',
              title: '3D点云分割',
              required: false,
              'x-decorator': 'FormItem',
              'x-decorator-props': {
                addonBefore: '（是否为3D点云分割标签）',
              },
              'x-component': 'Switch',
              'x-component-props': {
                ...componentStyle,
              },
              'x-reactions': {
                target: 'has_instance',
                effects: ['onFieldMount', 'onFieldValueChange'],
                when: '{{!$self.value}}',
                fulfill: {
                  state: {
                    value: '{{$self.value}}',
                  },
                },
              },
            },
            has_instance: {
              type: 'boolean',
              title: '存在实例',
              required: false,
              'x-decorator': 'FormItem',
              'x-decorator-props': {
                addonBefore: '（是否标注3D点云分割实例）',
              },
              'x-component': 'Switch',
              'x-component-props': {
                ...componentStyle,
              },
              'x-reactions': {
                dependencies: ['is_3d_segmentation'],
                fulfill: {
                  schema: {
                    'x-disabled': '{{!$deps[0]}}',
                  },
                },
              },
            },
            color: {
              type: 'string',
              title: '标签颜色',
              required: false,
              'x-decorator': 'FormItem',
              'x-decorator-props': {
                wrapperWidth: 40,
              },
              'x-component': 'ColorPicker',
              'x-component-props': {
                ...componentStyle,
                rootClassName: 'color-picker-panel',
                format: 'hex',
                disabledAlpha: true,
              },
              'x-reactions': {
                dependencies: ['.'],
                fulfill: {
                  state: {
                    value: '{{typeof $self.value === "string" ? $self.value : $self.value?.toHexString()}}',
                  },
                },
              },
            },
            widgets_v2: {
              type: 'array',
              title: '智能标签和预置尺寸',
              'x-decorator': 'FormItem',
              'x-decorator-props': {
                gridSpan: 2,
              },
              'x-component': 'ArrayTable',
              'x-component-props': {
                pagination: { pageSize: 10 },
                scroll: { x: '100%' },
              },
              'x-reactions': {
                dependencies: ['parts'],
                fulfill: {
                  schema: {
                    'x-visible': '{{!$deps[0] || $deps[0].length === 0}}',
                  },
                },
              },
              items: {
                type: 'object',
                properties: {
                  column1: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': { Header: '名字' },
                    properties: {
                      name: {
                        type: 'string',
                        'x-decorator': 'FormItem',
                        'x-component': 'Select',
                        'x-component-props': {
                          size: 'small',
                          width: '90px',
                          options: widgetsNameOpt,
                        },
                        'x-reactions': [
                          {
                            dependencies: ['.rawdata_type', '.scale_min', '.scale_max'],
                            fulfill: {
                              schema: {
                                required: '{{!!$deps[0]||!!$deps[1]||!!$deps[2]}}',
                              },
                            },
                          },
                        ],
                      },
                    },
                  },
                  // column6: {
                  //   type: 'void',
                  //   'x-component': 'ArrayTable.Column',
                  //   'x-component-props': { Header: '线型' },
                  //   properties: {
                  //     line_type: {
                  //       type: 'string',
                  //       'x-decorator': 'FormItem',
                  //       required: true,
                  //       'x-component': 'Select',
                  //       'x-component-props': {
                  //         size: 'small',
                  //         width: '90px',
                  //         options: [
                  //           {
                  //             label: '折线',
                  //             value: 'line',
                  //           },
                  //           {
                  //             label: '曲线',
                  //             value: 'crspline',
                  //           },
                  //         ],
                  //       },
                  //       'x-reactions': [
                  //         {
                  //           dependencies: ['.name'],
                  //           fulfill: {
                  //             schema: {
                  //               'x-visible': '{{$deps[0]==="line3d"}}',
                  //             },
                  //           },
                  //         },
                  //       ],
                  //     },
                  //   },
                  // },
                  // column2: {
                  //   type: 'void',
                  //   'x-component': 'ArrayTable.Column',
                  //   'x-component-props': { Header: '适应文件' },
                  //   properties: {
                  //     rawdata_type: {
                  //       type: 'string',
                  //       'x-decorator': 'FormItem',
                  //       'x-component': 'Select',
                  //       'x-component-props': {
                  //         size: 'small',
                  //         width: '90px',
                  //         options: [
                  //           {
                  //             label: '图片',
                  //             value: 'image',
                  //           },
                  //           {
                  //             label: '点云',
                  //             value: 'pointcloud',
                  //           },
                  //         ],
                  //       },
                  //       'x-reactions': [
                  //         {
                  //           dependencies: ['.name', '.scale_min', '.scale_max'],

                  //           fulfill: {
                  //             schema: {
                  //               required: '{{!!$deps[0]||!!$deps[1]||!!$deps[2]}}',
                  //             },
                  //           },
                  //         },
                  //       ],
                  //     },
                  //   },
                  // },
                  column3: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': { Header: '预设比例' },
                    properties: {
                      preset: {
                        type: 'string',
                        description: '宽/高/深(px)或sx/sy/sz(米)',
                        'x-decorator': 'FormItem',
                        'x-component': 'Input',
                        'x-component-props': {
                          size: 'small',
                          width: '150px',
                          placeholder: '填写数字,用/分隔',
                        },
                        pattern: '^[0-9/.]*$',
                      },
                    },
                  },
                  column4: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': { Header: '最大尺寸' },
                    properties: {
                      scale_max: {
                        type: 'string',
                        description: '元素数量和单位同[预设比例]',
                        'x-decorator': 'FormItem',
                        'x-component': 'Input',
                        'x-component-props': {
                          size: 'small',
                          width: '130px',
                          placeholder: '填写数字,用/分隔',
                        },
                        pattern: '^[0-9/.]*$',
                        'x-reactions': [
                          {
                            dependencies: ['.scale_min'],
                            fulfill: {
                              schema: {
                                required: '{{!!$deps[0]}}',
                              },
                            },
                          },
                        ],
                      },
                    },
                  },
                  column5: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': { Header: '最小尺寸' },
                    properties: {
                      scale_min: {
                        type: 'string',
                        description: '元素数量和单位同[预设比例]',
                        'x-decorator': 'FormItem',
                        'x-component': 'Input',
                        'x-component-props': {
                          size: 'small',
                          width: '130px',
                          placeholder: '填写数字,用/分隔',
                        },
                        pattern: '^[0-9/.]*$',
                        'x-reactions': [
                          {
                            dependencies: ['.scale_max'],
                            fulfill: {
                              schema: {
                                required: '{{!!$deps[0]}}',
                              },
                            },
                          },
                        ],
                      },
                    },
                  },
                  opt: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': {
                      Header: '操作',
                      fixed: 'right',
                    },
                    properties: {
                      item: {
                        type: 'void',
                        'x-component': 'FormItem',
                        properties: {
                          copy: {
                            type: 'void',
                            'x-component': 'ArrayTable.Copy',
                          },
                          remove: {
                            type: 'void',
                            'x-component': 'ArrayTable.Remove',
                          },
                        },
                      },
                    },
                  },
                },
              },
              properties: {
                add: {
                  type: 'void',
                  'x-component': 'ArrayTable.Addition',
                  'x-component-props': {
                    colorScheme: 'brand',
                  },
                  title: '添加',
                },
              },
            },
          },
        },
        attrs: {
          type: 'array',
          title: '属性列表',
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            gridSpan: 2,
          },
          'x-component': 'ArrayAccordion',
          'x-component-props': {
            size: 'sm',
          },
          items: {
            type: 'object',
            'x-component': 'ArrayAccordion.Panel',
            'x-component-props': {
              header: '待完善',
              panelStyle: {
                paddingRight: 6,
              },
            },
            properties: {
              index: {
                type: 'void',
                'x-component': 'ArrayAccordion.Index',
              },
              grid: {
                type: 'void',
                'x-component': 'FormGrid',
                'x-component-props': {
                  minColumns: 2,
                  maxColumns: 2,
                  columnGap: 24,
                  rowGap: 0,
                },
                properties: {
                  display_name: {
                    type: 'string',
                    title: '中文名',
                    required: true,
                    'x-decorator': 'FormItem',
                    'x-decorator-props': secondFormStyle.decorator,
                    'x-component': 'Input',
                    'x-component-props': {
                      ...secondFormStyle.component,
                      placeholder: '请输入标签名称',
                    },
                    pattern: '^[\u4E00-\u9FA5A-Za-z0-9]+$',
                    maxLength: 10,
                  },
                  name: {
                    type: 'string',
                    title: '英文名',
                    required: true,
                    'x-decorator': 'FormItem',
                    'x-decorator-props': secondFormStyle.decorator,
                    'x-component': 'Input',
                    'x-component-props': {
                      ...secondFormStyle.component,
                      placeholder: '请输入大小写英文字母或下划线、数字',
                    },
                    pattern: '^[A-Za-z0-9]+[A-Za-z0-9_]*$',
                    maxLength: 50,
                  },
                  avatar: {
                    type: 'string',
                    title: '图像',
                    default: '',
                    'x-decorator': 'FormItem',
                    'x-decorator-props': secondFormStyle.decorator,
                    'x-component': 'Input',
                    'x-component-props': {
                      ...secondFormStyle.component,
                      placeholder: '请上传',
                    },
                  },
                  desc: {
                    type: 'string',
                    title: '简介',
                    default: '',
                    'x-decorator': 'FormItem',
                    'x-decorator-props': secondFormStyle.decorator,
                    'x-component': 'Input',
                    'x-component-props': {
                      ...secondFormStyle.component,
                      placeholder: '请输入关于该属性的介绍',
                    },
                  },
                  type: {
                    type: 'string',
                    title: '展示形态',
                    required: true,
                    enum: attrTypes,
                    default: attrTypes[0].value,
                    'x-decorator': 'FormItem',
                    'x-decorator-props': secondFormStyle.decorator,
                    'x-component': 'Select',
                    'x-component-props': {
                      ...secondFormStyle.component,
                    },
                  },
                  required: {
                    type: 'boolean',
                    title: '必要性',
                    default: true,
                    description: '该属性是否必须出现在标签标注结果中',
                    'x-decorator': 'FormItem',
                    'x-decorator-props': secondFormStyle.decorator,
                    'x-component': 'Switch',
                    'x-component-props': {
                      paddingTop: '2.5',
                    },
                  },
                  choiceString: {
                    type: 'string',
                    title: '选择项列表',
                    required: true,
                    'x-decorator': 'FormItem',
                    'x-decorator-props': {
                      ...secondFormStyle.decorator,
                      gridSpan: 2,
                    },
                    'x-component': 'Input',
                    'x-component-props': {
                      ...secondFormStyle.component,
                      placeholder:
                        '请输入选择项，多项以/分割，如果需要中英文提示，需要用:分割。例如：白色:white/红色:red ',
                    },
                    'x-reactions': {
                      dependencies: ['.type'],
                      fulfill: {
                        schema: {
                          'x-visible': '{{$deps[0] === "radiobox" || $deps[0] === "checkbox"}}',
                        },
                      },
                    },
                  },
                  default: {
                    type: 'string',
                    title: '默认值',
                    required: false,
                    'x-decorator': 'FormItem',
                    'x-decorator-props': {
                      ...secondFormStyle.decorator,
                      gridSpan: 2,
                    },
                    'x-component': 'Input',
                    'x-component-props': {
                      ...secondFormStyle.component,
                      placeholder: '如果选择项有中英文，此处请填写【英文】，用英文逗号 "," 分隔多选框的多个默认值',
                    },
                  },
                },
              },
              remove: {
                type: 'void',
                'x-component': 'ArrayAccordion.Remove',
              },
              moveUp: {
                type: 'void',
                'x-component': 'ArrayAccordion.MoveUp',
              },
              moveDown: {
                type: 'void',
                'x-component': 'ArrayAccordion.MoveDown',
              },
            },
          },
          properties: {
            add: {
              type: 'void',
              title: '新建属性',
              'x-component': 'ArrayAccordion.Addition',
              'x-component-props': {
                width: '100%',
                marginTop: '4',
              },
            },
          },
        },
      },
    },
  },
};

export const labelConfigSchema = { config, schema };
