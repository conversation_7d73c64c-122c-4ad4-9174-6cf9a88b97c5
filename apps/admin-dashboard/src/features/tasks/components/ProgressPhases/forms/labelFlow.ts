import { FormGrid, FormItem, FormLayout, Input, Select } from '@forest/formily';
import type { ISchema } from '@formily/react';

/** 标注环节基本信息 */
const config = {
  components: {
    FormGrid,
    FormItem,
    FormLayout,
    Input,
    Select,
  },
};
const componentStyle = {
  size: 'sm',
  width: '200px',
};
const schema: ISchema = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        size: 'sm',
        labelWidth: '110px',
      },
      properties: {
        grid: {
          type: 'void',
          'x-component': 'FormGrid',
          'x-component-props': {
            maxColumns: [1, 3],
            rowGap: 0,
          },
          properties: {
            name: {
              type: 'string',
              title: '标注流程命名',
              required: true,
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              'x-component-props': {
                ...componentStyle,
              },
              // TODO: 添加正则校验
              // pattern: '^[+]861[0-9]{10}(/[+]861[0-9]{10})*$',
            },
            type: {
              type: 'string',
              title: '流程类型',
              required: true,
              'x-disabled': true,
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              'x-component-props': {
                ...componentStyle,
              },
            },
            // TODO: 请求过于频繁，数据源变为组件读取
            execteam: {
              type: 'string',
              title: '执行团队',
              required: true,
              'x-disabled': false,
              'x-decorator': 'FormItem',
              'x-component': 'Select',
              'x-component-props': {
                ...componentStyle,
                placeholder: '请选择执行团队',
              },
            },
            timeout: {
              type: 'number',
              title: '单次作业时限',
              description: '超出该时限会被撤回作业包',
              required: true,
              format: 'number',
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              'x-component-props': {
                ...componentStyle,
                suffix: '小时',
                placeholder: '请输入该环节执行耗时的最大值',
              },
            },
            job_size: {
              type: 'number',
              title: '单次作业容量',
              description: '作业包中含有的最大帧数',
              required: true,
              format: 'number',
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              'x-component-props': {
                ...componentStyle,
                placeholder: '',
              },
            },
          },
        },
      },
    },
  },
};

export const labelFlowSchema = { config, schema };
