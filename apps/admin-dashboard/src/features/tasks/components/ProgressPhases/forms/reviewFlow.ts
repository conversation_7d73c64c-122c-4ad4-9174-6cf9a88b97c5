import { FormGrid, FormItem, FormLayout, Input, Select, Switch } from '@forest/formily';
import type { ISchema } from '@formily/react';
import { RiPercentLine } from 'react-icons/ri';

/** 审核环节基本信息 */
const config = {
  components: {
    FormGrid,
    FormItem,
    FormLayout,
    Input,
    Select,
    Switch,
  },
  scope: {
    RiPercentLine,
  },
};
const componentStyle = {
  size: 'sm',
  width: '100%',
};
const schema: ISchema = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        size: 'sm',
      },
      properties: {
        grid: {
          type: 'void',
          'x-component': 'FormGrid',
          'x-component-props': {
            maxColumns: [1, 3],
            columnGap: 16,
            rowGap: 0,
          },
          properties: {
            name: {
              type: 'string',
              title: '审核流程命名',
              required: true,
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              'x-component-props': {
                ...componentStyle,
              },
              // TODO: 添加正则校验
              // pattern: '^[+]861[0-9]{10}(/[+]861[0-9]{10})*$',
            },
            type: {
              type: 'string',
              title: '流程类型',
              required: true,
              'x-disabled': true,
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              'x-component-props': {
                ...componentStyle,
              },
            },
            execteam: {
              type: 'string',
              title: '执行团队',
              required: true,
              'x-disabled': false,
              'x-decorator': 'FormItem',
              'x-component': 'Select',
              'x-component-props': {
                ...componentStyle,
                placeholder: '请选择执行团队',
              },
            },
            timeout: {
              type: 'string',
              title: '单次作业时限',
              required: true,
              description: '超出该时限会被撤回作业包',
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              'x-component-props': {
                ...componentStyle,
                placeholder: '',
                suffix: '小时',
              },
              format: 'integer',
            },
            sample_percent: {
              type: 'string',
              title: '抽检比例',
              required: true,
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              'x-component-props': {
                ...componentStyle,
                placeholder: '请输入抽检比例',
                suffix: '{{RiPercentLine}}',
              },
              format: 'integer',
            },

            editable: {
              type: 'string',
              title: '是否允许编辑',
              required: true,
              'x-decorator': 'FormItem',
              'x-component': 'Switch',
              'x-component-props': {
                ...componentStyle,
                paddingTop: '2.5',
              },
            },
          },
        },
      },
    },
  },
};

export const reviewFlowSchema = { config, schema };
