import { BehaviorSubject } from 'rxjs';

import { Task, Taskphase } from '../../../types';

export interface IReviewFlow extends Taskphase {
  key: string;
  reasons?: string;
  execteam?: string;
}
export type IReviewExport = Pick<Task, 'phases'>;

interface IState {
  reviewFlows: Array<IReviewFlow>;
}
const initialReviewInfo: Taskphase = {
  name: '审核1',
  type: 'review',
  editable: true,
  sample_percent: 100,
  execteams: [],
  min_skill_level: 0,
  timeout: 2,
  merge: false,
};

const phaseSubject = new BehaviorSubject<IState>({
  reviewFlows: [
    {
      ...initialReviewInfo,
      key: `review_${Date.now()}`,
    },
  ],
});

export const phaseStore = {
  subject: phaseSubject,
  flush: ({ phases }: Partial<IReviewExport>) => {
    if (Array.isArray(phases)) {
      const reviewPhases = phases.filter((p) => p.type === 'review');

      phaseSubject.next({
        reviewFlows: reviewPhases.map((rp, i) => ({
          ...rp,
          execteam: rp.execteams.length === 1 ? rp.execteams[0].execteam : '',
          timeout: Math.ceil(rp.timeout / 3600),
          key: `review_${i}`,
        })),
      });
    } else {
      phaseSubject.next({
        reviewFlows: [
          {
            ...initialReviewInfo,
            key: `review_${Date.now()}`,
          },
        ],
      });
    }
  },
  addReviewFlow: () => {
    const { reviewFlows } = phaseSubject.value;
    phaseSubject.next({
      reviewFlows: reviewFlows.concat({
        ...initialReviewInfo,
        name: `审核${reviewFlows.length + 1}`,
        key: `review_${Date.now()}`,
      }),
    });
  },
  copyReviewFlow: (index: number, data: IReviewFlow) => {
    const { reviewFlows } = phaseSubject.value;
    phaseSubject.next({
      reviewFlows: [
        ...reviewFlows.slice(0, index),
        data,
        {
          ...data,
          name: `${data.name}_copy`,
          key: `review_${Date.now()}`,
          reasons: undefined,
        },
        ...reviewFlows.slice(index + 1),
      ],
    });
  },
  deleteReviewFlow: (index: number) => {
    const { reviewFlows } = phaseSubject.value;
    if (index > 0 && index < reviewFlows.length) {
      phaseSubject.next({
        reviewFlows: [...reviewFlows.slice(0, index), ...reviewFlows.slice(index + 1)],
      });
    }
  },
  updateReviewInfo: (index: number, info: Partial<IReviewFlow>) => {
    const { reviewFlows } = phaseSubject.value;

    phaseSubject.next({
      reviewFlows: reviewFlows.slice(0, index).concat(
        // 使用 Object.assign 保持该对象索引不变，避免触发组件rerender
        [Object.assign(reviewFlows[index], info)],
        reviewFlows.slice(index + 1)
      ),
    });
  },
  exportData: (): IReviewExport => {
    const { reviewFlows } = phaseSubject.value;

    return {
      phases: reviewFlows.map((rp) => {
        const phase = {
          ...rp,
          execteams: [
            {
              execteam: rp.execteam!,
            },
          ],
          timeout: rp.timeout * 3600,
        };
        delete phase.execteam;
        return phase;
      }),
    };
  },
};
