import { BehaviorSubject } from 'rxjs';

import type { Attr, Label, Task, Taskphase } from '@/features/tasks/types';

// TODO: 之后需要改成之前配置多团队，所以此处需要删除，目前管理平台其他地方没有适配多团队的情况
export type IBaseInfo = Taskphase & { job_size: number; execteam?: string };
export interface ILabel extends Label {
  parts?: string[];
}
export interface ILabelFlow {
  baseInfo: IBaseInfo;
  labels: Array<ILabel>;
  attrs: Record<string, Attr>;
}
export type ILabelExport = Pick<Task, 'phases' | 'ontologies' | 'job_size'>;

const initialLabelGlobal: ILabel = {
  name: 'global',
  display_name: '全局配置',
  desc: '适用于 2D 中单张或 3D 中单帧的规则',
  avatar: '',
  color: '',
  is_3d_segmentation: false,
  has_instance: false,
  attrs: {
    optional_v2: [],
    required_v2: [],
  },
  widgets_v2: [],
};

const initialLabelInfo: IBaseInfo = {
  name: '标注',
  type: 'label',
  editable: true,
  sample_percent: 100,
  execteam: '',
  execteams: [],
  min_skill_level: 0,
  timeout: 2,
  job_size: 0,
  merge: false,
};

const labelFlowSubject = new BehaviorSubject<ILabelFlow>({
  baseInfo: initialLabelInfo,
  labels: [initialLabelGlobal],
  attrs: {},
});

export const labelFlowStore = {
  subject: labelFlowSubject,
  flush: ({ phases, ontologies, job_size = 0 }: Partial<ILabelExport>) => {
    if (phases) {
      const labelPhase = phases.find((p) => p.type === 'label');

      const originLabels = ontologies?.groups[0]?.labels.map((label) => {
        const { compound, ...rest } = label;
        const parts = compound ? compound.parts.map((val) => val.label) : [];
        return {
          ...rest,
          parts,
        };
      });

      const labels: Label[] = ontologies
        ? [{ ...initialLabelGlobal, attrs: ontologies.elem_attrs } as Label].concat(originLabels ?? [])
        : [initialLabelGlobal];

      const attrs = (ontologies?.attrs as Record<string, Attr>) || {};

      labelFlowSubject.next({
        baseInfo: {
          ...initialLabelInfo,
          ...labelPhase,
          execteam: labelPhase?.execteams.length === 1 ? labelPhase.execteams[0].execteam : '',
          timeout: labelPhase?.timeout ? Math.ceil(labelPhase?.timeout / 3600) : 2,
          job_size,
        },
        labels,
        attrs,
      });
    } else {
      labelFlowSubject.next({
        baseInfo: { ...initialLabelInfo },
        labels: [initialLabelGlobal],
        attrs: {},
      });
    }
  },
  addLabel: (data: Label, labelAttrs: Record<string, Attr>, index: number) => {
    const { baseInfo, labels, attrs } = labelFlowSubject.value;
    labelFlowSubject.next({
      baseInfo,
      labels: [...labels.slice(0, index + 1), data, ...labels.slice(index + 1)],
      attrs: { ...attrs, ...labelAttrs },
    });
  },
  updateLabel: (data: Label, labelAttrs: Record<string, Attr>, index: number) => {
    const { baseInfo, labels, attrs } = labelFlowSubject.value;
    labelFlowSubject.next({
      baseInfo,
      labels: [...labels.slice(0, index), data, ...labels.slice(index + 1)],
      attrs: { ...attrs, ...labelAttrs },
    });
  },
  deleteLabel: (index: number) => {
    const { baseInfo, labels, attrs } = labelFlowSubject.value;
    labelFlowSubject.next({
      baseInfo,
      labels: [...labels.slice(0, index), ...labels.slice(index + 1)],
      attrs,
    });
  },
  updateBaseInfo: (info: IBaseInfo) => {
    const { baseInfo, ...rest } = labelFlowSubject.value;
    labelFlowSubject.next({
      ...rest,
      baseInfo: {
        ...baseInfo,
        ...info,
      },
    });
  },
  exportData: (): ILabelExport | null => {
    const { baseInfo, labels, attrs } = labelFlowSubject.value;

    const phase = {
      ...baseInfo,
      timeout: baseInfo.timeout * 3600,
      execteams: [
        {
          execteam: baseInfo.execteam!,
        },
      ],
    };

    delete phase.execteam;

    if (labels.length > 1) {
      return {
        phases: [phase],
        ontologies: {
          attrs,
          elem_attrs: labels[0].attrs,
          groups: [
            {
              name: '',
              labels: labels.slice(1).map((label) => {
                const { parts, ...rest } = label;
                if (parts && parts.length > 0) {
                  return {
                    ...rest,
                    compound: {
                      parts: parts.map((part) => ({
                        label: part,
                      })),
                    },
                  };
                }
                return rest;
              }),
            },
          ],
        },
        job_size: baseInfo.job_size,
      };
    }

    return null;
  },
};
