/* eslint-disable react-hooks/exhaustive-deps */
import { Box, Button, Icon, SimpleGrid } from '@chakra-ui/react';
import { useConstant, useObservable } from '@forest/hooks';
import { SimpleDrawer } from '@forest/simple-ui';
import { createForm } from '@formily/core';
import { createSchemaField, FormProvider } from '@formily/react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { RiLightbulbLine } from 'react-icons/ri';

import { objHasValue, strTransArr } from '@/utils';

import type { Attr, Label } from '../../types';
import { FlowTitle } from './FlowTitle';
import { labelConfigSchema, labelFlowSchema } from './forms';
import { LabelCard } from './LabelCard';
import { useSupplier } from './providers';
import { ILabel, labelFlowStore } from './stores';
import { initialLabelColorList } from './utils/labelColorList';

import './index.css';

const LabelFlowSchemaField = createSchemaField(labelFlowSchema.config);
const LabelConfigSchemaField = createSchemaField(labelConfigSchema.config);

const attrsTransFormData = (labelAttrs: Label['attrs'], attrs: Record<string, Attr>) => {
  const { optional_v2 = [], required_v2 = [] } = labelAttrs || {};
  const splitNumber = optional_v2.length;
  return [...optional_v2, ...required_v2].map(({ name, default: defaultValue }, i) => ({
    ...attrs[name],
    default: defaultValue,
    required: i >= splitNumber,
    choiceString: attrs[name].choices.map(({ display_name, name }) => `${display_name}:${name}`).join('/'),
  }));
};
const formDataTransAttrs = (
  formAttrs: Array<Attr>
): {
  labelAttrs: Label['attrs'];
  changedAttrs: Record<string, Attr>;
} => {
  const optional: Array<{
      name: string;
      default: string;
    }> = [],
    required: Array<{
      name: string;
      default: string;
    }> = [];
  const changedAttrs: Record<string, Attr> = {};

  formAttrs.forEach((attr: Attr) => {
    changedAttrs[attr.name] = {
      ...attr,
      choices:
        attr.choiceString?.split('/').map((choice) => {
          const [display_name, value] = choice.split(':');
          let name = display_name;
          // 如果有 : 分割的第二个值，则使用第二个值
          if (value) name = value;
          return {
            name,
            display_name,
            desc: name,
            avatar: name,
            value: name,
          };
        }) || [],
    };

    if (attr.required) {
      required.push({
        name: attr.name,
        default: attr.default,
      });
    } else {
      optional.push({
        name: attr.name,
        default: attr.default,
      });
    }
  });

  return {
    labelAttrs: { optional_v2: optional, required_v2: required },
    changedAttrs,
  };
};

const formDataTransWidgets = (data: any) => {
  return data
    ?.map((item: any) => {
      const { name, rawdata_type, preset: originPreset, scale_min, scale_max, line_type } = item;
      const preset = strTransArr(originPreset);
      const scale =
        scale_min || scale_max
          ? {
              min: strTransArr(scale_min),
              max: strTransArr(scale_max),
            }
          : undefined;

      const ret = {
        name,
        line_type,
        rawdata_type,
        preset,
        scale,
      };

      if (objHasValue(ret)) {
        return ret;
      } else {
        return undefined;
      }
    })
    .filter(Boolean);
};

const widgetsTransFormData = (data: any) => {
  return data?.map((item: any) => {
    const { name, rawdata_type, preset, scale, line_type } = item;
    return {
      name,
      line_type,
      rawdata_type,
      preset: preset?.join('/'),
      scale_max: scale?.max?.join('/'),
      scale_min: scale?.min?.join('/'),
    };
  });
};

export interface LabelFlowProps {
  isDisabled: boolean;
  isSubmitting: boolean;
  onSubmitLabel: (err?: string) => void;
}
export const LabelFlow: React.FC<LabelFlowProps> = ({ isDisabled, isSubmitting, onSubmitLabel }) => {
  const [drawerData, setDrawerData] = useState<{
    index: number;
    mode: 'add' | 'edit';
  }>({ index: -1, mode: 'add' });

  const labelFlowForm = useConstant(() => createForm());
  const getNextColor = useConstant(() => initialLabelColorList());

  const labelConfigForm = useMemo(() => {
    if (drawerData.index >= 0) {
      return createForm();
    }
    return null;
  }, [drawerData]);

  const suppliers = useSupplier();

  const { baseInfo, labels, attrs } = useObservable(labelFlowStore.subject, labelFlowStore.subject.value);

  useEffect(() => {
    labelFlowForm.setValues(baseInfo, 'overwrite');
  }, [baseInfo]);

  useEffect(() => {
    if (suppliers) {
      labelFlowForm.query('execteam').take((field) => {
        if ('dataSource' in field) {
          field.dataSource = suppliers;
        }
      });
    }
  }, [suppliers]);

  useEffect(() => {
    labelFlowForm.disabled = isDisabled;
  }, [isDisabled]);

  useEffect(() => {
    if (isSubmitting) {
      labelFlowForm
        .validate()
        .then(() => {
          labelFlowForm.submit((values) => {
            labelFlowStore.updateBaseInfo(values);
            onSubmitLabel();
          });
        })
        .catch(() => {
          onSubmitLabel(`未完成步骤3：流程【${labelFlowForm.getValuesIn('name')}】配置`);
        });
    }
  }, [isSubmitting]);

  useEffect(() => {
    if (drawerData.index >= 0 && labelConfigForm) {
      const currentLabel =
        drawerData.index < labels.length
          ? drawerData.mode === 'edit'
            ? labels[drawerData.index]
            : {
                ...labels[drawerData.index],
                name: `${labels[drawerData.index].name}_copy`,
                display_name: `${labels[drawerData.index].display_name}_copy`,
              }
          : ({} as ILabel);

      const renderAttrs = currentLabel.hasOwnProperty('attrs')
        ? attrsTransFormData((currentLabel as Label).attrs, attrs)
        : [];

      const widgets_v2 = widgetsTransFormData((currentLabel as Label)?.widgets_v2);

      const color = currentLabel?.color ?? getNextColor();

      labelConfigForm.setValues(
        {
          ...currentLabel,
          attrs: renderAttrs,
          widgets_v2,
          color,
        },
        'overwrite'
      );
      // 修改时，如果已经是组合标签，不展示配置 widget
      labelConfigForm.setFieldState('widgets_v2', (state) => {
        state.display =
          currentLabel?.name === 'global' ||
          (drawerData.mode === 'edit' && currentLabel.parts && currentLabel.parts?.length > 0)
            ? 'none'
            : 'visible';
      });
      labelConfigForm.setFieldState('parts', (state) => {
        if (currentLabel.name === 'global') {
          state.display = 'none';
          return;
        }
        const sources = labels.slice(1).map((label) => ({ label: label.display_name, value: label.name }));
        const labelName = drawerData.mode === 'edit' ? currentLabel.name : '';
        // 如果是修改情况下，需要过滤掉自己的标签
        state.dataSource = labelName ? sources.filter((source) => source.value !== labelName) : sources;
      });
    }
  }, [drawerData]);

  const onOpenDrawer = useCallback((index: number, mode: 'add' | 'edit') => setDrawerData({ index, mode }), []);
  const onCloseDrawer = useCallback(() => setDrawerData({ index: -1, mode: 'add' }), []);

  const onSubmitDrawer = () => {
    labelConfigForm &&
      labelConfigForm.submit((formData) => {
        const { labelAttrs, changedAttrs } = formDataTransAttrs(formData.attrs);
        const widgets_v2 = formDataTransWidgets(formData.widgets_v2);
        // TODO(xiudan): 此处标签之后如果支持修改，需要保证继续往后选取颜色
        const label = { ...formData, attrs: labelAttrs, widgets_v2 };
        if (drawerData.mode === 'add') {
          labelFlowStore.addLabel(label, changedAttrs, drawerData.index);
        } else if (drawerData.mode === 'edit') {
          labelFlowStore.updateLabel(label, changedAttrs, drawerData.index);
        }
        onCloseDrawer();
      });
  };

  return (
    <Box mb="3" borderRadius="md" border="2px dashed" borderColor="gray.300" px="4">
      <FlowTitle index={1} borderBottom="1px dashed" borderColor="blackAlpha.200">
        <Box px="1.5" mb="-1">
          <FormProvider form={labelFlowForm}>
            <LabelFlowSchemaField schema={labelFlowSchema.schema} />
          </FormProvider>
        </Box>
      </FlowTitle>
      <SimpleGrid columns={4} spacing={4} px="1" py="5">
        {labels.map((label, index) => (
          <LabelCard
            key={label.name}
            label={label}
            onEdit={() => onOpenDrawer(index, 'edit')}
            onCopy={() => onOpenDrawer(index, 'add')}
            onDelete={() => labelFlowStore.deleteLabel(index)}
          />
        ))}
        <Button
          height="100%"
          variant="unstyled"
          textAlign="center"
          borderRadius="sm"
          border="1px solid"
          borderColor="gray.200"
          isDisabled={isDisabled}
          _hover={{
            boxShadow: 'xl',
            borderColor: 'transparent',
          }}
          onClick={() => onOpenDrawer(labels.length, 'add')}
        >
          <Icon as={RiLightbulbLine} fontSize="4xl" color="teal.500" />
          <Box color="gray.700" mt="1" letterSpacing="0.5px">
            添加标签配置
          </Box>
        </Button>
      </SimpleGrid>
      <SimpleDrawer
        isOpen={drawerData.index >= 0}
        title="配置详情"
        onClose={onCloseDrawer}
        onSubmit={onSubmitDrawer}
        px="5"
        pb="0"
        size="xl"
        scrollSnapType="y proximity"
        style={{ scrollbarGutter: 'stable both-edges' }}
      >
        {labelConfigForm && (
          <FormProvider form={labelConfigForm}>
            <LabelConfigSchemaField schema={labelConfigSchema.schema} />
          </FormProvider>
        )}
      </SimpleDrawer>
    </Box>
  );
};
