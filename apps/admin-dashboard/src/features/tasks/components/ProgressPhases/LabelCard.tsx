import { Avatar, Box, Flex, Text } from '@chakra-ui/react';
import { TipButton } from '@forest/simple-ui';
import { RiDeleteBinLine, RiFileCopyLine, RiSettingsLine } from 'react-icons/ri';

import { Label } from '../../types';

interface LabelCardProps {
  label: Label;
  onEdit: () => void;
  onCopy: () => void;
  onDelete: () => void;
}
const iconButtonStyles = {
  variant: 'ghost',
  isRound: true,
  size: 'sm',
  fontSize: '18px',
};
export const LabelCard = ({ label, onEdit, onCopy, onDelete }: LabelCardProps) => {
  return (
    <Box p="5" borderRadius="base" boxShadow="xl">
      <Box textAlign="center" mx="-0.5">
        <Box pt="0.5" pb="2.5">
          <Avatar name={label.display_name} src={label.avatar}></Avatar>
        </Box>
        <Box fontWeight="bold" textTransform="capitalize" color={label.name === 'global' ? 'logo.600' : label.color}>
          {label.name}_{label.display_name}
        </Box>
        <Text fontSize="xs" color="gray.500" mt="0.5" px="1" noOfLines={2}>
          {label.desc}
        </Text>
      </Box>
      <Box bg="blueGray.100" borderRadius="xs" fontSize="sm" fontWeight="bold" py="3" px="3.5" mt="3" mx="-1">
        <Text color="blueGray.900" mb="1">
          属性列表（共 {(label.attrs?.optional_v2.length || 0) + (label.attrs?.required_v2.length || 0)} 个）：
        </Text>
        <Text color="blueGray.500" noOfLines={6}>
          必选：{label.attrs?.required_v2.reduce((prev, { name }) => prev + name + ' / ', '') || '无'}
        </Text>
        <Text color="blueGray.500" noOfLines={6}>
          可选：{label.attrs?.optional_v2.reduce((prev, { name }) => prev + name + ' / ', '') || '无'}
        </Text>
      </Box>
      <Box bg="blueGray.100" borderRadius="xs" fontSize="sm" fontWeight="bold" py="3" px="3.5" mt="3" mx="-1">
        <Text color="blueGray.900" mb="1">
          小工具列表（共 {label?.widgets_v2?.length ?? 0} 个）：
        </Text>
        <Text color="blueGray.500" noOfLines={4}>
          {'无'}
        </Text>
      </Box>
      <Flex justify="space-between" mt="3">
        <TipButton
          aria-label="配置"
          colorScheme="logo"
          icon={<RiSettingsLine />}
          {...iconButtonStyles}
          onClick={onEdit}
        ></TipButton>
        <TipButton
          aria-label="复制"
          colorScheme="teal"
          icon={<RiFileCopyLine />}
          {...iconButtonStyles}
          onClick={onCopy}
        ></TipButton>
        <TipButton
          aria-label="删除"
          colorScheme="orange"
          icon={<RiDeleteBinLine />}
          {...iconButtonStyles}
          onClick={onDelete}
        ></TipButton>
      </Flex>
    </Box>
  );
};
