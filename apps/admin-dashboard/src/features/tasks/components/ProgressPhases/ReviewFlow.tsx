/* eslint-disable react-hooks/exhaustive-deps */
import { Box, HStack, IconButton } from '@chakra-ui/react';
import { useConstant } from '@forest/hooks';
import { createForm } from '@formily/core';
import { createSchemaField, FormProvider } from '@formily/react';
import { useEffect, useMemo } from 'react';
import { RiDeleteBinLine, RiFileCopyLine } from 'react-icons/ri';

import { FlowTitle } from './FlowTitle';
import { reviewFlowSchema } from './forms';
import { useSupplier } from './providers';
import { IReviewFlow, phaseStore } from './stores';

const iconButtonStyles = {
  variant: 'soft',
  size: 'sm',
  borderRadius: 'sm',
};
interface IOperation {
  name: 'copy' | 'delete';
  click: () => void;
}
const Operations = ({
  disabled = [],
  handler,
}: {
  disabled?: Array<IOperation['name']>;
  handler: Record<IOperation['name'], IOperation['click']>;
}) => {
  return (
    <HStack spacing="3">
      <IconButton
        aria-label="复制"
        icon={<RiFileCopyLine />}
        colorScheme="logo"
        {...iconButtonStyles}
        isDisabled={disabled.includes('copy')}
        onClick={handler.copy}
      ></IconButton>
      <IconButton
        aria-label="删除"
        icon={<RiDeleteBinLine />}
        colorScheme="orange"
        {...iconButtonStyles}
        isDisabled={disabled.includes('delete')}
        onClick={handler.delete}
      ></IconButton>
    </HStack>
  );
};

interface ReviewFlowProps {
  index: number;
  reviewInfo: IReviewFlow;
  isDisabled: boolean;
  handler: {
    copy: (index: number, data: IReviewFlow) => void;
    delete: (index: number) => void;
  };
  isSubmitting: boolean;
  onSubmitReview: (err?: string) => void;
}
const ReviewFlowSchemaField = createSchemaField(reviewFlowSchema.config);
export const ReviewFlow = ({
  index,
  isDisabled,
  reviewInfo,
  handler,
  isSubmitting,
  onSubmitReview,
}: ReviewFlowProps) => {
  const reviewFlowForm = useConstant(() => createForm());

  const suppliers = useSupplier();

  const clickHandler = useMemo(() => {
    return {
      copy: () => handler.copy(index, reviewFlowForm.values),
      delete: () => handler.delete(index),
    };
  }, [index]);

  useEffect(() => {
    reviewFlowForm.setValues(reviewInfo, 'overwrite');
  }, [reviewInfo]);

  useEffect(() => {
    if (suppliers) {
      reviewFlowForm.query('execteam').take((field) => {
        if ('dataSource' in field) {
          field.dataSource = suppliers;
        }
      });
    }
  }, [suppliers]);

  useEffect(() => {
    reviewFlowForm.disabled = isDisabled;
  }, [isDisabled]);

  useEffect(() => {
    if (isSubmitting) {
      reviewFlowForm
        .validate()
        .then(() => {
          reviewFlowForm.submit((values) => {
            phaseStore.updateReviewInfo(index, values);
            onSubmitReview();
          });
        })
        .catch(() => {
          onSubmitReview(`未完成步骤3：流程【${reviewFlowForm.getValuesIn('name')}】配置`);
        });
    }
  }, [isSubmitting]);

  return (
    <Box mb="3" borderRadius="md" border="2px dashed" borderColor="gray.300" px="4">
      <FlowTitle
        index={index + 2}
        pb="5"
        rightChildren={<Operations handler={clickHandler} disabled={index === 0 ? ['delete'] : []} />}
      >
        <FormProvider form={reviewFlowForm}>
          <ReviewFlowSchemaField schema={reviewFlowSchema.schema} />
        </FormProvider>
      </FlowTitle>
    </Box>
  );
};
