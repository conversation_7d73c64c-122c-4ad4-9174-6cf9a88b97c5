import { createContext, useContext, useMemo } from 'react';

import { getTeamList } from '@/features/teams';
import { useRequest } from '@/hooks/use-request';

const SupplierContext = createContext<
  | {
      label: string;
      value: string;
    }[]
  | null
>(null);
SupplierContext.displayName = 'SupplierContext';

export const SupplierProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const { data } = useRequest(getTeamList, {
    params: {
      page: 0,
      // 此处取消供应商的限制，使得审核环节可被分配给甲方团队
      // teamType: 'supplier',
      pagesz: 100,
    },
    enabled: true,
  });

  const suppliers = useMemo(
    () =>
      data
        ? data.teams.map((team) => ({
            label: team.name,
            value: team.uid,
          }))
        : null,
    [data]
  );

  return <SupplierContext.Provider value={suppliers}>{children}</SupplierContext.Provider>;
};
export const useSupplier = () => useContext(SupplierContext);
