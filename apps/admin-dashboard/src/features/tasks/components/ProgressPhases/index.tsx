import { Button } from '@chakra-ui/react';
import { useObservable } from '@forest/hooks';
import { useMemo, useRef } from 'react';

import { taskStore } from '../../stores';
import { LabelFlow } from './LabelFlow';
import { SupplierProvider } from './providers';
import { ReviewFlow } from './ReviewFlow';
import { labelFlowStore, phaseStore } from './stores';

export const ProgressPhases = () => {
  const { mode, state, changedData } = useObservable(taskStore.subject, taskStore.subject.value);
  const { reviewFlows } = useObservable(phaseStore.subject, phaseStore.subject.value);

  const reviewSubmitNumber = useRef(0);

  const onSubmitLabel = (err?: string) => {
    if (err) {
      taskStore.quitSubmit(err);
    } else {
      const labelData = labelFlowStore.exportData();
      if (labelData) {
        taskStore.pushNewState('label', labelData);
      } else {
        taskStore.quitSubmit('未完成步骤3：请至少添加一个标签配置');
      }
    }
  };

  const onSubmitReview = (err?: string) => {
    if (err) {
      taskStore.quitSubmit(err);
      reviewSubmitNumber.current = 0;
    } else {
      if (reviewSubmitNumber.current + 1 >= reviewFlows.length) {
        const { phases } = phaseStore.exportData();
        taskStore.pushNewState('review', {
          phases: [...(changedData.phases || []), ...phases],
        });
        reviewSubmitNumber.current = 0;
      } else {
        reviewSubmitNumber.current += 1;
      }
    }
  };

  const isDisabled = useMemo(() => mode === 'edit', [mode]);

  return (
    <SupplierProvider>
      <LabelFlow isDisabled={isDisabled} isSubmitting={state === 'label'} onSubmitLabel={onSubmitLabel} />
      {reviewFlows.map((item, i) => (
        <ReviewFlow
          key={item.key}
          reviewInfo={item}
          index={i}
          handler={{
            copy: phaseStore.copyReviewFlow,
            delete: phaseStore.deleteReviewFlow,
          }}
          isDisabled={isDisabled}
          isSubmitting={state === 'review'}
          onSubmitReview={onSubmitReview}
        />
      ))}
      <Button
        my="3"
        variant="outline"
        colorScheme="blue"
        w="100%"
        isDisabled={isDisabled}
        onClick={() => phaseStore.addReviewFlow()}
      >
        添加审核流程
      </Button>
    </SupplierProvider>
  );
};
