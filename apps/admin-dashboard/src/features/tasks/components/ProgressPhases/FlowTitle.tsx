import { Box, BoxProps, Flex } from '@chakra-ui/react';
import { ReactNode } from 'react';

interface FlowTitleProps extends BoxProps {
  index: number;
  rightChildren?: ReactNode;
}
export const FlowTitle = ({ index, rightChildren, children, ...extraProps }: FlowTitleProps) => {
  return (
    <Box pt="4" {...extraProps}>
      <Flex align="center" justify="space-between" mb="4" mx="1">
        <Box fontSize="xl" fontWeight="bold" color="logo.600" letterSpacing="1px">
          NO. {index}
        </Box>
        {rightChildren}
      </Flex>
      {children}
    </Box>
  );
};
