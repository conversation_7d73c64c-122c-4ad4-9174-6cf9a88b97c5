import { <PERSON>, HStack, I<PERSON><PERSON><PERSON><PERSON>, <PERSON>, Tag, Tooltip } from '@chakra-ui/react';
import { Column, IPagination, Row, Table, TipButton } from '@forest/simple-ui';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import {
  RiCloseLine,
  RiDeleteBinLine,
  RiEditLine,
  RiErrorWarningFill,
  RiFileCopy2Line,
  RiFileTextLine,
  RiPauseLine,
  RiPlayLine,
  RiSettings3Line,
} from 'react-icons/ri';

import { DATA_TYPE_MAP, TASK_STATE_MAP } from '../config';
import type { Task as oriTask, TaskOperation, TaskShowState } from '../types';

import './index.css';

interface Task extends oriTask {
  job_counts?: Array<{ count: number; phase: number }>;
}

const iconButtonStyles = {
  variant: 'outline',
  isRound: true,
  fontSize: '16px',
  size: 'sm',
};
export interface TasksTableProps<T> {
  name: TaskShowState;
  tasks?: Task[];
  pagination: IPagination;
  operations?: Array<{
    name: T;
    colorScheme: string;
    icon: JSX.Element;
    label: string;
  }>;
  onOperateTask: (type: T | TaskOperation, task: Task, showState: TaskShowState) => void;
}
export const taskOperationMap: Record<
  TaskOperation,
  {
    name: TaskOperation;
    colorScheme: string;
    icon: JSX.Element;
    label: string;
  }
> = {
  read: {
    name: 'read',
    colorScheme: 'twitter',
    icon: <RiFileTextLine />,
    label: '查看任务',
  },
  edit: {
    name: 'edit',
    label: '编辑',
    colorScheme: 'purple',
    icon: <RiEditLine />,
  },
  start: {
    name: 'start',
    label: '启动',
    colorScheme: 'green',
    icon: <RiPlayLine />,
  },
  pause: {
    name: 'pause',
    label: '暂停',
    colorScheme: 'yellow',
    icon: <RiPauseLine />,
  },
  cancel: {
    name: 'cancel',
    label: '终止',
    colorScheme: 'orange',
    icon: <RiCloseLine />,
  },
  delete: {
    name: 'delete',
    label: '删除',
    colorScheme: 'red',
    icon: <RiDeleteBinLine />,
  },
  config: {
    name: 'config',
    label: '配置',
    colorScheme: 'blue',
    icon: <RiSettings3Line />,
  },
  progress: {
    name: 'progress',
    label: '进度',
    colorScheme: 'purple',
    icon: <RiSettings3Line />,
  },
  copy: {
    name: 'copy',
    label: '复制',
    colorScheme: 'twitter',
    icon: <RiFileCopy2Line />,
  },
};
export interface TasksTableProps<T> {
  name: TaskShowState;
  tasks?: Task[];
  operations?: Array<{
    name: T;
    colorScheme: string;
    icon: JSX.Element;
    label: string;
  }>;
  onOperateTask: (type: T | TaskOperation, task: Task, showState: TaskShowState) => void;
}

export const TasksTable = <T extends string>({
  name,
  tasks,
  pagination,
  operations,
  onOperateTask,
}: TasksTableProps<T>) => {
  const renderHeader = (label: string) => (
    <HStack>
      <Box>{label}</Box>
      <Tooltip hasArrow label={label + '阶段的任务包数量'} bg="gray" fontSize="0.7rem">
        <IconButton aria-label="warn" icon={<RiErrorWarningFill color="#D3D3D3" />} size="sm" variant="unstyled" />
      </Tooltip>
    </HStack>
  );
  const renderJobCountCell = (job_counts: Task['job_counts'], phase: number) => {
    return (
      <Box as="span" fontSize="0.8rem">
        {job_counts?.find((item) => item.phase === phase)?.count ?? 0}
      </Box>
    );
  };

  const columns: Column<Task>[] = useMemo(
    () => [
      {
        Header: '任务名称',
        accessor: 'name',
        Cell: ({ row }) => {
          const { original: task } = row;
          return (
            <Box width="10rem">
              <Link color="brand.500" fontWeight="bold" onClick={() => onOperateTask('progress', task, name)}>
                {task.name}
              </Link>
              <Box color="gray.600" fontSize="0.7rem" mt="0.5">
                ID: {task.uid}
              </Box>
            </Box>
          );
        },
      },
      {
        Header: '任务包总数',
        id: 'total',
        Cell: ({ row }: { row: Row<Task> }) => (
          <Box fontSize="0.8rem">
            {row.original.job_counts?.reduce((prev, item) => (item.phase < 5 ? prev + item.count : prev), 0) ?? 0}
          </Box>
        ),
      },
      {
        Header: renderHeader('标注'),
        id: 'label',
        Cell: ({ row }: { row: Row<Task> }) => renderJobCountCell(row.original.job_counts, 1),
      },
      {
        Header: renderHeader('审核'),
        id: 'audit',
        Cell: ({ row }: { row: Row<Task> }) => renderJobCountCell(row.original.job_counts, 2),
      },
      {
        Header: renderHeader('质检'),
        id: 'quarantine',
        Cell: ({ row }: { row: Row<Task> }) => renderJobCountCell(row.original.job_counts, 3),
      },
      {
        Header: renderHeader('验收'),
        id: 'accept',
        Cell: ({ row }: { row: Row<Task> }) => renderJobCountCell(row.original.job_counts, 4),
      },
      {
        Header: '数据类型',
        accessor: 'data_type',
        Cell: ({ value }) => {
          return (
            <Box fontSize="0.8rem" color="gray.600">
              {DATA_TYPE_MAP[value ?? 'unspecified']}
            </Box>
          );
        },
      },
      {
        Header: '数据量',
        accessor: 'data_size',
        Cell: ({ value }) => {
          return (
            <Box fontSize="0.8rem" color="gray.600">
              {value}
            </Box>
          );
        },
      },
      {
        Header: '创建时间',
        accessor: 'created_at',
        Cell: ({ value }) => (
          <Box as="span" whiteSpace="pre" color="gray.600" fontSize="0.8rem">
            {dayjs(value).format('YYYY / MM / DD\nHH:mm')}
          </Box>
        ),
      },
      {
        Header: '预期完成时间',
        accessor: 'exp_end_time',
        Cell: ({ value }) => {
          const expired = dayjs().isAfter(dayjs(value));
          const mayExpire = dayjs().isAfter(dayjs(value).subtract(72, 'hour'));
          const status = expired
            ? { color: 'red.500', text: '（已过期）' }
            : mayExpire
            ? { color: 'orange.500', text: '（快过期）' }
            : { color: 'green.500', text: '' };
          return (
            <Box as="span" whiteSpace="pre" color={status.color} fontSize="0.8rem">
              {dayjs(value).format('YYYY / MM / DD\nHH:mm')}
              {status.text}
            </Box>
          );
        },
      },
      {
        Header: '任务状态',
        accessor: 'state',
        Cell: ({ value }) => {
          const { color, text } = TASK_STATE_MAP[value];
          return (
            <Tag variant="solid" colorScheme={color}>
              {text}
            </Tag>
          );
        },
      },
      {
        Header: '',
        accessor: 'uid',
        Cell: ({ row }) => {
          const { original: task } = row;
          const opts = operations || TASK_STATE_MAP[task.state].operations.map((name) => taskOperationMap[name]);
          return (
            <HStack width={32} justify="right" spacing="2.5">
              {opts.map(({ name: optName, icon, label, colorScheme }) => (
                <TipButton
                  key={optName}
                  {...iconButtonStyles}
                  colorScheme={colorScheme}
                  icon={icon}
                  aria-label={label}
                  onClick={() => onOperateTask(optName, task, name)}
                ></TipButton>
              ))}
            </HStack>
          );
        },
      },
    ],
    [name, operations, onOperateTask]
  );

  const data = useMemo(() => (Array.isArray(tasks) ? tasks : []), [tasks]);

  return (
    <Table
      className="tasks-table"
      data={data}
      columns={columns}
      isLoading={!Array.isArray(tasks)}
      pagination={pagination}
    />
  );
};
