import {
  Box,
  Button,
  Flex,
  Heading,
  Popover,
  PopoverArrow,
  PopoverBody,
  PopoverContent,
  PopoverTrigger,
  Tag,
  useDisclosure,
  UseToastOptions,
} from '@chakra-ui/react';
import { Column, Table } from '@forest/simple-ui';
import { createForm, onFieldInputValueChange, onFieldValueChange } from '@formily/core';
import { createSchemaField, FormProvider } from '@formily/react';
import { FC, useEffect, useMemo, useState } from 'react';
import { RiLogoutCircleLine } from 'react-icons/ri';

import { useRequest } from '@/hooks/use-request';

import { getJobList, ListJobRequest, PhasesProgressReply, revertJobsWithFilter } from '../api';
import type { Job } from '../types';
import {
  defaultExecteamOption,
  defaultExecutorOption,
  jobFilterFormProps,
  jobFilterSchema,
  jobRevertFormProps,
  jobRevertSchema,
  OptionProps,
} from './forms';

const getStateConifg = (state?: Job['state'], phaseName = '工作') => {
  let text = '';
  let color = '';
  switch (state) {
    case 'unstart':
      text = `待${phaseName}`;
      color = 'orange';
      break;
    case 'doing':
      text = `${phaseName}进行中`;
      color = 'blue';
      break;
    case 'finished':
      text = `${phaseName}完成`;
      color = 'green';
      break;
    case 'unspecified':
      text = '数据状态无指定';
      color = 'gray';
      break;
    default:
      text = '处理中';
      color = 'gray';
      break;
  }
  return { text, color };
};

const JobFilterSchemaField = createSchemaField(jobFilterSchema.config);
const JobRevertSchemaField = createSchemaField(jobRevertSchema.config);

export interface JobsDetailProps {
  taskUid: string;
  phaseIndex: number;
  phasesProgress?: PhasesProgressReply['phasesProgress'];
  updateProgress: () => void;
}

type RevertType = 'revert' | 'release';
const RevertText: Record<RevertType, string> = {
  release: '释放',
  revert: '打回',
};

export const JobsDetail: FC<JobsDetailProps> = ({ taskUid, phaseIndex, phasesProgress = [], updateProgress }) => {
  const [revertType, setRevertType] = useState<RevertType>('revert');
  const [jobFilterParams, setJobFilterParams] = useState<ListJobRequest>({
    filterLotUid: taskUid,
    filterPhase: 1,
  });
  const { onOpen, onClose: onCloseRevertPopover, isOpen } = useDisclosure();

  const { data: jobData, isLoading: jobLoading, refetch: updateJobs } = useRequest(getJobList);

  const { isLoading: reverting, refetch: revertJobs } = useRequest(revertJobsWithFilter, {
    onSuccess: (data, _, showToast) => {
      const { ok_job_uids, fail_job_uids } = data;
      const info: UseToastOptions =
        fail_job_uids.length === 0
          ? {
              title: `${RevertText[revertType]}成功，共计${ok_job_uids.length}个任务包`,
              status: 'success',
            }
          : {
              title: `部分任务包${RevertText[revertType]}成功`,
              description: `失败的任务包ID列表：${fail_job_uids.join(', ')}`,
              status: 'info',
              duration: 10000,
              isClosable: true,
            };
      showToast(info);
      updateProgress();
      updateJobs({ params: jobFilterParams });
      onCloseRevertPopover();
    },
  });

  const jobs = useMemo(() => {
    return jobData?.jobs ? jobData.jobs : [];
  }, [jobData]);

  const columns: Column<Job>[] = useMemo(
    () => [
      {
        Header: '任务包ID',
        accessor: 'uid',
      },
      {
        Header: '数据状态',
        accessor: 'state',
        Cell: ({ value }) => {
          const { text, color } = getStateConifg(value, phasesProgress[phaseIndex]?.name);
          return (
            <Tag variant="subtle" colorScheme={color}>
              {text}
            </Tag>
          );
        },
      },
      {
        Header: '最近处理团队',
        accessor: 'last_execteam',
        Cell: ({ value }) => {
          return <Box>{value?.name || '（未领取）'}</Box>;
        },
      },
      {
        Header: '最近处理人',
        accessor: 'last_executor',
        Cell: ({ value }) => {
          return <Box>{value ? `${value.name} (${value.uid})` : '（未领取）'}</Box>;
        },
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [jobData]
  );

  const jobFilterForm = useMemo(
    () =>
      createForm<jobFilterFormProps>({
        effects: () => {
          onFieldInputValueChange('execteam', (field) => {
            const { value, data } = field;
            field.query('executor').take((target) => {
              if ('dataSource' in target) {
                target.dataSource = [defaultExecutorOption, ...(data[value] || [])];
                target.value = defaultExecutorOption.value;
              }
            });
          });
        },
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [taskUid]
  );

  const jobRevertForm = useMemo(
    () =>
      createForm<jobRevertFormProps>({
        effects: () => {
          onFieldValueChange('toPhase', (field) => {
            const { value, dataSource } = field;
            if (Array.isArray(dataSource) && value === dataSource.length) {
              setRevertType('release');
            } else {
              setRevertType('revert');
            }
          });
        },
      }),
    []
  );

  useEffect(() => {
    onCloseRevertPopover();
    if (phasesProgress[phaseIndex]) {
      const phase = phasesProgress[phaseIndex];
      const isLastPhase = phaseIndex === phasesProgress.length - 1;

      const states: Job['state'][] = isLastPhase
        ? ['unspecified', 'unstart', 'doing', 'finished']
        : ['unspecified', 'unstart', 'doing'];
      const stateOptions = states.map((state) => ({
        label: getStateConifg(state, phase.name).text,
        value: state,
      }));

      const teamOptions: OptionProps[] = [];
      const teamExecutorMap: Record<string, OptionProps[]> = {};

      phase.teams.forEach(({ team, executors }) => {
        const { uid, name } = team;
        teamOptions.push({
          label: name,
          value: uid,
        });
        teamExecutorMap[uid] = executors.map((user) => ({
          label: user.name,
          value: user.uid,
        }));
      });

      jobFilterForm.setFieldState('execteam', {
        dataSource: [defaultExecteamOption, ...teamOptions],
        data: teamExecutorMap,
      });
      jobFilterForm.setFieldState('executor', {
        dataSource: [defaultExecutorOption],
      });
      jobFilterForm.setFieldState('filterState', {
        dataSource: stateOptions,
      });

      const prevPhaseOptions: OptionProps[] = [];
      for (let i = 0; i <= phaseIndex; i++) {
        const { name, id } = phasesProgress[i];
        prevPhaseOptions.push({
          label: name,
          value: id,
        });
      }
      // 每次改变 phase 的时候，需要 reset。保证打开的阶段和对应的限制是匹配的
      jobRevertForm.reset();
      jobRevertForm.setFieldState('toPhase', {
        dataSource: prevPhaseOptions,
        value: prevPhaseOptions[phaseIndex]?.value,
      });

      jobFilterForm.setValues(
        {
          uid: '',
          execteam: 'unspecified',
          executor: 'unspecified',
          filterState: 'unspecified',
        },
        'overwrite'
      );

      setJobFilterParams({
        page: 0,
        filterLotUid: taskUid,
        filterPhase: phaseIndex + 1,
        showLastExecutor: true,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [phasesProgress, phaseIndex]);

  useEffect(() => {
    if (jobFilterParams.filterLotUid?.length && typeof jobFilterParams.page === 'number') {
      updateJobs({
        params: jobFilterParams,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [jobFilterParams]);

  const onClickFilter = () => {
    jobFilterForm.submit((values: jobFilterFormProps) => {
      const { uid, execteam, executor, filterState } = values;
      setJobFilterParams((oldParams) => {
        // Job uid 筛选优先于条件筛选
        const filterParams = uid
          ? {
              filterUids: [uid],
              filterState: undefined,
              filterLastExecteam: undefined,
              filterLastExecutors: undefined,
            }
          : {
              filterUids: undefined,
              filterState,
              filterLastExecteam: execteam && execteam !== 'unspecified' ? execteam : undefined,
              filterLastExecutors: executor && executor !== 'unspecified' ? [executor] : undefined,
            };
        return {
          ...oldParams,
          ...filterParams,
          page: 0,
        };
      });
    });
  };

  const onRevertJobs = () => {
    if (jobFilterParams) {
      jobRevertForm.submit((values: jobRevertFormProps) => {
        const {
          toPhase: to_phase,
          keepAnnos: keep_annos,
          keepComments: keep_comments = false,
          toPreviousExecutor: to_previous_executor,
        } = values;
        const {
          filterUids: uids,
          filterLotUid: lot_uid,
          filterPhase: phase,
          filterState: state,
          filterLastExecutors: last_executors,
          filterLastExecteam: last_execteam,
        } = jobFilterParams;

        revertJobs({
          params: {
            action: { to_phase },
            options: { keep_annos, keep_comments, to_previous_executor },
            filter: {
              uids,
              lot_uid,
              phase,
              state,
              last_executors,
              last_execteam,
            },
          },
        });
      });
    }
  };

  return (
    <Box>
      <Flex justify="space-between">
        <Heading size="md">任务包详情</Heading>
        <Popover
          placement="bottom-end"
          closeOnBlur={false}
          isOpen={isOpen}
          onOpen={onOpen}
          onClose={onCloseRevertPopover}
        >
          <PopoverTrigger>
            <Button
              colorScheme="yellow"
              leftIcon={<RiLogoutCircleLine />}
              iconSpacing={1}
              isLoading={reverting}
              isDisabled={jobLoading || jobs.length === 0}
            >
              打回数据
            </Button>
          </PopoverTrigger>

          <PopoverContent>
            <PopoverArrow />
            <PopoverBody px="6" pt="4" pb="3.5">
              <FormProvider form={jobRevertForm}>
                <JobRevertSchemaField schema={jobRevertSchema.schema} />
              </FormProvider>
              <Box textAlign="right" mt={2}>
                <Button size="sm" onClick={onCloseRevertPopover} mr="2">
                  取消
                </Button>
                <Button size="sm" variant="outline" colorScheme="orange" onClick={onRevertJobs}>
                  {RevertText[revertType]}
                </Button>
              </Box>
            </PopoverBody>
          </PopoverContent>
        </Popover>
      </Flex>

      <Flex my={3}>
        <FormProvider form={jobFilterForm}>
          <JobFilterSchemaField schema={jobFilterSchema.schema} />
        </FormProvider>
        <Button variant="text" colorScheme="teal" onClick={onClickFilter}>
          提交筛选
        </Button>
      </Flex>

      <Table data={jobs} columns={columns} isLoading={jobLoading} />
    </Box>
  );
};
