import { Box, Flex } from '@chakra-ui/react';
import { ReactNode } from 'react';

export interface ProgressTitleProps {
  icon: ReactNode;
  title: string;
  desc: string;
  color?: string;
}
export const ProgressTitle = ({
  icon,
  title,
  desc,
  color = 'blue', // green / red
}: ProgressTitleProps) => (
  <Flex pt="1.5" align="top">
    <Box
      mr="3"
      mt="0.5"
      p="2"
      h="fit-content"
      fontSize="0.8rem"
      borderRadius="full"
      border="1px"
      borderColor={`${color}.600`}
      color={`${color}.800`}
      bg={`${color}.50`}
    >
      {icon}
    </Box>
    <Box>
      <Box fontSize="xl" color="gray.700">
        {title}
      </Box>
      <Box fontSize="sm" fontWeight="normal" color="gray.600">
        {desc}
      </Box>
    </Box>
  </Flex>
);
