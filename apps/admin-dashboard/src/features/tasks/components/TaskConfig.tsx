import { Box, useToast } from '@chakra-ui/react';
import { useObservable } from '@forest/hooks';
import { CollapseContainer } from '@forest/simple-ui';
import { useEffect } from 'react';
import { RiNumber1, RiNumber2, RiNumber3, RiNumber4 } from 'react-icons/ri';

import { ProgressData, ProgressExport, ProgressInfo, ProgressPhases, ProgressTitle } from '../components';
import { taskStore } from '../stores';

const ProgressConfig = [
  {
    name: 'data',
    title: '数据配置',
    desc: '请选择适合您的方式以配置数据',
    icon: <RiNumber1 />,
    children: <ProgressData />,
  },
  {
    name: 'info',
    title: '信息填写',
    desc: '请填写任务基本信息',
    icon: <RiNumber2 />,
    children: <ProgressInfo />,
  },
  {
    name: 'phases',
    title: '流程编排',
    desc: '请为任务编排流程，并添加标签配置',
    icon: <RiNumber3 />,
    children: <ProgressPhases />,
  },
  {
    name: 'export',
    title: '数据导出',
    desc: '请填写数据导出配置',
    icon: <RiNumber4 />,
    children: <ProgressExport />,
  },
];

interface TaskConfigProps {
  onError?: () => void;
}
export const TaskConfig = ({ onError }: TaskConfigProps) => {
  const { state, error, onSubmit, changedData } = useObservable(taskStore.subject, taskStore.subject.value);
  const showToast = useToast({ position: 'top', duration: 3000 });

  useEffect(() => {
    switch (state) {
      case 'fail':
        if (error) {
          showToast({
            title: error,
            description: '请完善配置后再提交',
            status: 'warning',
          });
        }
        onError?.();
        taskStore.resetState();
        break;
      case 'success':
        onSubmit?.(changedData);
        taskStore.resetState();
        break;
      default:
        break;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state]);

  return (
    <Box>
      {ProgressConfig.map(({ name, title, desc, icon, children }) => (
        <CollapseContainer
          key={name}
          title={<ProgressTitle icon={icon} title={title} desc={desc} />}
          defaultShow={true}
          expandButtonType="icon"
          mb="4"
          py="1.6rem"
          pr="9"
        >
          <Box pl="10" pr="6" pt="4" pb="2">
            {children}
          </Box>
        </CollapseContainer>
      ))}
    </Box>
  );
};
