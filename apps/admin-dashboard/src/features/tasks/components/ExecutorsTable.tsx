import { Avatar, Flex, Text } from '@chakra-ui/react';
import { Column, IPagination, Table } from '@forest/simple-ui';
import { useMemo } from 'react';

import type { User } from '@/features/users/types';

export interface ExecutorsTableProps {
  members?: User[];
  pagination: IPagination;
  onSelectMembersChange?: (changedIds: number[], selected: boolean) => void;
}

export const ExecutorsTable = ({ members, pagination, onSelectMembersChange = () => {} }: ExecutorsTableProps) => {
  const columns: Column<User>[] = useMemo(
    () => [
      {
        Header: '成员姓名',
        accessor: 'name',
        Cell: ({ row }) => {
          const {
            original: { name, avatar },
          } = row;
          return (
            <Flex align="center">
              <Avatar name={name} src={avatar} size="sm"></Avatar>
              <Text ml="2.5" noOfLines={1} width="6rem">
                {name}
              </Text>
            </Flex>
          );
        },
      },
      {
        Header: '已分配任务数',
        accessor: 'uid',
        Cell: ({ row }) => {
          return <Text>_</Text>;
        },
      },
      {
        Header: '手机号',
        accessor: 'phone',
      },
      {
        Header: '角色',
        accessor: 'role',
      },
    ],
    []
  );
  const data = useMemo(() => (Array.isArray(members) ? members : []), [members]);

  return (
    <Table
      data={data}
      columns={columns}
      pagination={pagination}
      isLoading={!Array.isArray(members)}
      rowSelection={{
        onChange: onSelectMembersChange,
      }}
    />
  );
};
