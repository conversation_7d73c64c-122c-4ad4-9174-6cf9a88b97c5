import { Box, Input, InputGroup, InputLeftElement, Tab, TabList, TabPanel, TabPanels, Tabs } from '@chakra-ui/react';
import { debounce } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { RiSearchLine } from 'react-icons/ri';

import { useRequest } from '@/hooks/use-request';

import { getDataList } from '../../api';
import type { Data } from '../../types';
import { DataTable } from './DataTable';

export interface DataTabsProps {
  selectedKey?: string;
  onChange: (dataUid: string) => void;
}
export const DataTabs: React.FC<DataTabsProps> = ({ onChange, selectedKey }) => {
  const [currentDatas, setCurrentDatas] = useState<Array<Data & { isSelected?: boolean }>>([]);

  const params = useRef({ page: 0, pagesz: 20, total: 0, namePattern: '' });

  const { refetch: updateDataList } = useRequest(getDataList, {
    params: { ...params.current },
    enabled: true,
    onSuccess({ datas, total }) {
      if (datas) {
        setCurrentDatas(datas);
      }
      if (params.current.page === 0 && total !== undefined) {
        params.current.total = total;
      }
    },
  });

  useEffect(() => {
    if (currentDatas.length === 0) return;
    if (!selectedKey) {
      setCurrentDatas((oldDatas) => {
        oldDatas.forEach((data) => (data.isSelected = false));
        return [...oldDatas];
      });
      return;
    }
    let current = 0;
    for (const len = currentDatas.length; current < len; current++) {
      if (currentDatas[current].uid === selectedKey) {
        break;
      }
    }
    // 如果没找到或者已经是 selected 状态了
    if (currentDatas[current] && !currentDatas[current].isSelected) {
      setCurrentDatas((oldDatas) => {
        oldDatas.forEach((data) => (data.isSelected = false));
        return oldDatas.slice(0, current).concat(
          [
            {
              ...oldDatas[current],
              isSelected: true,
            },
          ],
          oldDatas.slice(current + 1)
        );
      });
    }
  }, [selectedKey, currentDatas]);

  // 此处的 onSelectDataChange 无法获取最新的 currentDatas
  const onSelectDataChange = (changedIds: number[]) => {
    if (changedIds.length > 0) {
      // 仅需处理单选
      setCurrentDatas((oldDatas) => {
        const cid = changedIds[0];
        if (!oldDatas[cid].isSelected) {
          onChange(oldDatas[cid].uid);
          for (let i = 0; i < oldDatas.length; i++) {
            if (oldDatas[i].isSelected) {
              oldDatas[i].isSelected = false;
            }
          }
          return oldDatas.slice(0, cid).concat(
            [
              {
                ...oldDatas[cid],
                isSelected: true,
              },
            ],
            oldDatas.slice(cid + 1)
          );
        }
        return oldDatas;
      });
    }
  };

  const onChangePage = (currentPage: number) => {
    const newParams = { ...params.current, page: currentPage - 1 };
    updateDataList({ params: newParams });
    params.current = newParams;
  };

  const debounceUpdateList = debounce((e) => {
    params.current = { ...params.current, namePattern: e.target.value };
    onChangePage(1);
  }, 1000);

  return (
    <Tabs variant="soft-rounded" colorScheme="logo">
      <TabList>
        {/* <Tab>使用已有项目数据</Tab> */}
        <Tab>使用后台导入数据</Tab>
        <InputGroup size="sm" w="15rem" ml="auto">
          <InputLeftElement pointerEvents="none" children={<RiSearchLine />} />
          <Input variant="filled" pr="4.5rem" placeholder="请输入数据名称" onChange={debounceUpdateList} />
        </InputGroup>
      </TabList>
      <TabPanels>
        {/* <TabPanel>
          <p>使用已有项目数据</p>
        </TabPanel> */}
        <TabPanel>
          <Box py={4}>
            <DataTable
              datas={currentDatas}
              pagination={{
                total: params.current.total,
                defaultPageSize: params.current.pagesz,
                onChange: onChangePage,
              }}
              onSelectDataChange={onSelectDataChange}
            />
          </Box>
        </TabPanel>
      </TabPanels>
    </Tabs>
  );
};

export default DataTabs;
