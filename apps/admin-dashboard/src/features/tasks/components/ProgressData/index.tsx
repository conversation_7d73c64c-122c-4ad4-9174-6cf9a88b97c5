import { Box, Heading } from '@chakra-ui/react';
import { useObservable } from '@forest/hooks';
import { useEffect, useState } from 'react';

import { taskStore } from '../../stores';
import { DataTabs } from './DataTabs';

export const ProgressData = () => {
  const [dataUid, setDataUid] = useState<string>();

  const { task, mode, state } = useObservable(taskStore.subject, taskStore.subject.value);

  useEffect(() => {
    if (state === 'data') {
      if (dataUid || mode === 'edit') {
        taskStore.pushNewState('data', { data_uid: dataUid });
      } else {
        taskStore.quitSubmit('未完成步骤1：数据配置');
      }
    } else if (state === 'wait' && task?.data_uid) {
      setDataUid(task.data_uid);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state, task]);

  return (
    <Box>
      {mode === 'edit' ? (
        <Heading size="md">当前项目数据ID为：{task?.data_uid}</Heading>
      ) : (
        <DataTabs onChange={setDataUid} selectedKey={dataUid} />
      )}
    </Box>
  );
};
