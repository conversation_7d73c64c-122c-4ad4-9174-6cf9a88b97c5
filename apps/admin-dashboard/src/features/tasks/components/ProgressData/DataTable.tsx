import { Box, Link, Tag } from '@chakra-ui/react';
import { Column, IPagination, Table } from '@forest/simple-ui';
import dayjs from 'dayjs';
import { useMemo } from 'react';

import { DATA_TYPE_MAP } from '../../config';
import type { Data } from '../../types';

export interface DataTableProps {
  datas?: Data[];
  pagination: IPagination;
  onSelectDataChange: (changedIds: number[], selected: boolean) => void;
}

export const DataTable = ({ datas, pagination, onSelectDataChange }: DataTableProps) => {
  const columns: Column<Data>[] = useMemo(
    () => [
      {
        Header: '数据名称',
        accessor: 'name',
        Cell: ({ row }) => {
          const {
            original: { name, uid },
          } = row;
          return (
            <Box width="10rem">
              <Link color="brand.500" fontWeight="bold">
                {name}
              </Link>
              <Box color="gray.600" fontSize="0.8rem" mt="0.5">
                ID: {uid}
              </Box>
            </Box>
          );
        },
      },
      {
        Header: '描述',
        accessor: 'desc',
      },
      {
        Header: '类型',
        accessor: 'type',
        Cell: ({ value }) => {
          return <Tag colorScheme="gray">{DATA_TYPE_MAP[value]}</Tag>;
        },
      },
      {
        Header: '状态',
        accessor: 'state',
      },
      {
        Header: '创建时间',
        accessor: 'created_at',
        Cell: ({ value }) => (
          <Box as="span" whiteSpace="pre" color="gray.600" fontSize="0.9rem">
            {dayjs(value).format('YYYY / MM / DD\nHH:mm')}
          </Box>
        ),
      },
    ],
    []
  );
  const data = useMemo(() => (Array.isArray(datas) ? datas : []), [datas]);

  return (
    <Table
      data={data}
      columns={columns}
      isLoading={!Array.isArray(datas)}
      pagination={pagination}
      rowSelection={{
        type: 'radio',
        onChange: onSelectDataChange,
      }}
    />
  );
};
