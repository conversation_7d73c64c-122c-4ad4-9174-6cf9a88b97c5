import { Box } from '@chakra-ui/react';
import { useObservable } from '@forest/hooks';
import { createForm } from '@formily/core';
import { createSchemaField, FormProvider } from '@formily/react';
import { useEffect } from 'react';

import { taskStore } from '../stores';
import { taskExportSchema } from './forms';

const TaskExportSchemaField = createSchemaField(taskExportSchema.config);
const taskExportForm = createForm();

export const ProgressExport = () => {
  const { task, state, mode } = useObservable(taskStore.subject, taskStore.subject.value);

  useEffect(() => {
    const out = task?.out ?? { exporter: {} };
    taskExportForm.setValues({ out: JSON.stringify(out, undefined, 2) }, 'overwrite');
  }, [task]);

  useEffect(() => {
    taskExportForm.disabled = mode === 'edit';
  }, [mode]);

  useEffect(() => {
    if (state === 'export') {
      taskExportForm
        .validate()
        .then(() => {
          taskExportForm.submit(({ out }) => {
            taskStore.pushNewState(state, {
              out: JSON.parse(out),
            });
          });
        })
        .catch(() => {
          taskStore.quitSubmit('未完成步骤2：信息填写');
        });
    }
  }, [state]);

  return (
    <Box>
      <FormProvider form={taskExportForm}>
        <TaskExportSchemaField schema={taskExportSchema.schema}></TaskExportSchemaField>
      </FormProvider>
    </Box>
  );
};
