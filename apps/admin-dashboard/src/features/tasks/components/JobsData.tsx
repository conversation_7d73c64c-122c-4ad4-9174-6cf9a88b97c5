import {
  Box,
  Button,
  Flex,
  Heading,
  Input,
  InputGroup,
  InputLeftAddon,
  Popover,
  PopoverArrow,
  PopoverBody,
  PopoverContent,
  PopoverTrigger,
  Tab,
  TabList,
  Tabs,
  useDisclosure,
} from '@chakra-ui/react';
import { Column, Table } from '@forest/simple-ui';
import { FC, useEffect, useMemo, useRef, useState } from 'react';
import { RiLogoutCircleLine } from 'react-icons/ri';

import { useRequest } from '@/hooks/use-request';

import { ExecutorCounter, getLotStatData, PhasesProgressReply } from '../api';

const formatPercentNumber = (x: number) => {
  if (x >= 0 && x <= 1) {
    return `${Math.round(x * 100)}%`;
  }
  return '评估中';
};
export interface JobsDataProps {
  taskUid: string;
  phaseIndex: number;
  phasesProgress?: PhasesProgressReply['phasesProgress'];
  updateProgress: () => void;
}
export const JobsData: FC<JobsDataProps> = ({ taskUid, phaseIndex, phasesProgress = [] }) => {
  const [tabIndex, setTabIndex] = useState(0);
  const [filterName, setFilterName] = useState('');

  const { onOpen, onClose: onCloseRevertPopover, isOpen } = useDisclosure();

  const { data: statData, isLoading: statLoading, refetch: updateLotStat } = useRequest(getLotStatData);

  const inputRef = useRef<HTMLInputElement>(null);

  const counters = useMemo(() => {
    return statData?.counters?.length
      ? filterName.length
        ? statData.counters.filter((item) => item.executor.name === filterName)
        : statData.counters
      : [];
  }, [statData, filterName]);

  const columns: Column<ExecutorCounter>[] = useMemo(
    () => [
      {
        Header: '参与对象',
        accessor: 'executor',
        Cell: ({ value }) => {
          return (
            <Box>
              {value.name} ({value.uid})
            </Box>
          );
        },
      },
      {
        Header: '工作速度(帧 / 包)',
        accessor: 'elems_per_hour',
        Cell: ({ row }) => {
          const {
            original: { elems_per_hour, jobs_per_hour },
          } = row;
          return (
            <Box>
              {elems_per_hour.toFixed(1)} / {jobs_per_hour.toFixed(1)}
            </Box>
          );
        },
      },
      {
        Header: '准确率(元素准 / 帧准)',
        accessor: 'ins_accuracy',
        Cell: ({ row }) => {
          const {
            original: { ins_accuracy, elem_accuracy },
          } = row;

          return (
            <Box>
              {formatPercentNumber(ins_accuracy)} / {formatPercentNumber(elem_accuracy)}
            </Box>
          );
        },
      },
      {
        Header: '工作包量',
        accessor: 'jobs',
      },
      {
        Header: '工作帧数',
        accessor: 'elems',
      },
      {
        Header: '框数(2D / 3D)',
        accessor: 'ins_by_widget',
        Cell: ({ value }) => {
          return (
            <Box>
              {value.anno2d} / {value.anno3d}
            </Box>
          );
        },
      },
    ],
    []
  );

  useEffect(() => {
    updateLotStat({
      params: {
        phase: phasesProgress[phaseIndex]?.id || 1,
        uid: taskUid,
        byExecteam: tabIndex === 1 ? true : false,
        page: 0,
        pagesz: 100,
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [taskUid, phaseIndex, tabIndex]);

  const onChangeTab = (index: number) => {
    setTabIndex(index);
    setFilterName('');
    if (inputRef.current) {
      inputRef.current.value = '';
    }
  };

  const onClickFilter = () => {
    if (inputRef.current) {
      setFilterName(inputRef.current.value);
    }
  };

  return (
    <Box>
      <Flex justify="space-between">
        <Heading size="md">数据统计</Heading>
        <Popover
          placement="bottom-end"
          closeOnBlur={false}
          isOpen={isOpen}
          onOpen={onOpen}
          onClose={onCloseRevertPopover}
        >
          <PopoverTrigger>
            <Button colorScheme="blue" leftIcon={<RiLogoutCircleLine />} iconSpacing={1} isDisabled={true}>
              导出数据
            </Button>
          </PopoverTrigger>

          <PopoverContent>
            <PopoverArrow />
            <PopoverBody px="6" pt="4" pb="3.5">
              <Box textAlign="right" mt={2}>
                <Button size="sm" onClick={onCloseRevertPopover} mr="2">
                  取消
                </Button>
                <Button size="sm" variant="outline" colorScheme="orange">
                  导出
                </Button>
              </Box>
            </PopoverBody>
          </PopoverContent>
        </Popover>
      </Flex>

      <Tabs onChange={onChangeTab}>
        <TabList>
          <Tab>个人统计</Tab>
          <Tab>团队统计</Tab>
        </TabList>
      </Tabs>
      <Flex my={5} justify="flex-start">
        <InputGroup size="sm" width={300}>
          <InputLeftAddon children="名称" />
          <Input
            ref={inputRef}
            type="text"
            placeholder="请输入想查找的名称"
            onKeyDown={(event) => {
              if (event.key === 'Enter') {
                onClickFilter();
              }
            }}
          />
        </InputGroup>
        <Button variant="text" colorScheme="teal" onClick={onClickFilter}>
          筛选当前数据
        </Button>
      </Flex>
      <Table data={counters} columns={columns} isLoading={statLoading} />
    </Box>
  );
};

export default JobsData;
