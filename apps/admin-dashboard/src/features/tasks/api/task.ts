import { forkJoin, from, map, mergeMap, Observable, reduce, switchMap, throwError } from 'rxjs';

import {
  anno_v1_AssignExecteamRequest,
  anno_v1_ListExecteamsReply_Execteam as ListExecteamsReply_Execteam,
  anno_v1_Lot,
  anno_v1_ManageExecutorsRequest as ManageExecutorsRequest,
  Lots as LotService,
} from '@/service/anno/v1';

import { SHOW_STATE_MAP } from '../config';
import type { BaseUser, CloneLotProps, CreateLotProps, ListLotResult, Task, TaskShowState } from '../types';
import { revertJobsWithFilter } from './job';
import { getLotStatus } from './stat';

/** Task */

export type TaskListResult = Partial<
  Record<
    TaskShowState,
    {
      orgs: BaseUser[];
      total: number;
      lots: Task[];
    }
  >
>;
const formatTasksReply = (
  { orgs, total = 0, lots }: ListLotResult,
  showState: TaskShowState,
  jobCounts: any
): TaskListResult => {
  if (Array.isArray(orgs)) {
    const tasks = lots.map((lot, i) => {
      const jobCount = jobCounts.find((jobCount: any) => jobCount.lot_id === lot.uid);
      return {
        ...lot,
        job_counts: jobCount?.count ?? [],
        demandTeams: [orgs[i]],
      };
    });
    return { [showState]: { orgs, total, lots: tasks } };
  }
  const tasks = lots.map((lot, i) => {
    const jobCount = jobCounts.find((jobCount: any) => jobCount.lot_id === lot.uid);
    return {
      ...lot,
      job_counts: jobCount?.count ?? [],
    };
  });
  return { [showState]: { orgs: [] as BaseUser[], total, lots: tasks } };
};

export interface TaskListRequest {
  page: number | Array<number>;
  pagesz: number;
  states?: Array<Task['state']>;
  showStates?: Array<TaskShowState>;
  type?: Task['type'];
  namePattern?: string;
  orgUid?: string;
  creatorUid?: string;
  withOrg?: boolean;
}
export interface TaskListByExecutorRequest {
  page: number | Array<number>;
  pagesz: number;
  states?: Array<Task['state']>;
  showStates?: Array<TaskShowState>;
  teamUid?: string;
  userUid?: string;
  orgUid?: string;
  namePattern?: string;
  type?: Task['type'];
}
export type TransRequest<T extends TaskListRequest | TaskListByExecutorRequest> = {
  page: number;
  pagesz: number;
  states?: Array<Task['state']>;
} & Omit<T, 'showStates' | 'page' | 'pagesz' | 'states'>;

export const formatTasksRequest = <T extends TaskListRequest | TaskListByExecutorRequest>({
  api,
  query,
}: {
  api: (query: TransRequest<T>) => Observable<ListLotResult>;
  query: T;
}): Observable<TaskListResult> => {
  const { showStates, states, page = 0, pagesz, ...rest } = query;

  if (Array.isArray(showStates)) {
    return from(showStates).pipe(
      mergeMap((showState, i) => {
        const states = SHOW_STATE_MAP[showState] as Array<Task['state']>;
        return api({
          page: Array.isArray(page) ? page[i] : page,
          pagesz,
          states,
          ...rest,
        }).pipe(
          switchMap((value) => {
            const ids: string[] = value.lots?.map((lot) => lot.uid);
            return from(LotService.lotsJobCountByLots({ ids })).pipe(
              map(({ lots }) => formatTasksReply(value, showState, lots))
            );
          })
        );
      }),
      reduce((acc, value) => ({ ...acc, ...value }))
    );
  } else {
    return api({
      page: Array.isArray(page) ? page[0] : page,
      pagesz,
      states,
      ...rest,
    }).pipe(
      switchMap((value) => {
        const ids: string[] = value.lots?.map((lot) => lot.uid);
        return from(LotService.lotsJobCountByLots({ ids })).pipe(
          map(({ lots }) => formatTasksReply(value, 'unknown', lots))
        );
      })
    );
  }
};

export const getTaskList = (params: TransRequest<TaskListRequest>) => {
  const { pagesz = 30, withOrg = true, ...rest } = params;
  return from(
    LotService.lotsListLot({
      pagesz,
      withOrg,
      ...rest,
    })
  );
};
export const getTasksByExecutor = (params: TransRequest<TaskListByExecutorRequest>) => {
  const { pagesz = 30, states, ...rest } = params;
  return from(
    LotService.lotsListLotsByExecutor({
      pagesz,
      states,
      ...rest,
    })
  );
};

export const getTask = ({ uid }: { uid: string }) =>
  from(
    LotService.lotsGetLot({
      uid,
    })
  );

export const createTask = (params: CreateLotProps) =>
  from(
    LotService.lotsCreateLot({
      requestBody: params,
    })
  );

export const cloneTask = (params: CloneLotProps & { sourceUid: string }) => {
  const { sourceUid: uid, ...requestBody } = params;
  return from(
    LotService.lotsCloneLot({
      uid,
      requestBody,
    })
  );
};

export const startTask = (uid: string) =>
  from(
    LotService.lotsStartLot({
      uid,
      requestBody: { uid },
    })
  );

export const pauseTask = (uid: string) =>
  from(
    LotService.lotsPauseLot({
      uid,
      requestBody: { uid },
    })
  );

export const cancelTask = (uid: string) =>
  from(
    LotService.lotsCancelLot({
      uid,
      requestBody: { uid },
    })
  );
export const deleteTask = (uid: string) => from(LotService.lotsDeleteLot({ uid }));

export interface ListExecutorsRequest {
  uid: string;
  phases: number[];
  page?: number;
  pagesz?: number;
  subtype?: string;
}
export const getTaskExecutors = (params: ListExecutorsRequest) => {
  const { uid, phases, page = 0, pagesz = 50 } = params;
  return from(phases).pipe(
    mergeMap((phase) =>
      from(
        LotService.lotsListExecutors({
          uid,
          phase,
          page,
          pagesz,
        })
      ).pipe(map(({ users }) => ({ [phase]: users || [] })))
    ),
    reduce((acc, value) => ({ ...acc, ...value }))
  );
};

export type ListExecteamsRequest = Parameters<typeof LotService.lotsListExecteams>[0];
export const getTaskExecteams = (params: ListExecteamsRequest) => from(LotService.lotsListExecteams(params));

export type { ManageExecutorsRequest };

export const manageTaskExecutors = (requestBody: ManageExecutorsRequest) => {
  const { uid, phases } = requestBody;

  return phases.length > 0
    ? from(LotService.lotsManageExecutors({ uid, requestBody }))
    : throwError(() => new Error('未检测到更新'));
};

export const assignTaskExecteam = ({
  uid,
  execteams,
}: {
  uid: string;
  execteams: anno_v1_AssignExecteamRequest['phases'];
}) =>
  from(
    LotService.lotsAssignExecteam({
      uid,
      requestBody: {
        uid,
        phases: execteams,
      },
    })
  );

export interface PhasesProgressReply {
  taskName: string;
  phasesProgress: Array<{
    id: number;
    name: string;
    finishRate: number; // 取值范围为[0, 1]
    leftDayNum: number; // -1 表示暂未开始 / 评估中
    teams: Array<ListExecteamsReply_Execteam>;
  }>;
  taskState: anno_v1_Lot['state'];
}
export const getTaskPhasesProgress = (uid: string): Observable<PhasesProgressReply> =>
  forkJoin([
    getTask({ uid }),
    getTaskExecteams({ uid, withExecteams: true, withExecutors: true }),
    getLotStatus(uid),
  ]).pipe(
    map(([{ name, phases, state }, { phases: phasesExec }, { phases: phasesStatus, total_elems }]) => {
      const len = phases.length;
      const progress: PhasesProgressReply['phasesProgress'] = new Array(len);

      for (let i = 0; i < len; i++) {
        progress[i] = {
          id: i + 1,
          name: phases[i].name,
          finishRate: 1 - phasesStatus[i].elems_to_work / total_elems,
          leftDayNum: Math.ceil(phasesStatus[i].estimated_days_left),
          teams: phasesExec[i].teams,
        };
      }
      return { taskName: name, phasesProgress: progress, taskState: state };
    })
  );

// 默认打回到最后一个 phase
export const restartFinishedTask = ({ lotUid, toPhase }: { lotUid: string; toPhase: number }) =>
  from(LotService.lotsStartLot({ uid: lotUid, requestBody: { uid: lotUid } })).pipe(
    mergeMap(() =>
      revertJobsWithFilter({
        action: { to_phase: toPhase },
        options: {
          keep_annos: true,
          keep_comments: true,
          to_previous_executor: false,
        },
        filter: {
          lot_uid: lotUid,
          phase: toPhase + 1,
        },
      })
    )
  );
