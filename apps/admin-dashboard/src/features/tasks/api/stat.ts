import { from } from 'rxjs';

import { Stats as StatService } from '@/service/annostat/v1';

export type {
  annostat_v1_ExecutorCounter as ExecutorCounter,
  annostat_v1_GetLotStatByExecutorReply as LotStatByExecutorReply,
} from '@/service/annostat/v1';

export const getLotStatus = (uid: string) => from(StatService.statsGetLotStatus({ uid }));

export type LotStatByExecutorRequest = Parameters<typeof StatService.statsGetLotStatByExecutor>[0];
export const getLotStatData = (params: LotStatByExecutorRequest) => from(StatService.statsGetLotStatByExecutor(params));
