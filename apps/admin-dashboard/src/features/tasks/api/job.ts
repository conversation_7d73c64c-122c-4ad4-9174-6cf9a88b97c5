import { from } from 'rxjs';

import { anno_v1_BatchRevertJobRequest, Jobs as JobService } from '@/service/anno/v1';

export type { anno_v1_ListJobFilter as ListJobFilter } from '@/service/anno/v1';

type ListJobParameters = Parameters<typeof JobService.jobsListJob>[0];
export interface ListJobRequest extends ListJobParameters {
  filterLotUid: string;
  filterPhase: number;
}
export const getJobList = (params: ListJobRequest) => from(JobService.jobsListJob(params));

export const revertJobsWithFilter = (params: anno_v1_BatchRevertJobRequest) =>
  from(JobService.jobsBatchRevertJob({ requestBody: params }));
