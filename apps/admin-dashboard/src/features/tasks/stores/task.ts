import { BehaviorSubject } from 'rxjs';

import { Task } from '@/features/tasks/types';

import { labelFlowStore, phaseStore } from '../components/ProgressPhases/stores';

export type ISubmitState = 'wait' | 'data' | 'info' | 'label' | 'review' | 'export' | 'success' | 'fail';
export interface ITask {
  mode: 'new' | 'edit';
  state: ISubmitState;
  task: Partial<Task> | null;
  changedData: Partial<Task>;
  onSubmit?: (data: Partial<Task>) => void;
  error?: string;
}

const initialState: ITask = {
  mode: 'new',
  state: 'wait',
  task: null,
  changedData: {},
};

const taskSubject = new BehaviorSubject<ITask>(initialState);

const getNextState = (state: ISubmitState) =>
  ((
    {
      wait: 'data',
      data: 'info',
      info: 'label',
      label: 'review',
      review: 'export',
      export: 'success',
    } as Record<ISubmitState, ISubmitState>
  )[state]);

export const taskStore = {
  subject: taskSubject,
  flush: (task: Task | null, mode?: 'new' | 'edit') => {
    taskSubject.next({
      ...initialState,
      task,
      mode: mode || 'new',
    });

    const { phases, ontologies, job_size } = task || {};
    phaseStore.flush({ phases });
    labelFlowStore.flush({ phases, ontologies, job_size });
  },
  startSubmit: (onSubmit: (data: Partial<Task>) => void, changedData = {}) => {
    // 进入提交阶段，依次检验各环节数据
    taskSubject.next({
      ...taskSubject.value,
      state: 'data',
      changedData,
      onSubmit,
    });
  },
  pushNewState: (currentState: ISubmitState, data?: Partial<Task>) => {
    const { changedData, ...rest } = taskSubject.value;
    taskSubject.next({
      ...rest,
      state: getNextState(currentState),
      changedData: {
        ...changedData,
        ...data,
      },
    });
  },
  quitSubmit: (error?: string) => {
    // 仅响应提交阶段的退出操作
    if (taskSubject.value.state !== 'wait' && taskSubject.value.state !== 'fail') {
      taskSubject.next({
        ...taskSubject.value,
        state: 'fail',
        error,
      });
    }
  },
  resetState: () => {
    taskSubject.next({
      ...taskSubject.value,
      state: 'wait',
      changedData: {},
      error: undefined,
      onSubmit: undefined,
    });
  },
};
