import { Data, Task, TaskOperation } from '../types';

export const SHOW_STATE_MAP: Record<string, Array<Task['state']>> = {
  wait: ['initializing', 'unstart'],
  progress: ['ongoing', 'paused'],
  end: ['finished', 'canceled'],
  unknown: ['unspecified'],
};

export const TASK_TYPE_MAP: Record<Task['type'], string> = {
  annotate: '标注',
  segment_instance: '实例分割',
  segment_semantic: '语义分割',
  segment_panoptic: '全景分割',
  unspecified: '未知类型',
};

export const DATA_TYPE_MAP: Record<Data['type'], string> = {
  image: '单图片',
  pointcloud: '单点云',
  fusion2d: '图片融合',
  fusion3d: '3D融合',
  fusion4d: '4D融合',
  unspecified: '未知类型',
};

export const TASK_STATE_MAP: Record<
  Task['state'] | 'waiting' | 'failed',
  {
    text: string;
    color: string;
    operations: TaskOperation[];
  }
> = {
  initializing: {
    text: '处理中',
    color: 'blackAlpha',
    operations: ['config', 'delete'],
  },
  waiting: {
    text: '等待中',
    color: 'gray',
    operations: ['config', 'delete'],
  },
  unstart: {
    text: '待启动',
    color: 'green',
    operations: ['config', 'start', 'copy'],
  },
  ongoing: {
    text: '生产中',
    color: 'logo',
    operations: ['config', 'pause', 'cancel', 'copy'],
  },
  paused: {
    text: '待恢复',
    color: 'cyan',
    operations: ['config', 'start', 'cancel'],
  },
  finished: {
    text: '已完成',
    color: 'teal',
    operations: ['delete', 'copy'],
  },
  canceled: {
    text: '已终止',
    color: 'yellow',
    operations: ['delete', 'copy'],
  },
  failed: {
    text: '创建失败',
    color: 'yellow',
    operations: ['delete'],
  },
  unspecified: {
    text: '未指定',
    color: 'gray',
    operations: ['delete'],
  },
};
