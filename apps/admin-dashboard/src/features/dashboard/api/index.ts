import dayjs from 'dayjs';
import { from, map } from 'rxjs';

import {
  annostat_v1_GetOngoingLotsReply_Lot as GetOngoingLotsReply,
  Stats as StatsService,
} from '@/service/annostat/v1';
import { daysSince } from '@/utils';

export interface LotsList extends GetOngoingLotsReply {
  label_days_left: string;
  estimated_days_left: string;
}

const transDay = (time: string | number, defaultValue?: string) => {
  if (typeof time === 'number') {
    if (time < 0) return '评估中';
    if (time === 0) return defaultValue ?? '0天';
    if (time > 90) return '超过90天';
    return Math.round(time) + '天';
  } else {
    const since = daysSince(time);
    if (since > 90) return '超过90天';
    if (since > 0) return since + '天';
    if (since < 0) return '逾期' + -since + '天';
    return defaultValue ?? '0天';
  }
};

export const getOngoingLots = (params: { page?: number | undefined; pagesz?: number | undefined }) => {
  return from(StatsService.statsGetOngoingLots(params)).pipe(
    map((ret) => {
      return {
        lots: ret.lots.map((item) => {
          return {
            ...item,
            created_at: dayjs(item.created_at).format('YYYY/MM/DD HH:mm'),
            exp_end_time: transDay(item.exp_end_time),
            label_days_left: transDay(item.phases[0]?.estimated_days_left, '评估中'),
            estimated_days_left: transDay(item.phases[item.phases.length - 1]?.estimated_days_left),
          };
        }),
        total: ret.total,
      };
    })
  );
};

export interface Productions {
  labels: Array<string>;
  anno2d: Array<number>;
  anno3d: Array<number>;
}

export const getProduction = () => {
  return from(StatsService.statsProductionByTime({})).pipe(
    map((ret) => {
      return {
        labels: ret.units.map((item) => dayjs(item.time_range.to).format('HH:mm')),
        anno2d: ret.units.map((item) => item.items.anno2d),
        anno3d: ret.units.map((item) => item.items.anno3d),
      };
    })
  );
};

export const getOrderConversion = () => {
  return from(StatsService.statsGetOrderConversion({})).pipe(
    map((data) => {
      return Math.round((data.lots / data.orders) * 100 || 0);
    })
  );
};

export const getLotCount = () => {
  return from(StatsService.statsGetLotCount({}));
};
