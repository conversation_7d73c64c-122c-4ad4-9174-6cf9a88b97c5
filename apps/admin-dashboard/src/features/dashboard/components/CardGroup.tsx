import { Box, Card, CardBody, Flex, SimpleGrid, Text } from '@chakra-ui/react';
import { RiBarChartFill, RiBookmark2Line, RiEdit2Line, RiPauseLine } from 'react-icons/ri';

import type { TaskStateCount } from '../types';

export const CardGroup: React.FC<{ data: TaskStateCount; name: string }> = ({ data, name }) => {
  const group = [
    {
      title: name + '总数',
      value: data?.total,
      icon: <RiBarChartFill color="#5B93FF" />,
      backgroundColor: '#EFF4FF',
    },
    {
      title: '生产中' + name + '数',
      value: data?.ongoing,
      icon: <RiEdit2Line color="#FFC327" />,
      backgroundColor: '#FFF7E1',
    },
    {
      title: '暂停中' + name + '数',
      value: data?.paused,
      icon: <RiPauseLine color="#FF8F6B" />,
      backgroundColor: '#FFF4F0',
    },
    {
      title: '已完成' + name + '数（包含已删除）',
      value: data?.finished,
      icon: <RiBookmark2Line color="#605BFF" />,
      backgroundColor: '#EFEFFF',
    },
  ];
  const CardRender = () => {
    return group.map((item, index) => (
      <Card key={index} backgroundColor="whiteAlpha.900">
        <CardBody>
          <Flex>
            <Flex
              borderRadius="50%"
              w="60px"
              h="60px"
              backgroundColor={item.backgroundColor}
              justifyContent="center"
              alignItems="center"
              fontSize="24px"
            >
              {item.icon}
            </Flex>

            <Box marginLeft={4}>
              <Text fontSize="22px" fontWeight="800">
                {item.value}
              </Text>
              <Text fontSize="14px">{item.title}</Text>
            </Box>
          </Flex>
        </CardBody>
      </Card>
    ));
  };

  return (
    <SimpleGrid spacing={3} columns={4} w="100%">
      {CardRender()}
    </SimpleGrid>
  );
};
