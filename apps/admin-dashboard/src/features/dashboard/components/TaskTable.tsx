import { Tag } from '@chakra-ui/react';
import { Column, IPagination, Table } from '@forest/simple-ui';

import { DATA_TYPE_MAP } from '@/features/tasks/config';

import type { LotsList } from '../api';

interface TaskProps {
  type: 'unspecified' | 'demander' | 'operator' | 'supplier' | undefined;
  data: Array<LotsList>;
  pagination: IPagination;
}

export const Task: React.FC<TaskProps> = ({ type, data, pagination }) => {
  const columns: Column<LotsList>[] = [
    {
      Header: '任务ID',
      accessor: 'uid',
    },
    {
      Header: '任务名',
      accessor: 'name',
    },
    {
      Header: '类型',
      accessor: 'data_type',
      Cell: ({ value }) => <>{DATA_TYPE_MAP[value]}</>,
    },
    {
      Header: type === 'supplier' ? '' : '数据量（帧）',
      accessor: 'data_size',
      Cell: ({ value }) =>
        type === 'supplier' ? null : (
          <Tag size="sm" variant="solid" colorScheme="teal">
            {value}
          </Tag>
        ),
    },
    {
      Header: '创建时间',
      accessor: 'created_at',
    },
    {
      Header: '离任务截止剩余天数',
      accessor: 'exp_end_time',
    },
    {
      Header: type === 'demander' ? '' : '离标注完成剩余天数',
      accessor: 'label_days_left',
      Cell: ({ value }) => (type === 'demander' ? null : <span>{value}</span>),
    },
    {
      Header: type === 'supplier' ? '' : '离交付（质控）完成剩余天数',
      accessor: 'estimated_days_left',
      Cell: ({ value }) => (type === 'supplier' ? null : <span>{value}</span>),
    },
  ];

  return <Table data={data} columns={columns} pagination={pagination} />;
};
