import { CircularProgress, CircularProgress<PERSON>abel, Flex } from '@chakra-ui/react';

interface RingChartProps {
  value: number;
}

export const RingChart: React.FC<RingChartProps> = ({ value }) => {
  return (
    <Flex w="100%" justifyContent="center">
      <CircularProgress value={value} color="#5B93FF" size="200px" capIsRound trackColor="#F7FAFF">
        <CircularProgressLabel fontSize="30px" fontWeight={800}>
          {value}%
        </CircularProgressLabel>
      </CircularProgress>
    </Flex>
  );
};
