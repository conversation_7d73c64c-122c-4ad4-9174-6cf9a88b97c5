import { Box, Container, Flex, Tab, Tab<PERSON>ist, Tabs, Text } from '@chakra-ui/react';
import {
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip,
} from 'chart.js';
import { useState } from 'react';
import { Line } from 'react-chartjs-2';

import type { Productions } from '../api';

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);
interface LineChartProps {
  source: Productions;
}

export const LineChart: React.FC<LineChartProps> = ({ source }) => {
  const [type, setType] = useState<'anno2d' | 'anno3d'>('anno2d');
  const options = {
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          beginAtZero: true,
          min: 0,

          // 以下配置为了解决ts类型报错
          sampleSize: 100,
          autoSkip: true,
          autoSkipPadding: 0,
          includeBounds: true,
          maxTicksLimit: 10,
        },
      },
    },
  };

  const data = {
    labels: source.labels,
    datasets: [
      {
        data: source[type],
        borderColor: '#5B93FF',
      },
    ],
  };

  return (
    <Container marginTop={3} w="170%" padding={4}>
      <Flex justifyContent="space-between" alignItems="center" margin="12px 5px">
        <Text fontSize="18px" fontWeight="700" color="blue.800">
          {type === 'anno2d' ? '2D' : '3D'}标注产量（帧）
        </Text>
        <Tabs
          variant="unstyled"
          size="xs"
          fontSize="16px"
          onChange={(index) => setType(index === 0 ? 'anno2d' : 'anno3d')}
        >
          <TabList>
            <Tab border="1px solid blue.100" _selected={{ bg: 'blue.100' }} borderRadius="4px 0 0 4px " padding={0.5}>
              2D
            </Tab>
            <Tab border="1px solid blue.100" _selected={{ bg: 'blue.100' }} borderRadius="0 4px 4px 0" padding={0.5}>
              3D
            </Tab>
          </TabList>
        </Tabs>
      </Flex>
      <Box>
        <Line options={options} data={data} height="295px" />
      </Box>
    </Container>
  );
};
