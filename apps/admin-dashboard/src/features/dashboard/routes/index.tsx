import { Container, Flex, Text } from '@chakra-ui/react';
import { useEffect, useRef } from 'react';

import { Header } from '@/components/layout';
import { useRequest } from '@/hooks/use-request';
import { useAuth } from '@/providers/auth';

import { getLotCount, getOngoingLots, getOrderConversion, getProduction, Productions } from '../api';
import { CardGroup, LineChart, RingChart, Task } from '../components';
import type { TaskStateCount } from '../types';

const pathList = [{ name: '仪表盘', path: '#' }];

export const Dashboard = () => {
  const pagination = useRef({ page: 0, pagesz: 8, total: 0 });
  const { user } = useAuth();

  const type = user?.team?.type;

  const { refetch: updateTask, data: taskData } = useRequest(getOngoingLots, {
    params: pagination.current,
    enabled: true,
    onSuccess: ({ total }) => {
      if (total) {
        pagination.current.total = total;
      }
    },
  });

  const onChangePage = (currentPage: number) => {
    const newParams = { ...pagination.current, page: currentPage - 1 };
    updateTask({ params: newParams });
    pagination.current = newParams;
  };

  const { refetch: getProductionData, data: productionData } = useRequest(getProduction, {});

  const { refetch: getConversionData, data: conversionData } = useRequest(getOrderConversion, {});

  const { data: countData } = useRequest(getLotCount, {
    enabled: true,
  });

  useEffect(() => {
    if (type === 'operator') getConversionData();
    if (type !== 'demander') getProductionData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type]);

  return (
    <Flex direction="column" minH="100%">
      <Header title="仪表盘" pathList={pathList}></Header>
      <Container padding={4}>
        <Text fontSize="18px" fontWeight="700" color="blue.800" marginLeft={1} marginTop={3}>
          任务
        </Text>
        <Task
          type={type}
          data={taskData?.lots ?? []}
          pagination={{
            total: pagination.current.total,
            defaultPageSize: pagination.current.pagesz,
            onChange: onChangePage,
          }}
        />
      </Container>

      <Flex>
        {type !== 'demander' && <LineChart source={productionData || ({} as Productions)} />}

        {type === 'operator' && (
          <Container marginTop={3} marginLeft={3} padding={4}>
            <Text fontSize="18px" fontWeight="700" color="blue.800" marginLeft={1} marginTop={3} marginBottom={4}>
              订单转化任务比
            </Text>
            <RingChart value={conversionData ?? 0} />
          </Container>
        )}
      </Flex>

      <Flex marginTop={3}>
        <CardGroup data={countData || ({} as TaskStateCount)} name={type === 'demander' ? '订单' : '任务'} />
      </Flex>
    </Flex>
  );
};

export default Dashboard;
