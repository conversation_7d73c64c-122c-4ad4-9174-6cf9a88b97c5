import { Container, Flex, Input, InputGroup, InputLeftElement } from '@chakra-ui/react';
import { SimpleDialog } from '@forest/simple-ui';
import { debounce } from 'lodash';
import { useRef, useState } from 'react';
import { RiSearchLine, RiUserAddLine } from 'react-icons/ri';
import { tap } from 'rxjs';

import { Header } from '@/components/layout';
import { useRequest } from '@/hooks/use-request';

import { CreateUserProps, createUsers, deleteUser, getUserList } from '../api';
import { UserAddDrawer, UserCard, UsersTable } from '../components';

const pathList = [{ name: '用户管理', path: '#' }];
export const UserManage = () => {
  const [currentUserId, setCurrentUserId] = useState(0);
  const [openAddDrawer, setOpenAddDrawer] = useState(false);
  const [openDelDialog, setOpenDelDialog] = useState(false);

  const pagination = useRef({ page: 0, pagesz: 8, total: 0, namePattern: '' });

  const { refetch: updateUserList, data: userList } = useRequest(getUserList, {
    params: pagination.current,
    enabled: true,
    onSuccess: ({ total }) => {
      if (total) {
        pagination.current.total = total;
      }
    },
  });

  const debounceUpdateList = debounce((e) => {
    pagination.current = {
      ...pagination.current,
      namePattern: e.target.value,
    };
    updateUserList({ params: pagination.current });
  }, 500);

  const { refetch: delUser, isLoading: deleting } = useRequest(deleteUser, {
    onSuccess: (_, opt, showToast) => {
      showToast({ title: '删除成功', status: 'success' });
      setOpenDelDialog(false);

      pagination.current.total--;
      updateUserList({ params: pagination.current });
    },
  });

  const onClickAdd = () => {
    setOpenAddDrawer(true);
  };
  const onCloseAddDrawer = () => {
    setOpenAddDrawer(false);
  };
  const onSubmitAddDrawer = (data: { users: Array<CreateUserProps> }) => {
    return createUsers(data).pipe(
      tap(() => {
        updateUserList({ params: pagination.current });
        setOpenAddDrawer(false);
      })
    );
  };

  const onDeleteUser = () => {
    const { uid } = userList?.users?.[currentUserId]!;
    if (uid) {
      delUser({ params: { uid } });
    }
  };

  const onChangePage = (currentPage: number) => {
    const newParams = { ...pagination.current, page: currentPage - 1 };
    updateUserList({ params: newParams });
    pagination.current = newParams;
  };

  return (
    <Flex direction="column" minH="100%">
      <Header
        title="用户管理"
        pathList={pathList}
        rightButton={{ tip: '新建账户', icon: <RiUserAddLine />, onClick: onClickAdd }}
      >
        <InputGroup h="2.5rem" size="sm">
          <InputLeftElement pointerEvents="none" children={<RiSearchLine />} />
          <Input
            variant="filled"
            borderRadius="lg"
            placeholder="请输入用户名称"
            onKeyDown={(event) => {
              if (event.key === 'Enter') {
                updateUserList({ params: pagination.current });
              }
            }}
            onChange={debounceUpdateList}
          />
        </InputGroup>
      </Header>
      <Flex width="100%" alignSelf="stretch">
        <Container px="3" py="2.5">
          <UsersTable
            users={userList?.users}
            pagination={{
              total: pagination.current.total,
              defaultPageSize: pagination.current.pagesz,
              onChange: onChangePage,
            }}
            selectUser={(index) => setCurrentUserId(index)}
          />
        </Container>
        <Container width="30rem" ml="4">
          <UserCard
            user={userList?.users?.[currentUserId]}
            onDeleteUser={() => setOpenDelDialog(true)}
            onUpdateUser={() => updateUserList({ params: pagination.current })}
          />
        </Container>
      </Flex>
      <UserAddDrawer isOpen={openAddDrawer} onClose={onCloseAddDrawer} onSubmit={onSubmitAddDrawer} />
      <SimpleDialog
        title="删除用户"
        content={`您希望删除用户【${userList?.users?.[currentUserId]?.name}】吗？删除后无法恢复`}
        confirmText="删除"
        isOpen={openDelDialog}
        confirmButtonProps={{ isLoading: deleting }}
        onCancel={() => setOpenDelDialog(false)}
        onConfirm={onDeleteUser}
      />
    </Flex>
  );
};

export default UserManage;
