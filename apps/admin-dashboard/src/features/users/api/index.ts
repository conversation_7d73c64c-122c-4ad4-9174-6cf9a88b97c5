import { from, map, of } from 'rxjs';

import { iam_v1_CreateUserRequest as CreateUserProps, iam_v1_User, Users as UserService } from '@/service/iam/v1';
import { getUpdatedFields } from '@/utils';

export type { CreateUserProps };

export const getUserList = (params: Parameters<typeof UserService.usersListUser>[0]) =>
  from(
    UserService.usersListUser({
      withOrg: true,
      ...params,
    })
  ).pipe(
    map(({ users, total, orgs }) => {
      if (Array.isArray(orgs)) {
        return {
          users: users.map((user, i) => ({
            ...user,
            team: orgs[i],
          })),
          total,
        };
      }
      return { users, total };
    })
  );

export const deleteUser = ({ uid }: { uid: string }) => from(UserService.usersDeleteUser({ uid }));

export const createUsers = (params: { users: Array<CreateUserProps> }) => {
  return from(
    UserService.usersBatchCreateUsers({
      requestBody: params,
    })
  );
};

export interface UpdateUserProps {
  origin: iam_v1_User;
  current: CreateUserProps;
  formFields?: Array<string>;
}
export const updateUserInfo = ({ origin, current, formFields }: UpdateUserProps) => {
  const fields = getUpdatedFields(origin, current, formFields);
  if (fields.length > 0) {
    return from(
      UserService.usersUpdateUser({
        userUid: origin.uid,
        requestBody: current,
        fields,
      })
    ).pipe(
      map((user) => ({
        user: { ...user!, teams: [] },
        changed: true,
      }))
    );
  } else {
    return of(origin).pipe(map((user) => ({ user, changed: false })));
  }
};
