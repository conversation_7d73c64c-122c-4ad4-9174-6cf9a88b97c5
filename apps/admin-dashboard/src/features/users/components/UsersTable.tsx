import { Avatar, Box, Flex, Tag, Text } from '@chakra-ui/react';
import { Column, IPagination, Table } from '@forest/simple-ui';
import { useMemo } from 'react';

import { GENDER_ZH } from '../config';
import type { User } from '../types';

export interface UsersTableProps {
  users?: User[];
  pagination: IPagination;
  selectUser?: (userIndex: number) => void;
}

export const UsersTable = ({ users, pagination, selectUser }: UsersTableProps) => {
  const columns: Column<User>[] = useMemo(
    () => [
      {
        Header: '姓名',
        accessor: 'name',
        Cell: ({ row }) => {
          const {
            original: { name, avatar, uid },
          } = row;
          return (
            <Flex align="center">
              <Avatar name={name} src={avatar} size="sm"></Avatar>
              <Box ml="2.5" width="8rem">
                <Text width="100%" size="sm" fontWeight="bold" color="brand.500" noOfLines={1}>
                  {name}
                </Text>
                <Box color="gray.600" fontSize="0.8rem" mt="0.5">
                  ID: {uid}
                </Box>
              </Box>
            </Flex>
          );
        },
      },
      {
        Header: '角色',
        accessor: 'role',
      },
      {
        Header: '手机号',
        accessor: 'phone',
      },
      {
        Header: '组织名',
        accessor: 'team',
        Cell: ({ row }) => {
          const {
            original: { team },
          } = row;
          return (
            <Text fontSize="0.9rem" color="gray.600" noOfLines={1} width="6rem">
              {team?.name || '无'}
            </Text>
          );
        },
      },
      {
        Header: '城市',
        accessor: 'city',
      },
      {
        Header: '性别',
        accessor: 'gender',
        Cell: ({ value }) => {
          return <Tag variant="subtle">{GENDER_ZH[value]}</Tag>;
        },
      },
    ],
    []
  );

  const data = useMemo(() => (Array.isArray(users) ? users : []), [users]);

  return (
    <Table
      data={data}
      columns={columns}
      isLoading={!Array.isArray(users)}
      pagination={pagination}
      onRow={(row) => ({
        onClick: () => selectUser && selectUser(row?.index),
      })}
    />
  );
};
