import { Avatar, Box, HStack, Text } from '@chakra-ui/react';
import { TipButton } from '@forest/simple-ui';
import { createForm } from '@formily/core';
import { createSchemaField, FormProvider } from '@formily/react';
import { ReactNode, useEffect, useState } from 'react';
import { RiDeleteBinLine, RiEditLine, RiSaveLine } from 'react-icons/ri';

import { useRequest } from '@/hooks/use-request';

import { updateUserInfo } from '../api';
import type { User } from '../types';
import { userEditSchema } from './forms';

interface UserCardProps {
  user?: User;
  children?: ReactNode;
  onDeleteUser?: () => void;
  onEditUser?: () => void;
  onSaveUser?: () => void;
  onUpdateUser?: () => void;
}

const iconButtonStyles = {
  variant: 'ghost',
  isRound: true,
  size: 'lg',
  fontSize: '24',
};
const UserEditSchemaField = createSchemaField(userEditSchema.config);
const userEditForm = createForm({
  readPretty: true,
});
export const UserCard: React.FC<UserCardProps> = ({
  user,
  onDeleteUser,
  onEditUser,
  onSaveUser,
  onUpdateUser,
  children,
}) => {
  const [isEdit, setEdit] = useState(false);

  const { refetch: updateUser, isLoading } = useRequest(updateUserInfo, {
    onSuccess: ({ changed }, _, showToast) => {
      if (changed) {
        showToast({ title: '修改成功', status: 'success' });
        onUpdateUser?.();
      }
      setEdit(false);
    },
  });

  const onClickEdit = () => {
    if (isEdit) {
      userEditForm.submit((values) => {
        userEditForm.readPretty = true;
        onSaveUser?.();
        updateUser({
          params: {
            origin: user!,
            current: values,
            formFields: Object.keys(userEditForm.getFormGraph()),
          },
        });
      });
    } else {
      setEdit(true);
      userEditForm.readPretty = false;
      onEditUser?.();
    }
  };

  useEffect(() => {
    if (user) {
      setEdit(false);
      userEditForm.setValues(user);
      userEditForm.readPretty = true;
    }
  }, [user]);

  if (!user) {
    return <></>;
  }

  const { name, avatar } = user;

  const buttonConfig = isEdit
    ? {
        'aria-label': '保存',
        colorScheme: 'teal',
        icon: <RiSaveLine />,
      }
    : {
        'aria-label': '编辑',
        colorScheme: 'logo',
        icon: <RiEditLine />,
      };

  return (
    <Box textAlign="center" px="4" py="8">
      <Avatar name={name} src={avatar} mt="10" size="lg"></Avatar>
      <Text fontSize="xl" mt="2.5">
        {name}
      </Text>
      <HStack justify="center" pt="4" pb="5" spacing="14" borderBottom="1px" borderColor="gray.200">
        <TipButton
          {...buttonConfig}
          tipProps={{ placement: 'bottom' }}
          {...iconButtonStyles}
          isLoading={isLoading}
          onClick={onClickEdit}
        ></TipButton>
        <TipButton
          aria-label="删除"
          colorScheme="orange"
          icon={<RiDeleteBinLine />}
          tipProps={{ placement: 'bottom' }}
          {...iconButtonStyles}
          onClick={onDeleteUser}
        ></TipButton>
      </HStack>

      <Box textAlign="left" px="0.5" pb="2">
        <Text fontSize="lg" fontWeight="bold" color="gray.700" mt="7" mb="5">
          {isEdit ? '编辑信息' : '基本信息'}
        </Text>
        <FormProvider form={userEditForm}>
          <UserEditSchemaField schema={userEditSchema.schema}></UserEditSchemaField>
        </FormProvider>
      </Box>
      {children}
    </Box>
  );
};
