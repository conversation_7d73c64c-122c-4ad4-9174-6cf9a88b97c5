import { ArrayTable, FormItem, Input, Select } from '@forest/formily';
import type { Field } from '@formily/core';

import { provinces } from '@/config/locations';

const config = {
  components: {
    ArrayTable,
    FormItem,
    Input,
    Select,
  },
  scope: {
    disableFirstRow: (field: Field) => {
      if (field.path.segments[1] === 0) {
        field.disabled = true;
      }
    },
  },
};

const schema = {
  type: 'object',
  properties: {
    users: {
      type: 'array',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        style: {
          justifyContent: 'center',
        },
      },
      'x-component': 'ArrayTable',
      'x-component-props': {
        pagination: { pageSize: 10 },
        scroll: { x: '100%' },
      },
      items: {
        type: 'object',
        properties: {
          column2: {
            type: 'void',
            'x-component': 'ArrayTable.Column',
            'x-component-props': { Header: '姓名(必填)' },
            properties: {
              name: {
                type: 'string',
                required: true,
                'x-decorator': 'FormItem',
                'x-component': 'Input',
                'x-component-props': {
                  width: '90px',
                  size: 'sm',
                },
                pattern: '^[\u4E00-\u9FA5A-Za-z0-9]+$',
                'x-reactions': '{{disableFirstRow}}',
              },
            },
          },
          column3: {
            type: 'void',
            'x-component': 'ArrayTable.Column',
            'x-component-props': { Header: '手机号(必填)' },
            properties: {
              phone: {
                type: 'string',
                required: true,
                'x-decorator': 'FormItem',
                'x-component': 'Input',
                'x-component-props': {
                  size: 'sm',
                  width: '130px',
                },
                'x-reactions': '{{disableFirstRow}}',
              },
            },
          },
          column4: {
            type: 'void',
            'x-component': 'ArrayTable.Column',
            'x-component-props': { Header: '性别(必填)' },
            properties: {
              gender: {
                type: 'string',
                required: true,
                'x-decorator': 'FormItem',
                'x-component': 'Select',
                enum: [
                  { label: '女', value: 'female' },
                  { label: '男', value: 'male' },
                ],
                'x-component-props': {
                  size: 'sm',
                  width: '100px',
                },
                'x-reactions': '{{disableFirstRow}}',
              },
            },
          },
          column5: {
            type: 'void',
            'x-component': 'ArrayTable.Column',
            'x-component-props': { Header: '省份' },
            properties: {
              province: {
                type: 'string',
                'x-decorator': 'FormItem',
                'x-component': 'Select',
                'x-component-props': {
                  size: 'sm',
                  width: '140px',
                },
                enum: provinces,
                'x-reactions': '{{disableFirstRow}}',
              },
            },
          },
          column6: {
            type: 'void',
            'x-component': 'ArrayTable.Column',
            'x-component-props': { Header: '城市' },
            properties: {
              city: {
                type: 'string',
                enum: ['请先选择省份'],
                'x-decorator': 'FormItem',
                'x-component': 'Select',
                'x-component-props': {
                  size: 'sm',
                  width: '140px',
                },
                'x-reactions': '{{disableFirstRow}}',
              },
            },
          },
          opt: {
            type: 'void',
            'x-component': 'ArrayTable.Column',
            'x-component-props': {
              Header: '操作',
              fixed: 'right',
            },
            properties: {
              item: {
                type: 'void',
                'x-component': 'FormItem',
                properties: {
                  copy: {
                    type: 'void',
                    'x-component': 'ArrayTable.Copy',
                  },
                  remove: {
                    type: 'void',
                    'x-component': 'ArrayTable.Remove',
                    'x-reactions': '{{disableFirstRow}}',
                  },
                },
              },
            },
          },
        },
      },
      properties: {
        add: {
          type: 'void',
          'x-component': 'ArrayTable.Addition',
          'x-component-props': {
            colorScheme: 'brand',
          },
          title: '添加',
        },
      },
    },
  },
};

export const userAddSchema = { schema, config };
