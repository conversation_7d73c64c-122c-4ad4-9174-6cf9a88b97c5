import { FormItem, Input, Select } from '@forest/formily';
import type { Field } from '@formily/core';
import { RiCompass4Line, RiMapPinLine, RiPhoneLine, RiUser3Line } from 'react-icons/ri';

import { locations, provinces } from '@/config/locations';

const loadCitiesData = async (field: Field) => {
  const province: string = field.query('province').get('value');
  if (!province) return [];
  return new Promise((resolve) => {
    resolve(locations[province]);
  });
};

const useAsyncDataSource = (service: any) => (field: Field) => {
  field.loading = true;
  service(field).then((data: String[]) => {
    const dataSource = data.map((city) => ({
      label: city,
      value: city,
    }));
    if (field.value === ' ') {
      field.value = undefined;
    }
    field.dataSource = dataSource;
    field.loading = false;
  });
};

const config = {
  components: {
    FormItem,
    Input,
    Select,
  },
  scope: {
    RiUser3Line,
    RiPhoneLine,
    RiMapPinLine,
    RiCompass4Line,
    loadCitiesData,
    useAsyncDataSource,
  },
};

const schema = {
  type: 'object',
  properties: {
    name: {
      type: 'string',
      required: true,
      'x-decorator': 'FormItem',
      'x-component': 'Input',
      'x-component-props': {
        width: '200px',
        size: 'sm',
        prefix: '{{RiUser3Line}}',
      },
      pattern: '^[\u4E00-\u9FA5A-Za-z0-9]+$',
    },
    phone: {
      type: 'string',
      required: true,
      'x-decorator': 'FormItem',
      'x-component': 'Input',
      'x-component-props': {
        width: '200px',
        size: 'sm',
        prefix: '{{RiPhoneLine}}',
      },
      'x-disabled': true,
      // TODO: add pattern
    },
    gender: {
      type: 'string',
      required: true,
      'x-decorator': 'FormItem',
      'x-component': 'Select',
      enum: [
        { label: '女', value: 'female' },
        { label: '男', value: 'male' },
      ],
      'x-component-props': {
        width: '200px',
        size: 'sm',
        prefix: '{{RiCompass4Line}}',
      },
    },
    province: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        labelWidth: '80px',
        labelStyle: {
          size: 'sm',
        },
      },
      'x-component': 'Select',
      'x-component-props': {
        width: '200px',
        size: 'sm',
        prefix: '{{RiMapPinLine}}',
      },
      enum: provinces,
      'x-reactions': {
        target: 'city',
        effects: ['onFieldInputValueChange'],
        fulfill: {
          state: {
            inputValue: ' ',
            inputValues: [],
            value: ' ', // formily中空字符串不会更新对应的组件内容
          },
        },
      },
    },
    city: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        labelWidth: '80px',
        labelStyle: {
          size: 'sm',
        },
      },
      'x-component': 'Select',
      'x-component-props': {
        width: '200px',
        size: 'sm',
        prefix: '{{RiMapPinLine}}',
      },
      'x-reactions': ['{{useAsyncDataSource(loadCitiesData)}}'],
    },
  },
};

export const userEditSchema = { schema, config };
