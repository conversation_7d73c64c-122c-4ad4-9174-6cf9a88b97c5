/* eslint-disable react-hooks/exhaustive-deps */
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON><PERSON>,
} from '@chakra-ui/react';
import { createForm, DataField, onFieldReact } from '@formily/core';
import { createSchemaField, FormProvider } from '@formily/react';
import { useEffect, useMemo } from 'react';
import { Observable } from 'rxjs';

import { locations } from '@/config/locations';
import { useSubmit } from '@/hooks/use-submit';

import { CreateUserProps } from '../api';
import { userAddSchema } from './forms';

interface UserAddDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: { users: Array<CreateUserProps> }) => Observable<unknown>;
}

const UserAddSchemaField = createSchemaField(userAddSchema.config);
const initialValues = {
  users: [
    {
      name: '恺大',
      phone: '13312341234',
      gender: 'female',
      province: '北京市',
      city: '东城区',
    },
    {},
  ],
};
const provinceCities = (function (loc) {
  const data: Record<
    string,
    {
      label: string;
      value: string;
    }[]
  > = {};
  const entries = Object.entries(loc);
  for (const [prov, cities] of entries) {
    data[prov] = cities.map((city) => ({
      label: city,
      value: city,
    }));
  }
  return data;
})(locations);

export const UserAddDrawer = ({ isOpen, onClose, onSubmit }: UserAddDrawerProps) => {
  const userAddForm = useMemo(() => {
    return isOpen
      ? createForm({
          initialValues,
          effects: () => {
            onFieldReact('users.*.province', (field) => {
              const province = (field as DataField).value;
              if (province && provinceCities.hasOwnProperty(province)) {
                const index = field.path.segments[1];
                field.query(`users.${index}.city`).take((target) => {
                  if ('value' in target) {
                    target.dataSource = provinceCities[province];
                    if (!(locations as Record<string, string[]>)[province].includes(target.value)) {
                      target.value = provinceCities[province][0].value;
                    }
                  }
                });
              }
            });
          },
        })
      : createForm();
  }, [isOpen]);

  const [onClickSubmit, { isSubmitting, cancelSubmit }] = useSubmit(userAddForm, onSubmit, {
    dependencies: [userAddForm],
    successToast: { title: '已成功添加用户' },
    formatter: ({ users }) => ({
      users: users.slice(1).map((user) => ({
        ...user,
        phone: user.phone?.startsWith('+') ? user.phone : `+86${user.phone}`,
      })),
    }),
  });

  useEffect(() => {
    if (!isOpen) {
      cancelSubmit();
    }
  }, [isOpen]);

  return (
    <Drawer isOpen={isOpen} placement="right" onClose={onClose} size="xl">
      <DrawerOverlay />
      <DrawerContent>
        <DrawerCloseButton />
        <DrawerHeader>新建账户</DrawerHeader>
        <DrawerBody>
          <FormProvider form={userAddForm}>
            <UserAddSchemaField schema={userAddSchema.schema}></UserAddSchemaField>
          </FormProvider>
          {/* <Button variant="outline" colorScheme="blackAlpha" mt="4" width="40%">下载 Excel 模板文件</Button>
          <Button variant='outline' colorScheme="green" mt="4" width="40%">解析 Excel（覆盖）</Button> */}
        </DrawerBody>
        <DrawerFooter>
          <Button variant="outline" mr={3} onClick={onClose}>
            取消
          </Button>
          <Button colorScheme="logo" isLoading={isSubmitting} onClick={onClickSubmit}>
            提交
          </Button>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
};
