import { ChakraProvider } from '@chakra-ui/react';
import { setValidateLanguage } from '@formily/core';
import { Suspense } from 'react';
import { BrowserRouter } from 'react-router-dom';

import { GlobalLoader } from '@/components/loaders';

import { theme } from '../theme';
import { AuthProvider } from './auth';
import { ErrorProvider } from './error';

interface AppProviderProps {
  children: React.ReactNode;
}
// TODO: [3h]Add ConfigProvider contains lang/config
setValidateLanguage('zh-CN');

export const AppProvider = ({ children }: AppProviderProps) => {
  return (
    <Suspense fallback={<GlobalLoader />}>
      <ErrorProvider>
        <ChakraProvider theme={theme}>
          <AuthProvider>
            <BrowserRouter>{children}</BrowserRouter>
          </AuthProvider>
        </ChakraProvider>
      </ErrorProvider>
    </Suspense>
  );
};
