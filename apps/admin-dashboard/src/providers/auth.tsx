import { createContext, ReactNode, useContext, useEffect, useMemo, useState } from 'react';
import type { Observable } from 'rxjs';

import { GlobalLoader } from '@/components/loaders';
import type { FePermission } from '@/features/auth';
import { getMe as loadUser } from '@/features/auth';
import type { User } from '@/features/users';

export interface AuthProviderConfig<User, Permission, TError> {
  loadUser: () => Observable<[User, Permission]>;
  loadErrorHandler?: (error: TError) => string;
  LoaderComponent?: () => JSX.Element;
  ErrorComponent?: ({ error }: { error: TError | null }) => JSX.Element;
}

export interface AuthContextValue<User, Permission> {
  user: User | undefined;
  permission: Permission | undefined;
  setUser: (user: User) => void;
  setPermission: (permission: Permission) => void;
}
export interface AuthProviderProps {
  children: ReactNode;
}
function initReactQueryAuth<User = unknown, Permission = unknown, TError = unknown>(
  config: AuthProviderConfig<User, Permission, TError>
) {
  const AuthContext = createContext<AuthContextValue<User, Permission> | null>(null);
  AuthContext.displayName = 'AuthContext';

  const { loadUser, LoaderComponent = () => <div>waiting...</div> } = config;

  function AuthProvider({ children }: AuthProviderProps): JSX.Element {
    const [authStatus, setAuthStatus] = useState<'loading' | 'success' | 'loaded'>('loading');
    const [user, setUser] = useState<User | undefined>(undefined);
    const [permission, setPermission] = useState<Permission | undefined>(undefined);

    useEffect(() => {
      const loadSub = loadUser().subscribe({
        next: ([user, permission]) => {
          setAuthStatus('success');
          setUser(user);
          setPermission(permission);
          // if (user?.uid) {
          // if (agreementVersion > Number(localStorage.getItem(`${(user as User)?.uid}_agreement`))) {
          //   logout().subscribe(() => {
          //     window.location.assign(window.location.origin);
          //   });
          // }
          // }
        },
        error: (err: Error) => {
          setAuthStatus('loaded');
        },
      });
      return () => loadSub.unsubscribe();
    }, []);

    const value = useMemo(
      () => ({
        user,
        setUser,
        permission,
        setPermission,
      }),
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [user]
    );
    switch (authStatus) {
      case 'loading':
        return <LoaderComponent />;
      case 'success':
      case 'loaded':
        return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
      default:
        return <div>Unhandled status: {authStatus}</div>;
    }
  }

  function useAuth() {
    const context = useContext(AuthContext);
    if (!context) {
      throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
  }

  return { AuthProvider, useAuth };
}

export const { AuthProvider, useAuth } = initReactQueryAuth<User, FePermission, Error>({
  loadUser,
  loadErrorHandler: (error: any) => {
    if (error.status < 500) {
      return '';
    }
    return error.message;
  },
  LoaderComponent: GlobalLoader,
});
