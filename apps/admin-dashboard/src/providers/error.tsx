import { useToast } from '@chakra-ui/react';
import type { ApiError } from '@forest/fetch';
import { createContext, useContext, useEffect, useState } from 'react';
import type { Observable } from 'rxjs';
import { from, map } from 'rxjs';

import { Configs } from '@/service/iam/v1';

// todo: [8h]formulate the rule of different error types
type ErrorListType = Record<string, Record<'langs', Record<string, string>>>;
interface ErrorHandlerConfig {
  errorListService: () => Observable<ErrorListType>;
  errorRules?: Record<string, string>;
  ErrorComponent?: ({ error }: { error: Error }) => JSX.Element;
}
// todo: [5h]add error-boundary
interface ErrorContextValue {
  lang: string;
  errorList: ErrorListType;
  errorRules?: Record<string, string>;
}
export const initErrorHandler = (config: ErrorHandlerConfig) => {
  const ErrorContext = createContext<ErrorContextValue>({ errorList: {}, lang: 'zh-Hans' });
  ErrorContext.displayName = 'ErrorContext';

  const { errorListService } = config;

  const ErrorProvider = ({ children }: { children: JSX.Element }) => {
    const [errorList, setErrorList] = useState<ErrorListType>({});
    const [lang] = useState('zh-Hans');

    const toast = useToast({ position: 'top', status: 'error' });

    useEffect(() => {
      const errorListService$ = errorListService().subscribe({
        next: (errors) => {
          setErrorList(errors);
        },
      });
      return () => errorListService$.unsubscribe();
    }, []);

    useEffect(() => {
      const onAPIError = (error: ApiError, list: ErrorListType, lang: string) => {
        const { reason = '' } = error.body || {};
        const title = list[reason]?.langs[lang] || reason || error.message;
        toast({ title });
      };

      const onError = (event: ErrorEvent | PromiseRejectionEvent) => {
        event.preventDefault();
        const error = event instanceof ErrorEvent ? event.error : event.reason;

        if (error.name === 'ApiError') {
          onAPIError(error, errorList, lang);
        } else if (process.env.NODE_ENV === 'development') {
          console.error(error);
        }
      };

      const onOffline = (event: Event) => {
        event.preventDefault();
        toast({ title: '无法连接到网络' });
      };

      window.addEventListener('error', onError);
      window.addEventListener('unhandledrejection', onError);
      window.addEventListener('offline', onOffline);

      return () => {
        window.removeEventListener('error', onError);
        window.removeEventListener('unhandledrejection', onError);
        window.removeEventListener('offline', onOffline);
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [errorList, lang]);

    return <ErrorContext.Provider value={{ errorList, lang }}> {children}</ErrorContext.Provider>;
  };

  const useError = () => {
    const context = useContext(ErrorContext);
    if (!context) {
      throw new Error('useError must be used within an ErrorProvider');
    }
    return context;
  };

  return { ErrorProvider, useError };
};

export const { ErrorProvider, useError } = initErrorHandler({
  errorListService: () =>
    from(Configs.configsListErrors()).pipe(map(({ errors }) => errors!)) as Observable<ErrorListType>,
});
