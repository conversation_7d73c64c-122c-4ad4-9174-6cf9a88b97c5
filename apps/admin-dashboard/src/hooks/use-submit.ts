import { useToast, UseToastOptions } from '@chakra-ui/react';
import { useConstant } from '@forest/hooks';
import type { Form } from '@formily/core';
import { useEffect, useState } from 'react';
import { catchError, EMPTY, from, Observable, Subject, switchMap, tap } from 'rxjs';

import { useError } from '@/providers/error';

interface OptionProps<FV, SV> {
  successToast?: UseToastOptions;
  errorToast?: UseToastOptions;
  dependencies?: Array<any>;
  formatter?: (values: FV) => SV;
}
export const useSubmit = <E, D, V, R>(
  form: Form,
  onSubmit: (values: D | V) => Observable<R>,
  options: OptionProps<D, V> = {}
): [(event: E) => void, { isSubmitting: boolean; cancelSubmit: () => void }] => {
  const [isSubmitting, setSubmitting] = useState(false);
  const event$ = useConstant<Subject<E>>(() => new Subject());

  const showToast = useToast({ position: 'top', duration: 3000 });

  const { errorList, lang } = useError();

  const { dependencies = [], formatter } = options;

  useEffect(() => {
    const eventSub = event$
      .pipe(
        switchMap(() => from(form.submit() as Promise<D>).pipe(catchError(() => EMPTY))),
        tap(() => setSubmitting(true)),
        switchMap((values) =>
          onSubmit(formatter ? formatter(values) : values).pipe(
            catchError((error) => {
              setSubmitting(false);
              const { errorToast } = options;
              let title = error.message || error;
              if (error.name === 'ApiError') {
                const { reason = '' } = error.body || {};
                title = errorList[reason]?.langs[lang] || reason || error.message;
              }
              showToast({ status: 'error', title, ...errorToast });
              return EMPTY;
            })
          )
        ),
        tap(() => {
          setSubmitting(false);
          const { successToast } = options;
          if (successToast) {
            showToast({
              status: 'success',
              ...successToast,
            });
          }
        })
      )
      .subscribe({
        next: (data) => {
          console.log('Submit Done: ', data);
        },
        error: () => {},
        complete: () => {},
      });
    return () => {
      eventSub.unsubscribe();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, dependencies);

  return [
    (event: E) => event$.next(event),
    {
      isSubmitting,
      cancelSubmit: () => setSubmitting(false),
    },
  ];
};
