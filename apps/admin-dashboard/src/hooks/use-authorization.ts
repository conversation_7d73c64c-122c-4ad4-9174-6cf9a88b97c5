import { useCallback } from 'react';

import { useAuth } from '@/providers/auth';

interface CheckAccessParams {
  allowedRoles?: string[];
}

export const useAuthorization = () => {
  const { user } = useAuth();

  if (!user) {
    throw Error('User does not exist!');
  }

  const checkAccess = useCallback(
    ({ allowedRoles }: CheckAccessParams) => {
      if (allowedRoles && typeof allowedRoles.includes === 'function') {
        return allowedRoles.includes(user.role);
      }
      return false;
    },
    [user]
  );

  return { checkAccess, role: user.role };
};
