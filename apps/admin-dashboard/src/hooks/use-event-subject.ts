import { useToast, UseToastOptions } from '@chakra-ui/react';
import { useConstant } from '@forest/hooks';
import { useEffect, useState } from 'react';
import { BehaviorSubject, Observable, Subject, withLatestFrom } from 'rxjs';

interface HandlerOptionProps {
  toast?: UseToastOptions;
}
export const useEventSubject = <E, V extends Record<string, any>>(
  process: (
    evt$: Observable<[E, V]>
  ) => Observable<{ state?: Partial<V>; error?: Error; handler?: HandlerOptionProps }>,
  initialState: V,
  dependencies: Array<any> = []
): [(event: E) => void, V] => {
  const [states, setStates] = useState(initialState);
  const event$ = useConstant<Subject<E>>(() => new Subject());
  const state$ = useConstant<BehaviorSubject<V>>(() => new BehaviorSubject(initialState));

  const toaster = useToast({ position: 'top' });

  useEffect(() => {
    const stateSub = state$.subscribe(setStates);
    return () => {
      stateSub.unsubscribe();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const eventSub = process(event$.pipe(withLatestFrom(state$))).subscribe({
      next: (data) => {
        const { state = {}, handler, error } = data;
        if (handler) {
          const { toast } = handler;
          if (toast) {
            toaster(toast);
          }
        }
        state$.next({
          ...state$.value,
          ...state,
        });
        if (error) {
          throw error;
        }
      },
    });
    return () => {
      eventSub.unsubscribe();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, dependencies);

  return [(event: E) => event$.next(event), states];
};
