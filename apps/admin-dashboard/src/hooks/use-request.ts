import { ToastId, useToast, UseToastOptions } from '@chakra-ui/react';
import { useConstant } from '@forest/hooks';
import { useCallback, useEffect, useState } from 'react';
import { catchError, EMPTY, map, Observable, Subject, switchMap, tap, timeout as timeoutController } from 'rxjs';

import { useError } from '@/providers/error';

interface RequestOptions<T, R, E extends Error> {
  params?: T;
  timeout?: number;
  enabled?: boolean;
  meta?: Record<string, unknown>;
  onSuccess?: (
    data: R,
    options: RequestOptions<T, R, E>,
    showToast: (options?: UseToastOptions | undefined) => ToastId
  ) => void;
  onError?: (
    error: E,
    options: RequestOptions<T, R, E>,
    showToast: (options?: UseToastOptions | undefined) => ToastId
  ) => void;
}

export const useRequest = <T, R, E extends Error = Error>(
  fetchFunc: ((params: T) => Observable<R>) | (() => Observable<R>),
  options: RequestOptions<T, R, E> = {}
) => {
  const [result, setResult] = useState<R | null>(null);
  const [error, setError] = useState<E | null>(null);
  const [isLoading, setLoading] = useState<boolean>(false);

  const event$ = useConstant<Subject<RequestOptions<T, R, E>>>(() => new Subject());

  const showToast = useToast({ position: 'top', duration: 3000 });

  const { errorList, lang } = useError();

  useEffect(() => {
    const eventSub = event$
      .pipe(
        map((opt) => {
          setLoading(true);
          return {
            ...options,
            ...opt,
          };
        }),
        switchMap((opt) => {
          const { params = {}, timeout = 5000, onSuccess, onError } = opt;
          // console.log(`Request ${fetchFunc.name} Begin: `, params);
          return (fetchFunc.length === 0 ? (fetchFunc as () => Observable<R>)() : fetchFunc(params as T)).pipe(
            timeoutController({
              each: timeout,
              with: () => {
                throw new Error('网络超时，请检查网络连接或稍后再试');
              },
            }),
            tap((data) => {
              setLoading(false);
              if (onSuccess) {
                onSuccess(data, opt, showToast);
              }
              setResult(data);
            }),
            catchError((error) => {
              const { reason = '' } = error.body || {};
              const title = errorList[reason]?.langs[lang] || reason || error.message;
              error.message = title;
              if (onError) {
                onError(error, opt, showToast);
              } else {
                showToast({ status: 'error', title });
              }
              setLoading(false);
              setError(error);
              return EMPTY;
            })
          );
        })
      )
      .subscribe({
        next: (data) => {
          console.log(`Request ${fetchFunc.name} End: `, data);
        },
        error: () => {},
        complete: () => {},
      });
    if (options.enabled) {
      event$.next({});
    }
    return () => {
      eventSub.unsubscribe();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const refetch = useCallback((opt?: RequestOptions<T, R, E>) => event$.next(opt || {}), []);

  return {
    data: result,
    error: error,
    isLoading: isLoading,
    refetch,
  };
};
