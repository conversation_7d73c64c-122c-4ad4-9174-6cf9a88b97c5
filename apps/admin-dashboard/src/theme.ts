import { extendTheme } from '@chakra-ui/react';
import type { StyleFunctionProps } from '@chakra-ui/theme-tools';
import { mode } from '@chakra-ui/theme-tools';

const activeLabelStyles = {
  transform: 'scale(0.85) translateY(-26px)',
};
export const theme = extendTheme({
  colors: {
    brand: {
      10: '#80D0C7', // green
      50: '#EAF6FF',
      100: '#CEF0F9',
      200: '#A0DDF3',
      300: '#6BB7DB',
      400: '#428BB7',
      500: '#145687',
      600: '#0E4374',
      700: '#0A3261',
      800: '#06234E',
      900: '#031940',
    },
    logo: {
      50: '#F3FCFF',
      100: '#CFF1FD',
      200: '#A0DFFC',
      300: '#6FC4F6',
      400: '#4BA9EE',
      500: '#1481E3',
      600: '#0E64C3',
      700: '#0A4AA3',
      800: '#063483',
      900: '#03256C',
    },
    lightBlue: {
      100: '#D6F1FC',
      200: '#ADE0FA',
      300: '#82C6F0',
      400: '#60A9E1',
      500: '#3182CE',
      600: '#2365B1',
      700: '#184B94',
      800: '#0F3577',
      900: '#092562',
    },
    blueGray: {
      100: '#F6FAFE',
      200: '#E9F1F7',
      300: '#E1EEF8',
      400: '#E0E9F2',
      500: '#A3BBD0',
      600: '#8FA6BA',
      700: '#7094AE',
      800: '#708FAE',
      900: '#1B3959',
    },
  },
  radii: {
    xs: '6px',
    sm: '10px',
    base: '14px',
    md: '16px',
    lg: '20px',
    xl: '30px',
    xxl: '40px',
  },
  shadows: {
    xs: 'rgba(112, 144, 176, 0.1) 2px 4px 10px 2px',
    base: 'rgba(112, 144, 176, 0.1) 4px 6px 20px 3px',
    sm: 'rgba(112, 144, 176, 0.1) 8px 10px 26px 3px',
    md: 'rgba(112, 144, 176, 0.1) 10px 12px 30px 4px',
    xl: 'rgba(112, 144, 176, 0.1) 14px 17px 40px 4px',
  },
  lineHeights: {
    12: '3rem',
    14: '3.5rem',
  },
  components: {
    // 请按字母序排列
    Accordion: {
      baseStyle: {
        container: {},
        button: {
          paddingTop: 0,
          paddingBottom: 0,
        },
        panel: {
          color: 'gray.700',
        },
      },
      variants: {
        outline: (props: StyleFunctionProps) => {
          const { colorScheme: c } = props;
          const grayBorder = mode('gray.200', 'whiteAlpha.300')(props);
          const colorBorder = mode(`${c}.400`, `${c}.700`)(props);
          const raduisSize = 'sm';
          return {
            container: {
              borderLeftWidth: '1px',
              borderRightWidth: '1px',
              borderColor: c === 'gray' ? grayBorder : colorBorder,
              _first: {
                borderTopLeftRadius: raduisSize,
                borderTopRightRadius: raduisSize,
                ' .chakra-accordion__button': {
                  borderTopLeftRadius: raduisSize,
                  borderTopRightRadius: raduisSize,
                },
              },
              _last: {
                borderBottomLeftRadius: raduisSize,
                borderBottomRightRadius: raduisSize,
                ' .chakra-accordion__button[aria-expanded=false]': {
                  borderBottomLeftRadius: raduisSize,
                  borderBottomRightRadius: raduisSize,
                },
              },
            },
            button: {
              color: mode(`${c}.500`, `${c}.300`)(props),
              fontWeight: '500',
            },
          };
        },
      },
      sizes: {
        sm: {
          container: {
            fontSize: 'sm',
          },
          button: {
            fontSize: 'sm',
            height: '10',
          },
        },
        md: {
          container: {
            fontSize: 'md',
          },
          button: {
            fontSize: 'md',
            height: '12',
          },
        },
      },
      defaultProps: {
        variant: 'outline',
        colorScheme: 'gray',
        size: 'md',
      },
    },
    Badge: {
      baseStyle: {
        minWidth: '1.125rem',
        textAlign: 'center',
      },
    },
    Button: {
      variants: {
        text: (props: StyleFunctionProps) => {
          const { colorScheme: c } = props;
          const normal = mode(`${c}.500`, `${c}.200`)(props);
          return {
            bg: 'none',
            color: normal,
            _hover: {
              color: mode(`${c}.600`, `${c}.300`)(props),
              _disabled: {
                color: normal,
              },
            },
            _active: {
              color: mode(`${c}.700`, `${c}.400`)(props),
            },
          };
        },
        soft: (props: StyleFunctionProps) => {
          const { colorScheme: c } = props;
          const background = mode(`${c}.50`, `${c}.800`)(props);
          return {
            bg: background,
            color: mode(`${c}.600`, `${c}.100`)(props),
            _hover: {
              bg: mode(`${c}.100`, `${c}.700`)(props),
              _disabled: {
                bg: background,
              },
            },
            _active: {
              bg: mode(`${c}.100`, `${c}.700`)(props),
            },
          };
        },
      },
    },
    Checkbox: {
      baseStyle: {
        control: {
          borderRadius: 'xs',
        },
      },
      defaultProps: {
        colorScheme: 'linkedin',
      },
    },
    Container: {
      baseStyle: {
        borderRadius: 'xl',
        boxShadow: 'xl',
        maxWidth: 'none',
        backgroundColor: 'white',
      },
    },
    Drawer: {
      sizes: {
        '2xl': {
          dialog: {
            maxWidth: '5xl',
          },
        },
      },
    },
    Form: {
      baseStyle: {
        helperText: {
          position: 'absolute',
          marginTop: '0.5',
        },
      },
      variants: {
        floating: {
          container: {
            _focusWithin: {
              label: {
                ...activeLabelStyles,
              },
            },
            'input:not(:placeholder-shown) + label, .chakra-select__wrapper + label': {
              ...activeLabelStyles,
            },
            label: {
              top: 0,
              left: 0,
              zIndex: 2,
              position: 'absolute',
              backgroundColor: 'white',
              pointerEvents: 'none',
              mx: 3,
              px: 1,
              my: 3,
              transformOrigin: 'left top',
              borderRadius: 4,
            },
          },
        },
      },
    },
    FormError: {
      baseStyle: {
        text: {
          position: 'absolute',
          marginTop: '0.5',
        },
      },
    },
    FormLabel: {
      baseStyle: {
        color: 'gray.600',
      },
      sizes: {
        xs: {
          fontSize: 'xs',
          height: '8',
          lineHeight: '8',
          marginBottom: '-0.5',
        },
        sm: {
          fontSize: 'sm',
          height: '10',
          lineHeight: '10',
          marginBottom: '-1',
        },
        md: {
          fontSize: 'md',
          height: '12',
          lineHeight: '12',
          marginBottom: '-1',
        },
        lg: {
          fontSize: 'lg',
          height: '14',
          lineHeight: '14',
          marginBottom: '-1.5',
        },
      },
    },
    Input: {
      sizes: {
        xs: {
          field: {
            height: '8',
          },
          addon: {
            height: '8',
          },
        },
        sm: {
          field: {
            height: '10',
          },
          addon: {
            height: '10',
          },
        },
        md: {
          field: {
            height: '12',
          },
          addon: {
            height: '12',
          },
        },
      },
    },
    Link: {
      variants: {
        text: (props: StyleFunctionProps) => {
          const { colorScheme: c } = props;
          const normal = mode(`${c}.500`, `${c}.200`)(props);
          return {
            bg: 'none',
            color: normal,
            textDecoration: 'none',
            _hover: {
              color: mode(`${c}.600`, `${c}.300`)(props),
              fontWeight: 500,
              textDecoration: 'none',
            },
            _active: {
              color: mode(`${c}.700`, `${c}.400`)(props),
              fontWeight: 700,
              textDecoration: 'none',
            },
          };
        },
      },
    },
    Menu: {
      baseStyle: {
        list: {
          border: 'none',
          borderRadius: 'lg',
          boxShadow: 'rgba(112, 144, 176, 0.06) 5px 6px 20px 4px',
          minWidth: '11rem',
        },
        item: {
          pl: 5,
          py: 2.5,
          _hover: {
            background: 'none',
            fontWeight: 'bold',
          },
          _focus: {
            background: 'none',
            fontWeight: 'bold',
          },
        },
      },
    },
    Modal: {
      baseStyle: {
        dialog: {
          paddingTop: '2',
          paddingBottom: '2',
        },
        closeButton: {
          top: 3,
        },
      },
    },
    Select: {
      parts: ['select', 'option'],
      baseStyle: {
        select: {
          appearance: 'none',
        },
        option: {
          cursor: 'pointer',
          color: 'gray.600',
          mb: '1px',
          _hover: {
            bg: 'blackAlpha.50',
            color: 'gray.800',
          },
          _focus: {
            bg: 'blackAlpha.100',
            color: 'gray.800',
          },
        },
      },
      sizes: {
        xs: {
          option: {
            height: '8',
            lineHeight: '8',
            px: 2,
            mx: 1.5,
            fontSize: 'xs',
            borderRadius: 'xs',
            _first: {
              mt: 1,
            },
            _last: {
              mb: 1,
            },
          },
        },
        sm: {
          option: {
            height: '9',
            lineHeight: '9',
            px: 2.5,
            mx: 2,
            fontSize: 'sm',
            borderRadius: 'sm',
            _first: {
              mt: 1.5,
            },
            _last: {
              mb: 1.5,
            },
          },
        },
        md: {
          option: {
            height: '2.75rem',
            lineHeight: '2.75rem',
            px: 3.5,
            mx: 2.5,
            fontSize: 'md',
            borderRadius: 'md',
            _first: {
              mt: 2.5,
            },
            _last: {
              mb: 2.5,
            },
          },
        },
        lg: {
          option: {
            height: '12',
            lineHeight: '12',
            px: 4,
            mx: 2.5,
            fontSize: 'lg',
            borderRadius: 'md',
            _first: {
              mt: 2.5,
            },
            _last: {
              mb: 2.5,
            },
          },
        },
      },
      defaultProps: {
        size: 'md',
      },
    },
    Tag: {
      sizes: {
        sm: {
          container: {
            px: 3,
            py: 1,
          },
        },
        md: {
          container: {
            px: 3.5,
            py: 1.5,
          },
        },
        lg: {
          container: {
            px: 4,
            py: 1.5,
          },
        },
      },
    },
    Text: {
      baseStyle: {
        color: 'brand.800',
      },
    },
  },
});
