{"browserslist": {"development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"], "production": [">0.2%", "not dead", "not op_mini all", "not IE <= 11", "not Safari <= 8", "not Android <= 4.4"]}, "dependencies": {"@chakra-ui/react": "^2.4.4", "@chakra-ui/styled-system": "~2.3.4", "@chakra-ui/theme-tools": "~2.0.12", "@emotion/react": "^11.9.3", "@emotion/styled": "^11.9.3", "@forest/fetch": "workspace:*", "@forest/formily": "workspace:*", "@forest/hooks": "workspace:*", "@forest/simple-ui": "workspace:*", "@forest/utils": "workspace:*", "@formily/core": "^2.1.11", "@formily/react": "^2.1.11", "@formily/reactive": "^2.2.12", "@popperjs/core": "^2.11.6", "@sentry/react": "~7.38.0", "@sentry/tracing": "~7.38.0", "chart.js": "^4.2.1", "dayjs": "^1.11.5", "formik": "^2.2.9", "framer-motion": "^6.4.0", "react": "~18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "~18.2.0", "react-icons": "^4.10.1", "react-is": "~18.2.0", "react-router-dom": "^6.3.0", "rxjs": "~7.8.1", "spark-md5": "^3.0.2", "lodash": "~4.17.21"}, "devDependencies": {"@babel/core": "~7.19.6", "@babel/plugin-syntax-flow": "~7.18.6", "@babel/plugin-transform-react-jsx": "~7.19.0", "@craco/craco": "^7.1.0", "@forest/eslint-config": "workspace:*", "@testing-library/dom": ">=7.21.4", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^13.0.0", "@testing-library/user-event": "^13.2.1", "@types/jest": "^27.0.1", "@types/node": "~18.11.9", "@types/react": "~18.2.60", "@types/react-dom": "~18.2.19", "@types/spark-md5": "~3.0.2", "@types/testing-library__jest-dom": "~5.14.5", "postcss": "^8.1.0", "react-app-alias": "^2.2.0", "react-scripts": "5.0.1", "ts-node": ">=10.0.0", "typescript": "^4.4.2", "web-vitals": "^2.1.0", "@types/lodash": "~4.14.191"}, "name": "@konvery/admin-dashboard", "private": true, "scripts": {"build": "sh -a -c '. .env.${REACT_APP_ENV}; echo Current env: ${REACT_APP_ENV}; craco build'", "build:nonprod": "REACT_APP_ENV=nonprod npm run build", "build:production": "REACT_APP_ENV=production npm run build", "build:staging": "REACT_APP_ENV=staging npm run build", "build:productionnew": "REACT_APP_ENV=productionnew npm run build", "eject": "react-scripts eject", "start": "rush build -T @konvery/admin-dashboard & craco start", "test": "craco test"}, "version": "0.1.0"}