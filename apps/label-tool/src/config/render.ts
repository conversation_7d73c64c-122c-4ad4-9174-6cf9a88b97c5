import { CameraType } from '@/utils/render';

// 图片最大渲染宽度
export const IMG_RENDER_WIDTH_MAX = 4096;
// 各类形状的面默认透明度
export const OBJECT_FACE_OPACITY = 0.1;
// 这是拼接在16位颜色值后面的 alpha 值
export const OBJECT_FACE_ALPHA = Math.ceil(OBJECT_FACE_OPACITY * 256).toString(16);
// 2D 场景相机视野范围上限
export const CAMERA_FAR_2D = 100;
// 3D 场景相机视野范围上限
export const CAMERA_FAR_3D = 600;

// 默认渲染顺序
export const ORDER_DEFAULT = 0;
// hover 状态下渲染顺序
export const ORDER_HOVER = 1;
// 选择状态下渲染顺序
export const ORDER_ACTIVE = 2;

// ---------------------------- 点云渲染 ------------------------------------

/** 点云渲染高度的范围默认值 */
export const POINTS_HEIGHT_RANGE_DEFAULT: [number, number] = [-10, 30];

/** 点云反射率的范围默认值 */
export const POINTS_INTENSITY_RANGE_DEFAULT: [number, number] = [0, 255];

/** 点云背景色默认值 */
export const POINTS_DEFAULT_BACKGROUND_COLOR = '#1a202c';

/** 点云 rgb 颜色渲染 */
// 白色阈值，rgb 值归一化到 [0 - 1] 区间，超过阈值认为是白色
export const POINTS_COLOR_WHITE_THROHOLD = 0.5;
// 白色对比度
export const POINTS_COLOR_WHITE_CONTRAST = 5;
// 其他颜色对比度
export const POINTS_COLOR_BASE_CONTRAST = 1.1;

// 任务类型对应的默认 3D 场景相机类型配置
export const CAMERA_TYPE_MAP: Record<'fusion4d' | 'fusion3d', CameraType> = {
  fusion4d: 'PerspectiveCamera',
  fusion3d: 'OrthographicCamera',
};

// 3D 场景 controls 配置
export const CONTROLS_OPTIONS_MAP = {
  // 填 enableAdaptFar enabled 避免 ts 编译报错
  OrthographicCamera: {
    panSpeed: 1.0,
    enableAdaptFar: false,
    enabled: true,
  },
  PerspectiveCamera: {
    panSpeed: 0.4,
    enableAdaptFar: true,
    enabled: true,
  },
};
