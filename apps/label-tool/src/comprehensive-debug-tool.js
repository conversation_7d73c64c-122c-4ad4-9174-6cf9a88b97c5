/**
 * 综合调试工具：分析批注数据不一致的根本原因
 */

window.comprehensiveDebugTool = {
  
  // 1. 分析当前数据状态的一致性
  analyzeDataConsistency() {
    console.log('🔍 开始综合数据一致性分析...');
    
    try {
      // 获取当前状态（需要根据实际的store获取方式调整）
      const state = this.getCurrentState();
      if (!state) {
        console.error('❌ 无法获取当前状态');
        return;
      }
      
      const { commentMap, resolveComments, elementAnnos } = state;
      
      console.log('📊 数据概览:');
      console.log('- commentMap条目数:', Object.keys(commentMap || {}).length);
      console.log('- resolveComments数组长度:', (resolveComments || []).length);
      console.log('- elementAnnos数量:', (elementAnnos || []).length);
      
      // 分析批注状态分布
      const statusDistribution = {};
      Object.values(commentMap || {}).forEach(comment => {
        if (comment) {
          statusDistribution[comment.status] = (statusDistribution[comment.status] || 0) + 1;
        } else {
          statusDistribution['null'] = (statusDistribution['null'] || 0) + 1;
        }
      });
      
      console.log('📈 批注状态分布:', statusDistribution);
      
      // 检查数据不一致
      this.checkInconsistencies(commentMap, resolveComments, elementAnnos);
      
    } catch (error) {
      console.error('❌ 分析过程中出错:', error);
    }
  },
  
  // 2. 检查具体的数据不一致问题
  checkInconsistencies(commentMap, resolveComments, elementAnnos) {
    console.log('🔍 检查数据不一致问题...');
    
    const issues = [];
    
    // 问题1: resolveComments中的批注在commentMap中状态不是pending
    (resolveComments || []).forEach(resolveComment => {
      const comment = commentMap[resolveComment.uuid];
      if (!comment) {
        issues.push({
          type: 'missing_comment',
          uuid: resolveComment.uuid,
          description: 'resolveComments中的批注在commentMap中不存在'
        });
      } else if (comment.status !== 'pending') {
        issues.push({
          type: 'status_mismatch',
          uuid: resolveComment.uuid,
          expected: 'pending',
          actual: comment.status,
          description: 'resolveComments中的批注状态不是pending'
        });
      }
    });
    
    // 问题2: pending状态的批注不在resolveComments中
    const resolveUuids = new Set((resolveComments || []).map(r => r.uuid));
    Object.entries(commentMap || {}).forEach(([uuid, comment]) => {
      if (comment && comment.status === 'pending' && !resolveUuids.has(uuid)) {
        issues.push({
          type: 'missing_resolve',
          uuid,
          description: 'pending状态的批注不在resolveComments中'
        });
      }
    });
    
    // 问题3: 批注关联的标注物不存在
    Object.entries(commentMap || {}).forEach(([uuid, comment]) => {
      if (comment && comment.scope === 'object') {
        const elementAnno = elementAnnos[comment.elem_idx];
        if (elementAnno && elementAnno.annotations) {
          const existingUuids = elementAnno.annotations.map(anno => anno.uuid);
          const missingUuids = comment.obj_uuids.filter(objUuid => !existingUuids.includes(objUuid));
          
          if (missingUuids.length > 0) {
            issues.push({
              type: 'missing_annotation',
              uuid,
              missingUuids,
              description: '批注关联的标注物不存在'
            });
          }
        }
      }
    });
    
    // 输出问题报告
    if (issues.length > 0) {
      console.error('❌ 发现数据不一致问题:');
      issues.forEach((issue, index) => {
        console.error(`${index + 1}. ${issue.type}:`, issue);
      });
    } else {
      console.log('✅ 未发现明显的数据不一致问题');
    }
    
    return issues;
  },
  
  // 3. 模拟问题场景
  simulateProblematicScenarios() {
    console.log('🎭 模拟可能的问题场景...');
    
    // 场景1: element类型批注的状态计算错误
    console.log('场景1: element类型批注状态错误');
    const elementComment = {
      uuid: 'element-test-' + Date.now(),
      scope: 'element',
      elem_idx: 0,
      add_phase: 1,
      resolve_phase: 1, // 已解决
      content: '测试element批注'
    };
    
    // 按当前逻辑计算状态
    const correctStatus = elementComment.resolve_phase >= elementComment.add_phase ? 'resolved' : 'pending';
    const currentLogicStatus = 'unresolved'; // 当前代码的逻辑
    
    console.log('正确状态应该是:', correctStatus);
    console.log('当前逻辑会设置为:', currentLogicStatus);
    
    if (correctStatus !== currentLogicStatus) {
      console.error('❌ 发现element类型批注状态计算错误！');
    }
    
    // 场景2: resolveComments数组累积问题
    console.log('场景2: resolveComments数组可能的累积问题');
    console.log('⚠️ resolveComments数组只增不减，可能导致历史数据累积');
  },
  
  // 4. 获取当前状态的辅助方法
  getCurrentState() {
    // 尝试多种方式获取store状态
    if (window.__ZUSTAND_STORE__) {
      return window.__ZUSTAND_STORE__.getState();
    }
    
    // 尝试从React DevTools获取
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      // 这里需要根据实际情况调整
    }
    
    // 如果都获取不到，返回null
    console.warn('⚠️ 无法自动获取store状态，请手动传入');
    return null;
  },
  
  // 5. 生成修复建议
  generateFixSuggestions() {
    console.log('💡 生成修复建议...');
    
    const suggestions = [
      {
        issue: 'element类型批注状态计算错误',
        fix: '修改initComments中element类型的状态计算逻辑',
        code: `
case 'element':
  commentMap[comment.uuid] = {
    ...comment,
    status: getCommentStatus(comment), // 使用统一的状态计算
  };
  break;`
      },
      {
        issue: 'resolveComments数组管理问题',
        fix: '添加resolveComments的清理机制',
        code: `
// 在适当的时机清理resolveComments
const cleanResolveComments = () => {
  set(({ resolveComments, commentMap }) => ({
    resolveComments: resolveComments.filter(resolve => {
      const comment = commentMap[resolve.uuid];
      return comment && comment.status === 'pending';
    })
  }));
};`
      },
      {
        issue: '数据一致性验证缺失',
        fix: '在关键操作前后添加数据一致性检查',
        code: `
const validateDataConsistency = () => {
  // 验证逻辑
};`
      }
    ];
    
    suggestions.forEach((suggestion, index) => {
      console.log(`💡 建议${index + 1}: ${suggestion.issue}`);
      console.log(`解决方案: ${suggestion.fix}`);
      console.log('代码示例:', suggestion.code);
      console.log('---');
    });
  }
};

// 自动运行分析
console.log('🛠️ 综合调试工具已加载！');
console.log('使用方法:');
console.log('- comprehensiveDebugTool.analyzeDataConsistency() - 分析数据一致性');
console.log('- comprehensiveDebugTool.simulateProblematicScenarios() - 模拟问题场景');
console.log('- comprehensiveDebugTool.generateFixSuggestions() - 生成修复建议');

// 如果可以获取到状态，自动运行分析
setTimeout(() => {
  if (window.comprehensiveDebugTool.getCurrentState()) {
    console.log('🚀 自动运行数据一致性分析...');
    window.comprehensiveDebugTool.analyzeDataConsistency();
  }
}, 1000);
