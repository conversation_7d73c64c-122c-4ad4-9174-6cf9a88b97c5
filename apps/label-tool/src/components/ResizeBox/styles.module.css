.container {
  position: relative;
}

.line {
  position: absolute;
  top: 0;
  bottom: 0;
  right: -2px;
  width: 4px;
  cursor: col-resize;
  text-align: center;
}

.line[data-draggable='true'] {
  right: -100px;
  width: 200px;
}

.highlight {
  display: inline-block;
  width: 3px;
  height: 100%;
  background: none;
  position: relative;
  z-index: 9999;
}

.line[data-draggable='true'] > .highlight {
  background: #28c89b;
}
