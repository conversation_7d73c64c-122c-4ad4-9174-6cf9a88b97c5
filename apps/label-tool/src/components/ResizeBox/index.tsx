import { CSSProperties, FC, ReactNode, useEffect, useState } from 'react';

import { clampNumber } from '@/utils/math';

import styles from './styles.module.css';

export interface ResizeProps {
  initialWidth: number;
  minWidth?: number;
  maxWidth?: number;
  style?: CSSProperties;

  renderChildren: (width: number, isResizing: boolean) => ReactNode;
}
export const ResizeBox: FC<ResizeProps> = ({
  initialWidth,
  minWidth = 1,
  maxWidth = Number.MAX_SAFE_INTEGER,
  style,
  renderChildren,
}) => {
  // 子元素宽度
  const [width, setWidth] = useState(initialWidth);
  // 拖拽起始宽度
  const [startWidth, setStartWidth] = useState(-1);
  // 拖拽起始点的的 ClientX
  const [startX, setStartX] = useState(-1);

  useEffect(() => {
    setWidth((prevWidth) => clampNumber(prevWidth, minWidth, maxWidth));
  }, [minWidth, maxWidth]);

  const handleResizeStart = (e: React.PointerEvent) => {
    if (startWidth >= 0) return;
    setStartWidth(width);
    setStartX(e.clientX);
  };

  const handleResizing = (e: React.PointerEvent) => {
    if (startWidth < 0 || e.clientX <= 0) return;
    const newWidth = Math.round((e.clientX - startX + startWidth) * 100) / 100;
    setWidth(clampNumber(newWidth, minWidth, maxWidth));
  };

  const handleResizeEnd = () => {
    setStartWidth(-1);
    setStartX(-1);
  };

  return (
    <div className={styles.container} style={style} data-dragging={startWidth >= 0}>
      {renderChildren(width, startWidth >= 0)}
      <div
        className={styles.line}
        data-draggable={startWidth >= 0}
        onPointerDown={handleResizeStart}
        onPointerMove={handleResizing}
        onPointerUp={handleResizeEnd}
        onPointerLeave={handleResizeEnd}
      >
        <div className={styles.highlight}></div>
      </div>
    </div>
  );
};
