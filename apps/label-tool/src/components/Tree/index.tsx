import type { CSSProperties, FC } from 'react';
import { useState } from 'react';
import { RiFolderOpenLine } from 'react-icons/ri';

import styles from './styles.module.css';

type OnSelected = (name: string) => void;

export interface DataNode {
  title: string;
  key: string;
  icon: React.ReactNode;
  name: string;
  children?: DataNode[];
}

interface TreeNodeProps extends DataNode {
  selectedName: string;
  onSelected?: OnSelected;
}

export interface TreeProps {
  data: DataNode[];
  style?: CSSProperties;
  onSelected?: OnSelected;
}

const TreeNode: FC<TreeNodeProps> = ({ title, children, icon, onSelected, selectedName, name }) => {
  const [expand, setExpand] = useState<boolean>(true);
  const isLeaf = !children;
  const handleClick = () => {
    onSelected?.(name);
    if (!isLeaf) {
      setExpand(!expand);
    }
  };
  return (
    <li className={styles.treeNode}>
      <div className={styles.treeNodeTitle} onClick={handleClick} aria-selected={selectedName === name} title={title}>
        <span className={styles.treeNodeIcon}>{isLeaf || !expand ? icon : <RiFolderOpenLine />}</span>
        <span>{title}</span>
      </div>
      {children && (
        <ul className={styles.tree} aria-hidden={!expand}>
          {children.map((childNode) => (
            <TreeNode {...childNode} onSelected={onSelected} selectedName={selectedName} key={childNode.key} />
          ))}
        </ul>
      )}
    </li>
  );
};

export const Tree: FC<TreeProps> = ({ data, style: propStyle, onSelected }) => {
  const [selectedName, setSelectedName] = useState<string>('');
  const handleSelect = (name: string) => {
    onSelected?.(name);
    setSelectedName(name);
  };

  return (
    <ul style={propStyle} className={`${styles.tree} ${styles.container}`}>
      {data.map((node) => (
        <TreeNode {...node} onSelected={handleSelect} selectedName={selectedName} key={node.key} />
      ))}
    </ul>
  );
};
