.tree {
  list-style-type: none;
  margin: 0;
  padding-left: 20px;
  font-family: Arial, sans-serif;
  position: relative;
  user-select: none;
}

.tree[aria-hidden='true'] {
  display: none;
}

.tree::before {
  content: '';
  display: block;
  width: 0;
  height: calc(100% - 12px);
  position: absolute;
  top: 0;
  bottom: 0;
  left: 10px;
  border-left: 1px dashed #ccc;
}

.container {
  padding: 0;
}

.container::before {
  border: 0;
}

.treeNode {
  margin: 4px 0;
}

.treeNodeTitle {
  border-radius: 2px;
  background: rgba(23, 31, 38, 0.4);
  padding: 4px;
  color: #f5f7fa;
  box-sizing: border-box;

  font-family: PingFang SC;
  font-size: 12px;
  line-height: 20px;
  height: 28px;
  cursor: pointer;

  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.treeNodeIcon {
  margin-right: 4px;
  font-size: 16px;
  height: 16px;
  line-height: 16px;
  flex-shrink: 0;
  vertical-align: middle;
}

.treeNodeTitle[aria-selected='false']:hover {
  border: 1px solid #3aa691;
  padding: 3px;
}

.treeNodeTitle[aria-selected='true'] {
  background-color: rgba(58, 166, 145, 0.6);
}
