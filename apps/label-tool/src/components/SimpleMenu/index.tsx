import { Divider, Tooltip } from 'antd';
import type { CSSProperties, FC, ReactNode } from 'react';
import { useMemo } from 'react';
import { RiArrowDropDownLine } from 'react-icons/ri';

import { DropdownWithTooltip, MenuButton } from '@/components';
import type { SegModeType, ToolType } from '@/utils/tool';

import styles from './styles.module.css';

type GroupType = 'tool3d' | 'tool2d';

// 分别表示“辅助工具”，“测距”，“删除所有观察点” “漏标”
type HelperType = 'helper' | 'caliper' | 'caliper-h' | 'caliper-v' | 'delete-keypoints' | 'pyramid';

export interface BasicMenuItem {
  name: ToolType | GroupType | 'divider' | 'hotkey' | HelperType | SegModeType | 'comment';
  text?: string;
  icon?: ReactNode;
  sideElement?: ReactNode;
}
export interface SimpleMenuItem extends BasicMenuItem {
  type: 'button' | 'divider' | 'group';
  children?: BasicMenuItem[];
}
export interface SimpleMenuProps {
  items: Array<SimpleMenuItem>;
  onSelect: (name: string) => void;
  style?: CSSProperties;
  selectedKey?: string;
}

export const SimpleMenu: FC<SimpleMenuProps> = ({ items, style: propStyle, onSelect, selectedKey }) => {
  const { groupSelectedKey, selectedChild } = useMemo(() => {
    let selectedChild: BasicMenuItem | undefined = undefined;

    const groupSelectedKey =
      items.find((item) => {
        if (item.name === selectedKey) return true;

        selectedChild = item?.children?.find((child) => selectedKey === child?.name);

        if (selectedChild) return true;

        return false;
      })?.name ?? selectedKey;

    return { groupSelectedKey, selectedChild } as {
      groupSelectedKey: string | undefined;
      selectedChild: BasicMenuItem | undefined;
    };
  }, [selectedKey, items]);

  return (
    <div className={styles.container} style={propStyle}>
      {items.map(({ name, type, text, icon, children }) => {
        if (type === 'divider') return <Divider key={name} type="vertical" />;
        if (type === 'group') {
          return (
            <DropdownWithTooltip
              key={name}
              menu={{
                items: children?.map((child) => ({
                  key: child?.name,
                  label: (
                    <div className={styles['group-item']}>
                      {child.icon}
                      <span className={styles.title}>{child.text}</span>
                      <span style={{ letterSpacing: 1 }}>{child.sideElement}</span>
                    </div>
                  ),
                })),
                selectedKeys: selectedChild ? [selectedChild.name] : [],
                onClick: ({ key }) => onSelect(key),
              }}
              tooltipPlacement="bottom"
              tooltipTitle={text || ''}
            >
              <MenuButton aria-selected={groupSelectedKey === name}>
                {groupSelectedKey === name ? selectedChild?.icon ?? icon : icon}
                <RiArrowDropDownLine fontSize="16px" />
              </MenuButton>
            </DropdownWithTooltip>
          );
        } else if (type === 'button') {
          return (
            <Tooltip key={name} title={text} placement="bottom">
              <MenuButton
                aria-selected={selectedKey === name}
                onClick={() => {
                  onSelect(name);
                }}
              >
                {icon}
              </MenuButton>
            </Tooltip>
          );
        } else return <></>;
      })}
    </div>
  );
};
