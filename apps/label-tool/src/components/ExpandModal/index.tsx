import { KvCompoundClose, KvDownOut, KvExpand, KvShrink } from '@forest/icons';
import { Dropdown } from 'antd';
import { CSSProperties, PropsWithChildren, ReactNode, useMemo, useState } from 'react';

import styles from './index.module.css';

interface ExpandModalProps {
  title?: ReactNode | string;
  width?: number;
  height?: CSSProperties['height'];
  positionStyle?: CSSProperties;
  expandedSize?: {
    height: number;
    width: number;
  };
  expandedWidth?: number;

  /**
   * @description 底部按钮
   * null 表示不显示底部按钮
   * undefined 表示显示默认底部按钮
   * 其他值表示自定义底部按钮
   */
  footer?: ReactNode | null;

  /**
   * 当前面板的生效配置范围（此处在 footer 不为 null 时生效）
   */
  scopeSettings?: {
    scope: string;
    changeScope: (scope: string) => void;
    // 仅是允许的选项才在这里
    scopeOptions: Record<string, string>;
  };

  onOk?: () => void;
  onCancel?: () => void;
}

export const ExpandModal = ({
  footer,
  width = 240,
  height = 'unset',
  expandedSize,
  positionStyle = {},
  scopeSettings,
  title,
  onOk,
  onCancel,
  children,
}: PropsWithChildren<ExpandModalProps>) => {
  const [isExpand, setIsExpand] = useState(false);
  const wrapperStyle = useMemo(() => {
    if (expandedSize && isExpand) {
      return {
        ...positionStyle,
        ...expandedSize,
      };
    }
    return {
      ...positionStyle,
      width,
      height,
    };
  }, [expandedSize, height, isExpand, positionStyle, width]);

  const items = useMemo(() => {
    if (!scopeSettings) return [];
    return Object.entries(scopeSettings.scopeOptions).map(([key, value]) => ({
      key: key,
      label: value,
    }));
  }, [scopeSettings]);

  const handleSelectScope = ({ key }: { key: string }) => {
    scopeSettings?.changeScope(key);
  };

  const defaultFooter = (
    <>
      <button className="button" onClick={onCancel}>
        取消
      </button>
      <button className="button primary" onClick={onOk}>
        保存
      </button>
    </>
  );

  return (
    <div className={styles['expand-modal']} aria-expanded={isExpand} style={wrapperStyle}>
      <div className={styles['expand-modal-header']}>
        <div className={styles['expand-modal-title']}>{title}</div>
        <div className={styles['expand-modal-opt']}>
          {expandedSize && (
            <span className={styles['expand-modal-icon']} onClick={() => setIsExpand((pre) => !pre)}>
              {isExpand ? <KvShrink /> : <KvExpand />}
            </span>
          )}
          <span className={styles['expand-modal-icon']} onClick={onCancel}>
            <KvCompoundClose />
          </span>
        </div>
      </div>
      <div className={styles['expand-modal-body']}>{children}</div>
      {footer === null ? null : (
        <div className={styles['expand-modal-footer']}>
          <div>
            {scopeSettings && (
              <Dropdown menu={{ items, onClick: handleSelectScope }}>
                <span className={styles['expand-modal-footer-scope']}>
                  {scopeSettings.scopeOptions[scopeSettings.scope]} <KvDownOut />
                </span>
              </Dropdown>
            )}
          </div>
          <div> {footer ?? defaultFooter}</div>
        </div>
      )}
    </div>
  );
};
