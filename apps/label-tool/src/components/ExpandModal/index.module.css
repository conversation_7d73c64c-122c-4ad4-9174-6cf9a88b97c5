.expand-modal {
  display: flex;
  flex-direction: column;
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: #52606d;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  color: #f5f7fa;
  box-shadow: 0px 8px 11px 0px #00000040;
}

.expand-modal[aria-expanded='true'] {
  height: 400px;
  width: 400px;
}

.expand-modal-header {
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.expand-modal-title {
  margin-inline-start: 12px;
  font-weight: 600;
  font-size: 12px;
}

.expand-modal-title :global(strong) {
  color: #3aa691;
  margin-inline: 4px;
}

.expand-modal-opt {
  margin-right: 10px;
  font-size: 16px;
  display: inline-flex;
  align-items: center;
}

.expand-modal-icon {
  margin-inline-start: 8px;
  padding: 2px;
  cursor: pointer;
  line-height: 0;
}

.expand-modal-icon:hover {
  background-color: #cbd2d966;
  border-radius: 2px;
}

.expand-modal-body {
  flex: 1;
  background-color: #323f4b;
  padding: 8px 8px 4px;
  overflow-y: auto;
}

.expand-modal-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
}

.expand-modal-footer-scope {
  margin-left: 4px;
  font-size: 12px;
  display: inline-flex;
  align-items: center;
  padding: 4px;
}

.expand-modal-footer-scope:hover {
  background-color: #171f2666;
  border-radius: 4px;
  cursor: pointer;
}
.expand-modal-footer-scope svg {
  font-size: 16px;
  margin-inline-start: 4px;
}

.expand-modal-footer :global(.button) {
  display: inline-flex;
  background-color: #edf2f7;
  color: #1a202c;
  height: 24px;
  align-items: center;
  margin-inline: 4px;
  cursor: pointer;
  border-radius: 4px;
  border: none;
  padding: 0 10px;
  margin: 4px;
}

.expand-modal-footer :global(.button:hover),
.expand-modal-footer :global(.button:active) {
  color: #3aa691;
}

.expand-modal-footer :global(.button.primary) {
  color: #ffffff;
  background-color: #3aa691;
}

.expand-modal-footer :global(.button.primary:hover),
.expand-modal-footer :global(.button.primary:active) {
  background-color: #61b8a7;
}
