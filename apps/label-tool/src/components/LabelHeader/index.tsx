import { <PERSON><PERSON>, <PERSON><PERSON>, Di<PERSON>r, Popover, Tooltip } from 'antd';
import type { CSSProperties, PropsWithChildren } from 'react';
import { useState } from 'react';
import { RiHome3Line } from 'react-icons/ri';

import { ADMIN_ORIGIN, VERSION } from '@/config';

import './styles.css';

interface LabelHeaderProps {
  height: number;
  username?: string;
  style?: CSSProperties;
}
export const LabelHeader = ({ username, height, style: propStyle, children }: PropsWithChildren<LabelHeaderProps>) => {
  const [open, setOpen] = useState<boolean>(false);

  const content = (
    <div className="label-header-logo-popover">
      版本信息
      {VERSION && <span className="version">{VERSION}</span>}
    </div>
  );

  const onClickBack = () => {
    const url = `${ADMIN_ORIGIN}/admin/tasks/hall`;
    window.open(url, '_self');
  };

  return (
    <div className="label-header" style={{ height, ...propStyle }}>
      {/* 全局 loading zIndex 为 5000，tooltip 要显示在全局 loading 的 header 之上，因此 5001 */}
      <Popover placement="bottom" open={open} content={content} color="#323f4b" zIndex={5001}>
        <img
          className="logo"
          alt="logo"
          src="/logo_2x.png"
          onDoubleClick={() => setOpen(true)}
          tabIndex={0}
          onBlur={() => setOpen(false)}
        />
      </Popover>
      {children}
      <div className="opt-container-right">
        <Divider type="vertical" />
        {/* 全局 loading zIndex 为 5000，tooltip 要显示在全局 loading 的 header 之上，因此 5001 */}
        <Tooltip title={'返回大厅'} placement="bottom" zIndex={5001}>
          <Button className="opt-btn" type="text" icon={<RiHome3Line />} onClick={onClickBack}></Button>
        </Tooltip>
        <Avatar className="avatar" size="small">
          {username ? username.slice(0, 4) : 'U'}
        </Avatar>
      </div>
    </div>
  );
};
