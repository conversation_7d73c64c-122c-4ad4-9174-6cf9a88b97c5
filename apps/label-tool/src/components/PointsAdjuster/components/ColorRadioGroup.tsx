import { InputNumber, Radio, Slider } from 'antd';
import { FC } from 'react';

import styles from '../style.module.css';

export interface RadioOptions {
  value: string;
  label: string;
}

interface ColorRadioGroupProps {
  value?: string;
  options: Array<RadioOptions>;
  data: {
    intensitySeg?: number;
    intensityRange: [number, number];
    heightRange: [number, number];
    heightSeg?: [number, number];
  };
  onChange?: (evt: any) => void;
}

export const ColorRadioGroup: FC<ColorRadioGroupProps> = ({ value, options, data, onChange }) => {
  const { intensitySeg, intensityRange, heightSeg, heightRange } = data;

  return (
    <div>
      <Radio.Group
        value={value}
        options={options}
        style={{ marginTop: 10 }}
        onChange={(evt) => {
          onChange?.({ colorTheme: evt.target.value });
        }}
      ></Radio.Group>

      {value && ['binarization', 'intensity'].includes(value) && (
        <div className={styles['color-component']}>
          <div className={styles['color-label']}>
            {value === 'binarization' ? '对比度' : '强度值'}
            <InputNumber
              className={styles['color-input']}
              min={intensityRange[0]}
              max={intensityRange[1]}
              value={intensitySeg}
              step={0.1}
              onChange={(intensitySeg) => {
                onChange?.({ intensitySeg });
              }}
            />
          </div>
          <Slider
            value={intensitySeg}
            range={false}
            min={intensityRange[0]}
            max={intensityRange[1]}
            step={0.1}
            onChange={(intensitySeg) => {
              onChange?.({ intensitySeg });
            }}
          />
        </div>
      )}

      {value === 'height' && (
        <div className={styles['color-component']}>
          <div className={styles['color-label']}>高度颜色</div>
          <Slider
            className={styles['color-slider']}
            range={true}
            styles={{
              rail: {
                background: '#9C9C9C',
              },
            }}
            value={[heightSeg?.[0] ?? heightRange[0], heightSeg?.[1] ?? heightRange[1], heightRange[1]]}
            min={heightRange[0]}
            max={heightRange[1]}
            step={0.1}
            onChange={(heightSeg) => {
              onChange?.({ heightSeg });
            }}
          />
        </div>
      )}
    </div>
  );
};
