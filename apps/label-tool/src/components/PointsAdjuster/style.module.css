.container {
  width: 374px;
  padding: 0 12px;
}

.section {
  margin: 10px 0;
}

.section:last-child {
  margin-bottom: 0;
}

.section :global(.ant-radio-wrapper):last-child {
  margin-right: 0;
}

.label {
  color: #f5f7fa;
  font-size: 14px;
  line-height: 22px;
}

.divider {
  margin: 0 -20px;
  width: calc(100% + 40px);
}

.color-component {
  border-radius: 4px;
  border: 1px solid #52606d;
  margin: 10px 0;
  padding: 10px 12px 5px 10px;
}

.color-component .color-label {
  color: #f5f7fa;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.color-component .color-label .color-input {
  margin-left: auto;
}

.color-component .color-slider :global(.ant-slider-track-1) {
  background-color: #58ff91;
}

.color-component .color-slider :global(.ant-slider-track-1):hover {
  background-color: #58ff91;
}

.color-component .color-slider :global(:where(.ant-slider .ant-slider-handle-1))::after {
  box-shadow: 0 0 0 3px #58ff91 !important;
}

.color-component .color-slider :global(:where(.ant-slider .ant-slider-handle-2))::after {
  box-shadow: 0 0 0 3px #58ff91 !important;
}

.color-component .color-slider :global(:where(.ant-slider:hover .ant-slider-track-1)) {
  background-color: #58ff91 !important;
}

.color-component .color-slider :global(.ant-slider-track-2) {
  background-color: #006ae7;
}

.color-component.color-slider :global(.ant-slider-track-2):hover {
  background-color: #006ae7;
}

.color-component.color-slider :global(:where(.ant-slider:hover .ant-slider-track-2)) {
  background-color: #006ae7 !important;
}

.color-component .color-slider :global(:where(.ant-slider .ant-slider-handle-3))::after {
  box-shadow: 0 0 0 3px #006ae7 !important;
}
