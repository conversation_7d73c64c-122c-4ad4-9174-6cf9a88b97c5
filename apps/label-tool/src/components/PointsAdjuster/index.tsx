import { ColorPicker, Divider, InputNumber, Radio, Slider } from 'antd';
import { FC, useEffect, useMemo, useState } from 'react';

import {
  POINTS_DEFAULT_BACKGROUND_COLOR,
  POINTS_HEIGHT_RANGE_DEFAULT,
  POINTS_INTENSITY_RANGE_DEFAULT,
} from '@/config/render';
import { RAW_THEME_MAP, RawObjectOptions, RawPoints } from '@/utils/render';

import type { RadioOptions } from './components';
import { ColorRadioGroup } from './components';

import styles from './style.module.css';

/**
 * 点云设置
 */
export type IPointsValue = Pick<
  RawObjectOptions,
  'colorTheme' | 'pointSize' | 'visibleZ' | 'groundZ' | 'intensitySeg' | 'heightSeg'
> & {
  background: string;
};
export interface IPointsConfig {
  zRange?: [number, number];
  intensityRange?: [number, number];
}

export interface PointsAdjusterProps {
  value: IPointsValue;
  config?: IPointsConfig;
  onChange?: (value: Partial<IPointsValue>) => void;
  onMouseLeave?: () => void;
}

export const PointsAdjuster: FC<PointsAdjusterProps> = ({
  value: { visibleZ, pointSize, colorTheme, groundZ, intensitySeg, background, heightSeg },
  config,
  onChange,
  onMouseLeave,
}) => {
  const [heightRange, setHeightRange] = useState<[number, number]>(POINTS_HEIGHT_RANGE_DEFAULT);
  const [intensityRange, setIntensityRange] = useState<[number, number]>(POINTS_INTENSITY_RANGE_DEFAULT);

  const [colorOptions, setColorOptions] = useState<RadioOptions[]>([]);
  const [bgCustomColor, setBgCustomColor] = useState<string>(
    background === POINTS_DEFAULT_BACKGROUND_COLOR ? '#213359' : background
  );

  const backgroundName = useMemo(() => {
    return background === POINTS_DEFAULT_BACKGROUND_COLOR ? 'default' : 'custom';
  }, [background]);

  useEffect(() => {
    const { zRange, intensityRange } = config || {};

    if (zRange) {
      setHeightRange([Math.floor(zRange[0]), Math.ceil(zRange[1])]);
    }

    if (intensityRange) {
      setIntensityRange([Math.floor(intensityRange[0]), Math.ceil(intensityRange[1])]);
    }

    const themes = RawPoints.getAvailableThemes(intensityRange);
    const options = themes.map((value) => ({
      value,
      label: RAW_THEME_MAP[value],
    }));
    setColorOptions(options);

    if (colorTheme && themes.length > 0 && !themes.includes(colorTheme)) {
      onChange?.({ colorTheme: themes[0] });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [config]);

  return (
    <div className={styles['container']} onMouseLeave={onMouseLeave}>
      <div className={styles['section']}>
        <div className={styles['label']}>显示高度（z轴）</div>
        <Slider
          value={visibleZ}
          range={true}
          min={heightRange[0]}
          max={heightRange[1]}
          step={0.1}
          marks={{
            [heightRange[0]]: heightRange[0],
            [heightRange[1]]: heightRange[1],
          }}
          onChange={(visibleZ) => onChange?.({ visibleZ: visibleZ as [number, number] })}
        />
      </div>
      <Divider className={styles['divider']} />
      <div className={styles['section']}>
        <div className={styles['label']}>点大小</div>
        <Slider
          value={pointSize}
          min={1}
          max={10}
          step={0.1}
          marks={{
            1: 1,
            3: 3,
            6: 6,
            10: 10,
          }}
          onChange={(pointSize) => onChange?.({ pointSize })}
        />
      </div>
      <Divider className={styles['divider']} />

      <div className={styles['section']}>
        <div className={styles['label']}>点云色彩</div>
        <ColorRadioGroup
          value={colorTheme}
          options={colorOptions}
          data={{ intensitySeg, intensityRange, heightRange, heightSeg }}
          onChange={onChange}
        />
      </div>

      <Divider className={styles['divider']} />
      <div className={styles['section']}>
        <div className={styles['label']}>点云背景色</div>
        <Radio.Group
          value={backgroundName}
          style={{ marginTop: 10, display: 'flex', alignItems: 'center' }}
          onChange={(evt) => {
            onChange?.({
              background: evt.target.value === 'default' ? POINTS_DEFAULT_BACKGROUND_COLOR : bgCustomColor,
            });
          }}
        >
          <Radio value="default">默认黑色</Radio>
          <Radio value="custom">
            <ColorPicker
              trigger="hover"
              disabledAlpha
              size="small"
              showText={() => <span>自定义</span>}
              value={bgCustomColor}
              onChangeComplete={(color) => {
                const colorHex = color.toHexString();
                onChange?.({ background: colorHex });
                setBgCustomColor(colorHex);
              }}
            />
          </Radio>
        </Radio.Group>
      </div>
      <Divider className={styles['divider']} />
      <div className={styles['section']}>
        <div className={styles['label']}>地面高度</div>
        <InputNumber
          value={groundZ}
          size="small"
          style={{ marginTop: 10, marginBottom: 10 }}
          step={0.1}
          min={heightRange[0]}
          max={heightRange[1]}
          onChange={(groundZ) => {
            if (typeof groundZ === 'number' && onChange) {
              onChange({ groundZ });
            }
          }}
        />
      </div>
    </div>
  );
};
