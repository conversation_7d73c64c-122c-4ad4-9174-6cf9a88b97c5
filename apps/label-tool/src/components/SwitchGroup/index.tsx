import { Switch } from 'antd';
import type { CSSProperties, FC, PropsWithChildren } from 'react';

import { BasicMenuItem } from '../SimpleMenu';

import styles from './styles.module.css';

// 分别表示是否展示车姿、是否展示标注范围、是否开启 bev 模式
export type DisplayType = 'poseVisible' | 'rangeVisible' | 'isBevModel';

export interface BasicSwitchItem extends Omit<BasicMenuItem, 'name'> {
  name: DisplayType;
}

interface SwitchGroupProps {
  items: BasicSwitchItem[];
  values: Record<DisplayType, boolean>;
  style?: CSSProperties;
  onChange: (name: DisplayType) => void;
}

export const SwitchGroup: FC<PropsWithChildren<SwitchGroupProps>> = ({ items, values, style: propStyle, onChange }) => {
  return (
    <div className={styles.container} style={propStyle}>
      {items.map((child) => {
        return (
          <div className={styles['group-item']} key={child.name}>
            {child.icon}
            <span className={styles.title}>{child.text}</span>
            <Switch
              checked={values[child.name]}
              onChange={() => {
                onChange(child.name);
              }}
            ></Switch>
          </div>
        );
      })}
    </div>
  );
};
