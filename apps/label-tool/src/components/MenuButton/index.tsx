import { forwardRef, HTMLProps, PropsWithChildren } from 'react';

import styles from './styles.module.css';

export const MenuButton = forwardRef<HTMLDivElement, PropsWithChildren<HTMLProps<HTMLDivElement>>>(
  ({ children, className, ...props }, ref) => {
    return (
      <div ref={ref} className={`${styles['menu-button']} ${className || ''}`} {...props}>
        {children}
      </div>
    );
  }
);
