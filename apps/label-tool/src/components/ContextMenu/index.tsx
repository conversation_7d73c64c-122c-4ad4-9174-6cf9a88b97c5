import { Dropdown } from 'antd';
import { FC, useEffect, useState } from 'react';

import styles from './styles.module.css';

export type ContextMenuItem = Array<{
  name: string;
  text?: string;
  hotkey?: string;
  disabled?: boolean;
}>;

export const ContextMenu: FC<{
  items: ContextMenuItem;
  onItemClick: (key: string) => void;
  triggerElement?: HTMLElement | null;
}> = ({ items, onItemClick, triggerElement = document }) => {
  const [visible, setVisible] = useState<boolean>(false);
  const [position, setPosition] = useState<{ left: number; top: number }>({ left: 0, top: 0 });

  // 添加全局点击事件监听
  useEffect(() => {
    const handleGlobalClick = () => setVisible(false);
    if (visible) {
      document.addEventListener('click', handleGlobalClick);
    } else {
      document.removeEventListener('click', handleGlobalClick);
    }

    // 清除全局点击事件监听
    return () => {
      document.removeEventListener('click', handleGlobalClick);
    };
  }, [visible]);

  useEffect(() => {
    if (!triggerElement) return;

    let mouseDownTime: number;

    const handleMouseDown = (e: Event) => {
      e.preventDefault();
      setVisible(false);
      if (e instanceof MouseEvent && e.button === 2) {
        mouseDownTime = Date.now();
      }
    };

    const handleMouseUp = (e: Event) => {
      e.preventDefault();
      if (e instanceof MouseEvent && e.button === 2) {
        const mouseUpTime = Date.now();
        // 只有按下之后很快就松开才显示右键菜单，避免和拖动画布冲突
        if (mouseUpTime - mouseDownTime <= 150) {
          setPosition({ left: e.pageX, top: e.pageY });
          setVisible(true);
        }
      }
    };

    triggerElement.addEventListener('mousedown', handleMouseDown);
    triggerElement.addEventListener('mouseup', handleMouseUp);

    return () => {
      triggerElement.removeEventListener('mousedown', handleMouseDown);
      triggerElement.removeEventListener('mouseup', handleMouseUp);
    };
  }, [triggerElement]);

  return (
    <Dropdown
      menu={{
        items: items.map((item) =>
          item.name === 'divider'
            ? { type: 'divider' }
            : {
                key: item?.name,
                label: (
                  <div className={styles['contextmenu-item']}>
                    <span className={styles['contextmenu-item-title']}>{item.text}</span>
                    <span style={{ letterSpacing: 1 }}>{item.hotkey}</span>
                  </div>
                ),
                disabled: item?.disabled,
              }
        ),
        onClick: ({ key }) => {
          onItemClick(key);
          setVisible(false);
        },
      }}
      open={visible}
      placement="bottomLeft"
    >
      {/* 用于定位 dropdown 出现的位置 */}
      <div
        className={styles['contextmenu-anchor']}
        style={{
          left: `${position.left}px`,
          top: `${position.top}px`,
        }}
      />
    </Dropdown>
  );
};
