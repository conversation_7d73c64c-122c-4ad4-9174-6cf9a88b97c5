import { FormItem, Input, Select } from '@formily/antd-v5';
import { createForm } from '@formily/core';
import { createSchemaField, FormProvider } from '@formily/react';
import { forwardRef, useEffect, useImperativeHandle, useMemo } from 'react';

import { formTpls } from '@/features/anno/utils';
import { Label } from '@/types';
import { IAnnoItem } from '@/utils/annos';

import styles from './styles.module.css';

type IAttrFormData = Record<string, string[]>;

interface AttrFormProps {
  anno?: IAnnoItem | null;
  labelName?: string;
  labelMap: Map<Label['name'], Label>;
}
const SchemaField = createSchemaField({
  components: {
    FormItem,
    Input,
    Select,
  },
});

export interface IAttrFormMethods {
  submit: () => {
    label: Label;
    attrs?: Record<string, string[]>;
  } | null;
}

export const AttrForm = forwardRef<IAttrFormMethods, AttrFormProps>(({ anno, labelName, labelMap }, ref) => {
  const [form, schema] = useMemo(() => {
    if (!labelName || !anno) return [];
    const label = labelMap.get(labelName);
    if (!label) return [];
    const attrs = label.fullAttrs;
    const schema =
      Array.isArray(attrs) && attrs.length > 0
        ? {
            type: 'object',
            properties: attrs.reduce((pre, attr) => {
              return {
                ...pre,
                [attr.name]: {
                  'x-decorator': 'FormItem',
                  'x-decorator-props': {
                    colon: false,
                    label: <div className={styles['attrs-form-label']}>{attr.display_name}</div>,
                    feedbackLayout: 'none',
                    bordered: false,
                    className: styles['attrs-form-item'],
                    wrapperStyle: {
                      lineHeight: 'unset',
                    },
                  },
                  ...formTpls(attr.type, attr.choices, 'default'),
                },
              };
            }, {}),
          }
        : undefined;

    const attrValues =
      anno?.label === labelName ? { ...label.defaultAttrValues, ...anno.attrs } : { ...label.defaultAttrValues };

    const form = schema
      ? createForm<IAttrFormData>({
          initialValues: attrValues,
        })
      : undefined;
    return [form, schema];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [labelName]);

  useEffect(() => {
    if (anno?.attrs && anno?.label === labelName) {
      form?.setValues(anno?.attrs);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [anno]);

  useImperativeHandle(ref, () => ({
    submit: () => {
      if (!labelName || !anno) return null;
      const label = labelMap.get(labelName);
      if (!label) return null;

      const values = form?.values,
        attrs: Record<string, string[]> = {};
      if (values) {
        for (const key in values) {
          const attr = Reflect.get(values, key);

          if (typeof attr === 'string') {
            attrs[key] = [attr];
          } else {
            // 如果此处得到可能是 proxy<Array> 对象，通过 map 转化成普通的字符串对象
            attrs[key] = attr.map((item: string) => item);
          }
        }
      }

      return { label, attrs };
    },
  }));

  return form ? (
    <FormProvider form={form}>
      <SchemaField schema={schema} />
    </FormProvider>
  ) : (
    <></>
  );
});
