import { FormItem, Input } from '@formily/antd-v5';
import { createForm } from '@formily/core';
import { createSchemaField, FormProvider, useForm } from '@formily/react';
import { CSSProperties, useMemo, useState } from 'react';

import styles from './styles.module.css';

export interface IDescItem {
  label: string;
  value: number;
  color?: string;
}

export interface DescInfoProps {
  type?: string;
  items?: Array<IDescItem>;
  style?: CSSProperties;
}

const MyInput = ({ hoveredEditable = false, name = '', label = '', color = '', ...props }) => {
  const [hover, setHover] = useState(false);
  const { getFieldState } = useForm();

  const { value } = getFieldState(name);

  return (
    <div className={styles.content} onMouseEnter={() => setHover(true)} onMouseLeave={() => setHover(false)}>
      {hover && hoveredEditable ? (
        <Input
          className={styles.input}
          prefix={<div className={styles.label}>{label}</div>}
          value={value}
          style={
            color
              ? {
                  color,
                }
              : {}
          }
          {...props}
        />
      ) : (
        <div
          className={styles.text}
          style={
            color
              ? {
                  color,
                }
              : {}
          }
        >
          <div className={styles.label}>{label}</div>
          {value}
        </div>
      )}
    </div>
  );
};

const SchemaField = createSchemaField({
  components: {
    FormItem,
    MyInput,
  },
});

export const DescInfo = ({ type, items, style }: DescInfoProps) => {
  const [form, schema] = useMemo(() => {
    const schema = {
      type: 'object',
      properties: items?.reduce((pre, item) => {
        return {
          ...pre,
          [item.label]: {
            type: 'number',
            'x-decorator': 'FormItem',
            'x-component': 'MyInput',
            'x-component-props': {
              hoveredEditable: false, // 目前不支持编辑
              label: item.label,
              name: item.label,
              color: item.color,
            },
            'x-decorator-props': {
              className: styles.content,
            },
          },
        };
      }, {}),
    };

    const form = createForm({
      values: items?.reduce(
        (pre, item) => ({
          ...pre,
          [item.label]: item.value,
        }),
        {}
      ),
    });
    return [form, schema];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [items]);

  if (!type || !items) return null;

  return (
    <div className={styles.wrapper} style={{ columnCount: items.length > 3 ? 2 : 1, ...style }}>
      <FormProvider form={form}>
        <SchemaField schema={schema} />
      </FormProvider>
    </div>
  );
};
