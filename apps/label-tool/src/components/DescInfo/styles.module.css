.wrapper {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 9;

  width: 242px;
  padding: 8px;

  column-count: 2;
  column-gap: 4px;

  border-radius: 4px;
  border: 1px solid #52606d;
  background: #323f4b;
  box-shadow: 0px 8px 11px 0px rgba(0, 0, 0, 0.25);

  color: #f5f7fa;
  font-size: 12px;
  line-height: 20px;
}

.content {
  display: inline-block;
  margin: 0;
  width: 100%;
}

.content :global(.ant-input-affix-wrapper) {
  padding: 4px 6px;
}

.content input {
  font-weight: 600;
  color: inherit;
}

.label {
  font-weight: 400;
}

.text {
  display: flex;
  padding: 0 6px;
  font-weight: 600;
}

.text .label {
  margin-right: 4px;
}
