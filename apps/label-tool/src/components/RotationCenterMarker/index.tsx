import { FC, useEffect, useRef, useState } from 'react';

import { OrbitControls } from '@/lib/three';

import styles from './styles.module.css';

const ROTATION_START_DELAY_MS = 200;

export const RotationCenterMarker: FC<{ controls?: OrbitControls }> = ({ controls }) => {
  const [isRotating, setIsRotating] = useState(false);
  const delayRotationStart = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (controls) {
      const onStartRotation = () => {
        delayRotationStart.current = setTimeout(() => setIsRotating(true), ROTATION_START_DELAY_MS);
      };

      const onEndRotation = () => {
        if (delayRotationStart.current) {
          clearTimeout(delayRotationStart.current);
          delayRotationStart.current = null;
        }
        setIsRotating(false);
      };

      controls.addEventListener('startRotate', onStartRotation);
      controls.addEventListener('end', onEndRotation);

      return () => {
        controls.removeEventListener('startRotate', onStartRotation);
        controls.removeEventListener('end', onEndRotation);
      };
    }
  }, [controls]);

  if (!isRotating) return null;

  return (
    <svg
      className={styles['rotation-center-marker']}
      xmlns="http://www.w3.org/2000/svg"
      width="48"
      height="48"
      viewBox="0 0 48 48"
      fill="none"
    >
      <g filter="url(#filter0_d_3338_70855)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M23.5 4H24.5V12.0098C30.7285 12.2656 35.7344 17.2715 35.9902 23.5H44V24.5H35.9902C35.7344 30.7285 30.7285 35.7344 24.5 35.9902V44H23.5V35.9902C17.2715 35.7344 12.2656 30.7285 12.0098 24.5H4V23.5H12.0098C12.2656 17.2715 17.2715 12.2656 23.5 12.0098V4ZM24 34C29.5234 34 34 29.5234 34 24C34 18.4766 29.5234 14 24 14C18.4766 14 14 18.4766 14 24C14 29.5234 18.4766 34 24 34Z"
          fill="white"
        />
      </g>
      <g opacity="0.5" filter="url(#filter1_d_3338_70855)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7.11719 22C8.0293 14.209 14.209 8.0293 22 7.11719V8.12305C14.7617 9.02734 9.02539 14.7617 8.12305 22H7.11719Z"
          fill="white"
        />
      </g>
      <g opacity="0.5" filter="url(#filter2_d_3338_70855)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M40.8828 22.002C39.9695 14.212 33.7894 8.03209 25.9993 7.11914V8.12642C33.2357 9.02824 38.9734 14.7657 39.8755 22.002H40.8828Z"
          fill="white"
        />
      </g>
      <g opacity="0.5" filter="url(#filter3_d_3338_70855)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7.11914 25.9992C8.03212 33.7896 14.2124 39.9698 22.0027 40.8828V39.8755C14.7661 38.9737 9.02827 33.2359 8.12642 25.9992H7.11914Z"
          fill="white"
        />
      </g>
      <g opacity="0.5" filter="url(#filter4_d_3338_70855)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M40.8809 25.9973C39.9679 33.7876 33.7876 39.9679 25.9973 40.8809V39.8736C33.2339 38.9717 38.9717 33.2339 39.8736 25.9973H40.8809Z"
          fill="white"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_3338_70855"
          x="0"
          y="0"
          width="48"
          height="48"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3338_70855" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3338_70855" result="shape" />
        </filter>
        <filter
          id="filter1_d_3338_70855"
          x="3.11719"
          y="3.11719"
          width="22.8828"
          height="22.8828"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3338_70855" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3338_70855" result="shape" />
        </filter>
        <filter
          id="filter2_d_3338_70855"
          x="22"
          y="3.11914"
          width="22.8828"
          height="22.8828"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3338_70855" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3338_70855" result="shape" />
        </filter>
        <filter
          id="filter3_d_3338_70855"
          x="3.11914"
          y="22"
          width="22.8828"
          height="22.8828"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3338_70855" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3338_70855" result="shape" />
        </filter>
        <filter
          id="filter4_d_3338_70855"
          x="21.998"
          y="21.998"
          width="22.8828"
          height="22.8828"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3338_70855" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3338_70855" result="shape" />
        </filter>
      </defs>
    </svg>
  );
};
