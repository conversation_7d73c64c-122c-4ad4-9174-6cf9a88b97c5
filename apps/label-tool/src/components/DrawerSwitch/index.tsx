import Icon from '@ant-design/icons';
import { CSSProperties, PropsWithChildren, useMemo, useState } from 'react';

import { DoubleLeftOutlined, DoubleRightOutlined } from './icon';

import './index.css';

export interface DrawerSwitchProps {
  position: 'left' | 'right';
  wantToOpen: boolean;
  isAllowClick?: boolean;
  drawerStyle?: CSSProperties;
}

export const DrawerSwitch = ({
  position,
  wantToOpen,
  isAllowClick = true,
  drawerStyle: propStyle,
  children,
}: PropsWithChildren<DrawerSwitchProps>) => {
  const [isManualOpen, setIsManualOpen] = useState(true);

  const shouldOpen = useMemo(() => {
    // 在手动开关是开着的情况下，控制权交由外部控制
    if (isManualOpen) return wantToOpen;
    return isManualOpen;
  }, [wantToOpen, isManualOpen]);

  // 条件渲染可能导致内部元素绑定失效，故此处使用 display 属性控制显示隐藏
  const drawerStyle = useMemo(
    () => (shouldOpen ? propStyle : { ...propStyle, display: 'none' }),
    [shouldOpen, propStyle]
  );

  const openIcon =
      position === 'left' ? <Icon component={DoubleRightOutlined} /> : <Icon component={DoubleLeftOutlined} />,
    closeIcon =
      position === 'left' ? <Icon component={DoubleLeftOutlined} /> : <Icon component={DoubleRightOutlined} />;

  return (
    <div
      className="drawer-switch"
      style={position === 'left' ? { flexDirection: 'row', left: 0 } : { flexDirection: 'row-reverse', right: 0 }}
    >
      {isAllowClick ? (
        <div
          className="switch"
          aria-disabled={false}
          onClick={() => {
            setIsManualOpen((pre) => !pre);
          }}
        >
          <div className="switch-icon">{shouldOpen ? closeIcon : openIcon}</div>
        </div>
      ) : (
        <div className="switch"></div>
      )}
      <div className="drawer" style={drawerStyle}>
        {children}
      </div>
    </div>
  );
};
