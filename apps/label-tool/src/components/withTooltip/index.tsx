import { Dropdown, DropdownProps, Popover, Tooltip, TooltipProps } from 'antd';
import { TooltipPlacement } from 'antd/lib/tooltip';
import { ComponentType, FC, PropsWithChildren, useState } from 'react';

import styles from './styles.module.css';

type OnOpenChange = DropdownProps['onOpenChange'] | TooltipProps['onOpenChange'];

type WithTooltipProps = PropsWithChildren<{
  tooltipTitle: string;
  tooltipPlacement: TooltipPlacement;
  onOpenChange?: OnOpenChange;
}>;

const withTooltip =
  <P extends object>(Component: ComponentType<P>): FC<WithTooltipProps & P> =>
  ({ children, tooltipTitle, tooltipPlacement, onOpenChange, ...props }) => {
    const [isSelected, setIsSelected] = useState(false);
    const [showTooltip, setShowTooltip] = useState(false);
    const handleOpenChange: OnOpenChange = (open, info) => {
      setIsSelected(open);
      setShowTooltip(false);
      onOpenChange?.(open, info);
    };
    return (
      <Component
        rootClassName={styles['dropdown']}
        trigger={['click']}
        arrow={false}
        onOpenChange={handleOpenChange}
        {...(props as P)}
      >
        <div className={styles['wrapper']} aria-selected={isSelected}>
          <Tooltip
            title={tooltipTitle}
            placement={tooltipPlacement}
            open={showTooltip}
            onOpenChange={(open: boolean) => {
              if (!isSelected || !open) {
                setShowTooltip(open);
              }
            }}
            fresh={true}
          >
            {children}
          </Tooltip>
        </div>
      </Component>
    );
  };

export const PopoverWithTooltip = withTooltip(Popover);
export const DropdownWithTooltip = withTooltip(Dropdown);
