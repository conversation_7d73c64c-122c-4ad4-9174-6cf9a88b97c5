import { useEffect, useState } from 'react';

export interface CrosshairProps {
  parentRef: React.RefObject<HTMLDivElement>;
  visibility: boolean;
}
export const Crosshair: React.FC<CrosshairProps> = ({ parentRef, visibility }) => {
  const [pointer, setPointer] = useState([10, 10]);

  useEffect(() => {
    if (parentRef.current) {
      const parent = parentRef.current;
      const onMouseMove = (event: MouseEvent) => {
        event.stopPropagation();
        event.preventDefault();
        setPointer([event.offsetX, event.offsetY]);
      };
      parent.addEventListener('mousemove', onMouseMove);
      return () => parent.removeEventListener('mousemove', onMouseMove);
    }
  }, [parentRef]);

  return (
    <div
      style={{
        visibility: visibility ? 'visible' : 'hidden',
      }}
    >
      <svg
        width="100%"
        height="1"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        style={{
          position: 'absolute',
          top: pointer[1],
          left: 0,
          pointerEvents: 'none',
        }}
      >
        <line x1="0" y1="0" x2="100%" y2="0" stroke="#fdf6e3" strokeWidth="1" />
      </svg>
      <svg
        width="1"
        height="100%"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        style={{
          position: 'absolute',
          top: 0,
          left: pointer[0],
          pointerEvents: 'none',
        }}
      >
        <line x1="0" y1="0" x2="0" y2="100%" stroke="#fdf6e3" strokeWidth="1" />
      </svg>
    </div>
  );
};
