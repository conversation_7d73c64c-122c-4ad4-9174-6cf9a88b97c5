import React, { useEffect, useState } from 'react';
import type { Observable } from 'rxjs';

export interface AuthProviderConfig<User = unknown, TError = unknown> {
  loadUser: () => Observable<User>;
  loadErrorHandler?: (error: TError) => string;
  LoaderComponent?: () => JSX.Element;
  ErrorComponent?: ({ error }: { error: TError | null }) => JSX.Element;
}

export interface AuthContextValue<User = unknown> {
  user: User | undefined;
  updateUser: (user: User) => void;
}
export interface AuthProviderProps {
  children: React.ReactNode;
}
export function initReactQueryAuth<User = unknown, TError = unknown>(config: AuthProviderConfig<User, TError>) {
  const AuthContext = React.createContext<AuthContextValue<User> | null>(null);
  AuthContext.displayName = 'AuthContext';

  const { loadUser, LoaderComponent = () => <div>waiting...</div> } = config;

  function AuthProvider({ children }: AuthProviderProps): JSX.Element {
    const [authStatus, setAuthStatus] = useState<'loading' | 'success' | 'loaded'>('loading');
    const [user, setUser] = useState<User | undefined>(undefined);

    useEffect(() => {
      // TODO: refetch the user
      const loadUser$ = loadUser();
      const loadSub = loadUser$.subscribe({
        next: (user) => {
          setAuthStatus('success');
          setUser(user);
        },
        error: (err: Error) => {
          setAuthStatus('loaded');
          throw err;
        },
      });
      return () => loadSub.unsubscribe();
    }, []);

    const value = React.useMemo(
      () => ({
        user,
        updateUser: (user: User) => setUser(user),
      }),
      [user]
    );
    switch (authStatus) {
      case 'loading':
        return <LoaderComponent />;
      case 'success':
      case 'loaded':
        return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
      default:
        return <div>Unhandled status: {authStatus}</div>;
    }
  }

  function useAuth() {
    const context = React.useContext(AuthContext);
    if (!context) {
      throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
  }

  return { AuthProvider, useAuth };
}
