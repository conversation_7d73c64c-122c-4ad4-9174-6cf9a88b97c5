import { Camera, Object3D, Scene, Vector2 } from 'three';

export declare class CSS2DObject extends Object3D {
  constructor(element?: HTMLElement);
  element: HTMLElement;
  center: Vector2;

  readonly isCSS2DObject: true;

  onBeforeRender: (renderer: unknown, scene: Scene, camera: Camera) => void;
  onAfterRender: (renderer: unknown, scene: Scene, camera: Camera) => void;
}

export type CSS2DParameters = {
  /** 渲染出的 dom 节点挂载的根节点 */
  element?: HTMLElement;
  /** 支持渲染手动构造的 dom 树的最大深度，默认为一层 */
  manualMaxDepth?: number;
};

export declare class CSS2DRenderer {
  constructor(parameters?: CSS2DParameters);
  domElement: HTMLElement;
  /** 支持渲染手动构造的 dom 树的最大深度，默认为一层 */
  manualMaxDepth: number;

  getSize(): { width: number; height: number };
  setSize(width: number, height: number): void;
  setDisplayArea(width: number, height: number): void;
  render(scene: Scene, camera: Camera): void;
  clear(): void;
}
