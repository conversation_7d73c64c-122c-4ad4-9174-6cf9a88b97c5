import { BufferGeometry, Group, Mesh, Points, ShaderMaterial } from 'three';
import { acceleratedRaycast, disposeBoundsTree } from 'three-mesh-bvh';

import { GenerateMeshBVHWorker } from '@/workers';

Mesh.prototype.raycast = acceleratedRaycast;
BufferGeometry.prototype.disposeBoundsTree = disposeBoundsTree;

const generateMeshBVHWorker = new GenerateMeshBVHWorker();

const buildBVHTree = async (geometry: BufferGeometry) => {
  const oldPositionAttribute = geometry.attributes.position,
    bvhGeometry = new BufferGeometry(),
    indices = [];
  // 只克隆需要的 position 数据
  bvhGeometry.setAttribute('position', oldPositionAttribute.clone());

  for (let i = 0, l = geometry.attributes.position.count; i < l; i++) {
    indices.push(i, i, i);
  }
  bvhGeometry.setIndex(indices);

  let bvh;
  try {
    bvh = await generateMeshBVHWorker.generate(bvhGeometry);

    if (bvh) {
      // 为了优化内存使用，此处改成使用之前的 position
      bvh.geometry.setAttribute('position', oldPositionAttribute);
    }
  } catch (err) {
    console.error('generate bvh error', err);
  }
  return bvh;
};
// 为每个 Group 中的 Points 生成 BVH
const buildAllBVHTree = (function () {
  let queue: Points<BufferGeometry, ShaderMaterial>[] = [],
    onFinish: () => void | undefined;

  const process = () => {
    if (queue.length === 0) {
      onFinish?.();
      return;
    }
    const point = queue.shift()!;
    buildBVHTree(point.geometry).then((bvh) => {
      point.geometry.boundsTree = bvh;
      process();
    });
  };

  return (_group: Group, _onFinish: () => void) => {
    onFinish = _onFinish;
    for (let i = 0, l = _group.children.length; i < l; i++) {
      const point = _group.children[i] as Points<BufferGeometry, ShaderMaterial>;
      queue.push(point);
    }
    process();
  };
})();

// 每个任务的完成，都会触发这个任务对应的 onFinish 回调
export const buildBVHTreeForGroup = (function () {
  let isProcessing = false,
    queue: [Group, () => void][] = [];

  const process = () => {
    if (queue.length === 0) {
      isProcessing = false;
      return;
    }
    isProcessing = true;
    const [group, onFinish] = queue.shift()!;
    buildAllBVHTree(group, () => {
      onFinish();
      process();
    });
  };

  return (_group: Group, onFinish: () => void) => {
    queue.push([_group, onFinish]);
    if (isProcessing) return;
    process();
  };
})();
