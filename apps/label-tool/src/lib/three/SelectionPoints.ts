import type {
  <PERSON><PERSON>erG<PERSON><PERSON>,
  Mesh,
  MeshBasicMaterial,
  NormalBufferAttributes,
  OrthographicCamera,
  PerspectiveCamera,
  Points,
} from 'three';
import { <PERSON><PERSON><PERSON>Attribute, Frustum, Matrix4, Object3D, Quaternion, Vector3 } from 'three';

import { Cuboid } from '@/utils/annos';

const _frustum = new Frustum();
const _point = new Vector3();

const _tmpPoint = new Vector3();

const _vecNear = new Vector3();
const _vecTopLeft = new Vector3();
const _vecTopRight = new Vector3();
const _vecDownRight = new Vector3();
const _vecDownLeft = new Vector3();

const _vecFarTopLeft = new Vector3();
const _vecFarTopRight = new Vector3();
const _vecFarDownRight = new Vector3();
const _vecFarDownLeft = new Vector3();

const _vectemp1 = new Vector3();
const _vectemp2 = new Vector3();
const _vectemp3 = new Vector3();

const _startMatrix4 = new Matrix4();
const _endMatrix4 = new Matrix4();
const _quaternion = new Quaternion();

/**
 * This is a class to check whether objects are in a selection area in 3D space
 */
class SelectionPoints {
  camera: PerspectiveCamera | OrthographicCamera | null = null;
  target: Object3D | null = null;
  collection: number[] = [];
  selection: { [uuid: string]: number[] } = {};
  deep: number = Number.MAX_VALUE;

  public updateCameraAndPointCloud(camera: PerspectiveCamera | OrthographicCamera, target: Object3D) {
    this.camera = camera;
    this.target = target;
  }

  private canSelect() {
    if (!this.camera || !this.target) {
      console.error('THREE.SelectionPoints: camera or target is null');
      return false;
    }
    return true;
  }

  /**
   * 选择矩形区域内的点
   * @param startPoint 矩形开始点
   * @param endPoint 矩形结束点
   * @returns
   */
  public selectFromRectangle(startPoint: Vector3, endPoint: Vector3) {
    if (!this.canSelect()) return [];
    this.collection = [];

    // 如果开始和结束点之间差距过小，不足以画一个矩形，就直接返回了
    if (
      Math.abs(startPoint.y - endPoint.y) <= Number.EPSILON ||
      Math.abs(startPoint.x - endPoint.x) <= Number.EPSILON
    ) {
      return this.collection;
    }

    this.updateFrustum(startPoint, endPoint);
    this.searchChildInFrustum(_frustum, this.target!);

    return this.collection;
  }

  public selectCubeCritical(cuboid: Mesh<BufferGeometry<NormalBufferAttributes>, MeshBasicMaterial>) {
    if (!this.canSelect()) return [];
    const { position, quaternion, scale } = cuboid;

    // 这样创建的视锥 planes 依次代表左平面、右平面、上平面、下平面、近平面、远平面。
    // (世界坐标系下）假设你位于原点，面向Z轴的负方向（这通常被视为前方），上方是Y轴的正方向，那么：
    // 左平面：在你的左边，即X轴的负方向。
    // 右平面：在你的右边，即X轴的正方向。
    // 上平面：在你的上方，即Y轴的正方向。
    // 下平面：在你的下方，即Y轴的负方向。
    // 近平面：在你的前方，即Z轴的负方向。
    // 远平面：在你的后方，即Z轴的正方向。
    // 得到的 criticalPoint 也按此顺序排列。
    this.searchChildInVertexs(Cuboid.getVertexs(position, quaternion, scale));

    if (this.collection.length < 2) return [];
    const criticalPoint: number[] = [];
    _frustum.planes.forEach((plane, planeIndex) => {
      let min = Number.MAX_SAFE_INTEGER;
      let minIndex = -1;
      for (let i = 0; i < this.collection.length; i = i + 3) {
        _point.set(this.collection[i], this.collection[i + 1], this.collection[i + 2]);
        const distance = plane.distanceToPoint(_point);
        if (distance === 0) {
          criticalPoint[planeIndex * 3] = this.collection[i];
          criticalPoint[planeIndex * 3 + 1] = this.collection[i + 1];
          criticalPoint[planeIndex * 3 + 2] = this.collection[i + 2];
          return;
        } else if (distance < min) {
          min = distance;
          minIndex = i;
        }
      }
      criticalPoint[planeIndex * 3] = this.collection[minIndex];
      criticalPoint[planeIndex * 3 + 1] = this.collection[minIndex + 1];
      criticalPoint[planeIndex * 3 + 2] = this.collection[minIndex + 2];
    });
    return criticalPoint;
  }

  /**
   *
   * @param worldVertices 顶点坐标，适用范围：正交相机
   * 这些坐标的顺序必须是从视锥体的一个平面看过去，先是近平面的顺时针的四个点，再是远平面的顺时针的四个点，
   * 其中每个平面的开始点位置相同
   * @returns
   */
  private updateFrustumFromVertexs(worldVertices: Vector3[]) {
    const planes = _frustum.planes;
    planes[0].setFromCoplanarPoints(worldVertices[7], worldVertices[3], worldVertices[2]);
    planes[1].setFromCoplanarPoints(worldVertices[5], worldVertices[1], worldVertices[0]);
    planes[2].setFromCoplanarPoints(worldVertices[4], worldVertices[0], worldVertices[3]);
    planes[3].setFromCoplanarPoints(worldVertices[6], worldVertices[2], worldVertices[1]);
    planes[4].setFromCoplanarPoints(worldVertices[4], worldVertices[7], worldVertices[6]);
    planes[5].setFromCoplanarPoints(worldVertices[2], worldVertices[3], worldVertices[0]);
  }

  private updateFrustum(startPoint: Vector3, endPoint: Vector3) {
    if (!this.camera) return;
    this.camera.updateProjectionMatrix();
    this.camera.updateMatrixWorld();

    if ((this.camera as PerspectiveCamera).isPerspectiveCamera) {
      _tmpPoint.copy(startPoint);
      _tmpPoint.x = Math.min(startPoint.x, endPoint.x);
      _tmpPoint.y = Math.max(startPoint.y, endPoint.y);
      endPoint.x = Math.max(startPoint.x, endPoint.x);
      endPoint.y = Math.min(startPoint.y, endPoint.y);

      _vecNear.setFromMatrixPosition(this.camera.matrixWorld);
      _vecTopLeft.copy(_tmpPoint);
      _vecTopRight.set(endPoint.x, _tmpPoint.y, 0);
      _vecDownRight.copy(endPoint);
      _vecDownLeft.set(_tmpPoint.x, endPoint.y, 0);

      _vecTopLeft.unproject(this.camera);
      _vecTopRight.unproject(this.camera);
      _vecDownRight.unproject(this.camera);
      _vecDownLeft.unproject(this.camera);

      _vectemp1.copy(_vecTopLeft).sub(_vecNear);
      _vectemp2.copy(_vecTopRight).sub(_vecNear);
      _vectemp3.copy(_vecDownRight).sub(_vecNear);
      _vectemp1.normalize();
      _vectemp2.normalize();
      _vectemp3.normalize();

      _vectemp1.multiplyScalar(this.deep);
      _vectemp2.multiplyScalar(this.deep);
      _vectemp3.multiplyScalar(this.deep);
      _vectemp1.add(_vecNear);
      _vectemp2.add(_vecNear);
      _vectemp3.add(_vecNear);

      const planes = _frustum.planes;

      planes[0].setFromCoplanarPoints(_vecNear, _vecTopLeft, _vecTopRight);
      planes[1].setFromCoplanarPoints(_vecNear, _vecTopRight, _vecDownRight);
      planes[2].setFromCoplanarPoints(_vecDownRight, _vecDownLeft, _vecNear);
      planes[3].setFromCoplanarPoints(_vecDownLeft, _vecTopLeft, _vecNear);
      planes[4].setFromCoplanarPoints(_vecTopRight, _vecDownRight, _vecDownLeft);
      planes[5].setFromCoplanarPoints(_vectemp3, _vectemp2, _vectemp1);
      planes[5].normal.multiplyScalar(-1);
    } else if ((this.camera as OrthographicCamera).isOrthographicCamera) {
      const left = Math.min(startPoint.x, endPoint.x);
      const top = Math.max(startPoint.y, endPoint.y);
      const right = Math.max(startPoint.x, endPoint.x);
      const down = Math.min(startPoint.y, endPoint.y);

      // z轴正向，视椎体近相机侧
      _vecTopLeft.set(left, top, -1);
      _vecTopRight.set(right, top, -1);
      _vecDownRight.set(right, down, -1);
      _vecDownLeft.set(left, down, -1);

      // z轴负向，视椎体远相机侧
      _vecFarTopLeft.set(left, top, 1);
      _vecFarTopRight.set(right, top, 1);
      _vecFarDownRight.set(right, down, 1);
      _vecFarDownLeft.set(left, down, 1);

      _vecTopLeft.unproject(this.camera);
      _vecTopRight.unproject(this.camera);
      _vecDownRight.unproject(this.camera);
      _vecDownLeft.unproject(this.camera);

      _vecFarTopLeft.unproject(this.camera);
      _vecFarTopRight.unproject(this.camera);
      _vecFarDownRight.unproject(this.camera);
      _vecFarDownLeft.unproject(this.camera);

      this.updateFrustumFromVertexs([
        _vecTopLeft,
        _vecTopRight,
        _vecDownRight,
        _vecDownLeft,
        _vecFarTopLeft,
        _vecFarTopRight,
        _vecFarDownRight,
        _vecFarDownLeft,
      ]);
    } else {
      console.error('THREE.SelectionPoints: Unsupported camera type.');
    }
  }

  private searchChildInFrustum(frustum: Frustum, object: Object3D) {
    if ((object as any).isPoints && object.visible) {
      const positions = (object as Points).geometry.attributes.position.array;
      const selectArray = new Float32Array(positions.length / 3).fill(0);
      for (let i = 0; i < positions.length; i += 3) {
        _point.set(positions[i], positions[i + 1], positions[i + 2]);
        if (frustum.containsPoint(_point)) {
          this.collection.push(positions[i], positions[i + 1], positions[i + 2]);
          selectArray[i / 3] = 1;
        }
      }
      (object as Points).geometry.setAttribute('selectObject', new BufferAttribute(selectArray, 1));
      (object as Points).geometry.attributes.selectObject.needsUpdate = true;
    }
    if (object.children.length > 0) {
      for (let i = 0; i < object.children.length; i++) {
        this.searchChildInFrustum(frustum, object.children[i]);
      }
    }
  }

  /**
   * 得到在8个顶点坐标内的所有子元素
   * @param worldVertices 8 个顶点坐标
   * @returns
   */
  public searchChildInVertexs(worldVertices: Vector3[]) {
    if (!this.canSelect()) return [];
    this.collection = [];
    this.updateFrustumFromVertexs(worldVertices);
    this.searchChildInFrustum(_frustum, this.target!);
    return this.collection;
  }

  public removeSelection(object: Object3D) {
    if ((object as any).isPoints && object.visible) {
      (object as Points).geometry.deleteAttribute('selectObject');
    }
    if (object.children.length > 0) {
      for (let i = 0; i < object.children.length; i++) {
        this.removeSelection(object.children[i]);
      }
    }
  }

  /**
   * 得到以 startPoint 和 endPoint 为中心线的视锥体内的所有子元素
   * @param startPoint 中心线起点
   * @param endPoint 中心线终点
   * @param len 中心线到与中心线平行的视锥体边的距离
   * @param endScale 终点位置平面的长度扩大倍数
   * @returns 视锥体中的点集合
   */
  public selectFromTwoPoint(startPoint: Vector3, endPoint: Vector3, len: number = 0.1, endScale = 1) {
    if (!this.canSelect()) return [];

    _quaternion.setFromUnitVectors(new Vector3(1, 0, 0), new Vector3().copy(endPoint).sub(startPoint).normalize());
    _startMatrix4.compose(startPoint, _quaternion, new Vector3(1, 1, 1));

    // 结束点的范围可以通过传入的 endScale 控制，形成一个四角锥；默认值为 1 时，形成的是个长方体。
    _endMatrix4.compose(endPoint, _quaternion, new Vector3(1 * endScale, 1 * endScale, 1 * endScale));
    const points = [
      [0, len, len],
      [0, len, -len],
      [0, -len, -len],
      [0, -len, len],
    ];
    const vertexs = points
      .map((point) => {
        const vector = new Vector3(...point);
        return vector.applyMatrix4(_endMatrix4);
      })
      .concat(
        points.map((point) => {
          const vector = new Vector3(...point);
          return vector.applyMatrix4(_startMatrix4);
        })
      );

    return this.searchChildInVertexs(vertexs);
  }

  /**
   * 在starPoint 和 endPoint 形成的视锥体内，找到距离起点最近的点
   * @param startPoint
   * @param endPoint
   * @param len
   * @returns
   */
  public getMinDistancePoint(startPoint: Vector3, endPoint: Vector3, len: number = 0.1) {
    if (!this.canSelect()) return;
    // 此处重试 3 次，每次重试都是之前的 2 倍
    for (let i = 0; i < 3; i++) {
      this.selectFromTwoPoint(startPoint, endPoint, len, Math.pow(2, i));
      if (this.collection.length >= 3) break;
    }
    if (this.collection.length < 3) return;
    let min = Number.MAX_SAFE_INTEGER,
      hasChange = false;
    for (let i = 0; i < this.collection.length; i += 3) {
      const distance = startPoint.distanceToSquared(
        _point.set(this.collection[i], this.collection[i + 1], this.collection[i + 2])
      );
      if (distance < min) {
        !hasChange && (hasChange = true);
        min = distance;
      }
    }

    if (hasChange) {
      // 此处在角度非常小时，cos的值非常接近1，所以此处就直接按照1来处理了
      return _point.set(Math.sqrt(min), 0, 0).applyMatrix4(_startMatrix4).toArray();
    }
  }
}

export const selectionPoints = new SelectionPoints();
