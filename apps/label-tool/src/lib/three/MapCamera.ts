import { Matrix3, Matrix4, <PERSON><PERSON><PERSON><PERSON><PERSON>, ShaderMaterial, Vector3 } from 'three';
import { Pass } from 'three/examples/jsm/postprocessing/Pass';

import { BaseCamera, ZOOM_FOR_DISTORTION_FACTOR } from '@/utils/camera';

/**
 * 使用相机视野（图片尺寸）生成 NDC 映射矩阵
 * @param left 默认为0
 * @param right 图片宽度
 * @param bottom 图片高度
 * @param top 默认为0
 * @param near 近平面
 * @param far 远平面
 * @returns
 */
function makeNdcMatrix(left: number, right: number, bottom: number, top: number, near: number, far: number) {
  const tx = -(right + left) / (right - left);
  const ty = -(top + bottom) / (top - bottom);
  const tz = -(far + near) / (far - near);

  const ndc = new Matrix4();
  // prettier-ignore
  ndc.set(
      2 / (right - left), 0, 0, tx,
      0, 2 / (top - bottom), 0, ty,
      0, 0, -2 / (far - near), tz,
      0, 0, 0, 1,
    );
  return ndc;
}

/**
 * 使用标准相机内参生成透视矩阵
 * @param s
 * @param alpha fx
 * @param beta fy
 * @param x0 cx
 * @param y0 cy
 * @param near 近平面
 * @param far 远平面
 * @returns 透视矩阵
 */
function makePerspectiveMatrix(
  s: number,
  alpha: number,
  beta: number,
  x0: number,
  y0: number,
  near: number,
  far: number
) {
  const A = near + far;
  const B = near * far;

  const perspective = new Matrix4();
  // prettier-ignore
  perspective.set(
      alpha, s, -x0, 0,
      0, beta, -y0, 0,
      0, 0, A, B,
      0, 0, -1, 0,
    );
  return perspective;
}

export class MapCamera extends PerspectiveCamera {
  /** 相机内参 */
  public intrinsic: number[];
  /** 图片源文件宽度 */
  public imageWidth: number;
  /** 图片源文件高度 */
  public imageHeight: number;
  /** 相机视野偏移，用于匹配图片拖拽交互 */
  public imageOffset: Vector3;
  /** 相机主点坐标与图片中心点的差，主要用于计算缩放下的内参矩阵  */
  private defaultPrincipalOffset: [number, number];
  /** 处理带畸变的相机模型时，初始化的缩放倍率  */
  private zoomForDistortionFactor: number;

  constructor(
    intrinsic: number[],
    extrinsic: Matrix4,
    imageWidth: number,
    imageHeight: number,
    near: number,
    far: number
  ) {
    super(45, 1, near, far);
    this.scale.set(1, 1, -1);

    this.setExtrinsic(extrinsic);

    this.intrinsic = intrinsic;
    this.imageWidth = imageWidth;
    this.imageHeight = imageHeight;
    this.zoom = 1;
    this.zoomForDistortionFactor = 1;
    this.imageOffset = new Vector3(0, 0, 0);
    this.defaultPrincipalOffset = [0, 0];

    this.updateProjectionMatrix();
  }

  setExtrinsicMatrix(R: Matrix3, T: Vector3) {
    const rotationMatrix4 = new Matrix4().setFromMatrix3(R);
    rotationMatrix4.setPosition(T);
    rotationMatrix4.invert();
    this.quaternion.setFromRotationMatrix(rotationMatrix4);
    this.position.setFromMatrixPosition(rotationMatrix4);
  }

  setIntrinsic(intrinsic?: number[]) {
    if (intrinsic && intrinsic.length >= 4) {
      this.intrinsic = intrinsic;
      this.defaultPrincipalOffset = [this.imageWidth / 2 - intrinsic[2], this.imageHeight / 2 - intrinsic[3]];
    }
  }

  setExtrinsic(mat: Matrix4) {
    this.quaternion.setFromRotationMatrix(mat);
    this.position.setFromMatrixPosition(mat);
  }

  updateFromParams(ext: Matrix4, model: BaseCamera) {
    this.imageWidth = model.width;
    this.imageHeight = model.height;

    this.setExtrinsic(ext);
    this.setIntrinsic(model.intrinsic);

    // 判断相机模型是否有畸变，如有畸变，则应用初始化的缩放倍率，加大相机拍摄范围；否则初始化时无缩放，即为1
    this.zoomForDistortionFactor = model.distortion ? ZOOM_FOR_DISTORTION_FACTOR : 1;
    this.zoom = this.zoomForDistortionFactor;
    this.updateProjectionMatrix();
  }

  updateProjectionMatrix() {
    if (!Array.isArray(this.intrinsic) || this.intrinsic.length < 4) {
      return;
    }
    // s represents the skew coefficient between the x and the y axis, and is often 0
    const [fx, fy, cx, cy, s = 0] = this.intrinsic;

    const persp = makePerspectiveMatrix(
      s,
      fx * this.zoom,
      fy * this.zoom,
      cx + this.defaultPrincipalOffset[0] * (1 - this.zoom),
      cy + this.defaultPrincipalOffset[1] * (1 - this.zoom),
      this.near,
      this.far
    );

    const left = this.imageOffset.x;
    const right = this.imageOffset.x + this.imageWidth;
    const top = -this.imageOffset.y;
    const bottom = -this.imageOffset.y + this.imageHeight;

    const ndc = makeNdcMatrix(left, right, bottom, top, this.near, this.far);
    const projection = ndc.multiply(persp);

    this.projectionMatrix.copy(projection);
    this.projectionMatrixInverse.copy(this.projectionMatrix).invert();
  }

  focusOn(data?: { zoom?: number; offset?: Vector3 }, shaderPass?: Pass) {
    const { zoom = 1, offset } = data || {};

    if (shaderPass && 'material' in shaderPass && shaderPass.material instanceof ShaderMaterial) {
      // 有畸变：通过 后处理 响应平移与缩放操作
      shaderPass.material.uniforms.uZoom.value = zoom;
      offset
        ? (shaderPass.material.uniforms.uImageOffset.value as Vector3).copy(offset)
        : shaderPass.material.uniforms.uImageOffset.value.set(0, 0, 0);
    } else {
      // 无畸变：通过 改变相机投影矩阵 响应平移与缩放操作
      this.zoom = zoom;
      if (offset) {
        this.imageOffset.x = offset.x * this.imageWidth * this.zoom;
        this.imageOffset.y = offset.y * this.imageHeight * this.zoom;
      } else {
        this.imageOffset.set(0, 0, 0);
      }
      this.updateProjectionMatrix();
    }
  }
}
