import {
  <PERSON>ufferGeometry,
  FileLoader,
  Float32BufferAttribute,
  Int32BufferAttribute,
  Loader,
  LoaderUtils,
  LoadingManager,
  Matrix4,
  Points,
  ShaderMaterial,
  Vector3,
} from 'three';

import { POINTS_COLOR_BASE_CONTRAST, POINTS_COLOR_WHITE_CONTRAST, POINTS_COLOR_WHITE_THROHOLD } from '@/config/render';
import { applyContrast, sRGBToLinear } from '@/utils/render';

const formatNumber = (num: number) => {
  return Number.isNaN(num) ? 0 : num;
};

/**
 * 大点云采用分批加载策略，当前值为单次加载点数上限
 */
class PCDLoader extends Loader {
  static POINTS_MAX = 200_0000;

  private littleEndian: boolean;

  /** 矫正矩阵，在读取阶段将点云渲染效果矫正至水平且居中 */
  private matrix?: Matrix4;

  constructor(manager?: LoadingManager) {
    super(manager);
    this.littleEndian = true;
  }

  load(
    url: string,
    onLoad: (obj: Points<BufferGeometry, ShaderMaterial>, isDone: boolean) => void,
    onProgress: (request: ProgressEvent) => void,
    onError: any
  ) {
    const scope = this;

    const loader = new FileLoader(scope.manager);
    loader.setPath(scope.path);
    loader.setResponseType('arraybuffer');
    loader.setRequestHeader(scope.requestHeader);
    loader.setWithCredentials(scope.withCredentials);

    loader.load(
      url,
      (data) => {
        // 异步调用以确保优先更新全局状态
        setTimeout(() => {
          try {
            scope.parse(data as ArrayBuffer, onLoad);
          } catch (e) {
            if (onError) {
              onError(e);
            } else {
              console.error(e);
            }

            scope.manager.itemError(url);
          }
        }, 0);
      },
      onProgress,
      onError
    );
  }

  /**
   * 根据传入的 PointsMaxSize 计算合适的分块大小，以减少分块后碎片的产生
   * @param total 点云文件的总点数
   * @param maxSize 一个 Points 包含的最大点数基准线
   * @returns 调整后的 Points 最大点数，不超过基准线的 1.1 倍
   */
  static getFitMaxSize(total: number, maxSize: number): number {
    const extra = total % maxSize;

    if (total > maxSize && extra > 0) {
      // 计算分块后符合 maxSize 的块数
      const n = Math.floor(total / maxSize);

      if (extra <= n * maxSize * 0.1) {
        return maxSize + Math.ceil(extra / n);
      }
    }

    return maxSize;
  }

  parse(data: ArrayBuffer, onParse: (obj: Points<BufferGeometry, ShaderMaterial>, isDone: boolean) => void) {
    // from https://gitlab.com/taketwo/three-pcd-loader/blob/master/decompress-lzf.js

    // 取文件的前 1k 数据解析成头部文本信息
    const headerText = LoaderUtils.decodeText(new Uint8Array(data.slice(0, 1024)));

    // parse header (always ascii format)

    const PCDheader = this.parseHeader(headerText);
    const maxPoints = PCDLoader.getFitMaxSize(PCDheader.points, PCDLoader.POINTS_MAX);

    let endIndex = 0;
    // parse data
    for (let i = 0; i < PCDheader.points; i += maxPoints) {
      endIndex = Math.min(PCDheader.points, i + maxPoints);
      onParse(this.generatePoints(data, PCDheader, i, endIndex), endIndex === PCDheader.points);
    }
  }

  parseBuffer(data: ArrayBuffer) {
    // 取文件的前 1k 数据解析成头部文本信息
    const headerText = LoaderUtils.decodeText(new Uint8Array(data.slice(0, 1024)));

    // parse header (always ascii format)

    const PCDheader = this.parseHeader(headerText);
    const maxPoints = PCDLoader.getFitMaxSize(PCDheader.points, PCDLoader.POINTS_MAX);

    const result: Array<Points<BufferGeometry, ShaderMaterial>> = [];

    let endIndex = 0;
    // parse data
    for (let i = 0; i < PCDheader.points; i += maxPoints) {
      endIndex = Math.min(PCDheader.points, i + maxPoints);
      result.push(this.generatePoints(data, PCDheader, i, endIndex));
    }
    return result;
  }

  parseHeader(data: string) {
    const PCDheader: Record<string, any> = {};
    const result1 = data.search(/[\r\n]DATA\s(\S*)\s/i);
    const result2 = /[\r\n]DATA\s(\S*)\s/i.exec(data.slice(result1 - 1)) as any;

    PCDheader.data = result2[1];
    PCDheader.headerLen = result2[0].length + result1;
    PCDheader.str = data.slice(0, PCDheader.headerLen);

    // remove comments

    PCDheader.str = PCDheader.str.replace(/#.*/gi, '');

    // parse

    PCDheader.version = /VERSION (.*)/i.exec(PCDheader.str);
    PCDheader.fields = /FIELDS (.*)/i.exec(PCDheader.str);
    PCDheader.size = /SIZE (.*)/i.exec(PCDheader.str);
    PCDheader.type = /TYPE (.*)/i.exec(PCDheader.str);
    PCDheader.count = /COUNT (.*)/i.exec(PCDheader.str);
    PCDheader.width = /WIDTH (.*)/i.exec(PCDheader.str);
    PCDheader.height = /HEIGHT (.*)/i.exec(PCDheader.str);
    PCDheader.points = /POINTS (.*)/i.exec(PCDheader.str);

    // evaluate

    if (PCDheader.version !== null) PCDheader.version = parseFloat(PCDheader.version[1]);

    PCDheader.fields = PCDheader.fields !== null ? PCDheader.fields[1].split(' ') : [];

    if (PCDheader.type !== null) PCDheader.type = PCDheader.type[1].split(' ');

    if (PCDheader.width !== null) PCDheader.width = parseInt(PCDheader.width[1]);

    if (PCDheader.height !== null) PCDheader.height = parseInt(PCDheader.height[1]);

    if (PCDheader.points !== null) PCDheader.points = parseInt(PCDheader.points[1], 10);

    if (PCDheader.points === null) PCDheader.points = PCDheader.width * PCDheader.height;

    if (PCDheader.size !== null) {
      PCDheader.size = PCDheader.size[1].split(' ').map(function (x: string) {
        return parseInt(x, 10);
      });
    }

    if (PCDheader.count !== null) {
      PCDheader.count = PCDheader.count[1].split(' ').map(function (x: string) {
        return parseInt(x, 10);
      });
    } else {
      PCDheader.count = [];

      for (let i = 0, l = PCDheader.fields.length; i < l; i++) {
        PCDheader.count.push(1);
      }
    }

    PCDheader.offset = {};
    PCDheader.fieldType = {};

    let sizeSum = 0;

    for (let i = 0, l = PCDheader.fields.length; i < l; i++) {
      PCDheader.fieldType[PCDheader.fields[i]] = `${PCDheader.type[i]}${PCDheader.size[i] * 8}`;

      if (PCDheader.data === 'ascii') {
        PCDheader.offset[PCDheader.fields[i]] = i;
      } else {
        PCDheader.offset[PCDheader.fields[i]] = sizeSum;
        sizeSum += PCDheader.size[i] * PCDheader.count[i];
      }
    }

    // for binary only

    PCDheader.rowSize = sizeSum;

    return PCDheader;
  }

  generatePoints(data: ArrayBuffer, PCDheader: Record<string, any>, startIndex: number, endIndex: number) {
    const position: number[] = [];
    const normal = [];
    const color = [];
    const intensity = [];
    const label = [];

    const _vec = new Vector3();

    // ascii 未处理分批读取 —— 暂时不考虑 ascii

    // if (PCDheader.data === 'ascii') {
    //   const offset = PCDheader.offset;
    //   const pcdData = textData.slice(PCDheader.headerLen);
    //   const lines = pcdData.split('\n');

    //   for (let i = 0, l = lines.length; i < l; i++) {
    //     if (lines[i] === '') continue;

    //     const line = lines[i].split(' ');

    //     if (offset.x !== undefined) {
    //       position.push(parseFloat(line[offset.x]), parseFloat(line[offset.y]), parseFloat(line[offset.z]));
    //     }

    //     if (offset.rgb !== undefined) {
    //       const rgb_field_index = PCDheader.fields.findIndex((field: string) => field === 'rgb');
    //       const rgb_type = PCDheader.type[rgb_field_index];

    //       const float = parseFloat(line[offset.rgb]);
    //       let rgb = float;

    //       if (rgb_type === 'F') {
    //         // treat float values as int
    //         // https://github.com/daavoo/pyntcloud/pull/204/commits/7b4205e64d5ed09abe708b2e91b615690c24d518
    //         const farr = new Float32Array(1);
    //         farr[0] = float;
    //         rgb = new Int32Array(farr.buffer)[0];
    //       }

    //       const r = (rgb >> 16) & 0x0000ff;
    //       const g = (rgb >> 8) & 0x0000ff;
    //       const b = (rgb >> 0) & 0x0000ff;
    //       color.push(r / 255, g / 255, b / 255);
    //     }

    //     if (offset.normal_x !== undefined) {
    //       normal.push(parseFloat(line[offset.normal_x]));
    //       normal.push(parseFloat(line[offset.normal_y]));
    //       normal.push(parseFloat(line[offset.normal_z]));
    //     }

    //     if (offset.intensity !== undefined) {
    //       intensity.push(parseFloat(line[offset.intensity]));
    //     }

    //     if (offset.label !== undefined) {
    //       label.push(parseInt(line[offset.label]));
    //     }
    //   }
    // }

    // binary-compressed 未处理分批读取

    // normally data in PCD files are organized as array of structures: XYZRGBXYZRGB
    // binary compressed PCD files organize their data as structure of arrays: XXYYZZRGBRGB
    // that requires a totally different parsing approach compared to non-compressed data

    if (PCDheader.data === 'binary_compressed') {
      const sizes = new Uint32Array(data.slice(PCDheader.headerLen, PCDheader.headerLen + 8));
      const compressedSize = sizes[0];
      const decompressedSize = sizes[1];
      const decompressed = this.decompressLZF(
        new Uint8Array(data, PCDheader.headerLen + 8, compressedSize),
        decompressedSize
      );
      const dataview = new DataView(decompressed.buffer);

      const offset = PCDheader.offset;

      for (let i = 0; i < PCDheader.points; i++) {
        if (offset.x !== undefined) {
          const xIndex = PCDheader.fields.indexOf('x');
          const yIndex = PCDheader.fields.indexOf('y');
          const zIndex = PCDheader.fields.indexOf('z');
          position.push(
            dataview.getFloat32(PCDheader.points * offset.x + PCDheader.size[xIndex] * i, this.littleEndian),
            dataview.getFloat32(PCDheader.points * offset.y + PCDheader.size[yIndex] * i, this.littleEndian),
            dataview.getFloat32(PCDheader.points * offset.z + PCDheader.size[zIndex] * i, this.littleEndian)
          );
        }

        if (offset.rgb !== undefined) {
          const rgbIndex = PCDheader.fields.indexOf('rgb');
          color.push(dataview.getUint8(PCDheader.points * offset.rgb + PCDheader.size[rgbIndex] * i + 2) / 255.0);
          color.push(dataview.getUint8(PCDheader.points * offset.rgb + PCDheader.size[rgbIndex] * i + 1) / 255.0);
          color.push(dataview.getUint8(PCDheader.points * offset.rgb + PCDheader.size[rgbIndex] * i + 0) / 255.0);
        }

        if (offset.normal_x !== undefined) {
          const xIndex = PCDheader.fields.indexOf('normal_x');
          const yIndex = PCDheader.fields.indexOf('normal_y');
          const zIndex = PCDheader.fields.indexOf('normal_z');
          normal.push(
            dataview.getFloat32(PCDheader.points * offset.normal_x + PCDheader.size[xIndex] * i, this.littleEndian)
          );
          normal.push(
            dataview.getFloat32(PCDheader.points * offset.normal_y + PCDheader.size[yIndex] * i, this.littleEndian)
          );
          normal.push(
            dataview.getFloat32(PCDheader.points * offset.normal_z + PCDheader.size[zIndex] * i, this.littleEndian)
          );
        }

        if (offset.intensity !== undefined) {
          const intensityIndex = PCDheader.fields.indexOf('intensity');
          intensity.push(
            dataview.getFloat32(
              PCDheader.points * offset.intensity + PCDheader.size[intensityIndex] * i,
              this.littleEndian
            )
          );
        }

        if (offset.label !== undefined) {
          const labelIndex = PCDheader.fields.indexOf('label');
          label.push(
            dataview.getInt32(PCDheader.points * offset.label + PCDheader.size[labelIndex] * i, this.littleEndian)
          );
        }
      }
    }

    // binary - 处理为分批读取
    if (PCDheader.data === 'binary') {
      const dataview = new DataView(data, PCDheader.headerLen);
      const { offset, fieldType } = PCDheader;

      let x = 0,
        y = 0,
        z = 0;

      for (let i = startIndex, row = startIndex * PCDheader.rowSize; i < endIndex; i++, row += PCDheader.rowSize) {
        if (offset.x === undefined) continue;

        x = dataview.getFloat32(row + offset.x, this.littleEndian);
        y = dataview.getFloat32(row + offset.y, this.littleEndian);
        z = dataview.getFloat32(row + offset.z, this.littleEndian);

        // 点数据中任意维度为 NaN，则过滤点
        if (Number.isNaN(x) || Number.isNaN(y) || Number.isNaN(z)) continue;

        _vec.set(x, y, z);

        if (this.matrix) {
          _vec.applyMatrix4(this.matrix);
        }

        position.push(_vec.x, _vec.y, _vec.z);

        if (offset.rgb !== undefined) {
          const r = sRGBToLinear(formatNumber(dataview.getUint8(row + offset.rgb + 2)) / 255.0);
          const g = sRGBToLinear(formatNumber(dataview.getUint8(row + offset.rgb + 1)) / 255.0);
          const b = sRGBToLinear(formatNumber(dataview.getUint8(row + offset.rgb + 0)) / 255.0);

          const data = applyContrast(
            [r, g, b],
            POINTS_COLOR_WHITE_THROHOLD,
            POINTS_COLOR_WHITE_CONTRAST,
            POINTS_COLOR_BASE_CONTRAST
          );

          color.push(...data);
        }
        if (offset.normal_x !== undefined) {
          normal.push(dataview.getFloat32(row + offset.normal_x, this.littleEndian));
          normal.push(dataview.getFloat32(row + offset.normal_y, this.littleEndian));
          normal.push(dataview.getFloat32(row + offset.normal_z, this.littleEndian));
        }

        if (offset.intensity !== undefined) {
          switch (fieldType.intensity) {
            case 'F32':
              intensity.push(formatNumber(dataview.getFloat32(row + offset.intensity, this.littleEndian)));
              break;
            case 'U8':
              intensity.push(formatNumber(dataview.getUint8(row + offset.intensity)));
              break;
            case 'U16':
              intensity.push(formatNumber(dataview.getUint16(row + offset.intensity)));
              break;
            case 'U32':
              intensity.push(formatNumber(dataview.getUint32(row + offset.intensity)));
              break;
            default:
              break;
          }
        }

        if (offset.label !== undefined) {
          label.push(dataview.getInt32(row + offset.label, this.littleEndian));
        }
      }
    }

    // build geometry

    const geometry = new BufferGeometry();

    if (position.length > 0) geometry.setAttribute('position', new Float32BufferAttribute(position, 3));
    if (normal.length > 0) geometry.setAttribute('normal', new Float32BufferAttribute(normal, 3));
    if (color.length > 0) geometry.setAttribute('color', new Float32BufferAttribute(color, 3));
    if (intensity.length > 0) geometry.setAttribute('intensity', new Float32BufferAttribute(intensity, 1));
    if (label.length > 0) geometry.setAttribute('label', new Int32BufferAttribute(label, 1));

    geometry.computeBoundingSphere();

    // 自定义材料，支持调整点云的点大小和z轴可视范围
    const material = new ShaderMaterial({
      vertexColors: true,
      uniforms: {
        size: {
          value: 1.0,
        },
        minZ: {
          value: -999.9,
        },
        maxZ: {
          value: 999.9,
        },
        alphaValue: { value: 1.0 },
      },
      vertexShader: `
        uniform float size;
        uniform float alphaValue;
        varying vec4 vColor;
        varying vec3 vPosition;
        attribute vec3 sseColor;
        attribute float hidePoint;
        attribute float selectPoint;
        attribute float selectObject;
        attribute float categoryID;

        void clipPoint() {
          gl_Position = vec4(100.0, 100.0, 100.0, 0.0);
        }

        void main() {
          vPosition = position;
          if(hidePoint == 1.0) {
            clipPoint();
            return;
          }
          if(categoryID == 1.0) {
            float opacity = selectPoint == 1.0 ? 0.1 : 0.8;
            vColor = vec4(sseColor, opacity);
          } else {
            if (selectObject == 1.0) {
              vColor = vec4(1.0, 0.831, 0.0, alphaValue);
            } else {
              vColor = vec4(color, alphaValue);
            }
          }
          gl_PointSize = size;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform float minZ;
        uniform float maxZ;

        varying vec4 vColor;
        varying vec3 vPosition;

        void main() {
          if (vPosition.z < minZ || vPosition.z > maxZ) {
            discard;
          } else {
            gl_FragColor = vColor;
          }

          #include <colorspace_fragment>
        }
      `,
    });
    return new Points(geometry, material);
  }

  decompressLZF(inData: Uint8Array, outLength: number) {
    const inLength = inData.length;
    const outData = new Uint8Array(outLength);
    let inPtr = 0;
    let outPtr = 0;
    let ctrl;
    let len;
    let ref;
    do {
      ctrl = inData[inPtr++];
      if (ctrl < 1 << 5) {
        ctrl++;
        if (outPtr + ctrl > outLength) throw new Error('Output buffer is not large enough');
        if (inPtr + ctrl > inLength) throw new Error('Invalid compressed data');
        do {
          outData[outPtr++] = inData[inPtr++];
        } while (--ctrl);
      } else {
        len = ctrl >> 5;
        ref = outPtr - ((ctrl & 0x1f) << 8) - 1;
        if (inPtr >= inLength) throw new Error('Invalid compressed data');
        if (len === 7) {
          len += inData[inPtr++];
          if (inPtr >= inLength) throw new Error('Invalid compressed data');
        }

        ref -= inData[inPtr++];
        if (outPtr + len + 2 > outLength) throw new Error('Output buffer is not large enough');
        if (ref < 0) throw new Error('Invalid compressed data');
        if (ref >= outPtr) throw new Error('Invalid compressed data');
        do {
          outData[outPtr++] = outData[ref++];
        } while (--len + 2);
      }
    } while (inPtr < inLength);

    return outData;
  }

  /**
   * 设置矫正矩阵，将点云数据还原至与 xy 平面平行 且 中心点坐标靠近原点
   * @param data
   */
  setCorrectMatrix(mat?: Matrix4) {
    if (mat) {
      this.matrix = (this.matrix ?? new Matrix4()).fromArray(mat.elements);
    } else {
      this.matrix = undefined;
    }
  }
}

export { PCDLoader };
