import { EdgesGeometry, InstancedBufferGeometry, Line, LineSegments, Matrix4, Mesh, WireframeGeometry } from 'three';

export declare class LineSegmentsGeometry extends InstancedBufferGeometry {
  constructor();
  readonly isLineSegmentsGeometry: true;

  applyMatrix4(matrix: Matrix4): this;
  computeBoundingBox(): void;
  computeBoundingSphere(): void;
  fromEdgesGeometry(geometry: EdgesGeometry): this;
  fromLineSegments(lineSegments: LineSegments): this;
  fromMesh(mesh: Mesh): this;
  fromWireframeGeometry(geometry: WireframeGeometry): this;
  setColors(array: number[] | Float32Array): this;
  // 重置位置
  setPositions(array: number[] | Float32Array): this;
}

export declare class LineGeometry extends LineSegmentsGeometry {
  constructor();
  readonly isLineGeometry: true;
  pointCount: number;

  fromLine(line: Line): this;
  // 更新位置
  updatePositions(array: number[] | Float32Array, start: number): this;
}
