import { Mesh } from 'three';

import { LineGeometry, LineSegmentsGeometry } from './LineGeometry.js';
import { LineMaterial } from './LineMaterial.js';

export declare class LineSegmentsMesh extends Mesh {
  geometry: LineSegmentsGeometry;
  material: LineMaterial;

  constructor(geometry?: LineSegmentsGeometry, material?: LineMaterial);
  readonly isLineSegmentsMesh: true;

  computeLineDistances(): this;
}

export declare class FatLine extends LineSegmentsMesh {
  geometry: LineGeometry;
  material: LineMaterial;

  constructor(geometry?: LineGeometry, material?: LineMaterial);
  readonly isFatLine: true;
}
