import {
  Box3,
  Float32<PERSON><PERSON>er<PERSON>ttribute,
  InstancedBufferGeometry,
  InstancedInterleavedBuffer,
  InterleavedBufferAttribute,
  Sphere,
  Vector3,
  WireframeGeometry,
} from 'three';

const _box = new Box3();
const _vector = new Vector3();

export class LineSegmentsGeometry extends InstancedBufferGeometry {
  constructor() {
    super();

    this.isLineSegmentsGeometry = true;

    this.type = 'LineSegmentsGeometry';

    const positions = [-1, 2, 0, 1, 2, 0, -1, 1, 0, 1, 1, 0, -1, 0, 0, 1, 0, 0, -1, -1, 0, 1, -1, 0];
    const uvs = [-1, 2, 1, 2, -1, 1, 1, 1, -1, -1, 1, -1, -1, -2, 1, -2];
    const index = [0, 2, 1, 2, 3, 1, 2, 4, 3, 4, 5, 3, 4, 6, 5, 6, 7, 5];

    this.setIndex(index);
    this.setAttribute('position', new Float32BufferAttribute(positions, 3));
    this.setAttribute('uv', new Float32BufferAttribute(uvs, 2));
  }

  applyMatrix4(matrix) {
    const start = this.attributes.instanceStart;
    const end = this.attributes.instanceEnd;

    if (start !== undefined) {
      start.applyMatrix4(matrix);

      end.applyMatrix4(matrix);

      start.needsUpdate = true;
    }

    if (this.boundingBox !== null) {
      this.computeBoundingBox();
    }

    if (this.boundingSphere !== null) {
      this.computeBoundingSphere();
    }

    return this;
  }

  setPositions(array) {
    let lineSegments;

    if (array instanceof Float32Array) {
      lineSegments = array;
    } else if (Array.isArray(array)) {
      lineSegments = new Float32Array(array);
    }

    const instanceBuffer = new InstancedInterleavedBuffer(lineSegments, 6, 1); // xyz, xyz（instanceStart, instanceEnd）

    this.setAttribute('instanceStart', new InterleavedBufferAttribute(instanceBuffer, 3, 0)); // xyz
    this.setAttribute('instanceEnd', new InterleavedBufferAttribute(instanceBuffer, 3, 3)); // xyz

    this.computeBoundingBox();
    this.computeBoundingSphere();

    return this;
  }

  setColors(array) {
    let colors;

    if (array instanceof Float32Array) {
      colors = array;
    } else if (Array.isArray(array)) {
      colors = new Float32Array(array);
    }

    const instanceColorBuffer = new InstancedInterleavedBuffer(colors, 6, 1); // rgb, rgb

    this.setAttribute('instanceColorStart', new InterleavedBufferAttribute(instanceColorBuffer, 3, 0)); // rgb
    this.setAttribute('instanceColorEnd', new InterleavedBufferAttribute(instanceColorBuffer, 3, 3)); // rgb

    return this;
  }

  fromWireframeGeometry(geometry) {
    this.setPositions(geometry.attributes.position.array);

    return this;
  }

  fromEdgesGeometry(geometry) {
    this.setPositions(geometry.attributes.position.array);

    return this;
  }

  fromMesh(mesh) {
    this.fromWireframeGeometry(new WireframeGeometry(mesh.geometry));

    // set colors, maybe

    return this;
  }

  fromLineSegments(lineSegments) {
    const geometry = lineSegments.geometry;

    this.setPositions(geometry.attributes.position.array); // assumes non-indexed

    // set colors, maybe

    return this;
  }

  // 限制处理有效的数据（对于线段，this.instanceCount 可能比 attribute.count 小）
  getUsingInstancePosition(attribute) {
    const n = Math.min(this.instanceCount, attribute.count); // this.instanceCount 可能为无限大
    const positions = new Float32Array(n * 3);
    const _vector = new Vector3();
    for (let i = 0; i < n; i++) {
      _vector.fromBufferAttribute(attribute, i).toArray(positions, i * 3);
    }
    return positions;
  }

  computeBoundingBox() {
    if (this.boundingBox === null) {
      this.boundingBox = new Box3();
    }

    const start = this.attributes.instanceStart;
    const end = this.attributes.instanceEnd;

    if (start !== undefined && end !== undefined) {
      this.boundingBox.setFromArray(this.getUsingInstancePosition(start));
      _box.setFromArray(this.getUsingInstancePosition(end));

      this.boundingBox.union(_box);
    }
  }

  computeBoundingSphere() {
    if (this.boundingSphere === null) {
      this.boundingSphere = new Sphere();
    }

    if (this.boundingBox === null) {
      this.computeBoundingBox();
    }

    const start = this.attributes.instanceStart;
    const end = this.attributes.instanceEnd;

    if (start !== undefined && end !== undefined) {
      const center = this.boundingSphere.center;

      this.boundingBox.getCenter(center);

      let maxRadiusSq = 0;

      for (let i = 0, il = Math.min(start.count, this.instanceCount); i < il; i++) {
        _vector.fromBufferAttribute(start, i);
        maxRadiusSq = Math.max(maxRadiusSq, center.distanceToSquared(_vector));

        _vector.fromBufferAttribute(end, i);
        maxRadiusSq = Math.max(maxRadiusSq, center.distanceToSquared(_vector));
      }

      this.boundingSphere.radius = Math.sqrt(maxRadiusSq);

      if (isNaN(this.boundingSphere.radius)) {
        console.error(
          'THREE.LineSegmentsGeometry.computeBoundingSphere(): Computed radius is NaN. The instanced position data is likely to have NaN values.',
          this
        );
      }
    }
  }

  toJSON() {
    // todo
  }

  applyMatrix(matrix) {
    console.warn('THREE.LineSegmentsGeometry: applyMatrix() has been renamed to applyMatrix4().');

    return this.applyMatrix4(matrix);
  }
}

export class LineGeometry extends LineSegmentsGeometry {
  constructor() {
    super();

    this.isLineGeometry = true;

    this.type = 'LineGeometry';

    // 因为无法区分 0 个点和 1 个点，所以此处维护一个线上点数
    this.pointCount = 0;
  }

  // 如果之前传入的 array 长度不够，才需要调用此处
  setPositions(array) {
    // converts [ x1, y1, z1,  x2, y2, z2, ... ] to pairs format

    const length = array.length - 3;
    const points = new Float32Array(2 * length);

    for (let i = 0; i < length; i += 3) {
      points[2 * i] = array[i];
      points[2 * i + 1] = array[i + 1];
      points[2 * i + 2] = array[i + 2];

      points[2 * i + 3] = array[i + 3];
      points[2 * i + 4] = array[i + 4];
      points[2 * i + 5] = array[i + 5];
    }

    // 非常关键，如果在重新 set 的时候没有这个，新加的线条就渲染不出来了
    this._maxInstanceCount = length / 3;
    super.setPositions(points);

    return this;
  }

  updatePositions(array, start) {
    const length = array.length;
    const points = new Float32Array(2 * length);

    for (let i = 0; i < length; i += 3) {
      points[2 * i] = array[i];
      points[2 * i + 1] = array[i + 1];
      points[2 * i + 2] = array[i + 2];

      points[2 * i + 3] = array[i];
      points[2 * i + 4] = array[i + 1];
      points[2 * i + 5] = array[i + 2];
    }

    const positions = this.attributes.instanceStart.array;

    for (let i = start === 0 ? 3 : 0, j = start === 0 ? 0 : 2 * start - 3; i < points.length; i++, j++) {
      positions[j] = points[i];
    }

    this.attributes.instanceStart.needsUpdate = true;
    this.attributes.instanceEnd.needsUpdate = true;

    this.computeBoundingBox();
    this.computeBoundingSphere();
    return this;
  }

  /**
   * 设置线段显示颜色的值，color 的长度跟 position 的长度一致的
   * @param {*} array
   * @returns
   */
  setColors(array) {
    // 此处修改一下，希望是每一个段是一个颜色，而不是渐变的颜色，所以最后的 pairs 应该是
    // [ r1, g1, b1, r1, g1, b1, r2 , g2, b2, r2, g2, b2,...]

    const length = array.length - 3;
    const colors = new Float32Array(2 * length);

    for (let i = 0; i < length; i += 3) {
      colors[2 * i] = array[i];
      colors[2 * i + 1] = array[i + 1];
      colors[2 * i + 2] = array[i + 2];

      // 注意：此处原来的处理是设置成  array[i + 3], array[i + 4], array[i + 5], 根据需要做了修改
      colors[2 * i + 3] = array[i];
      colors[2 * i + 4] = array[i + 1];
      colors[2 * i + 5] = array[i + 2];
    }

    super.setColors(colors);

    return this;
  }

  fromLine(line) {
    const geometry = line.geometry;

    this.setPositions(geometry.attributes.position.array); // assumes non-indexed

    // set colors, maybe

    return this;
  }

  // 必须复制点数
  copy(source) {
    super.copy(source);
    this.pointCount = source.pointCount;
    return this;
  }
}
