import { BufferGeometry, Mesh } from 'three';

import { LineSegmentsGeometry } from './LineGeometry.js';
import { LineMaterial } from './LineMaterial.js';

export declare class Wireframe extends Mesh {
  constructor(geometry?: LineSegmentsGeometry, material?: LineMaterial);
  readonly isWireframe: true;

  computeLineDistances(): this;
}

export declare class WireframeGeometry2 extends LineSegmentsGeometry {
  constructor(geometry: BufferGeometry);
  readonly sWireframeGeometry2: boolean;
}
