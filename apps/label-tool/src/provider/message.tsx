import { message } from 'antd';
import { MessageInstance } from 'antd/es/message/interface';
import { createContext, useContext } from 'react';

export const initMessageProvider = () => {
  const MessageContext = createContext<MessageInstance | null>(null);

  const MessageProvider = ({ children }: { children: JSX.Element }) => {
    const [messageApi, contextHolder] = message.useMessage();

    return (
      <MessageContext.Provider value={messageApi}>
        {contextHolder}
        {children}
      </MessageContext.Provider>
    );
  };

  const useMessage = () => {
    return useContext(MessageContext);
  };

  return {
    MessageProvider,
    useMessage,
  };
};

export const { MessageProvider, useMessage } = initMessageProvider();
