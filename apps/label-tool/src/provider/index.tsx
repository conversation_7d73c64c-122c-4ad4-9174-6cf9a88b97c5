import { setValidateLanguage } from '@formily/core';
import { ConfigProvider, MappingAlgorithm, theme } from 'antd';
import { Suspense } from 'react';
import { HotkeysProvider } from 'react-hotkeys-hook';
import { BrowserRouter } from 'react-router-dom';

import { GlobalLoader } from '@/components';
import { AuthProvider } from '@/features/auth';
import { DEFAULT_HOTKEY_SCOPE } from '@/features/hotkey';

import { ErrorProvider, useError } from './error';
import { MessageProvider, useMessage } from './message';

interface AppProviderProps {
  children: React.ReactNode;
}
// TODO: [3h]Add ConfigProvider contains lang/config
setValidateLanguage('zh-CN');

export { useError, useMessage };

const labelDarkAlgorithm: MappingAlgorithm = (seedToken, mapToken) => {
  const baseToken = theme.darkAlgorithm(
    {
      ...seedToken,
      colorBgBase: '#090d11',
      colorPrimary: '#27c1a4',
      colorSuccess: '#31aa07',
      colorWarning: '#ed7112',
      colorError: '#f53f3f',
      colorInfo: '#0f76f5',
      borderRadius: 4,
      fontSize: 12,
      // menuItemPaddingInline: 0,
    },
    mapToken
  );

  return {
    ...baseToken,
    colorPrimaryBorder: '#3aa691',
    colorPrimaryBorderHover: '#4daf9b',
  };
};

export const AppProvider = ({ children }: AppProviderProps) => {
  return (
    <Suspense fallback={<GlobalLoader />}>
      <ConfigProvider
        theme={{
          algorithm: labelDarkAlgorithm,
          token: {
            colorBgElevated: '#323F4B',
          },
          components: {
            Menu: {
              margin: 0,
            },
            Collapse: {
              paddingSM: 0,
            },
            Select: {
              borderRadius: 2,
              optionActiveBg: '#171F26',
              // optionSelectedBg: 'transparent',
            },
            Button: {
              lineWidthFocus: 2,
            },
            Alert: {
              colorWarningBg: '#915930',
            },
            Modal: {
              contentBg: '#323F4B',
              headerBg: '#323F4B',
              titleColor: '#F5F7FA',
            },
            Slider: {
              dotBorderColor: '#52606D',
            },
          },
        }}
      >
        <MessageProvider>
          <ErrorProvider>
            <AuthProvider>
              <HotkeysProvider initiallyActiveScopes={[DEFAULT_HOTKEY_SCOPE]}>
                <BrowserRouter>{children}</BrowserRouter>
              </HotkeysProvider>
            </AuthProvider>
          </ErrorProvider>
        </MessageProvider>
      </ConfigProvider>
    </Suspense>
  );
};
