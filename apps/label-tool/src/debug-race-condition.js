/**
 * 调试工具：模拟高负载环境来增加竞态条件发生的概率
 * 在浏览器控制台中运行这些函数
 */

// 1. 模拟CPU密集型任务
window.simulateHighCPULoad = function(duration = 1000) {
  console.log('🔥 开始模拟高CPU负载...');
  const start = Date.now();
  while (Date.now() - start < duration) {
    // 空循环消耗CPU
    Math.random();
  }
  console.log('✅ CPU负载模拟结束');
};

// 2. 模拟内存压力
window.simulateMemoryPressure = function() {
  console.log('💾 开始模拟内存压力...');
  const arrays = [];
  for (let i = 0; i < 100; i++) {
    arrays.push(new Array(100000).fill(Math.random()));
  }
  setTimeout(() => {
    arrays.length = 0; // 清理内存
    console.log('✅ 内存压力模拟结束');
  }, 2000);
};

// 3. 模拟网络延迟
window.simulateNetworkDelay = function() {
  console.log('🌐 开始模拟网络延迟...');
  const originalFetch = window.fetch;
  window.fetch = function(...args) {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(originalFetch.apply(this, args));
      }, Math.random() * 500 + 100); // 100-600ms延迟
    });
  };
  
  setTimeout(() => {
    window.fetch = originalFetch;
    console.log('✅ 网络延迟模拟结束');
  }, 10000);
};

// 4. 模拟事件循环阻塞
window.blockEventLoop = function(times = 5) {
  console.log('⏰ 开始阻塞事件循环...');
  let count = 0;
  
  function block() {
    if (count < times) {
      simulateHighCPULoad(50); // 短时间阻塞
      count++;
      setTimeout(block, 10); // 短间隔重复
    } else {
      console.log('✅ 事件循环阻塞结束');
    }
  }
  
  block();
};

// 5. 监控状态变化
window.monitorStateChanges = function() {
  console.log('👁️ 开始监控状态变化...');
  
  // 监控commentMap变化
  let lastCommentMapState = null;
  const checkInterval = setInterval(() => {
    try {
      const store = window.__ZUSTAND_STORE__; // 假设store暴露在全局
      if (store) {
        const currentState = store.getState();
        const currentCommentMap = JSON.stringify(currentState.commentMap);
        
        if (lastCommentMapState && lastCommentMapState !== currentCommentMap) {
          console.log('🔄 commentMap状态发生变化:', {
            timestamp: Date.now(),
            changes: Object.keys(currentState.commentMap || {}).length
          });
        }
        lastCommentMapState = currentCommentMap;
      }
    } catch (e) {
      // 忽略错误
    }
  }, 50); // 高频检查
  
  // 10秒后停止监控
  setTimeout(() => {
    clearInterval(checkInterval);
    console.log('✅ 状态监控结束');
  }, 10000);
};

// 6. 综合压力测试
window.runRaceConditionTest = function() {
  console.log('🚀 开始竞态条件压力测试...');
  
  // 同时启动多种压力
  simulateMemoryPressure();
  blockEventLoop(3);
  monitorStateChanges();
  
  setTimeout(() => {
    simulateNetworkDelay();
  }, 1000);
  
  console.log('⚠️ 现在是测试的最佳时机！请执行以下步骤：');
  console.log('1. 创建一些批注');
  console.log('2. 点击提交');
  console.log('3. 在确认框中快速点击"确定"');
  console.log('4. 观察控制台日志');
};

console.log('🛠️ 竞态条件调试工具已加载！');
console.log('可用函数：');
console.log('- simulateHighCPULoad() - 模拟CPU负载');
console.log('- simulateMemoryPressure() - 模拟内存压力');
console.log('- simulateNetworkDelay() - 模拟网络延迟');
console.log('- blockEventLoop() - 阻塞事件循环');
console.log('- monitorStateChanges() - 监控状态变化');
console.log('- runRaceConditionTest() - 综合压力测试');
