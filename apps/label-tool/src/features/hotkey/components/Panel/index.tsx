import { <PERSON><PERSON>, Tabs } from 'antd';
import { useMemo, useState } from 'react';
import { RiArrowDownLine, RiArrowLeftLine, RiArrowRightLine, RiArrowUpLine, RiCloseLine } from 'react-icons/ri';

import { HotkeyItem } from '../../types';

import './style.css';

export interface HotkeyPanelProps {
  config: Record<
    string,
    {
      label: string;
      children: HotkeyItem[][];
    }
  >;
  isOpen: boolean;
  onClose: () => void;
}
const generateKey = (i: number) => `hotkey-panel-${i}`;
const renderKey = (key: string, i: number) => {
  switch (key) {
    case 'OR':
      return (
        <span className="hotkey-panel-item-link" key={`key-${i}`}>
          或
        </span>
      );
    case 'AND':
      return (
        <span className="hotkey-panel-item-link" key={`key-${i}`}>
          和
        </span>
      );
    case 'Up':
      return (
        <strong key={`key-${i}`}>
          <RiArrowUpLine />
        </strong>
      );
    case 'Down':
      return (
        <strong key={`key-${i}`}>
          <RiArrowDownLine />
        </strong>
      );
    case 'Left':
      return (
        <strong key={`key-${i}`}>
          <RiArrowLeftLine />
        </strong>
      );
    case 'Right':
      return (
        <strong key={`key-${i}`}>
          <RiArrowRightLine />
        </strong>
      );
    default:
      return <strong key={`key-${i}`}>{key}</strong>;
  }
};

export const HotkeyPanel: React.FC<HotkeyPanelProps> = ({ config, isOpen, onClose }) => {
  const [activeKey, setActiveKey] = useState(() => generateKey(0));

  const classes = useMemo(() => Object.keys(config), [config]);

  return (
    <Drawer
      placement="bottom"
      open={isOpen}
      forceRender={true}
      styles={{
        body: {
          padding: '0px',
          backgroundColor: '#1F2933',
        },
      }}
      height={212}
      getContainer={false}
      mask={false}
      closable={false}
    >
      <div>
        <Tabs
          activeKey={activeKey}
          rootClassName="hot-key-tabs"
          centered
          type="card"
          size="large"
          onChange={(activeKey) => setActiveKey(activeKey)}
          tabBarExtraContent={{
            left: <span className="hotkey-header-title">快捷键面板</span>,
            right: (
              <span className="hotkey-header-close" onClick={onClose}>
                <RiCloseLine
                  style={{
                    width: '22px',
                    height: '22px',
                    color: '#CBD2D9',
                  }}
                />
              </span>
            ),
          }}
          items={classes.map((name, index) => ({
            label: config[name].label,
            key: `hotkey-panel-${index}`,
            children: (
              <div className="hotkey-panel-content">
                {config[name].children.map((list, index) => (
                  <div className="hotkey-panel-list" key={`panel-${name}-${index}`}>
                    {list.map(({ label, keys }, index) => (
                      <div className="hotkey-panel-item" key={`item-${index}`}>
                        <span className="hotkey-panel-item-label">{label}</span>
                        <span className="hotkey-panel-item-keys">{keys.map(renderKey)}</span>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            ),
          }))}
        />
      </div>
    </Drawer>
  );
};
