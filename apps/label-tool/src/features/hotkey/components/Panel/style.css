.hot-key-tabs .ant-tabs-nav {
  background: #171f26;
}
.hot-key-tabs.ant-tabs-card .ant-tabs-nav::before {
  top: 0;
  border-color: #323f4b;
  bottom: unset;
  z-index: 1;
}
.hot-key-tabs.ant-tabs-card .ant-tabs-nav .ant-tabs-tab {
  border: none;
  background: transparent;
  font-size: 14px;
  font-weight: 600;
  padding: 8px 36px 6px;
  color: #52606d;
}
.hot-key-tabs.ant-tabs-card .ant-tabs-nav .ant-tabs-tab-active {
  background-color: #1f2933;
}
.hot-key-tabs.ant-tabs-card .ant-tabs-nav .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #f5f7fa;
}
.hot-key-tabs.ant-tabs .ant-tabs-tab-btn:focus-visible {
  outline: 0;
}

.hotkey-header-title {
  color: #f5f7fa;
  font-size: 14px;
  margin-left: 16px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
}
.hotkey-header-title:before {
  content: ' ';
  display: inline-block;
  width: 2px;
  height: 13px;
  background-color: #64d9c3;
  border-radius: 4px;
  line-height: 22px;
  margin-right: 8px;
}

.hotkey-header-close {
  cursor: pointer;
  padding: 10px;
}

.hotkey-panel-content {
  display: flex;
  flex-direction: row;
  justify-content: center;
  color: #f5f7fa;
  user-select: none;
}

.hotkey-panel-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 300px;
  height: 32px;
  padding: 6px 20px;
  margin: 4px 0;
}
.hotkey-panel-item-link {
  margin-inline: 6px 2px;
}
.hotkey-panel-item-keys strong {
  border: 1px solid #cbd2d9;
  border-radius: 2px;
  padding: 1px 4px;
  line-height: 20px;
  display: inline-block;
  min-width: 24px;
  text-align: center;
  margin-inline-start: 4px;
}
.hotkey-panel-item-keys strong svg {
  margin-top: 2px;
}
