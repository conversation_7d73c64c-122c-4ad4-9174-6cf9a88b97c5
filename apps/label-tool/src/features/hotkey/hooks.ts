import { useMemo } from 'react';
import { Options, useHotkeys } from 'react-hotkeys-hook';

import { DEFAULT_HOTKEY_SCOPE } from './config';
import { HotkeyItem } from './types';
import { formatHotkeys } from './utils';

export const useHotkeyWithScope = (hotkey: string, callback: () => void, options: Options = {}) => {
  return useHotkeys<HTMLDivElement>(hotkey, callback, {
    scopes: DEFAULT_HOTKEY_SCOPE,
    ...options,
  });
};

export const useHotkeyConfigWithScope = (
  list: Array<Array<HotkeyItem>>,
  hotkeyName: string | Array<string>,
  callback: () => void,
  options: Options = {}
) => {
  const splitKey = '&';
  const keyStr = useMemo(() => {
    const names = Array.isArray(hotkeyName) ? hotkeyName : [hotkeyName];

    let keyItems: Array<HotkeyItem> = [];
    for (let i = 0; i < list.length; i++) {
      const items = list[i].filter((item) => names.includes(item.name));
      items.length > 0 && keyItems.push(...items);

      if (keyItems.length >= names.length) {
        break;
      }
    }

    const str = keyItems.map(({ keys }) => formatHotkeys(keys, splitKey)).join(splitKey);

    return str;
  }, [list, hotkeyName]);

  return useHotkeys<HTMLDivElement>(keyStr, callback, {
    scopes: DEFAULT_HOTKEY_SCOPE,
    splitKey,
    ...options,
  });
};
