/**
 * 将快捷键字符数组格式化为 react-hotkeys-hook 的快捷键入参
 * 1. 不区分大小写
 * 2. ctrl 默认等同于 ctrl / command
 * 3. 支持 or 连字符，例：['Shift', 'Up', 'OR', 'Shift', 'W'] => "shift+up,shift+w"
 * @param keys
 * @returns
 */

const SPECIAL_KEY_MAP: Record<string, string> = {
  '<': ',',
  '>': '.',
  '+': 'equal',
};

export const formatHotkeys = (keys: string[], splitKey = ',') => {
  return keys
    .map((key) => SPECIAL_KEY_MAP[key] ?? key)
    .join('+')
    .toLowerCase()
    .split('+or+')
    .map((key) => key.replace('ctrl+', 'mod+'))
    .join(splitKey);
};

/**
 * 按条件过滤快捷键配置项，并根据配置个数组合为二级结构
 * @param keys 快捷键配置列表
 * @param condition 过滤条件
 * @param num 配置个数
 * @returns
 */
export const filterAndGroupKeys = <T>(keys: Array<T>, condition: (k: T) => boolean, num = 4): Array<Array<T>> => {
  const res: Array<Array<T>> = [];
  const filtered = keys.filter(condition);

  for (let i = 0; i < filtered.length; i += num) {
    res.push(filtered.slice(i, i + num));
  }

  return res;
};
