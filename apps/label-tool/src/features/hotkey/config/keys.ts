/**
 * 该文件为项目所有快捷键配置，变更配置请遵循以下规则：
 * 1. 【用户需要了解的快捷键】均需须添加至文件
 * 2. 分组配置，组内为两层数组结构，新增组需添加注释说明组操作范围
 * 3. 【工具类】快捷键配置请参考 HOTKEY_TOOL_CONFIG 注释
 * 4. 尚未实现的功能但需占位快捷键的情况，请以【被注释的配置】形式添加
 */
import type { ToolType } from '@/utils/tool';

import { HotkeyItem } from '../types';

/**
 * 通用类快捷键
 */
export const HOTKEY_NORMAL_CONFIG: Array<Array<HotkeyItem>> = [
  [
    { name: 'keyboard', label: '显示/隐藏快捷键面板', keys: ['Ctrl', '\\'] },
    { name: 'exit', label: '退出编辑/关闭弹窗', keys: ['Esc'] },
    { name: 'home', label: '返回大厅', keys: ['Home'] },
  ],
  [
    { name: 'resolve', label: '提交/通过', keys: ['Ctrl', 'Enter'] },
    { name: 'reject', label: '驳回', keys: ['Ctrl', 'R'] },
  ],
  [
    { name: 'next', label: '选择下一个同级选项', keys: ['Tab'] },
    { name: 'prev', label: '选择上一个同级选项', keys: ['Shift', 'Tab'] },
  ],
  [
    // { name: 'vertical', label: '纵向导航切换', keys: ['[', 'AND', ']'] },
    { name: 'element', label: '横向帧切换', keys: ['<', 'AND', '>'] },
    // { name: 'element-overlay', label: '向左/向右叠帧', keys: ['Ctrl', '<', 'AND', 'Ctrl', '>'] },
  ],
];

/**
 * 点云操作
 */
export const HOTKEY_PCD_CONFIG: Array<Array<HotkeyItem>> = [
  [
    { name: 'zoom', label: '放大/缩小点云', keys: ['+', 'AND', '-', 'OR', '鼠标滚轮'] },
    { name: 'pan', label: '拖动点云', keys: ['右键长按'] },
    { name: 'rotate', label: '旋转点云', keys: ['空格', '左键长按'] },
  ],
  [
    { name: 'bev', label: '进入/退出 BEV 模式', keys: ['Ctrl', 'B'] },
    // { name: 'reset', label: '还原点云默认状态', keys: ['Ctrl', 'X'] },
  ],
  [
    { name: 'rotate-up', label: '向上旋转点云', keys: ['Z'] },
    { name: 'rotate-down', label: '向下旋转点云', keys: ['C'] },
    { name: 'rotate-left', label: '向左旋转点云', keys: ['Q'] },
    { name: 'rotate-right', label: '向右旋转点云', keys: ['E'] },
  ],
  [
    { name: 'pan-up', label: '向上移动点云', keys: ['Up', 'OR', 'W'] },
    { name: 'pan-down', label: '向下移动点云', keys: ['Down', 'OR', 'S'] },
    { name: 'pan-left', label: '向左移动点云', keys: ['Left', 'OR', 'A'] },
    { name: 'pan-right', label: '向右移动点云', keys: ['Right', 'OR', 'D'] },
  ],
];

/**
 * 组件查看类操作
 */
export const HOTKEY_VIEW_CONFIG: Array<Array<HotkeyItem>> = [
  [
    { name: 'attr-panel', label: '打开属性面板', keys: ['F4'] },
    { name: 'element-attr-panel', label: '打开帧属性面板', keys: ['F5'] },
    { name: 'map', label: '打开2D图片', keys: ['图像序号对应的数字键1-4'] },
  ],
  [
    { name: 'element-lock', label: '锁定/打开帧码跟随鼠标模式', keys: ['Ctrl', 'L'] },
    { name: 'attr-bar-visible', label: '隐藏/显示所有属性条', keys: ['Shift', 'Ctrl', 'H'] },
    { name: 'object-visible', label: '隐藏/显示其他标注物', keys: ['Shift', 'H'] },
  ],
  [
    { name: 'forward-overlay', label: '叠帧向前叠一帧', keys: ['Ctrl', '>'] },
    { name: 'backward-overlay', label: '叠帧向后退一帧', keys: ['Ctrl', '<'] },
    { name: 'clear-overlay', label: '清除叠帧', keys: ['Ctrl', '/'] },
    //   { name: 'edit-zoomin', label: '放大编辑视图倍率', keys: ['Ctrl', '+', 'OR', '鼠标滚轮'] },
    //   { name: 'edit-zoomout', label: '缩小编辑视图倍率', keys: ['Ctrl', '-', 'OR', '鼠标滚轮'] },
  ],
];

/**
 * 标注物相关操作
 */
export const HOTKEY_ANNO_CONFIG: Array<Array<HotkeyItem>> = [
  [
    { name: 'prev', label: '上一个标注物', keys: ['Shift', 'Up', 'OR', 'Shift', 'W'] },
    { name: 'next', label: '下一个标注物', keys: ['Shift', 'Down', 'OR', 'Shift', 'S'] },
    { name: 'direct', label: '调整标注框方向', keys: ['F'] },
  ],
  [
    { name: 'edit', label: '编辑已选中的标注物', keys: ['Enter'] },
    { name: 'delete', label: '删除折线上的选中点', keys: ['Delete'] },
    { name: 'undo', label: '绘制撤销', keys: ['Ctrl', 'Z'] },
    { name: 'redo', label: '绘制重做', keys: ['Shift', 'Ctrl', 'Z'] },
  ],
  [
    { name: 'edge-up', label: '悬浮对象向上移动', keys: ['Up', 'OR', 'W'] },
    { name: 'edge-down', label: '悬浮对象向下移动', keys: ['Down', 'OR', 'S'] },
    { name: 'edge-left', label: '悬浮对象向左移动', keys: ['Left', 'OR', 'A'] },
    { name: 'edge-right', label: '悬浮对象向右移动', keys: ['Right', 'OR', 'D'] },
  ],
  [
    { name: 'rotate-up', label: '悬浮对象逆时针旋转', keys: ['Z'] },
    { name: 'rotate-down', label: '悬浮对象顺时针旋转', keys: ['X'] },
  ],
  [
    { name: 'fit', label: '3D立体框自动贴合', keys: ['Ctrl', 'K'] },
    // { name: 'expand', label: '3D立体框自动扩展', keys: ['Shift', 'Ctrl', 'K'] },
    { name: 'remove', label: '删除选中的标注物', keys: ['Delete', 'OR', 'Backspace'] },
    { name: 'copy', label: '复制选中的标注物', keys: ['Ctrl', 'C'] },
    { name: 'paste', label: '粘贴标注物', keys: ['Ctrl', 'V'] },
  ],
];

interface HotkeyToolItem extends Omit<HotkeyItem, 'name'> {
  name: ToolType;
}
/**
 * 全局工具快捷键配置：[[选择工具 + 所有标注工具], [所有辅助工具]]
 * 1. 选择工具与 3D 标注工具 设计为单个字母
 * 2. 2D 标注工具在 对应的 3D 标注工具基础上 + Shift
 * 3. 辅助工具均需 + Ctrl
 */
export const HOTKEY_TOOL_CONFIG: Array<Array<HotkeyToolItem>> = [
  [
    { name: 'select', label: '选择', keys: ['V'] },
    // --------------------------- 3D 工具 -------------------------------------
    { name: 'cuboid', label: '立体框', keys: ['R'] },
    { name: 'line3d', label: '折线', keys: ['L'] },
    { name: 'spline3d', label: '样条曲线', keys: ['Y'] },
    { name: 'poly3d', label: '多边形', keys: ['P'] },
    { name: 'point3d', label: '点', keys: ['I'] },
    { name: 'series-point3d', label: '折线上添加独立点', keys: ['Shift', '+'] },
    // --------------------------- 2D 工具 -------------------------------------
    // { name: 'pscuboid', label: '伪立体框', keys: ['Shift', 'R'] },
    { name: 'line2d', label: '折线', keys: ['Shift', 'L'] },
    { name: 'box2d', label: '矩形框', keys: ['Shift', 'J'] },
    { name: 'poly2d', label: '多边形', keys: ['Shift', 'P'] },
    { name: 'point2d', label: '点', keys: ['Shift', 'I'] },
  ],
  [
    // { name: 'crosshair', label: '隐藏/显示误差准线', keys: ['Ctrl', 'M'] },
    { name: 'caliper', label: '测距', keys: ['Ctrl', 'T'] },
    { name: 'pyramid', label: '漏标', keys: ['Ctrl', 'O'] },
  ],
];
