import {
  filterAndGroupKeys,
  HOTKEY_ANNO_CONFIG,
  HOTKEY_NORMAL_CONFIG,
  HOTKEY_PCD_CONFIG,
  HOTKEY_TOOL_CONFIG,
  HOTKEY_VIEW_CONFIG,
} from '@/features/hotkey';
import { Tool } from '@/utils/tool';

/** 3D 工具列表 */
const HOTKEY_TOOL3 = filterAndGroupKeys(
  HOTKEY_TOOL_CONFIG[0],
  ({ name }) => name === 'select' || Tool.isAnno3DTool(name)
);

// /** 2D 工具列表 */
// const HOTKEY_TOOL2 = filterAndGroupKeys(
//   HOTKEY_TOOL_CONFIG[0],
//   ({ name }) => name === 'select' || Tool.isAnno2DTool(name)
// );

export const HOTKEY_CONFIG = {
  normal: {
    label: '通用',
    children: HOTKEY_NORMAL_CONFIG,
  },
  view: {
    label: '查看',
    children: HOTKEY_VIEW_CONFIG,
  },
  pcd: {
    label: '点云',
    children: HOTKEY_PCD_CONFIG,
  },
  anno: {
    label: '编辑',
    children: HOTKEY_ANNO_CONFIG,
  },
  tool3: {
    label: '3D工具',
    children: [...HOTKEY_TOOL3, HOTKEY_TOOL_CONFIG[1]],
  },
  // tool2: {
  //   label: '2D工具',
  //   children: [...HOTKEY_TOOL2, HOTKEY_TOOL_CONFIG[1]],
  // },
};
