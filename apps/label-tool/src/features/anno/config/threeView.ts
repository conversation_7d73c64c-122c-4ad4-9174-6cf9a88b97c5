import type { ThreeViewOptions } from '@/utils/render';

export const THREE_VIEW_WIDTH = 242;
export const THREE_VIEW_HEIGHT_RATIO = 0.333;
export const THREE_VIEW_TOP = 150;
export const THREE_VIEW_RIGHT = 8;
export const THREE_VIEW_BOTTOM = 8;
export const VIEWS_ZOOM = 5 / 3;

export const PCD_THREE_VIEWS_OPTIONS: Array<ThreeViewOptions> = [
  // 顶视图
  {
    type: 'top',
    top: THREE_VIEW_TOP,
    right: THREE_VIEW_RIGHT,
    bottom: THREE_VIEW_BOTTOM,
    height: THREE_VIEW_HEIGHT_RATIO,
    width: THREE_VIEW_WIDTH,
    zoom: VIEWS_ZOOM,
  },
  // 侧视图 - y轴
  {
    type: 'side',
    top: THREE_VIEW_TOP,
    right: THREE_VIEW_RIGHT,
    bottom: THREE_VIEW_BOTTOM,
    height: THREE_VIEW_HEIGHT_RATIO,
    width: THREE_VIEW_WIDTH,
    zoom: VIEWS_ZOOM,
  },
  // 前视图 - x轴
  {
    type: 'front',
    top: THREE_VIEW_TOP,
    right: THREE_VIEW_RIGHT,
    bottom: THREE_VIEW_BOTTOM,
    height: THREE_VIEW_HEIGHT_RATIO,
    width: THREE_VIEW_WIDTH,
    zoom: VIEWS_ZOOM,
  },
];
