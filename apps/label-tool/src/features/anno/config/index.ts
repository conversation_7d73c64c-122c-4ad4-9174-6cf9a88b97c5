import { Comment, SeriesOptScope } from '@/types';

export * from './hotkey';
export * from './threeView';
export * from './widget';

export const DEFAULT_UNMAP_COLOR = '#99c2ff';
// 线条感应范围
export const LINE_THRESHOLD = 16;

export const COMMENT_RED = '#F54C46';
export const COMMENT_YELLOW = '#FA8C16';
export const COMMENT_GREEN = '#00D18B';

export const COMMENT_STATUS_MAP: Record<Exclude<Comment['status'], undefined>, string> = {
  unresolved: COMMENT_RED,
  pending: COMMENT_YELLOW,
  resolved: COMMENT_GREEN,
};

export const DOUBLE_CLICK_INTERVAL = 200;
export const ROTATION_DISTANCE_THRESHOLD = 4; // 位移超过 2px 判断为旋转，阈值用平方

// 折线拼接时，如果被拼接的两端点距离小于阈值，两端点合并。
export const MERGE_DOT_THRESHOLD = 0.1;

export const SERIES_OPT_SCOPE_MAP: Record<SeriesOptScope, string> = {
  [SeriesOptScope.CURRENT]: '仅当前帧',
  [SeriesOptScope.FORWARD]: '向前同步帧',
  [SeriesOptScope.BACKWARD]: '向后同步帧',
  [SeriesOptScope.ALL]: '同步全部帧',
};

// 从右边开始，逆时针方向旋转 90 度的次数
export const DIRECTION_ROTATE_TIMES_MAP = {
  right: 0,
  top: 1,
  left: 2,
  bottom: 3,
};
