import { KvLabelLine, KvLabelPoint, KvPolygon3D, KvSplineLine } from '@forest/icons';
import { ReactNode } from 'react';
import { RiBox3Line, RiFolder2Line } from 'react-icons/ri';

import { WidgetType } from '@/types';

// 标注物展示时候的 icon
type AnnoWidgetType = WidgetType | 'spline3d' | 'compound';

export const WIDGET_INFO_MAP: {
  [key in AnnoWidgetType]?: ReactNode;
} = {
  line3d: <KvLabelLine />,
  spline3d: <KvSplineLine />,
  point3d: <KvLabelPoint />,
  cuboid: <RiBox3Line />,
  compound: <RiFolder2Line />,
  poly3d: <KvPolygon3D />,
};

// 需展示序号的标注物类别
export const WIDGET_SHOW_DOT = ['line3d', 'poly3d'];
