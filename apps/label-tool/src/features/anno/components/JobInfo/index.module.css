.container {
  position: absolute;
  display: inline-block;
  margin-left: 6px;
}
.icon {
  font-size: 16px;
  color: #f5f7fa88;
  line-height: 22px;
}
.icon:hover {
  cursor: pointer;
  color: #f5f7facc;
}
.wrapper {
  position: absolute;
  background-color: #323f4b;
  border: 1px solid #52606d;
  font-size: 14px;
  width: 300px;
  border-radius: 4px;
  line-height: 22px;
  font-weight: 400;
}
.title {
  padding: 10px 20px;
  border-bottom: 1px solid #52606d;
  color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.copy {
  font-size: 16px;
  line-height: 0;
  margin-inline-start: 4px;
  padding: 6px;
}
.copy:hover {
  background-color: #cbd2d966;
  border-radius: 2px;
  cursor: pointer;
}

.body {
  padding: 8px 20px;
}
.item {
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.item-name {
  color: #cbd2d9;
  text-wrap: nowrap;
}
.item-name::after {
  content: ':';
}
.item-value {
  flex: 1;
  color: #f5f7fa;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: right;
}
