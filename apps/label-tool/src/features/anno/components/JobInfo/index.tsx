import { KvCopy, KvExclamation } from '@forest/icons';
import { Tooltip } from 'antd';
import copy from 'copy-to-clipboard';
import { pick } from 'lodash';
import { FC, useEffect, useRef, useState } from 'react';

import { useAuth } from '@/features/auth';
import { useMessage } from '@/provider';

import { getLotDataAndPrevPhaseOperator } from '../../api';
import { useContextStore } from '../../stores';

import styles from './index.module.css';

type InfoItem = {
  name: string;
  value: string;
};

interface Info {
  taskName: InfoItem;
  jobDataName: InfoItem;
  jobId: InfoItem;
  currentPhaseOperator: InfoItem;
  prevPhaseOperator?: InfoItem;
}

export const JobInfo: FC = () => {
  const [info, setInfo] = useState<Info | null>(null);
  const [open, setOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);
  const { user } = useAuth();
  const { job, lot, phaseMap } = useContextStore((state) => pick(state, ['job', 'lot', 'phaseMap']));
  const messageApi = useMessage();

  useEffect(() => {
    if (!job || !lot || !user) return;
    const prevPhase = job.phase - 1;

    getLotDataAndPrevPhaseOperator(lot.data_uid, job.uid, prevPhase).subscribe({
      next: ({ dataName, prevPhaseOperator }) => {
        setInfo(() => {
          const info: Info = {
            taskName: {
              name: '任务名',
              value: lot.name,
            },
            jobDataName: {
              name: '任务数据包',
              value: dataName,
            },
            jobId: {
              name: '任务包ID',
              value: job.uid,
            },
            currentPhaseOperator: {
              name: '当前环节操作人',
              value: user.name + '/' + user.uid,
            },
          };
          if (prevPhaseOperator) {
            info.prevPhaseOperator = {
              name: `${phaseMap[prevPhase].name}环节操作人`,
              value: prevPhaseOperator,
            };
          }
          return info;
        });
      },
      error: () => {
        messageApi?.error('获取任务信息失败');
      },
    });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [job, lot, user]);

  useEffect(() => {
    // 此处的作用是点击页面其他地方时，关闭任务信息
    const handle = (e: MouseEvent) => {
      e.stopPropagation();
      if (!ref.current) return;
      const target = e.target as HTMLElement;
      if (!ref.current.contains(target)) {
        setOpen(false);
      }
    };
    document.body.addEventListener('click', handle);

    return () => {
      document.body.removeEventListener('click', handle);
    };
  }, []);

  const handleCopy = () => {
    if (!info) return;
    const text = Object.values(info)
      .map((item) => `${item.name}: ${item.value}`)
      .join('\n');

    if (copy(text)) {
      messageApi?.success('复制成功');
    } else {
      messageApi?.error('复制失败，请手动选中后通过 Ctrl + C 进行复制');
    }
  };
  return (
    <div
      className={styles.container}
      onClick={() => {
        setOpen(true);
      }}
      ref={ref}
    >
      <span className={styles.icon}>
        <KvExclamation />
      </span>

      <div className={styles.wrapper} style={{ display: open && info ? 'block' : 'none' }}>
        <div className={styles.title}>
          <span>任务信息</span>
          <Tooltip title="复制全部" placement="bottom">
            <span className={styles.copy} onClick={handleCopy}>
              <KvCopy />
            </span>
          </Tooltip>
        </div>
        <div className={styles.body}>
          {info &&
            Object.values(info).map((item: InfoItem, index: number) => (
              <div className={styles.item} key={item.value + index}>
                <span className={styles['item-name']}>{item.name}</span>
                <Tooltip title={item.value} placement="right">
                  <span className={styles['item-value']} title={item.value}>
                    {item.value}
                  </span>
                </Tooltip>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};
