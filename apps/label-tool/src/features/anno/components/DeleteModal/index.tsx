import { KvAttention } from '@forest/icons';
import type { RadioChangeEvent } from 'antd';
import { Modal, Radio, Space } from 'antd';
import { pick } from 'lodash';
import { useEffect, useState } from 'react';

import { SeriesOptScope } from '@/types';

import { useRemoveAnno } from '../../hooks';
import { useContextStore } from '../../stores';

import styles from './styles.module.css';

const SERIES_OPT_SCOPE_MAP = {
  [SeriesOptScope.CURRENT]: '删除此帧',
  [SeriesOptScope.FORWARD]: '向前删除',
  [SeriesOptScope.BACKWARD]: '向后删除',
  [SeriesOptScope.ALL]: '删除全部',
};

export const DeleteModal: React.FC<{}> = () => {
  const { commentHoverInfo, deleteModalAnnos, seriesLinkage, setCommentHoverInfo, setDeleteModalAnnos, setEditAnno } =
    useContextStore((state) =>
      pick(state, [
        'commentHoverInfo',
        'deleteModalAnnos',
        'seriesLinkage',
        'setCommentHoverInfo',
        'setDeleteModalAnnos',
        'setEditAnno',
      ])
    );

  const { handleRemoveAnnos } = useRemoveAnno();

  const [value, setValue] = useState<SeriesOptScope>(seriesLinkage.seriesOptScope);

  useEffect(() => {
    setValue(seriesLinkage.seriesOptScope);
  }, [seriesLinkage.seriesOptScope]);

  const onChange = (e: RadioChangeEvent) => {
    setValue(e.target.value);
  };

  const handleSelectRemoveScope = () => {
    // 检查是否能否进行删除
    seriesLinkage.setSeriesOptScope(value);
    commentHoverInfo && setCommentHoverInfo(null);
    handleRemoveAnnos(deleteModalAnnos);
    handleCancel();
  };

  const handleCancel = () => {
    setDeleteModalAnnos([]);
    setEditAnno(null);
  };

  return (
    <Modal
      className={styles.modal}
      title={'删除'}
      open={deleteModalAnnos.length > 0}
      onOk={handleSelectRemoveScope}
      onCancel={handleCancel}
      okText="确定"
      cancelText="取消"
      footer={(_, { OkBtn, CancelBtn }) => (
        <div className={styles.footer}>
          <div className={styles['footer-left']}>
            <KvAttention fontSize={16} />
            <span>删除暂无法恢复，请谨慎操作</span>
          </div>
          <div>
            <CancelBtn />
            <OkBtn />
          </div>
        </div>
      )}
    >
      <div className={styles['modal-content']}>
        {deleteModalAnnos.length <= 1 ? (
          <>
            <p className={styles.text}>请选择当前的删除操作类型？</p>
            <Radio.Group onChange={onChange} value={value}>
              <Space direction="vertical" size={'middle'}>
                {Object.entries(SERIES_OPT_SCOPE_MAP).map(([key, value]) => (
                  <Radio key={key} value={key}>
                    {value}
                  </Radio>
                ))}
              </Space>
            </Radio.Group>
          </>
        ) : (
          // 批量删除的操作类型在 AnnosModal 中选择，这里只提醒选择的结果
          <p className={styles.text}>确定{SERIES_OPT_SCOPE_MAP[value]}吗？</p>
        )}
      </div>
    </Modal>
  );
};
