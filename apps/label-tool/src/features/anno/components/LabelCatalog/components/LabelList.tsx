import { InfoCircleOutlined } from '@ant-design/icons';
import { KvLabelFoldAll, KvLabelUnfoldAll } from '@forest/icons';
import { Affix, Popover, Tooltip } from 'antd';
import { pick } from 'lodash';
import { FC, MouseEvent, useEffect, useMemo, useRef, useState } from 'react';
import { RiArrowDownSFill, RiArrowLeftSFill, RiEyeCloseLine, RiEyeLine } from 'react-icons/ri';

import { useContextStore } from '@/features/anno/stores';
import { VisibleStatus } from '@/features/anno/stores/slices';
import { AnnoItemWithAttrsStr } from '@/features/anno/types';
import { Label } from '@/types';
import { IAnnoItem } from '@/utils/annos';

import { ListAnno } from './ListAnno';

export interface LabelTreeNode extends Label {
  commentStatus?: string;
  children: AnnoItemWithAttrsStr[];
}
export interface LabelListProps {
  stickyDirection: 'top' | 'bottom';
  title: string;
  labelTreeData: LabelTreeNode[];
  expandedKeys: LabelTreeNode['name'][];
  selectedKeys: IAnnoItem['name'][];
  selectedRelatedKeys: IAnnoItem['name'][];
  childAnnosCountMap?: Record<string, number>;
  info?: React.ReactNode;
  onExpand: (expandedKeys: LabelTreeNode['name'][]) => void;
  onExpandAll: () => void;
  onSelectAnno: (selectedKey: IAnnoItem['name'], multiple: boolean) => void;
  onClickLabel?: (labelName: Label['name']) => void;
  onRemoveAnno: (anno: IAnnoItem['name']) => void;
  onEditAnno: (anno: IAnnoItem['name']) => void;
}

export const LabelList: FC<LabelListProps> = ({
  stickyDirection,
  title,
  labelTreeData,
  expandedKeys,
  selectedKeys,
  selectedRelatedKeys,
  childAnnosCountMap,
  info,
  onExpand,
  onExpandAll,
  onClickLabel,
  onEditAnno,
  onRemoveAnno,
  onSelectAnno,
}) => {
  const [topDistance, setTopDistance] = useState<number | undefined>(undefined);
  // 控制全部的显隐
  const [allVisible, setAllVisible] = useState<boolean>(true);
  // 控制全部的展开和收起
  const [allExpanded, setAllExpanded] = useState<boolean>(true);
  const wrapperRef = useRef<HTMLDivElement | null>(null);
  const isAffixed = useRef<boolean>(false);
  const { currentLabel, trackIdsVisibleFlag, changeAnnoListVisible, setSelectedAnnos } = useContextStore((state) =>
    pick(state, ['currentLabel', 'trackIdsVisibleFlag', 'changeAnnoListVisible', 'setSelectedAnnos'])
  );

  const [allLabels, allAnnos] = useMemo(() => {
    const labelNames: string[] = [],
      annoList: IAnnoItem[] = [];
    labelTreeData.forEach(({ children, name }) => {
      labelNames.push(name);
      children.forEach((anno) => annoList.push(anno));
    });
    return [labelNames, annoList];
  }, [labelTreeData]);

  useEffect(() => {
    let showCount = 0,
      hiddenCount = 0,
      allCount = allAnnos.length;
    for (let i = 0, l = allAnnos.length; i < l; i++) {
      // 如果不是全部变成显示或者隐藏，则不起作用
      if (showCount > 0 && hiddenCount > 0) return;
      const isShow = (trackIdsVisibleFlag[allAnnos[i].trackId] ?? VisibleStatus.Show) === VisibleStatus.Show;
      if (isShow) {
        showCount++;
      } else {
        hiddenCount++;
      }
    }

    if (showCount === allCount) {
      setAllVisible(true);
    } else if (hiddenCount === allCount) {
      setAllVisible(false);
    }
  }, [trackIdsVisibleFlag, allAnnos]);

  useEffect(() => {
    if (expandedKeys.length === allLabels.length) {
      setAllExpanded(true);
    } else if (expandedKeys.length === 0) {
      setAllExpanded(false);
    }
  }, [allLabels, expandedKeys]);

  const handleExpand = (name: string) => {
    const index = expandedKeys.indexOf(name);
    let newKeys;
    if (index > -1) {
      newKeys = expandedKeys.slice(0, index).concat(expandedKeys.slice(index + 1));
    } else {
      newKeys = [...expandedKeys, name];
    }
    onExpand(newKeys);
  };

  const handleExpandLabel = (e: MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    const key = e.currentTarget.dataset.name as string;
    handleExpand(key);
  };

  const handleClickLabel = (e: MouseEvent<HTMLElement>) => {
    let labelName = e.currentTarget.dataset.name as string;
    if (labelName === currentLabel?.name) {
      labelName = '';
    }
    onClickLabel && onClickLabel(labelName);
  };

  const handleClickSticky = () => {
    if (isAffixed.current === true && stickyDirection === 'top') {
      wrapperRef.current?.parentElement?.scroll(0, 0);
    }
  };

  const handleAffixChange = (affixed: boolean | undefined) => {
    if (affixed !== undefined) {
      isAffixed.current = affixed;
      if (stickyDirection === 'bottom' && affixed === false) {
        setTopDistance(32);
      }
    }
  };

  const handleAllVisibleChange = (isShow: boolean) => {
    allAnnos.length > 0 && changeAnnoListVisible(allAnnos, isShow);
    setAllVisible(isShow);
  };

  const handleToggleVisibility = (anno: IAnnoItem, isShow: boolean) => {
    setSelectedAnnos(isShow ? [anno] : []);
    changeAnnoListVisible([anno], isShow);
  };

  const renderLabel = (label: LabelTreeNode) => {
    const { name, children, display_name, color, commentStatus } = label;
    const isOpen = expandedKeys.includes(label.name);
    return (
      <div className="label-list-item" key={`label-${name}`}>
        <div className="label" aria-selected={currentLabel?.name === name} data-name={name} onClick={handleClickLabel}>
          {!isOpen && commentStatus && (
            <span
              className="comment-status"
              style={{
                borderTopColor: commentStatus,
                borderLeftColor: commentStatus,
              }}
            ></span>
          )}
          <span className="dot" style={{ backgroundColor: color }}></span>
          <span className="text">
            {display_name} ({children.length})
          </span>
          <span className="icon" data-name={name} onClick={handleExpandLabel}>
            {isOpen ? <RiArrowDownSFill /> : <RiArrowLeftSFill />}
          </span>
        </div>
        {isOpen && (
          <div className="anno-list">
            {children.map((child, index) => (
              <ListAnno
                key={`list-anno-${child.name}-${index}`}
                anno={child}
                index={index}
                selectedKeys={selectedKeys}
                selectedRelatedKeys={selectedRelatedKeys}
                childAnnosCount={childAnnosCountMap?.[child.name]}
                onSelectAnno={onSelectAnno}
                onRemoveAnno={onRemoveAnno}
                onEditAnno={onEditAnno}
                onToggleVisible={handleToggleVisibility}
              />
            ))}
          </div>
        )}
      </div>
    );
  };

  if (labelTreeData.length === 0) return null;

  const visibleSetting = allVisible
    ? { title: '全部隐藏', icon: <RiEyeLine />, handler: () => handleAllVisibleChange(false) }
    : { title: '全部显示', icon: <RiEyeCloseLine />, handler: () => handleAllVisibleChange(true) };

  const expandSetting = allExpanded
    ? {
        title: '全部收起',
        icon: <KvLabelFoldAll />,
        handler: () => {
          onExpand([]);
        },
      }
    : { title: '全部展开', icon: <KvLabelUnfoldAll />, handler: onExpandAll };

  return (
    <div className="label-list" ref={wrapperRef}>
      <Affix
        offsetTop={stickyDirection === 'top' ? 0 : topDistance}
        offsetBottom={stickyDirection === 'bottom' ? 0 : undefined}
        target={() => wrapperRef.current?.parentElement as HTMLElement}
        onChange={handleAffixChange}
      >
        <div className="label-list-title" onClick={handleClickSticky}>
          <span className="text">{title}</span>

          <span className="opt">
            {info ? (
              <Popover placement="left" content={info} trigger="click">
                <InfoCircleOutlined className="icon" />
              </Popover>
            ) : null}

            <Tooltip placement="bottom" title={visibleSetting.title}>
              <span onClick={visibleSetting.handler} className="icon">
                {visibleSetting.icon}
              </span>
            </Tooltip>

            <Tooltip placement="bottom" title={expandSetting.title}>
              <span onClick={expandSetting.handler} className="icon">
                {expandSetting.icon}
              </span>
            </Tooltip>
          </span>
        </div>
      </Affix>
      <div className="label-list-content">{labelTreeData.map(renderLabel)}</div>
    </div>
  );
};
