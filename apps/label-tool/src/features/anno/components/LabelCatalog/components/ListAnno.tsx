import { pick } from 'lodash';
import { FC, MouseEvent, useMemo } from 'react';
import { RiDeleteBinLine, RiEyeCloseLine, RiEyeLine, RiListUnordered } from 'react-icons/ri';

import { COMMENT_STATUS_MAP } from '@/features/anno/config';
import { useRemoveAnno } from '@/features/anno/hooks';
import { useContextStore } from '@/features/anno/stores';
import { VisibleStatus } from '@/features/anno/stores/slices';
import { AnnoItemWithAttrsStr } from '@/features/anno/types';
import { formatTrackIdDisplayName, getAnnoWidgetIcon } from '@/features/anno/utils';
import { useMessage } from '@/provider';
import { IAnnoItem } from '@/utils/annos';

interface ListAnnoProps {
  anno: AnnoItemWithAttrsStr;
  index: number;
  selectedKeys: IAnnoItem['name'][];
  selectedRelatedKeys: IAnnoItem['name'][];
  childAnnosCount?: number;
  onSelectAnno: (selectedKey: IAnnoItem['name'], multiple: boolean) => void;
  onRemoveAnno: (anno: IAnnoItem['name']) => void;
  onEditAnno: (anno: IAnnoItem['name']) => void;
  onToggleVisible: (anno: IAnnoItem, visible: boolean) => void;
}

export const ListAnno: FC<ListAnnoProps> = ({
  anno,
  index,
  selectedKeys,
  selectedRelatedKeys,
  childAnnosCount,
  onSelectAnno,
  onRemoveAnno,
  onEditAnno,
  onToggleVisible,
}) => {
  const messageApi = useMessage();

  const { name, attrsStr = '', trackId } = anno;
  const isSelected = useMemo(() => selectedKeys.includes(anno.name), [anno, selectedKeys]);
  const isSelectedRelated = useMemo(() => selectedRelatedKeys.includes(anno.name), [anno, selectedRelatedKeys]);
  const {
    annoCommentMap,
    trackIdsVisibleFlag,
    commentHoverInfo,
    setCommentHoverInfo,
    createCommentHoverTimeoutId,
    clearCommentHoverTimeoutId,
  } = useContextStore((state) =>
    pick(state, [
      'annoCommentMap',
      'trackIdsVisibleFlag',
      'commentHoverInfo',
      'setCommentHoverInfo',
      'createCommentHoverTimeoutId',
      'clearCommentHoverTimeoutId',
    ])
  );
  const visibilityStatus = trackIdsVisibleFlag[trackId] ?? VisibleStatus.Show;

  const comment = annoCommentMap[name];

  const commentColor = comment?.status ? COMMENT_STATUS_MAP[comment.status] : undefined;

  // 如果正在编辑漏标时，是不能删除的，但是如果这个标注物在漏标的编辑物中，就可以删除
  const { checkRemoveDuringCommentEdit } = useRemoveAnno();
  const canDelete = checkRemoveDuringCommentEdit();

  const handleSelectAnno = (e: MouseEvent<HTMLElement>) => {
    const key = e.currentTarget.dataset.name as string;
    onSelectAnno(key, e.ctrlKey || e.metaKey);
    commentHoverInfo && setCommentHoverInfo(null);
  };

  const handleRemoveAnno = (e: MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    commentHoverInfo && setCommentHoverInfo(null);
    canDelete && onRemoveAnno(e.currentTarget.dataset.name as string);
  };

  const handleEditAnno = (e: MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    onEditAnno(e.currentTarget.dataset.name as string);
  };

  const handleShowComment = (event: MouseEvent<HTMLElement>) => {
    clearCommentHoverTimeoutId();
    const { top, left } = event.currentTarget.getBoundingClientRect();
    if (!top || !left) return;

    const position = [top, window.innerWidth - left + 16, 'unset', 'unset'];

    if (comment) {
      setCommentHoverInfo({
        anno,
        comment,
        position,
      });
    } else {
      setCommentHoverInfo({
        anno,
        position,
      });
    }
  };

  const handleToggleVisible = (e: MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    // 如果有包含该标注物的组合标注物是隐藏的，就返回
    if (visibilityStatus >= VisibleStatus.DisableMin) {
      messageApi?.warning({
        content: '当前标注物所属的组合已被隐藏，请先恢复显示包含此标注物的组合标注物',
        duration: 5,
      });
      return;
    }

    onToggleVisible(anno, visibilityStatus === VisibleStatus.Hidden ? true : false);
  };

  return (
    <div
      className={`anno-list-item ${isSelectedRelated ? 'anno-selected-related' : ''}`}
      id={`anno-${name}`}
      key={`anno-${name}`}
      data-name={name}
      aria-selected={isSelected}
      aria-hidden={visibilityStatus !== VisibleStatus.Show}
      onClick={handleSelectAnno}
      onMouseEnter={handleShowComment}
      onMouseLeave={createCommentHoverTimeoutId}
    >
      <span className="name">
        {commentColor && (
          <span
            className="comment-status"
            style={{
              borderTopColor: commentColor,
              borderLeftColor: commentColor,
            }}
          ></span>
        )}
        <span className="index">{index + 1}</span>
        <span className="icon">{getAnnoWidgetIcon(anno)}</span>
        <span className="text-wrap">
          <span className="text ellipsis">
            {formatTrackIdDisplayName(anno.trackId) + (attrsStr ? '/' + attrsStr : '')}
          </span>
          {childAnnosCount !== undefined && <span className="text">({childAnnosCount})</span>}
        </span>
        <span className="icon" aria-hidden={visibilityStatus === VisibleStatus.Show}>
          <RiEyeCloseLine />
        </span>
      </span>
      <span className="opt">
        <span
          data-name={name}
          className="icon"
          onClick={handleToggleVisible}
          aria-disabled={visibilityStatus >= VisibleStatus.DisableMin}
        >
          {visibilityStatus === VisibleStatus.Show ? <RiEyeLine /> : <RiEyeCloseLine />}
        </span>
        <span data-name={name} className="icon" onClick={handleRemoveAnno} aria-disabled={!canDelete}>
          <RiDeleteBinLine />
        </span>
        <span data-name={name} className="icon" onClick={handleEditAnno}>
          <RiListUnordered />
        </span>
      </span>
    </div>
  );
};
