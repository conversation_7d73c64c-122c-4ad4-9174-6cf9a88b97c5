import { KvConnectLines } from '@forest/icons';
import { pick } from 'lodash';
import { FC, useEffect, useMemo, useState } from 'react';
import { RiDeleteBinLine, RiFolder2Line } from 'react-icons/ri';

import { ExpandModal, Tree } from '@/components';
import type { DataNode } from '@/components/Tree';
import { SERIES_OPT_SCOPE_MAP } from '@/features/anno/config';
import { useRemoveAnno } from '@/features/anno/hooks';
import { useContextStore } from '@/features/anno/stores';
import { AnnoItemWithAttrsStr } from '@/features/anno/types';
import {
  addAttrsStrToAnno,
  checkDeleteAnnos,
  formatTrackIdDisplayName,
  getAnnoWidgetIcon,
  labelTemplate,
} from '@/features/anno/utils';
import { SeriesOptScope } from '@/types';
import { AnnoCompoundItem, AnnoInstanceItem, IAnnoItem } from '@/utils/annos';

import { formatTreeData } from '../utils';

interface AnnosModalProps {
  compoundChildCountMap?: Record<string, number>;
  onAddAnno: () => void;
  onRemoveAnno: () => void;
  onRemoveAnnoAll: () => void;
  onSelectAnno: (name: string) => void;
  onLine3dConnect: () => void;
  onCreateCenterLine: () => void;
}

export const AnnosModal: FC<AnnosModalProps> = ({
  compoundChildCountMap,
  onAddAnno,
  onRemoveAnno,
  onRemoveAnnoAll,
  onSelectAnno,
  onLine3dConnect,
  onCreateCenterLine,
}) => {
  const {
    selectedAnnos,
    annotations,
    commentMap,
    seriesLinkage,
    selectedComment,
    currentComment,
    labelMap,
    labels,
    setSelectedAnnos,
    setSelectedComment,
  } = useContextStore((state) =>
    pick(state, [
      'selectedAnnos',
      'annotations',
      'commentMap',
      'seriesLinkage',
      'setSelectedAnnos',
      'selectedComment',
      'currentComment',
      'labelMap',
      'labels',
      'setSelectedComment',
    ])
  );

  const [scope, setScope] = useState<SeriesOptScope>(SeriesOptScope.ALL);

  const { checkRemoveDuringCommentEdit } = useRemoveAnno();

  const [showType, showData] = useMemo(() => {
    setScope(SeriesOptScope.ALL);
    if (selectedAnnos.length === 1 && selectedAnnos[0] instanceof AnnoCompoundItem && compoundChildCountMap) {
      const expandedKeys: string[] = [];
      const data = formatTreeData(selectedAnnos[0], annotations, compoundChildCountMap, expandedKeys);

      return ['view-compound', { data, expandedKeys }];
    } else if (selectedAnnos.length > 1) {
      const list: AnnoItemWithAttrsStr[] = [];

      for (let i = 0, len = selectedAnnos.length; i < len; i++) {
        const anno = selectedAnnos[i];
        list.push(addAttrsStrToAnno(anno, labelMap));
      }

      return ['multiple', list];
    }
    return [null, null];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [annotations, selectedAnnos, selectedComment]);

  // 拼接折线逻辑保留：第二条线无法删除就不能拼接
  const findAnnosParent = (annos: IAnnoItem[], annotations: Record<string, IAnnoItem | null>) => {
    const res = new Set<AnnoCompoundItem>();
    annos.forEach((anno) => {
      Object.values(annotations).forEach((item) => {
        if (item instanceof AnnoCompoundItem && item.compound.includes(anno.name)) {
          res.add(item);
        }
      });
    });
    return [...res];
  };

  const checkParentAnnos = (annos: IAnnoItem[], annotations: Record<string, IAnnoItem | null>) => {
    const parents = findAnnosParent(annos, annotations);
    const excludeParents = parents.filter((parent) => annos.every((item) => item.name !== parent.name));
    if (excludeParents.length > 0) {
      return `删除失败，需要先删除 ${excludeParents
        .map((anno) => formatTrackIdDisplayName(anno.trackId))
        .join(',')} 组合`;
    }
  };

  // 是否允许拼接和删除
  const [enableConnect, enableDelete, enableCreate] = useMemo(() => {
    let enableConnect = true,
      enableDelete = true,
      enableCreate = labels?.some((label) => label.name === 'centerline');

    for (let i = 0, l = selectedAnnos.length; i < l; i++) {
      const anno = selectedAnnos[i];
      if (
        enableConnect &&
        (i >= 2 ||
          !(anno instanceof AnnoInstanceItem) ||
          anno.widget.line_type !== 'line' ||
          checkDeleteAnnos([anno], commentMap) ||
          checkParentAnnos([anno], annotations))
      ) {
        enableConnect = false;
      }

      if (enableCreate && (i >= 2 || !(anno instanceof AnnoInstanceItem) || anno.widget.line_type !== 'line')) {
        enableCreate = false;
      }

      // 在漏标批注中且不是当前可以删除的状态
      if (!checkRemoveDuringCommentEdit() && (enableDelete || enableConnect)) {
        enableDelete = false;
        if (i === 1 && enableConnect) {
          enableConnect = false;
        }
      }

      if (!enableConnect && !enableDelete && !enableCreate) break;
    }
    return [enableConnect, enableDelete, enableCreate];

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [annotations, commentMap, selectedAnnos, currentComment]);

  const seriesSettings = useMemo(() => {
    if (!seriesLinkage.isEnabled) return undefined;
    return {
      scope,
      changeScope: (scope: string) => setScope(scope as SeriesOptScope),
      disabled: false,
      scopeOptions: SERIES_OPT_SCOPE_MAP,
    };
  }, [scope, seriesLinkage]);

  // 同步设置范围，在外部调用的时候，就不用关心
  useEffect(() => {
    if (seriesLinkage.isEnabled) {
      seriesLinkage.setSeriesOptScope(scope);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [scope]);

  const handleEmptySelected = () => {
    selectedAnnos.length > 0 && setSelectedAnnos([]);
    selectedComment && setSelectedComment(null);
  };

  if (!showType) {
    return null;
  }

  let title = null,
    footer = null,
    content = null;
  switch (showType) {
    case 'view-compound':
      title =
        formatTrackIdDisplayName(selectedAnnos[0].trackId) + ` (${compoundChildCountMap?.[selectedAnnos[0]?.name]})`;
      footer = (
        <button className="button primary" onClick={onRemoveAnno}>
          删除编组
        </button>
      );
      content = (
        <div className="tree-data">
          <Tree
            // expandedKeys={showData?.expandedKeys}
            onSelected={onSelectAnno}
            data={[
              (
                showData as {
                  data: DataNode;
                  expandedKeys: string[];
                }
              ).data,
            ]}
          />
        </div>
      );
      break;
    case 'multiple':
      title = (
        <>
          已选<strong>{selectedAnnos.length}</strong>项子对象
        </>
      );
      footer = (
        <>
          {enableConnect && (
            <button className="button" onClick={onLine3dConnect}>
              <span>
                <KvConnectLines />
              </span>
              拼接折线
            </button>
          )}
          {enableCreate && (
            <button className="button" onClick={onCreateCenterLine}>
              <span>
                <KvConnectLines />
              </span>
              生成中心线
            </button>
          )}
          {enableDelete && (
            <button className="button" onClick={onRemoveAnnoAll}>
              <span>
                <RiDeleteBinLine />
              </span>
              批量删除
            </button>
          )}
          {labelTemplate.compoundLabels.length > 0 && (
            <button className="button" onClick={onAddAnno}>
              <span>
                <RiFolder2Line />
              </span>
              新建编组
            </button>
          )}
        </>
      );
      content = (showData as AnnoItemWithAttrsStr[]).map((anno, index) => {
        return (
          <div className="annos-model-item" key={`compound-${index}`}>
            <span className="index">{index + 1}</span>
            <span className="icon">{getAnnoWidgetIcon(anno)}</span>
            <span className="text">
              {formatTrackIdDisplayName(anno.trackId) + (anno.attrsStr ? '/' + anno.attrsStr : '')}
            </span>
          </div>
        );
      });
  }

  return (
    <ExpandModal
      title={title}
      onCancel={handleEmptySelected}
      positionStyle={{
        right: 250,
      }}
      scopeSettings={seriesSettings}
      width={340}
      height={240}
      expandedSize={{
        height: 400,
        width: 400,
      }}
      footer={footer}
    >
      {content}
    </ExpandModal>
  );
};
