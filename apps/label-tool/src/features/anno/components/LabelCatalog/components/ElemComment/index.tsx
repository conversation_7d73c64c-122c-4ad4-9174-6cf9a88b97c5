import { KvCompoundClose } from '@forest/icons';
import { pick } from 'lodash';
import { FC, useMemo } from 'react';

import { useContextStore } from '@/features/anno/stores';

import styles from './styles.module.css';

export const ElemComment: FC = () => {
  const { currentPhase, elementCommentMap, dataElementIndex, setCommentModal, deleteComment, createSnapshot } =
    useContextStore((state) =>
      pick(state, [
        'currentPhase',
        'elementCommentMap',
        'dataElementIndex',
        'setCommentModal',
        'deleteComment',
        'createSnapshot',
      ])
    );

  const [comment, showType] = useMemo(() => {
    const comment = elementCommentMap[dataElementIndex];
    if (currentPhase && currentPhase.number > 1 && (!comment || comment.status === 'resolved')) {
      return [null, 'button'];
    } else if (comment && comment.status !== 'resolved') {
      return [comment, 'info'];
    }
    return [null, null];
  }, [currentPhase, dataElementIndex, elementCommentMap]);

  const handleAddComment = () => {
    setCommentModal({
      type: 'add',
      scope: 'element',
      obj_uuids: [],
    });
  };

  const handleCloseComment = () => {
    if (comment) {
      deleteComment(comment.uuid);
      createSnapshot();
    }
  };

  if (!showType) return null;
  return (
    <div className={styles.container}>
      {showType === 'info' && comment ? (
        <div className={styles.comment}>
          <div className={styles.header}>
            <span>当前帧驳回原因</span>
            <span className={styles.icon} onClick={handleCloseComment}>
              <KvCompoundClose />
            </span>
          </div>
          <div className={styles.content}>{comment.content}</div>
        </div>
      ) : (
        <div className={styles.button} onClick={handleAddComment}>
          当前帧驳回
        </div>
      )}
    </div>
  );
};
