import { useEffect, useMemo, useRef, useState } from 'react';

export const useExpandedKeys = (
  labelList: any[],
  defaultOpen: boolean = true
): [string[], (keys: string[]) => void, () => void] => {
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);

  const isMount = useRef(false);

  const defaultList = useMemo(() => labelList.map((val) => val.name), [labelList]);

  const onExpand = (keys: string[]) => {
    setExpandedKeys(keys);
  };

  const onExpandAll = () => {
    setExpandedKeys([...defaultList]);
  };

  useEffect(() => {
    if (!defaultList.length) return;
    if (isMount.current) {
      // 更新
      setExpandedKeys(defaultList);
    } else if (!isMount.current && defaultOpen) {
      // 初始化
      isMount.current = true;
      setExpandedKeys(defaultList);
    }
  }, [defaultOpen, defaultList]);

  return [expandedKeys, onExpand, onExpandAll];
};
