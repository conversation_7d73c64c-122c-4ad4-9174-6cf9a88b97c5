.label-catalog {
  width: 242px;
}
.label-catalog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
}
.label-catalog-container-list {
  flex: 1;
  color: #ffffff;
  overflow: auto;
  background-color: #323f4b;
}

.label-catalog-container .label-list {
  padding: 8px;
  font-size: 12px;
  user-select: none;
}

.label-catalog-container .label-list .label-list-title {
  width: 226px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  padding: 4px;
  background-color: #323f4b;
  height: 28px;
  color: #cbd2d9;
  width: 226px;
}
.label-catalog-container .label-list .label-list-title .text {
  margin-inline-start: 4px;
}

.label-catalog-container .label-list .label-list-title .opt {
  display: inline-flex;
  align-items: center;
}

.label-catalog-container .label-list .label-list-title .icon {
  font-size: 16px;
  line-height: 0;
  margin-inline-start: 4px;
  padding: 2px;
}

.label-catalog-container .label-list .label-list-title .icon:hover,
.label-catalog-container .label-list .label-list-title .icon:active {
  background-color: #cbd2d966;
  border-radius: 2px;
  cursor: pointer;
}

.label-catalog-container .label-list .label-list-content,
.label-catalog-container .label-list .comment-list-content {
  color: #f5f7fa;
}

.label-catalog-container .label-list .comment-status,
.label-catalog-container .comment-list .comment-status {
  position: absolute;
  width: 0;
  height: 0;
  border-bottom: 8px solid transparent;
  border-right: 8px solid transparent;
  border-left: 8px solid red;
  border-top: 8px solid red;
  left: -4px;
  top: -6px;
}

.label-catalog-container .label-list .label-list-item .label,
.label-catalog-container .comment-list-content .label {
  width: 226px;
  height: 28px;
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  padding: 4px;
  border-radius: 2px;
  background-color: #171f26;
  font-size: 12px;
  overflow: hidden;
  position: relative;
}

.label-catalog-container .label-list .label-list-item .label:hover,
.label-catalog-container .comment-list-content .label:hover {
  cursor: pointer;
  border: 1px solid #3aa691;
}
.label-catalog-container .label-list .label-list-item .label[aria-selected='true'] {
  background-color: rgba(58, 166, 145, 0.8);
  border: none;
}

.label-catalog-container .label-list .label-list-item .label .icon,
.label-catalog-container .comment-list-content .label .icon {
  display: none;
  width: 12px;
  text-align: center;
  line-height: 0;
  padding: 3px 0;
  margin-inline-end: 4px;
}

.label-catalog-container .label-list .label-list-item .label:hover .icon,
.label-catalog-container .comment-list-content .label:hover .icon {
  display: inline-block;
  cursor: pointer;
}

.label-catalog-container .label-list .label-list-item .label .text,
.label-catalog-container .comment-list-content .label .text {
  flex: 1;
}

.label-catalog-container .label-list .label-list-item .label .dot {
  content: ' ';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin: 0 4px;
}

.label-catalog-container .comment-list-content .label .missed-icon {
  display: inline-block;
  width: 16px;
  text-align: center;
  line-height: 0;
  margin-inline: 4px;
  font-size: 16px;
}

.label-catalog-container .label-list .label-list-item .label .comment-status,
.label-catalog-container .comment-list-content .label .comment-status {
  top: 0;
  left: 0;
}

.label-catalog-container .label-list .anno-list-item,
.label-catalog-container .comment-list .comment-list-item {
  background-color: #171f2666;
  padding: 4px;
  margin-bottom: 4px;
  height: 28px;
  width: 226px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 2px;
  overflow: hidden;
}
.label-catalog-container .comment-list .comment-list-item[aria-disabled='true'] {
  opacity: 0.5;
  pointer-events: none;
}

.label-catalog-container .label-list .anno-list-item .name,
.label-catalog-container .comment-list .comment-list-item .name {
  display: inline-flex;
  align-items: center;
  position: relative;
  width: 100%;
}
.label-catalog-container .label-list .anno-list-item .index,
.label-catalog-container .comment-list .comment-list-item .index {
  display: inline-block;
  min-width: 12px;
  text-align: center;
  color: #cbd2d9;
  margin-inline-start: 6px;
  margin-inline-end: 2px;
}
.label-catalog-container .label-list .anno-list-item .icon,
.label-catalog-container .comment-list .comment-list-item .icon {
  line-height: 0;
  margin-inline-start: 2px;
  font-size: 16px;
  margin-inline-end: 2px;
}

.label-catalog-container .label-list .anno-list-item .icon[aria-hidden='true'],
.label-catalog-container .label-list .anno-list-item:hover .icon[aria-hidden='false'],
.label-catalog-container .comment-list .comment-list-item .icon[aria-hidden='true'],
.label-catalog-container .comment-list .comment-list-item:hover .icon[aria-hidden='false'] {
  display: none;
}

.label-catalog-container .label-list .anno-list-item .text-wrap,
.label-catalog-container .comment-list .comment-list-item .text-wrap {
  flex: 1;
  display: flex;
  overflow: hidden;
}
.label-catalog-container .label-list .anno-list-item .text,
.label-catalog-container .comment-list .comment-list-item .text {
  margin-inline-start: 2px;
}
.label-catalog-container .label-list .anno-list-item .ellipsis,
.label-catalog-container .comment-list .comment-list-item .ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.label-catalog-container .label-list .anno-list-item .opt,
.label-catalog-container .comment-list .comment-list-item .opt {
  display: none;
  color: #cbd2d9;
}
.label-catalog-container .label-list .anno-list-item .opt .icon,
.label-catalog-container .comment-list .comment-list-item .opt .icon {
  padding: 2px;
}

.label-catalog-container .label-list .anno-list-item .opt .icon[aria-disabled='true'] {
  cursor: not-allowed;
  opacity: 0.4;
}

.label-catalog-container .label-list .anno-list-item:hover,
.label-catalog-container .comment-list .comment-list-item:hover {
  cursor: pointer;
  border: 1px solid #3aa691;
}

.label-catalog-container .label-list .anno-list-item[aria-selected='true'],
.label-catalog-container .comment-list .comment-list-item[aria-selected='true'] {
  background-color: rgba(58, 166, 145, 0.6);
  border: none;
}

.label-catalog-container .label-list .anno-list-item.anno-selected-related {
  box-shadow: 0px 0px 4px 0px rgba(58, 166, 145, 0.8);
}

.label-catalog-container .label-list .anno-list-item[aria-hidden='true'] .name,
.label-catalog-container .label-list .anno-list-item[aria-hidden='true'] .opt,
.label-catalog-container .comment-list .comment-list-item[aria-hidden='true'] .name,
.label-catalog-container .comment-list .comment-list-item[aria-hidden='true'] .opt {
  opacity: 0.4;
}

.label-catalog-container .label-list .anno-list-item:hover .name {
  width: 144px;
}
.label-catalog-container .comment-list .comment-list-item:hover .name {
  width: 192px;
}
.label-catalog-container .label-list .anno-list-item:hover .opt,
.label-catalog-container .comment-list .comment-list-item:hover .opt {
  display: inline-flex;
  align-items: center;
}

.label-catalog-container .label-list .anno-list-item:hover .opt .icon:hover,
.label-catalog-container .label-list .anno-list-item:hover .opt .icon:active,
.label-catalog-container .comment-list .comment-list-item:hover .opt .icon:hover,
.label-catalog-container .comment-list .comment-list-item:hover .opt .icon:active {
  cursor: pointer;
  background-color: #cbd2d966;
  border-radius: 2px;
}

.label-catalog-container .label-list .anno-list-item:hover .opt .icon[aria-disabled='true']:hover,
.label-catalog-container .label-list .anno-list-item:hover .opt .icon[aria-disabled='true']:active,
.label-catalog-container .comment-list .comment-list-item:hover .opt .icon[aria-disabled='true']:hover,
.label-catalog-container .comment-list .comment-list-item:hover .opt .icon[aria-disabled='true']:active {
  cursor: not-allowed;
}

.label-catalog-container .annos-model-item {
  height: 28px;
  display: flex;
  align-items: center;
  padding: 4px;
  margin-bottom: 4px;
  background-color: #171f26;
  border-radius: 2px;
  color: #f5f7fa;
}

.label-catalog-container .annos-model-item.view-comment {
  cursor: pointer;
}

.label-catalog-container .annos-model-item.view-comment:hover {
  border: 1px solid #3aa691;
}
.label-catalog-container .annos-model-item.view-comment:active {
  background-color: rgba(58, 166, 145, 0.6);
  border: none;
}

.label-catalog-container .annos-model-item-empty {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.label-catalog-container .annos-model-item > span {
  margin-inline-start: 4px;
}
.label-catalog-container .annos-model-item .index {
  width: 14px;
  height: 14px;
  border-radius: 2px;
  background: #3aa691;
  font-size: 6px;
  color: #ffffff;
  text-align: center;
  line-height: 14px;
}
.label-catalog-container .annos-model-item .icon {
  font-size: 16px;
  line-height: 0;
}

.label-catalog-container .annos-model-item .text-wrap {
  width: 260px;
  font-size: 12px;
}

.label-catalog-container .annos-model-item .ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.label-catalog-container .button {
  display: inline-flex;
  background-color: #edf2f7;
  color: #1a202c;
  height: 24px;
  align-items: center;
  margin-inline: 4px;
  cursor: pointer;
  border-radius: 4px;
  border: none;
  padding: 0 10px;
}

.label-catalog-container .button:hover,
.label-catalog-container .button:active {
  color: #3aa691;
}

.label-catalog-container .button span {
  margin-inline-end: 2px;
  line-height: 0;
  font-size: 16px;
  color: #52606d99;
}

.label-catalog-container .button:hover span,
.label-catalog-container .button:active span {
  color: #61b8a7;
}

.label-catalog-container .button.primary {
  color: #ffffff;
  background-color: #3aa691;
}

.label-catalog-container .button.primary:hover,
.label-catalog-container .button.primary:active {
  background-color: #61b8a7;
}

.label-catalog-container .divider {
  height: 1px;
  left: 0;
  background-color: #171f26;
  margin: 8px -8px;
}
.label-catalog-container .select-label {
  display: flex;
  border-radius: 2px;
  border: 1px solid #52606d;
  margin-bottom: 4px;
  overflow: hidden;
}
.label-catalog-container .select-label .tag {
  height: 32px;
  line-height: 32px;
  min-width: 41px;
  background: #323f4b;
  color: #f5f7fa;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  border-right: 1px solid #52606d;
  flex-shrink: 0;
  padding-inline: 8px;
}
.label-catalog-container .select-label .ant-formily-item-colon {
  display: none;
}
.label-catalog-container .select-label .selector {
  width: 100%;
}
.label-catalog-container .select-label .ant-input {
  line-height: 22px;
}

/* 打组弹窗 */
.compound-warning-wrapper :global(.ant-message-notice-content) {
  border: 1px solid #52606d;
  color: #f5f7fa;
  height: 48px;
}

.compound-warning-wrapper :global(.ant-message-notice-content .anticon) {
  display: flex;
  justify-content: center;
  align-items: center;
}

.compound-warning {
  display: flex;
  align-items: center;
}

.compound-warning .compound-warning-icon {
  color: #28c89b;
  width: 24px;
  height: 24px;
}
.compound-warning .compound-warning-icon > svg {
  width: 20px;
  height: 20px;
}

.compound-warning .compound-warning-close {
  color: #f5f7fa;
  width: 16px;
  height: 16px;
  margin-left: 16px;
}

.compound-warning .compound-warning-close > svg {
  width: 12px;
  height: 12px;
}

.compound-warning .compound-warning-text {
  margin: 0 16px;
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  width: 208px;
  text-align: left;
}

.compound-warning button {
  border: 1px solid #f5f7fa;
  background-color: transparent;
  border-radius: 4px;
  width: 72px;
  height: 24px;
  font-size: 12px;
  line-height: 20px;
}
