import type { DataNode } from '@/components/Tree';
import { Comment, Label } from '@/types';
import { IAnnoItem } from '@/utils/annos';

import { COMMENT_GREEN, COMMENT_RED, COMMENT_YELLOW } from '../../config';
import { AnnoItemWithAttrsStr } from '../../types';
import { addAttrsStrToAnno, formatTrackIdDisplayName, getAnnoWidgetIcon } from '../../utils';

export const formatTreeData = (
  anno: AnnoItemWithAttrsStr,
  annotations: Record<string, IAnnoItem | null>,
  compoundChildCountMap: Record<string, number>,
  keys: string[],
  prefix: string = 'tree-data-'
): DataNode => {
  const { name, attrsStr = '' } = anno;
  const isCompound = 'compound' in anno;
  keys.push(prefix + name);
  const data: DataNode = {
    title:
      formatTrackIdDisplayName(anno.trackId) + attrsStr + (isCompound ? ` (${compoundChildCountMap[anno.name]})` : ''),
    name: name,
    // 可能就是会有一个标注物是在多个子节点的位置
    key: prefix + name,
    icon: getAnnoWidgetIcon(anno),
    children: isCompound
      ? anno.compound
          .map((val) => annotations[val])
          .filter(Boolean)
          .map((anno) => {
            return formatTreeData(anno!, annotations, compoundChildCountMap, keys, prefix + name + '-');
          })
      : undefined,
  };
  return data;
};

export const formatLabelChildren = (
  list: Pick<IAnnoItem, 'name' | 'trackId'>[],
  annotations: Record<string, IAnnoItem | null>,
  pcdAnnoSet: Set<string>,
  labelMap: Map<Label['name'], Label>
) =>
  list
    .map(({ name }) => annotations[name])
    .filter((val) => {
      if (val && pcdAnnoSet.has(val.name)) return true;
      return false;
    })
    .map((anno) => addAttrsStrToAnno(anno!, labelMap));

export const getLabelCommentStatusColor = (
  list: AnnoItemWithAttrsStr[],
  annoCommentMap: Record<IAnnoItem['name'], Comment>
) => {
  let red = 0,
    green = 0,
    yellow = 0;
  for (let i = 0, len = list.length; i < len; i++) {
    const comment = annoCommentMap[list[i].name];
    if (!comment) continue;

    if (comment.status === 'unresolved') {
      red++;
      break;
    } else if (comment.status === 'pending') {
      yellow++;
    } else {
      green++;
    }
  }
  // 没有批注
  if (red + yellow + green === 0) return;
  // 有一个待 fix 或者新增的批注
  if (red > 0) return COMMENT_RED;
  // 不可能出现黄色和绿色同时的情况，所以此处批注都是黄色或者绿色
  if (green > 0) return COMMENT_GREEN;
  if (yellow > 0) return COMMENT_YELLOW;
};
