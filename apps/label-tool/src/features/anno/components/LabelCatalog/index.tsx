import { pick } from 'lodash';
import { CSSProperties, FC, useEffect, useMemo, useState } from 'react';
import { MathUtils, Vector3 } from 'three';
import { shallow } from 'zustand/shallow';

import { useRemoveAnno } from '@/features/anno/hooks/use-remove-anno';
import { FatLine } from '@/lib/three';
import { useMessage } from '@/provider';
import { WidgetObject } from '@/types';
import { AnnoCompoundItem, AnnoFactory, AnnoInstanceItem, IAnnoItem, Line3d } from '@/utils/annos';

import { MERGE_DOT_THRESHOLD } from '../../config';
import { useContextStore } from '../../stores';
import { labelTemplate } from '../../utils';
import { AttrsModal } from '../AttrsModal';
import { AnnosModal, ElemComment, LabelList } from './components';
import { useExpandedKeys } from './hooks';
import { formatLabelChildren, getLabelCommentStatusColor } from './utils';

import './index.css';

export interface LabelCatalogProps {
  style?: CSSProperties;
}

const SOURCE = 'LabelCatalog';

const shapeLabels = {
  cuboid: '立体框',
  line3d: '折线',
  spline3d: '样条曲线',
  poly3d: '多边形',
  point3d: '点',
};

export const LabelCatalog: FC<LabelCatalogProps> = ({ style }) => {
  const [attrChange, setAttrChange] = useState(false);
  const messageApi = useMessage();
  const {
    editAnno,
    labels,
    annotations,
    labelAnnoMap,
    fileAnnoMap,
    selectedAnnos,
    selectedComment,
    labelStage,
    labelMap,
    annoCommentMap,
    seriesLinkage,
    setDeleteModalAnnos,
    setCurrentLabel,
    setCurrentAnno,
    setEditAnno,
    setSelectedAnnos,
    resetCurrentLabel,
    getAnnoInstanceNames,
  } = useContextStore(
    (state) =>
      pick(state, [
        'editAnno',
        'labels',
        'annotations',
        'labelAnnoMap',
        'fileAnnoMap',
        'annoCommentMap',
        'labelStage',
        'labelMap',
        'seriesLinkage',
        'setDeleteModalAnnos',
        'selectedAnnos',
        'selectedComment',
        'setCurrentLabel',
        'setCurrentAnno',
        'setEditAnno',
        'setSelectedAnnos',
        'resetCurrentLabel',
        'getAnnoInstanceNames',
      ]),
    shallow
  );
  const pcdAnnoSet = useMemo(() => {
    let list: Array<IAnnoItem['name']> = [];
    Object.keys(fileAnnoMap).forEach((name) => {
      if (fileAnnoMap[name] && name.search('pcd') > -1) {
        list = list.concat(fileAnnoMap[name]);
      }
    });
    return new Set(list);
  }, [fileAnnoMap]);

  const [instanceData, compoundData, compoundChildCount] = useMemo(() => {
    const instances = [],
      compounds = [];

    for (let i = 0, len = labels.length; i < len; i++) {
      const label = labels[i];
      const children = labelAnnoMap[label.name]
        ? formatLabelChildren(labelAnnoMap[label.name], annotations, pcdAnnoSet, labelMap)
        : [];
      const commentStatus = getLabelCommentStatusColor(children, annoCommentMap);
      const item = {
        ...label,
        commentStatus,
        children,
      };

      if (label.compound) {
        compounds.push(item);
      } else {
        instances.push(item);
      }
    }

    // 计算 compounds 中的 childCount
    const map: Record<string, number> = {};

    for (const { children } of compounds) {
      for (const item of children) {
        if (item instanceof AnnoCompoundItem) {
          const childAnnos = new Set();
          getAnnoInstanceNames(item).forEach((name) => childAnnos.add(name));
          map[item.name] = childAnnos.size;
        } else {
          break;
        }
      }
    }

    return [instances, compounds, map];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [labels, labelAnnoMap, annotations, pcdAnnoSet, attrChange, annoCommentMap]);

  const annoSummary = useMemo(() => {
    const annoTypeCounts = new Map<string, number>();

    for (const anno of Object.values(annotations)) {
      if (anno instanceof AnnoInstanceItem) {
        const widgetType = AnnoFactory.getWidgetType(anno.widget);
        annoTypeCounts.set(widgetType, (annoTypeCounts.get(widgetType) || 0) + 1);
      }
    }

    const typeDescs = [];
    let totalAnnos = 0;
    for (const [type, count] of annoTypeCounts.entries()) {
      const label = shapeLabels[type as keyof typeof shapeLabels] || type;
      typeDescs.push(`${label}：${count}`);
      totalAnnos += count;
    }

    return `总数量：${totalAnnos}；${typeDescs.join('；')}`;
  }, [annotations]);

  const [instanceSelectedKeys, compoundSelectedKeys] = useMemo(() => {
    const instanceKeys: IAnnoItem['name'][] = [],
      compoundKeys: IAnnoItem['name'][] = [];
    selectedAnnos.forEach((anno) => {
      if ('compound' in anno) {
        compoundKeys.push(anno.name);
      } else {
        instanceKeys.push(anno.name);
      }
    });
    // 编辑标签属性时，对应条目高亮
    if (editAnno?.type === 'label') {
      if ('compound' in editAnno.anno) {
        compoundKeys.push(editAnno.anno.name);
      } else {
        instanceKeys.push(editAnno.anno.name);
      }
    }
    return [instanceKeys, compoundKeys];
  }, [selectedAnnos, editAnno]);

  const compoundSelectedRelatedKeys = useMemo(() => {
    const compoundSelectedRelatedKeys: IAnnoItem['name'][] = [];

    // 条件：选中一个标注物，此时要么 editAnno 类型为 label， 要么 selectedAnnos 只有一个
    if (editAnno?.type === 'label' || selectedAnnos.length === 1) {
      const currAnnoName = editAnno?.anno?.name ?? selectedAnnos[0]?.name;
      for (const name in annotations) {
        const anno = annotations[name];
        // 查找包含当前标注物的组合标注物
        if (anno && 'compound' in anno && anno.compound.includes(currAnnoName)) {
          compoundSelectedRelatedKeys.push(name);
        }
      }
    }

    return compoundSelectedRelatedKeys;
  }, [selectedAnnos, editAnno, annotations]);

  const [instanceExpandedKeys, handleInstanceExpand, handleInstanceExpandAll] = useExpandedKeys(instanceData, true);
  const [compoundExpandedKeys, handleCompoundExpand, handleCompoundExpandAll] = useExpandedKeys(compoundData);

  // 如果 editAnno, selectedAnnos, selectedComment，则对应的 label 展开。此三者中每次只会有一个有值
  useEffect(() => {
    let object = null,
      className: string = '',
      domId: string = '';

    if (editAnno?.type === 'label' || selectedAnnos.length === 1) {
      object = editAnno?.anno ?? selectedAnnos[0];
      className = object.label;
      domId = 'anno-' + object.name;
    } else if (selectedComment) {
      if (selectedComment.scope === 'unspecified') return;
      object = selectedComment;
      className = 'missed';
      domId = 'comment-' + object.uuid;
    }

    if (object && className && domId) {
      // 展开对应的 label
      if (object instanceof AnnoCompoundItem) {
        if (!compoundExpandedKeys.includes(className)) {
          handleCompoundExpand([...compoundExpandedKeys, className]);
        }
      } else {
        if (!instanceExpandedKeys.includes(className)) {
          handleInstanceExpand([...instanceExpandedKeys, className]);
        }
      }

      // 把标注物移动到视野列表中间，此处 setTimeout 是为了让可能隐藏的 label 优先进行展开
      setTimeout(() => {
        const annoDom = document.getElementById(domId),
          offsetParent = annoDom?.offsetParent;
        if (!offsetParent) return;

        const { top, height } = annoDom.getBoundingClientRect(),
          { top: offsetParentTop, bottom } = offsetParent.getBoundingClientRect();

        // 元素在可视区域上面 或者 元素在可视区域的下面
        if (top - offsetParentTop < height || top > bottom - height) {
          annoDom.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editAnno, selectedAnnos, selectedComment]);

  const handleSelectAnnos = (name: IAnnoItem['name'], isMultiple: boolean) => {
    const anno = annotations[name];
    const index = selectedAnnos.findIndex((val) => val.name === name);
    if (anno) {
      if (isMultiple) {
        if (index > -1) {
          selectedAnnos.splice(index, 1);
        } else {
          selectedAnnos.push(anno);
        }
        setSelectedAnnos([...selectedAnnos], SOURCE);
      } else {
        setSelectedAnnos([anno], SOURCE);
      }
    }
  };

  // 在标注物窗口点击实体标注物
  const handleSelectInstanceAnno = (name: string) => {
    const anno = annotations[name];
    // 移到视野中间
    if (anno instanceof AnnoInstanceItem && labelStage) {
      labelStage.currentSight?.moveToObject(anno.name);
    }
  };

  const { handleRemoveAnno: _handleRemoveAnno, handleRemoveAnnos } = useRemoveAnno();

  const handleRemoveAnno = (name: string) => {
    const anno = annotations[name];
    if (!anno) return;
    // 连续帧删除需要弹窗确认
    if (seriesLinkage.isEnabled) {
      // 弹窗，暂不执行后续删除逻辑
      setDeleteModalAnnos([anno]);
      return;
    }
    _handleRemoveAnno(anno, SOURCE);
  };

  const handleRemoveAnnoAll = () => {
    // 连续帧删除需要弹窗确认
    if (seriesLinkage.isEnabled) {
      // 弹窗，暂不执行后续删除逻辑
      setDeleteModalAnnos(selectedAnnos);
      return;
    }
    handleRemoveAnnos(selectedAnnos);
  };

  const handleEditAnno = (name: string) => {
    const anno = annotations[name];
    if (anno) {
      setEditAnno({ anno, type: 'label' });
    }
  };

  const handleAddCompoundAnno = () => {
    const [compound, labelGroup] = selectedAnnos.reduce(
      ([list, group], anno) => {
        list.push(anno.name);
        if (!group[anno.label]) {
          group[anno.label] = 0;
        }
        group[anno.label]++;
        return [list, group];
      },
      [[], {}] as [string[], Record<string, number>]
    );
    // 根据 annoLabel 选择对应的 label
    const label = labelTemplate.matchLabelByCompound(labelGroup);
    if (!label) {
      messageApi?.error('创建失败，未找到匹配的类别');
      return;
    }
    const anno = new AnnoCompoundItem(MathUtils.generateUUID(), label.name, compound, {
      attrs: { ...label.defaultAttrValues },
    });
    setCurrentAnno({ anno, opt: 'add' });
    setEditAnno({ anno, type: 'label' });
  };

  const handleSelectLabel = (name: string) => {
    if (name) {
      setCurrentLabel(name);
    } else {
      resetCurrentLabel();
    }
  };
  const handleUpdateAnno = () => {
    // 如果更新了标签属性，就需要更新列表中的属性展示
    setAttrChange((pre) => !pre);
  };

  const handleConnectLine3d = () => {
    if (
      !labelStage ||
      !labelStage.currentSight ||
      !(selectedAnnos[0] instanceof AnnoInstanceItem) ||
      !(selectedAnnos[1] instanceof AnnoInstanceItem)
    )
      return;

    const anno0 = selectedAnnos[0];
    const anno1 = selectedAnnos[1];

    const line0 = anno0.widget.data;
    const line1 = anno1.widget.data;
    const data = Line3d.connectLines(line0, line1, MERGE_DOT_THRESHOLD);
    anno0.widget.data = data;

    const target = labelStage.currentSight?.getObjectByName(anno0.name);
    if (target) {
      AnnoFactory.updateFromWidget(target, anno0.widget, labelStage.currentSight.getMatrixs()[1]);

      // TO(xiyan): 排查问题：折线只要进过编辑态，第二条线上的点拼接后就会全变成黄色。https://gitlab.rp.konvery.work/dept9/forest/-/issues/14
      AnnoFactory.changeColor(target, target.userData.color);
      handleRemoveAnno(anno1.name);
    }
  };

  const calcCenterLine = (longerLine: number[], anno: FatLine): number[] => {
    const centerLine: number[] = [];
    for (let i = 0; i < longerLine.length; i += 3) {
      const [x0, y0, z0] = [longerLine[i], longerLine[i + 1], longerLine[i + 2]];
      const point = new Vector3();
      Line3d.at(anno, i / (longerLine.length - 3), point);
      const { x, y, z } = point;
      centerLine.push((x0 + x) / 2, (y0 + y) / 2, (z0 + z) / 2);
    }
    return centerLine;
  };

  const createCenterAnno = (centerLine: number[], parent_lines: string[]) => {
    const widget: WidgetObject = {
      data: centerLine,
      line_type: 'centerline',
      name: 'line3d',
      parent_lines,
    };
    const uuid = MathUtils.generateUUID();
    const label = labels.find((l) => l.name === 'centerline');
    if (label) {
      const anno = new AnnoInstanceItem(uuid, label.name, widget, {
        attrs: { ...label.defaultAttrValues },
      });
      setCurrentAnno({ anno, opt: 'add' });
    }
  };

  const createCenterLine = () => {
    if (
      !labelStage ||
      !labelStage.currentSight ||
      !(selectedAnnos[0] instanceof AnnoInstanceItem) ||
      !(selectedAnnos[1] instanceof AnnoInstanceItem)
    )
      return;

    const anno0 = selectedAnnos[0];
    const anno1 = selectedAnnos[1];

    const line0 = anno0.widget.data;
    const line1 = anno1.widget.data;
    const [longerLine, shorterAnno] = line0.length > line1.length ? [line0, anno1] : [line1, anno0];

    const anno = labelStage.currentSight.getObjectByName(shorterAnno.name) as FatLine;
    if (!anno) return;

    const centerLine = calcCenterLine(longerLine, anno);
    createCenterAnno(centerLine, [anno0.name, anno1.name]);
  };

  // 新增 / 编辑 单个 标注物时隐藏标签列表
  if (editAnno?.type === 'widget' && editAnno?.anno instanceof AnnoInstanceItem) return null;
  return (
    <div className="label-catalog" style={style}>
      <div className="label-catalog-container">
        <ElemComment />
        <div className="label-catalog-container-list">
          <LabelList
            title="标注物"
            stickyDirection="top"
            labelTreeData={instanceData}
            onClickLabel={handleSelectLabel}
            expandedKeys={instanceExpandedKeys}
            onExpand={handleInstanceExpand}
            onExpandAll={handleInstanceExpandAll}
            selectedKeys={instanceSelectedKeys}
            selectedRelatedKeys={[]}
            onSelectAnno={handleSelectAnnos}
            onRemoveAnno={handleRemoveAnno}
            onEditAnno={handleEditAnno}
            info={annoSummary}
          />
          <LabelList
            title="标注物组合"
            stickyDirection="bottom"
            labelTreeData={compoundData}
            expandedKeys={compoundExpandedKeys}
            onExpand={handleCompoundExpand}
            onExpandAll={handleCompoundExpandAll}
            selectedKeys={compoundSelectedKeys}
            selectedRelatedKeys={compoundSelectedRelatedKeys}
            childAnnosCountMap={compoundChildCount}
            onSelectAnno={handleSelectAnnos}
            onRemoveAnno={handleRemoveAnno}
            onEditAnno={handleEditAnno}
          />
          <AnnosModal
            compoundChildCountMap={compoundChildCount}
            onAddAnno={handleAddCompoundAnno}
            onRemoveAnno={() => handleRemoveAnno(selectedAnnos[0].name)}
            onRemoveAnnoAll={handleRemoveAnnoAll}
            onSelectAnno={handleSelectInstanceAnno}
            onLine3dConnect={handleConnectLine3d}
            onCreateCenterLine={createCenterLine}
          />
        </div>
        <AttrsModal onUpdateAnno={handleUpdateAnno} />
      </div>
    </div>
  );
};
