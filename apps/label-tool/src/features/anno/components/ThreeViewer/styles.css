.view-container {
  border-radius: 4px;
  overflow: hidden;
  outline: 1px solid #323f4b;
}

.view-manipulator {
  position: relative;
  overflow: hidden;
  color: #ffffff88;
  outline: 0.5px solid #323f4b;
}

.view-manipulator .view-empty {
  height: 100%;
  background-color: #171f26;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.view-manipulator .view-empty span {
  font-size: 14px;
  font-weight: 300;
  line-height: 32px;
}

.view-manipulator .view-svg {
  opacity: 0;
}
.view-manipulator:hover .view-svg {
  opacity: 1;
}

.view-manipulator .view-g:hover {
  color: #ffffff;
}

.view-manipulator .view-label {
  position: absolute;
  pointer-events: none;
  left: 0;
  width: 60px;
  height: 24px;
  background: #323f4b;
  color: #f5f7fa;
  font-size: 12px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-manipulator .view-arrow {
  opacity: 0.5;
}

.view-manipulator .view-opt {
  position: absolute;
  right: 4px;
  bottom: 4px;
  font-size: 16px;
  display: flex;
  align-items: center;
  color: #cbd2d9;
}

.view-manipulator .view-opt .icon {
  display: inline-flex;
  width: 24px;
  height: 24px;
  box-sizing: border-box;
  align-items: center;
  justify-content: center;
  line-height: 0;
  margin-inline-start: 4px;
}
.view-manipulator .view-opt .icon:hover {
  cursor: pointer;
  border-radius: 2px;
  border: 1px solid #cbd2d9;
}

.view-manipulator .highlight {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* 车姿 */
.label-scene-pose-div {
  color: #27cd9d;
}
.label-scene-pose-div-active {
  color: #ffd400;
}
