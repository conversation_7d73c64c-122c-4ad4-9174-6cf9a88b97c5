import { useConstant } from '@forest/hooks';
import { KvAutoFit, KvFlipDirection, KvThreeViewDefault } from '@forest/icons';
import { Tooltip } from 'antd';
import { isEqual, uniqWith } from 'lodash';
import { FC, PointerEvent as ReactPointerEvent, ReactNode, useEffect, useMemo, useRef, useState } from 'react';
import { Vector3 } from 'three';

import emptyPng from '@/assets/three-view-empty.png';
import { OBJECT_EDIT_YELLOW } from '@/config/color';
import { HOTKEY_ANNO_CONFIG, useHotkeyConfigWithScope } from '@/features/hotkey';
import { ThreeView } from '@/utils/render';

import { DIRECTION_ROTATE_TIMES_MAP } from '../../config';
import { LEFT_MOUSE, RADIUS_FACTOR } from './config';
import { useControls } from './use-controls';

import './styles.css';

export type Direction = 'top' | 'bottom' | 'left' | 'right';
export type Vector = { x: number; y: number };

const _position = new Vector3();

const dirVector: Record<Direction, Vector> = {
  left: { x: -1, y: 0 },
  right: { x: 1, y: 0 },
  top: { x: 0, y: -1 },
  bottom: { x: 0, y: 1 },
};
const lineStyle = {
  strokeWidth: 1,
  stroke: 'currentColor',
};
const detectRange = 20;

const calcDegrees = (radian: number) => {
  return (radian * 180) / Math.PI;
};

const shouldMoving = (selectedGroup: string, key: string) =>
  ((selectedGroup === 'left' || selectedGroup === 'right') &&
    (key === 'a' || key === 'd' || key === 'left' || key === 'right')) ||
  ((selectedGroup === 'top' || selectedGroup === 'bottom') &&
    (key === 'w' || key === 's' || key === 'up' || key === 'down'));

export interface ViewProps {
  name: string;
  icon: ReactNode;
  viewWidth: number;
  viewHeight: number;
  boxWidth?: number;
  boxHeight?: number;
  boxPosition?: [number, number, number];
  boxDir?: number;
  isEmpty: boolean;
  showDirBtn?: boolean;
  fitPosition: number[][];
  view?: ThreeView;
  onLineMove: (width: number, height: number, dir: Vector) => void;
  onRotate: (theta: number) => void;
  onDirect: (dir: number) => void;
  onFit: () => void;
  onObjectMove: (x: number, y: number) => void;
}
export const View: FC<ViewProps> = ({
  name,
  icon,
  viewWidth,
  viewHeight,
  boxWidth = 0,
  boxHeight = 0,
  // box 在世界坐标系下的位置
  boxPosition,
  boxDir,
  showDirBtn,
  fitPosition,
  view,
  isEmpty,
  onLineMove,
  onRotate,
  onDirect,
  onFit,
  onObjectMove,
}) => {
  const [sizes, setSizes] = useState<Record<Direction, number>>({ left: 0, right: 0, top: 0, bottom: 0 });
  const [startData, setStartData] = useState([0, 0, 0]);
  const [movingLine, setMovingLine] = useState<Direction | undefined>();
  const [degrees, setDegrees] = useState(0);
  const [move, setMove] = useState<boolean>(false);
  const [startRadian, setStartRadian] = useState<number>();
  const [showRectDir, setShowRectDir] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<Direction | undefined>();

  const lastFocusRef = useRef<HTMLElement | null>(null);
  const keys = useConstant(() => ['edge-up', 'edge-down', 'edge-left', 'edge-right', 'rotate-up', 'rotate-down']);

  const { left, top, right, bottom } = sizes;

  // 三视图的快捷键
  const ref = useHotkeyConfigWithScope(HOTKEY_ANNO_CONFIG, keys, function () {
    const key = arguments[1].keys[0];
    if (selectedGroup) {
      if (shouldMoving(selectedGroup, key)) {
        const isMinus = key === 'w' || key === 'a' || key === 'left' || key === 'up';
        const newSizes = {
          ...sizes,
          [selectedGroup]: isMinus ? sizes[selectedGroup] - 1 * scale : sizes[selectedGroup] + 1 * scale,
        };
        setSizes(newSizes);
        onLineMove(
          (newSizes.right - newSizes.left) / scale,
          (newSizes.bottom - newSizes.top) / scale,
          dirVector[selectedGroup]
        );
      }
    } else {
      if (key === 'w' || key === 'up') {
        onObjectMove(0, -1 / scale);
      } else if (key === 's' || key === 'down') {
        onObjectMove(0, 1 / scale);
      } else if (key === 'a' || key === 'left') {
        onObjectMove(-1 / scale, 0);
      } else if (key === 'd' || key === 'right') {
        onObjectMove(1 / scale, 0);
      } else if (key === 'z') {
        onRotate(-Math.PI / 180);
      } else if (key === 'x') {
        onRotate(Math.PI / 180);
      }
    }
  });

  const { scale, cameraUpdate, handleResetCamera } = useControls(ref, view, { viewWidth, viewHeight, onRotate });

  const [centerOffsetX, centerOffsetY, scaledWidth, scaledHeight, scaledRadius] = useMemo(() => {
    if (!view || !boxPosition) return [viewWidth / 2, viewHeight / 2, boxWidth, boxHeight];
    const [x, y] = _position.fromArray(boxPosition).project(view.camera).toArray();
    const scaledWidth = boxWidth * scale,
      scaledHeight = boxHeight * scale;
    return [
      ((x + 1) / 2) * viewWidth,
      -((y - 1) / 2) * viewHeight,
      scaledWidth,
      scaledHeight,
      (Math.sqrt(scaledWidth ** 2 + scaledHeight ** 2) * RADIUS_FACTOR) / 2,
    ];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [view, boxPosition, viewWidth, viewHeight, boxWidth, boxHeight, scale, cameraUpdate]);

  const updatedFitPosition = useMemo(() => {
    if (!view || !fitPosition.length) return [];
    // 处于棱上、顶点上的临界点会重复出现，需过滤掉。
    return uniqWith(
      fitPosition.map(([x, y, z]) => {
        const [x1, y1] = _position.fromArray([x, y, z]).project(view.camera).toArray();
        return [((x1 + 1) / 2) * viewWidth, -((y1 - 1) / 2) * viewHeight];
      }),
      isEqual
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fitPosition, viewWidth, viewHeight, cameraUpdate]);

  useEffect(() => {
    setSizes({
      left: centerOffsetX - scaledWidth / 2,
      right: centerOffsetX + scaledWidth / 2,
      top: centerOffsetY - scaledHeight / 2,
      bottom: centerOffsetY + scaledHeight / 2,
    });
  }, [centerOffsetX, centerOffsetY, scaledWidth, scaledHeight, boxPosition]);

  const handleUpdateSize = (dir: Direction, x: number, y: number) => {
    switch (dir) {
      case 'left':
        const newLeft = startData[0] + x - startData[1];
        return newLeft >= 0 && newLeft < right ? newLeft : left;
      case 'right':
        const newRight = startData[0] + x - startData[1];
        return newRight <= viewWidth && newRight > left ? newRight : right;
      case 'top':
        const newTop = startData[0] + y - startData[2];
        return newTop >= 0 && newTop < bottom ? newTop : top;
      case 'bottom':
        const newBottom = startData[0] + y - startData[2];
        return newBottom <= viewHeight && newBottom > top ? newBottom : bottom;
      default:
        return 0;
    }
  };

  // 边移动
  const onLineMoveStart = (event: PointerEvent, line: Direction) => {
    if (event.button !== LEFT_MOUSE) return;
    event.stopPropagation();
    event.preventDefault();
    setStartData([sizes[line], event.offsetX, event.offsetY]);
    setMovingLine(line);
  };
  const onLineMoving = (event: ReactPointerEvent) => {
    if (movingLine) {
      const pos = handleUpdateSize(movingLine, event.nativeEvent.offsetX, event.nativeEvent.offsetY);
      setSizes({ ...sizes, [movingLine]: pos });
    }
  };
  const onLineMoveEnd = (event: ReactPointerEvent) => {
    if (movingLine) {
      const pos = handleUpdateSize(movingLine, event.nativeEvent.offsetX, event.nativeEvent.offsetY);
      const {
        left: ul,
        top: ut,
        right: ur,
        bottom: ub,
      }: Record<Direction, number> = {
        ...sizes,
        [movingLine]: pos,
      };
      onLineMove((ur - ul) / scale, (ub - ut) / scale, dirVector[movingLine]);
    }
    setMovingLine(undefined);
  };

  // 移动
  const onObjectMoveStart = (event: ReactPointerEvent) => {
    if (event.button !== LEFT_MOUSE) return;
    event.stopPropagation();
    event.preventDefault();
    setMove(true);
    setStartData([0, event.nativeEvent.offsetX, event.nativeEvent.offsetY]);
  };
  const onObjectMoving = (event: ReactPointerEvent) => {
    if (move) {
      const x = event.nativeEvent.offsetX - startData[1];
      const y = event.nativeEvent.offsetY - startData[2];
      setSizes({
        left: centerOffsetX + x - scaledWidth / 2,
        right: centerOffsetX + x + scaledWidth / 2,
        top: centerOffsetY + y - scaledHeight / 2,
        bottom: centerOffsetY + y + scaledHeight / 2,
      });
    }
  };
  const onObjectMoveEnd = (event: ReactPointerEvent) => {
    if (move) {
      const x = event.nativeEvent.offsetX - startData[1];
      const y = event.nativeEvent.offsetY - startData[2];
      onObjectMove(x / scale, y / scale);
      setMove(false);
    }
  };

  // 旋转
  const onRotateStart = (event: ReactPointerEvent) => {
    if (event.button !== LEFT_MOUSE) return;
    event.stopPropagation();
    event.preventDefault();
    const { offsetX, offsetY } = event.nativeEvent;
    setStartRadian(Math.atan2(offsetY - centerOffsetY, offsetX - centerOffsetX));
    setShowRectDir(true);
  };
  const onRotating = (event: React.PointerEvent) => {
    if (typeof startRadian === 'number') {
      const { offsetX, offsetY } = event.nativeEvent;
      // 计算弧度差值后转为度数
      const deg = calcDegrees(Math.atan2(offsetY - centerOffsetY, offsetX - centerOffsetX) - startRadian);
      setDegrees(deg);
    }
  };
  const onRotateEnd = (event: ReactPointerEvent) => {
    if (typeof startRadian === 'number') {
      const { offsetX, offsetY } = event.nativeEvent;
      onRotate(Math.atan2(offsetY - centerOffsetY, offsetX - centerOffsetX) - startRadian);
    }
    setDegrees(0);
    setStartRadian(undefined);
    setShowRectDir(false);
  };

  const onPointerMove = (event: ReactPointerEvent) => {
    event.stopPropagation();
    event.preventDefault();
    onLineMoving(event);
    onObjectMoving(event);
    onRotating(event);
  };

  const onPointerUp = (event: ReactPointerEvent) => {
    event.stopPropagation();
    event.preventDefault();
    onLineMoveEnd(event);
    onObjectMoveEnd(event);
    onRotateEnd(event);
  };

  const onSelectedGroup = (type: Direction) => {
    setSelectedGroup(type);
    lastFocusRef.current = document.activeElement as HTMLElement;
    ref.current?.focus();
  };

  const onUnSelectedGroup = () => {
    setSelectedGroup(undefined);
    lastFocusRef.current?.focus();
    lastFocusRef.current = null;
  };

  return (
    <div
      className="view-manipulator"
      ref={ref}
      tabIndex={-1}
      style={{
        width: viewWidth,
        height: viewHeight,
      }}
      onPointerMove={onPointerMove}
      onPointerUp={onPointerUp}
      onPointerLeave={(event) => {
        onPointerUp(event);
        lastFocusRef.current?.focus();
        lastFocusRef.current = null;
      }}
      onPointerEnter={() => {
        lastFocusRef.current = document.activeElement as HTMLElement;
        ref.current?.focus();
      }}
    >
      <div className="view-label">
        <span
          style={{
            marginTop: 4,
            marginRight: 4,
          }}
        >
          {icon}
        </span>
        <span>{name}</span>
      </div>
      {isEmpty ? (
        <div className="view-empty">
          <img src={emptyPng} width="25%" alt="无标注物" />
          <span>该帧无当前选中的标注物</span>
        </div>
      ) : (
        <>
          <svg className="view-svg" width="100%" height="100%">
            <g transform={`rotate(${degrees}, ${centerOffsetX}, ${centerOffsetY})`}>
              <g
                className="view-g"
                onPointerDown={(evt) => onLineMoveStart(evt.nativeEvent, 'left')}
                onPointerEnter={() => onSelectedGroup('left')}
                onPointerLeave={onUnSelectedGroup}
              >
                <rect
                  id="line-left-handle"
                  x={left - detectRange / 2}
                  y={top}
                  width={detectRange}
                  height={scaledHeight}
                  fill="#00000000"
                />
                <line id="line-left" x1={left} y1={top} x2={left} y2={bottom} {...lineStyle} />
              </g>

              <g
                className="view-g"
                onPointerDown={(evt) => onLineMoveStart(evt.nativeEvent, 'top')}
                onPointerEnter={() => onSelectedGroup('top')}
                onPointerLeave={onUnSelectedGroup}
              >
                <rect
                  id="line-top-handle"
                  x={left}
                  y={top - detectRange / 2}
                  width={scaledWidth}
                  height={detectRange}
                  fill="#00000000"
                />
                <line id="line-top" x1={left} y1={top} x2={right} y2={top} {...lineStyle} />
              </g>

              <g
                className="view-g"
                onPointerDown={(evt) => onLineMoveStart(evt.nativeEvent, 'right')}
                onPointerEnter={() => onSelectedGroup('right')}
                onPointerLeave={onUnSelectedGroup}
              >
                <rect
                  id="line-right-handle"
                  x={right - detectRange / 2}
                  y={top}
                  width={detectRange}
                  height={scaledHeight}
                  fill="#00000000"
                />
                <line id="line-right" x1={right} y1={top} x2={right} y2={bottom} {...lineStyle} />
              </g>

              <g
                className="view-g"
                onPointerDown={(evt) => onLineMoveStart(evt.nativeEvent, 'bottom')}
                onPointerEnter={() => onSelectedGroup('bottom')}
                onPointerLeave={onUnSelectedGroup}
              >
                <rect
                  id="line-bottom-handle"
                  x={left}
                  y={bottom - detectRange / 2}
                  width={scaledWidth}
                  height={detectRange}
                  fill="#00000000"
                />
                <line id="line-bottom" x1={left} y1={bottom} x2={right} y2={bottom} {...lineStyle} />
              </g>

              {/* 箭头 */}
              <g className="view-arrow" style={{ visibility: boxDir === 1 && showRectDir ? 'visible' : 'hidden' }}>
                <line
                  x1={centerOffsetX}
                  y1={centerOffsetY}
                  x2={centerOffsetX + 90 * scale}
                  y2={centerOffsetY}
                  stroke={OBJECT_EDIT_YELLOW}
                  strokeDasharray={'5,5'}
                  strokeWidth={1}
                />
                <polygon
                  points={`${centerOffsetX + 90 * scale}, ${centerOffsetY - 3 * scale},
                      ${centerOffsetX + 98 * scale}, ${centerOffsetY},
                      ${centerOffsetX + 90 * scale}, ${centerOffsetY + 3 * scale}`}
                  fill={OBJECT_EDIT_YELLOW}
                />
              </g>

              <g className="view-g" onPointerDown={onRotateStart}>
                <circle
                  cx={centerOffsetX}
                  cy={centerOffsetY}
                  r={scaledRadius}
                  stroke="#00000000"
                  strokeWidth={detectRange}
                  fill="none"
                ></circle>
                <circle
                  cx={centerOffsetX}
                  cy={centerOffsetY}
                  r={scaledRadius}
                  stroke="currentColor"
                  fill="none"
                ></circle>
              </g>
              <g className="view-g" onPointerDown={onObjectMoveStart}>
                <polygon
                  points={`${centerOffsetX},${centerOffsetY - 8 * scale - detectRange / 2} ${
                    centerOffsetX + 8 * scale + detectRange / 2
                  },${centerOffsetY} ${centerOffsetX},${centerOffsetY + 8 * scale + detectRange / 2} ${
                    centerOffsetX - 8 * scale - detectRange / 2
                  },${centerOffsetY}`}
                  fill="#00000000"
                />
                <polygon
                  points={`${centerOffsetX},${centerOffsetY - 8 * scale} ${
                    centerOffsetX + 8 * scale
                  },${centerOffsetY} ${centerOffsetX},${centerOffsetY + 8 * scale} ${
                    centerOffsetX - 8 * scale
                  },${centerOffsetY}`}
                  fill="currentColor"
                />
              </g>
            </g>
          </svg>
          <div
            className="view-opt"
            style={{ top: '4px', bottom: 'auto', visibility: showDirBtn ? 'visible' : 'hidden' }}
          >
            <Tooltip title="修改车头方向" placement="bottomRight">
              <span className="icon" onClick={() => onDirect(DIRECTION_ROTATE_TIMES_MAP['left'])}>
                <KvFlipDirection />
              </span>
            </Tooltip>
          </div>

          <div className="view-opt">
            <Tooltip title="回到默认视图" placement="top">
              <span className="icon" onClick={handleResetCamera}>
                <KvThreeViewDefault />
              </span>
            </Tooltip>
            <Tooltip title="贴合" placement="top">
              <span className="icon" onClick={onFit}>
                <KvAutoFit onClick={onFit} />
              </span>
            </Tooltip>
          </div>
        </>
      )}

      {updatedFitPosition.length !== 0 && (
        <div className="highlight">
          <svg width="100%" height="100%">
            {updatedFitPosition.map(([x, y], index) => (
              <circle key={index} r="1.5" fill="white" cx={x} cy={y} />
            ))}
          </svg>
        </div>
      )}
    </div>
  );
};
