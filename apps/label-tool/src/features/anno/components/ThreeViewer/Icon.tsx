export const TopViewIcon = () => (
  <svg width="15" height="13" viewBox="0 0 15 13" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M13.9973 0.332813C13.9957 0.328125 13.9973 0.325 13.9957 0.320312C13.9957 0.315625 13.9941 0.310937 13.9926 0.30625C13.991 0.301562 13.991 0.298438 13.991 0.295312C13.991 0.292188 13.9879 0.284375 13.9879 0.279688C13.9863 0.275 13.9863 0.273438 13.9863 0.271875C13.9832 0.264063 13.9816 0.25625 13.9785 0.248438L13.9691 0.225C13.9676 0.221875 13.966 0.21875 13.9645 0.217188C13.9629 0.215625 13.9598 0.207812 13.9566 0.203125L13.9504 0.19375C13.9473 0.190625 13.9457 0.185937 13.9426 0.182812C13.9395 0.179687 13.9379 0.176562 13.9363 0.173437C13.9348 0.170312 13.9316 0.167188 13.9285 0.1625C13.9254 0.159375 13.9238 0.15625 13.9207 0.153125C13.9176 0.15 13.916 0.146875 13.9129 0.14375L13.9051 0.135937L13.8957 0.126562L13.8879 0.11875C13.8848 0.115625 13.8816 0.1125 13.877 0.109375L13.8691 0.103125L13.8582 0.0953125L13.8488 0.0890624L13.8379 0.0812499L13.8285 0.075L13.8176 0.06875L13.8066 0.0625L13.802 0.0609375L13.7941 0.0578125L13.7832 0.053125L13.7707 0.0484375C13.766 0.046875 13.7629 0.0453125 13.7598 0.0453125L13.7473 0.0421875L13.7363 0.0390625L13.7223 0.0359375L13.7113 0.034375C13.7066 0.034375 13.7004 0.0328125 13.6957 0.0328125C13.691 0.0328125 13.6895 0.0328125 13.6863 0.03125H5.14727C5.0707 0.03125 4.9957 0.0578125 4.93633 0.10625L0.126953 3.98125L0.125391 3.98281L0.119141 3.98906L0.103516 4.00312L0.0957031 4.01094C0.0910156 4.01562 0.0863281 4.02031 0.0816406 4.02656C0.0800781 4.02969 0.0769532 4.03125 0.0753907 4.03437C0.0707032 4.04063 0.0660156 4.04531 0.0628906 4.05156C0.0613282 4.05312 0.0597656 4.05625 0.0582031 4.05781C0.0535156 4.06562 0.0472656 4.07344 0.0425781 4.08281L0.0378907 4.09219C0.0347657 4.09844 0.0316407 4.10312 0.0300782 4.10937C0.0285157 4.11562 0.0269531 4.11719 0.0253906 4.12187C0.0238281 4.12656 0.0207031 4.13281 0.0191406 4.1375C0.0175781 4.14219 0.0160157 4.14531 0.0144532 4.15C0.0128907 4.15625 0.0113281 4.16406 0.00976562 4.17031C0.00976562 4.17344 0.00820315 4.17656 0.00820315 4.17969L0.0035156 4.20781V4.21719C0.0035156 4.225 0.00195312 4.23281 0.00195312 4.24062V12.6391C0.00195312 12.6484 0.0019531 12.6578 0.0035156 12.6672V12.6703C0.0050781 12.6797 0.00664065 12.6906 0.00820315 12.7C0.00820315 12.7047 0.0097656 12.7078 0.0113281 12.7125C0.0128906 12.7172 0.0144531 12.7234 0.0160156 12.7297C0.0175781 12.7359 0.0191407 12.7391 0.0207032 12.7437C0.0207032 12.7453 0.0222657 12.7484 0.0222657 12.75C0.0222657 12.7516 0.0253906 12.7562 0.0253906 12.7594C0.0253906 12.7625 0.0285157 12.7672 0.0300782 12.7719C0.0332032 12.7781 0.0363281 12.7844 0.0394531 12.7922L0.0441407 12.8016C0.0488282 12.8094 0.0519531 12.8172 0.0566406 12.8234C0.0582031 12.8266 0.0597656 12.8281 0.0628906 12.8312C0.0675782 12.8375 0.0707032 12.8422 0.0753907 12.8484L0.0816406 12.8562C0.0863281 12.8609 0.0910156 12.8672 0.0957031 12.8719L0.101953 12.8781C0.109766 12.8844 0.116016 12.8922 0.123828 12.8984L0.125391 12.9C0.131641 12.9062 0.139453 12.9109 0.147266 12.9156L0.156641 12.9219L0.172266 12.9312L0.183203 12.9375C0.187891 12.9406 0.194141 12.9422 0.198828 12.9453L0.209766 12.95C0.217578 12.9531 0.226953 12.9562 0.236328 12.9594C0.239453 12.9609 0.242578 12.9609 0.245703 12.9625L0.264453 12.9672L0.275391 12.9687C0.281641 12.9703 0.287891 12.9719 0.295703 12.9719L0.306641 12.9734C0.316016 12.975 0.326953 12.975 0.337891 12.975H9.25195C9.3707 12.975 9.48008 12.9125 9.54102 12.8109L13.866 9.03125L13.8832 9.01562C13.8895 9.01094 13.8941 9.00469 13.9004 9L13.9051 8.99531C13.9098 8.99062 13.9129 8.98594 13.9176 8.98125L13.9207 8.97812C13.9254 8.97187 13.9301 8.96719 13.9348 8.96094L13.9395 8.95625L13.9488 8.94219L13.952 8.93594C13.9551 8.92969 13.9598 8.92344 13.9629 8.91719C13.9629 8.91562 13.9645 8.91406 13.9645 8.9125C13.9676 8.90781 13.9691 8.90156 13.9723 8.89687C13.9738 8.89531 13.9738 8.89219 13.9754 8.89062C13.9785 8.88437 13.9801 8.87812 13.9832 8.87031V8.86875C13.9863 8.86094 13.9879 8.85469 13.9895 8.84687L13.991 8.84062C13.9926 8.83437 13.9941 8.82969 13.9941 8.82344L13.9957 8.81875C13.9973 8.81094 13.9973 8.80469 13.9988 8.79687V8.79219C13.9988 8.78594 13.9988 8.78125 14.0004 8.775V0.367187H14.002V0.359375V0.346875C13.9973 0.340625 13.9973 0.3375 13.9973 0.332813ZM0.764453 4.57812L4.85039 4.56719V8.56562L0.741016 11.8766L0.764453 4.57812ZM8.89102 12.2734L1.35508 12.2937L5.30508 9.09219H8.90195L8.89102 12.2734ZM8.90352 8.42031H5.5207V4.56562L8.91445 4.55625L8.90352 8.42031ZM9.58789 11.8687V9.09219H12.766L9.58789 11.8687ZM13.3254 8.42031H9.58789V4.37187L13.3254 1.10625V8.42031Z"
      fill="#8C8C8C"
    />
  </svg>
);

export const SideViewIcon = () => (
  <svg width="15" height="13" viewBox="0 0 15 13" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M13.9973 0.332813C13.9957 0.328125 13.9973 0.325 13.9957 0.320312C13.9957 0.315625 13.9941 0.310937 13.9926 0.30625C13.991 0.301562 13.991 0.298438 13.991 0.295313C13.991 0.292188 13.9879 0.284375 13.9879 0.279688C13.9863 0.275 13.9863 0.273438 13.9863 0.271875C13.9832 0.264063 13.9816 0.25625 13.9785 0.248438L13.9691 0.225C13.9676 0.221875 13.966 0.21875 13.9645 0.217188C13.9629 0.215625 13.9598 0.207813 13.9566 0.203125L13.9504 0.19375C13.9473 0.190625 13.9457 0.185937 13.9426 0.182812C13.9395 0.179687 13.9379 0.176562 13.9363 0.173437C13.9348 0.170312 13.9316 0.167188 13.9285 0.1625C13.9254 0.159375 13.9238 0.15625 13.9207 0.153125C13.9176 0.15 13.916 0.146875 13.9129 0.14375L13.9051 0.135937L13.8957 0.126562L13.8879 0.11875C13.8848 0.115625 13.8816 0.1125 13.877 0.109375L13.8691 0.103125L13.8582 0.0953125L13.8488 0.0890625L13.8379 0.08125L13.8285 0.075L13.8176 0.06875L13.8066 0.0625L13.802 0.0609375L13.7941 0.0578125L13.7832 0.053125L13.7707 0.0484375C13.766 0.046875 13.7629 0.0453125 13.7598 0.0453125L13.7473 0.0421875L13.7363 0.0390625L13.7223 0.0359375L13.7113 0.034375C13.7066 0.034375 13.7004 0.0328125 13.6957 0.0328125C13.691 0.0328125 13.6895 0.0328125 13.6863 0.03125H5.14727C5.0707 0.03125 4.9957 0.0578125 4.93633 0.10625L0.126953 3.98125L0.125391 3.98281L0.119141 3.98906L0.103516 4.00313L0.0957031 4.01094C0.0910156 4.01563 0.0863281 4.02031 0.0816406 4.02656C0.0800781 4.02969 0.0769532 4.03125 0.0753907 4.03438C0.0707032 4.04063 0.0660156 4.04531 0.0628906 4.05156C0.0613282 4.05312 0.0597656 4.05625 0.0582031 4.05781C0.0535156 4.06563 0.0472656 4.07344 0.0425781 4.08281L0.0378907 4.09219C0.0347657 4.09844 0.0316407 4.10313 0.0300782 4.10938C0.0285157 4.11562 0.0269531 4.11719 0.0253906 4.12187C0.0238281 4.12656 0.0207031 4.13281 0.0191406 4.1375C0.0175781 4.14219 0.0160157 4.14531 0.0144532 4.15C0.0128907 4.15625 0.0113281 4.16406 0.00976562 4.17031C0.00976562 4.17344 0.00820315 4.17656 0.00820315 4.17969L0.0035156 4.20781V4.21719C0.0035156 4.225 0.00195312 4.23281 0.00195312 4.24062V12.6391C0.00195312 12.6484 0.0019531 12.6578 0.0035156 12.6672V12.6703C0.0050781 12.6797 0.00664065 12.6906 0.00820315 12.7C0.00820315 12.7047 0.0097656 12.7078 0.0113281 12.7125C0.0128906 12.7172 0.0144531 12.7234 0.0160156 12.7297C0.0175781 12.7359 0.0191407 12.7391 0.0207032 12.7437C0.0207032 12.7453 0.0222657 12.7484 0.0222657 12.75C0.0222657 12.7516 0.0253906 12.7562 0.0253906 12.7594C0.0253906 12.7625 0.0285157 12.7672 0.0300782 12.7719C0.0332032 12.7781 0.0363281 12.7844 0.0394531 12.7922L0.0441407 12.8016C0.0488282 12.8094 0.0519531 12.8172 0.0566406 12.8234C0.0582031 12.8266 0.0597656 12.8281 0.0628906 12.8313C0.0675782 12.8375 0.0707032 12.8422 0.0753907 12.8484L0.0816406 12.8562C0.0863281 12.8609 0.0910156 12.8672 0.0957031 12.8719L0.101953 12.8781C0.109766 12.8844 0.116016 12.8922 0.123828 12.8984L0.125391 12.9C0.131641 12.9062 0.139453 12.9109 0.147266 12.9156L0.156641 12.9219L0.172266 12.9312L0.183203 12.9375C0.187891 12.9406 0.194141 12.9422 0.198828 12.9453L0.209766 12.95C0.217578 12.9531 0.226953 12.9563 0.236328 12.9594C0.239453 12.9609 0.242578 12.9609 0.245703 12.9625L0.264453 12.9672L0.275391 12.9688C0.281641 12.9703 0.287891 12.9719 0.295703 12.9719L0.306641 12.9734C0.316016 12.975 0.326953 12.975 0.337891 12.975H9.25195C9.3707 12.975 9.48008 12.9125 9.54102 12.8109L13.866 9.03125L13.8832 9.01562C13.8895 9.01094 13.8941 9.00469 13.9004 9L13.9051 8.99531C13.9098 8.99063 13.9129 8.98594 13.9176 8.98125L13.9207 8.97812C13.9254 8.97187 13.9301 8.96719 13.9348 8.96094L13.9395 8.95625L13.9488 8.94219L13.952 8.93594C13.9551 8.92969 13.9598 8.92344 13.9629 8.91719C13.9629 8.91563 13.9645 8.91406 13.9645 8.9125C13.9676 8.90781 13.9691 8.90156 13.9723 8.89688C13.9738 8.89531 13.9738 8.89219 13.9754 8.89062C13.9785 8.88437 13.9801 8.87813 13.9832 8.87031V8.86875C13.9863 8.86094 13.9879 8.85469 13.9895 8.84688L13.991 8.84062C13.9926 8.83437 13.9941 8.82969 13.9941 8.82344L13.9957 8.81875C13.9973 8.81094 13.9973 8.80469 13.9988 8.79688V8.79219C13.9988 8.78594 13.9988 8.78125 14.0004 8.775V0.367188H14.002V0.359375V0.346875C13.9973 0.340625 13.9973 0.3375 13.9973 0.332813ZM4.84883 1.06875V3.89531L1.32383 3.90469L4.84883 1.06875ZM0.764453 4.57812L4.85039 4.56719V8.56563L0.741016 11.8766L0.764453 4.57812ZM8.89102 12.2734L1.35508 12.2937L5.30508 9.09219H8.90195L8.89102 12.2734ZM8.90352 8.42031H5.5207V4.56563L8.91445 4.55625L8.90352 8.42031ZM5.5207 3.89375V0.703125H12.7613L9.09727 3.88281L5.5207 3.89375Z"
      fill="#8C8C8C"
    />
  </svg>
);

export const FrontViewIcon = () => (
  <svg width="15" height="13" viewBox="0 0 15 13" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M13.9973 0.332813C13.9957 0.328125 13.9973 0.325 13.9957 0.320312C13.9957 0.315625 13.9941 0.310937 13.9926 0.30625C13.991 0.301562 13.991 0.298438 13.991 0.295313C13.991 0.292188 13.9879 0.284375 13.9879 0.279688C13.9863 0.275 13.9863 0.273438 13.9863 0.271875C13.9832 0.264063 13.9816 0.25625 13.9785 0.248438L13.9691 0.225C13.9676 0.221875 13.966 0.21875 13.9645 0.217188C13.9629 0.215625 13.9598 0.207813 13.9566 0.203125L13.9504 0.19375C13.9473 0.190625 13.9457 0.185937 13.9426 0.182812C13.9395 0.179687 13.9379 0.176562 13.9363 0.173437C13.9348 0.170312 13.9316 0.167188 13.9285 0.1625C13.9254 0.159375 13.9238 0.15625 13.9207 0.153125C13.9176 0.15 13.916 0.146875 13.9129 0.14375L13.9051 0.135937L13.8957 0.126562L13.8879 0.11875C13.8848 0.115625 13.8816 0.1125 13.877 0.109375L13.8691 0.103125L13.8582 0.0953125L13.8488 0.0890625L13.8379 0.08125L13.8285 0.075L13.8176 0.06875L13.8066 0.0625L13.802 0.0609375L13.7941 0.0578125L13.7832 0.053125L13.7707 0.0484375C13.766 0.046875 13.7629 0.0453125 13.7598 0.0453125L13.7473 0.0421875L13.7363 0.0390625L13.7223 0.0359375L13.7113 0.034375C13.7066 0.034375 13.7004 0.0328125 13.6957 0.0328125C13.691 0.0328125 13.6895 0.0328125 13.6863 0.03125H5.14727C5.0707 0.03125 4.9957 0.0578125 4.93633 0.10625L0.126953 3.98125L0.125391 3.98281L0.119141 3.98906L0.103516 4.00313L0.0957031 4.01094C0.0910156 4.01563 0.0863281 4.02031 0.0816406 4.02656C0.0800781 4.02969 0.0769532 4.03125 0.0753907 4.03438C0.0707032 4.04063 0.0660156 4.04531 0.0628906 4.05156C0.0613282 4.05312 0.0597656 4.05625 0.0582031 4.05781C0.0535156 4.06563 0.0472656 4.07344 0.0425781 4.08281L0.0378907 4.09219C0.0347657 4.09844 0.0316407 4.10313 0.0300782 4.10938C0.0285157 4.11562 0.0269531 4.11719 0.0253906 4.12187C0.0238281 4.12656 0.0207031 4.13281 0.0191406 4.1375C0.0175781 4.14219 0.0160157 4.14531 0.0144532 4.15C0.0128907 4.15625 0.0113281 4.16406 0.00976562 4.17031C0.00976562 4.17344 0.00820315 4.17656 0.00820315 4.17969L0.0035156 4.20781V4.21719C0.0035156 4.225 0.00195312 4.23281 0.00195312 4.24062V12.6391C0.00195312 12.6484 0.0019531 12.6578 0.0035156 12.6672V12.6703C0.0050781 12.6797 0.00664065 12.6906 0.00820315 12.7C0.00820315 12.7047 0.0097656 12.7078 0.0113281 12.7125C0.0128906 12.7172 0.0144531 12.7234 0.0160156 12.7297C0.0175781 12.7359 0.0191407 12.7391 0.0207032 12.7437C0.0207032 12.7453 0.0222657 12.7484 0.0222657 12.75C0.0222657 12.7516 0.0253906 12.7562 0.0253906 12.7594C0.0253906 12.7625 0.0285157 12.7672 0.0300782 12.7719C0.0332032 12.7781 0.0363281 12.7844 0.0394531 12.7922L0.0441407 12.8016C0.0488282 12.8094 0.0519531 12.8172 0.0566406 12.8234C0.0582031 12.8266 0.0597656 12.8281 0.0628906 12.8313C0.0675782 12.8375 0.0707032 12.8422 0.0753907 12.8484L0.0816406 12.8562C0.0863281 12.8609 0.0910156 12.8672 0.0957031 12.8719L0.101953 12.8781C0.109766 12.8844 0.116016 12.8922 0.123828 12.8984L0.125391 12.9C0.131641 12.9062 0.139453 12.9109 0.147266 12.9156L0.156641 12.9219L0.172266 12.9312L0.183203 12.9375C0.187891 12.9406 0.194141 12.9422 0.198828 12.9453L0.209766 12.95C0.217578 12.9531 0.226953 12.9563 0.236328 12.9594C0.239453 12.9609 0.242578 12.9609 0.245703 12.9625L0.264453 12.9672L0.275391 12.9688C0.281641 12.9703 0.287891 12.9719 0.295703 12.9719L0.306641 12.9734C0.316016 12.975 0.326953 12.975 0.337891 12.975H9.25195C9.3707 12.975 9.48008 12.9125 9.54102 12.8109L13.866 9.03125L13.8832 9.01562C13.8895 9.01094 13.8941 9.00469 13.9004 9L13.9051 8.99531C13.9098 8.99063 13.9129 8.98594 13.9176 8.98125L13.9207 8.97812C13.9254 8.97187 13.9301 8.96719 13.9348 8.96094L13.9395 8.95625L13.9488 8.94219L13.952 8.93594C13.9551 8.92969 13.9598 8.92344 13.9629 8.91719C13.9629 8.91563 13.9645 8.91406 13.9645 8.9125C13.9676 8.90781 13.9691 8.90156 13.9723 8.89688C13.9738 8.89531 13.9738 8.89219 13.9754 8.89062C13.9785 8.88437 13.9801 8.87813 13.9832 8.87031V8.86875C13.9863 8.86094 13.9879 8.85469 13.9895 8.84688L13.991 8.84062C13.9926 8.83437 13.9941 8.82969 13.9941 8.82344L13.9957 8.81875C13.9973 8.81094 13.9973 8.80469 13.9988 8.79688V8.79219C13.9988 8.78594 13.9988 8.78125 14.0004 8.775V0.367188H14.002V0.359375V0.346875C13.9973 0.340625 13.9973 0.3375 13.9973 0.332813ZM4.84883 1.06875V3.89531L1.32383 3.90469L4.84883 1.06875ZM9.09727 3.88281L5.5207 3.89219V0.703125H12.7613L9.09727 3.88281ZM9.58789 11.8687V9.09219H12.766L9.58789 11.8687ZM13.3254 8.42031H9.58789V4.37187L13.3254 1.10625V8.42031Z"
      fill="#8C8C8C"
    />
  </svg>
);
