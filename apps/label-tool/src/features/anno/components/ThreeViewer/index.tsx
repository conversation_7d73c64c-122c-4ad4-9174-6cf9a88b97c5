/* eslint-disable react-hooks/exhaustive-deps */
import { pick } from 'lodash';
import { CSSProperties, FC, ReactNode, useEffect, useMemo, useState } from 'react';
import { BufferGeometry, Mesh, MeshBasicMaterial, Object3D } from 'three';
import { shallow } from 'zustand/shallow';

import { useContextStore } from '@/features/anno/stores';
import { HOTKEY_ANNO_CONFIG, useHotkeyConfigWithScope } from '@/features/hotkey';
import { selectionPoints } from '@/lib/three/SelectionPoints';
import { AnnoFactory, AnnoInstanceItem } from '@/utils/annos';
import { ThreeViewType, ViewData } from '@/utils/render';

import { THREE_VIEW_RIGHT } from '../../config';
import { ClipboardContentType } from '../LabelScene/hooks';
import { FrontViewIcon, SideViewIcon, TopViewIcon } from './Icon';
import { Vector, <PERSON> } from './Viewer';

const ThreeViewMap: Record<
  ThreeViewType,
  {
    name: string;
    icon: ReactNode;
  }
> = {
  top: {
    name: '俯视',
    icon: <TopViewIcon />,
  },
  side: {
    name: '侧视',
    icon: <SideViewIcon />,
  },
  front: {
    name: '前视',
    icon: <FrontViewIcon />,
  },
};

export interface ThreeViewerProps {
  width: number;
  height: number;
  clipboard: ClipboardContentType | null;
  subViewWidthRatio?: number;
  subViewHeightRatio?: number;
  style?: CSSProperties;
}
export const ThreeViewer: FC<ThreeViewerProps> = ({
  width: propWidth,
  height: propHeight,
  clipboard,
  subViewWidthRatio = 1,
  subViewHeightRatio = 0.333,
  style: propStyle,
}) => {
  const [boxData, setBoxData] = useState<ViewData[]>([]);
  const [boxViewSizes, setBoxViewSizes] = useState<
    Array<
      Omit<ViewData, 'fitPoints'> & {
        name: string;
        icon: ReactNode;
        fitPosition: number[][];
      }
    >
  >([]);

  const { currentAnno, editAnno, labelStage, seriesLinkage, setCurrentAnno } = useContextStore(
    (state) => pick(state, ['currentAnno', 'editAnno', 'labelStage', 'setCurrentAnno', 'seriesLinkage']),
    shallow
  );

  const viewsHandler = labelStage?.currentSight?.subViews;

  const { subViewWidth, subViewHeight, subViewAspect } = useMemo(() => {
    const subViewWidth = propWidth * subViewWidthRatio;
    const subViewHeight = propHeight * subViewHeightRatio;
    const subViewAspect = subViewWidth / subViewHeight;

    return { subViewWidth, subViewHeight, subViewAspect };
  }, [propWidth, propHeight, subViewWidthRatio, subViewHeightRatio]);

  useEffect(() => {
    if (
      editAnno?.type === 'widget' &&
      editAnno.anno instanceof AnnoInstanceItem &&
      editAnno.anno.widget.name === 'cuboid' &&
      labelStage?.currentSight &&
      viewsHandler
    ) {
      const cuboid = labelStage.currentSight.getObjectByName(editAnno.anno.name) as Mesh<
        BufferGeometry,
        MeshBasicMaterial
      >;
      if (cuboid) {
        viewsHandler.isShow = true;
        // 限制标签的展示区域，避免标签遮挡三视图
        labelStage.updateLabelDisplayArea(labelStage.width - subViewWidth - THREE_VIEW_RIGHT, labelStage.height);
        setBoxData(viewsHandler.focusOnBox(cuboid));
      } else {
        viewsHandler.isShow = false;
        labelStage.updateLabelDisplayArea(labelStage.width, labelStage.height);
        setBoxData([]);
      }
    } else {
      if (viewsHandler) {
        viewsHandler.isShow = false;
        labelStage.updateLabelDisplayArea(labelStage.width, labelStage.height);
      }
      setBoxData([]);
    }
    // 三视图编辑会在 currentAnno 更新数据，因此 currentAnno 变化要更新三视图
  }, [currentAnno, editAnno, labelStage, propWidth, propHeight]);

  useEffect(() => {
    if (boxData.length === 3) {
      const sizes = boxData.map(({ type, zoom, width, height, dir, fitPosition, position }) => {
        let renderWidth = width;
        let renderHeight = height;
        if (width / height > subViewAspect) {
          renderWidth = subViewWidth / zoom;
          renderHeight = (renderWidth * height) / width;
        } else {
          renderHeight = subViewHeight / zoom;
          renderWidth = (renderHeight * width) / height;
        }

        return {
          type,
          zoom,
          width: renderWidth,
          height: renderHeight,
          dir,
          fitPosition,
          position,
          ...ThreeViewMap[type],
        };
      });

      setBoxViewSizes(sizes);
    }
  }, [boxData]);

  const handleChangeAnnoInstanceItemByObject = (target: Object3D) => {
    if (
      editAnno?.type !== 'widget' ||
      !(editAnno.anno instanceof AnnoInstanceItem) ||
      !labelStage ||
      !labelStage.currentSight
    )
      return;

    const oldWidget = target.userData.widget;
    const newWidget = AnnoFactory.transOutWidget(
      target,
      labelStage.currentSight.getMatrixs()[0],
      labelStage.currentSight.camera,
      labelStage.currentSight.getCurrentUsingRawObject().object
    );

    if (newWidget) {
      setCurrentAnno({ anno: editAnno.anno, opt: 'update', widget: newWidget, prevWidget: oldWidget });
    }
  };

  const onViewAdjust = (
    index: number,
    viewZoom: number,
    updatedWidth: number,
    updatedHeight: number,
    direction: Vector
  ) => {
    const { width: renderWidth, height: renderHeight, type } = boxViewSizes[index];
    if (
      editAnno?.type === 'widget' &&
      editAnno.anno instanceof AnnoInstanceItem &&
      editAnno.anno.widget.name === 'cuboid' &&
      labelStage?.currentSight &&
      viewsHandler
    ) {
      const cuboid = labelStage.currentSight.getObjectByName(editAnno.anno.name) as Mesh<
        BufferGeometry,
        MeshBasicMaterial
      >;
      viewsHandler.updateBoxScaleFromView(
        cuboid,
        type,
        (boxData[index].width * updatedWidth) / renderWidth,
        (boxData[index].height * updatedHeight) / renderHeight,
        direction,
        viewZoom
      );
      handleChangeAnnoInstanceItemByObject(cuboid);
    }
  };

  const onViewRotate = (type: ThreeViewType, theta: number) => {
    if (
      editAnno?.type === 'widget' &&
      editAnno.anno instanceof AnnoInstanceItem &&
      editAnno.anno.widget.name === 'cuboid' &&
      labelStage?.currentSight &&
      viewsHandler
    ) {
      const cuboid = labelStage.currentSight.getObjectByName(editAnno.anno.name) as Mesh<
        BufferGeometry,
        MeshBasicMaterial
      >;
      viewsHandler.updateBoxQuaternionFromView(cuboid, type, theta);

      handleChangeAnnoInstanceItemByObject(cuboid);
    }
  };

  const onViewDirect = (type: ThreeViewType, times: number) => {
    if (
      editAnno?.type === 'widget' &&
      editAnno.anno instanceof AnnoInstanceItem &&
      editAnno.anno.widget.name === 'cuboid' &&
      labelStage?.currentSight &&
      viewsHandler
    ) {
      const cuboid = labelStage.currentSight.getObjectByName(editAnno.anno.name) as Mesh<
        BufferGeometry,
        MeshBasicMaterial
      >;
      viewsHandler.updateBoxDirectionFromView(cuboid, type, times);
      handleChangeAnnoInstanceItemByObject(cuboid);
    }
  };

  /**
   * 标标注物移动
   * @param x view 中 x 轴位移
   * @param y view 中 y 轴位移
   */
  const onViewObjectMove = (index: number, x: number, y: number) => {
    const { type } = boxViewSizes[index];
    const { width: boxWidth, height: boxHeight } = boxData[index];
    if (
      editAnno?.type === 'widget' &&
      editAnno.anno instanceof AnnoInstanceItem &&
      editAnno.anno.widget.name === 'cuboid' &&
      labelStage?.currentSight &&
      viewsHandler
    ) {
      const cuboid = labelStage.currentSight.getObjectByName(editAnno.anno.name) as Mesh<
        BufferGeometry,
        MeshBasicMaterial
      >;

      if (boxWidth / boxHeight > subViewAspect) {
        x = (x * boxWidth) / subViewWidth;
        y = (-y * boxWidth) / subViewWidth;
      } else {
        y = (-y * boxHeight) / subViewHeight;
        x = (x * boxHeight) / subViewHeight;
      }

      viewsHandler.updateBoxPositionFromView(cuboid, type, [x, y]);
      handleChangeAnnoInstanceItemByObject(cuboid);
    }
  };

  const autoFit = () => {
    if (
      editAnno?.type === 'widget' &&
      editAnno.anno instanceof AnnoInstanceItem &&
      editAnno.anno.widget.name === 'cuboid' &&
      labelStage?.currentSight &&
      viewsHandler
    ) {
      selectionPoints.updateCameraAndPointCloud(
        labelStage.currentSight.camera,
        labelStage.currentSight.getUsingRawObject().object
      );
      const cuboid = labelStage.currentSight.getObjectByName(editAnno.anno.name) as Mesh<
        BufferGeometry,
        MeshBasicMaterial
      >;
      const criticalPoint = selectionPoints.selectCubeCritical(cuboid);
      viewsHandler.updateBoxScaleFromCritical(cuboid, criticalPoint);
      handleChangeAnnoInstanceItemByObject(cuboid);
    }
  };

  useHotkeyConfigWithScope(HOTKEY_ANNO_CONFIG, 'direct', () => {
    onViewRotate('top', Math.PI);
  });

  useHotkeyConfigWithScope(HOTKEY_ANNO_CONFIG, 'fit', () => {
    if (editAnno?.type !== 'widget' || !labelStage || !labelStage.currentSight) return;
    autoFit();
  });

  return (
    <div
      style={{
        width: propWidth,
        height: propHeight,
        position: 'absolute',
        display: boxData.length === 3 ? 'block' : 'none',
        ...propStyle,
      }}
      className="view-container"
    >
      {boxViewSizes.map(({ type, name, icon, zoom, width, height, dir, fitPosition, position }, i) => (
        <View
          key={type}
          name={name}
          icon={icon}
          viewWidth={subViewWidth}
          viewHeight={subViewHeight}
          boxWidth={width}
          boxHeight={height}
          boxDir={dir}
          isEmpty={seriesLinkage.isEmptyWhenTrackIdSelected && clipboard === null}
          showDirBtn={type === 'top'}
          boxPosition={position}
          fitPosition={fitPosition}
          onLineMove={(width: number, height: number, moveDir: Vector) => onViewAdjust(i, zoom, width, height, moveDir)}
          onRotate={(theta: number) => onViewRotate(type, theta)}
          onDirect={(times: number) => onViewDirect(type, times)}
          onFit={autoFit}
          view={viewsHandler?.views[i]}
          onObjectMove={(x, y) => onViewObjectMove(i, x, y)}
        />
      ))}
    </div>
  );
};
