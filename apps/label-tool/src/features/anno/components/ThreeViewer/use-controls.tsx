import { RefObject, useEffect, useState } from 'react';
import { Vector3 } from 'three';

import { ThreeView } from '@/utils/render';

import { useContextStore } from '../../stores';
import { MAX_ZOOM, MIN_ZOOM, SCALE_SPEED } from './config';

const mouseBefore = new Vector3(),
  mouseAfter = new Vector3();

type Options = {
  viewWidth: number;
  viewHeight: number;
  onRotate: (theta: number) => void;
};

// 给三视图添加鼠标控制相机的功能
export const useControls = (
  ref: RefObject<HTMLDivElement>,
  view: ThreeView | undefined,
  { viewWidth, viewHeight, onRotate }: Options
) => {
  const { camera } = view ?? {};

  const [scale, setScale] = useState<number>(1);
  // 当拖动画布的时候，相机的位置会移动，需要外部更新标注物的相关信息
  const [cameraUpdate, setCameraUpdate] = useState<boolean>(false);

  // 通过监听页面的变化，知道是否切换页面，只在 23D 中判断，所以使用的 dataElementIndex
  const dataElementIndex = useContextStore((state) => state.dataElementIndex);

  const handleResetCamera = () => {
    if (camera) {
      camera.zoom = 1;
      camera.updateProjectionMatrix();
      camera.position.set(0, 0, 0);
      camera.updateMatrixWorld();
      setScale(1);
      setCameraUpdate((pre) => !pre);
    }
  };

  useEffect(() => {
    const el = ref.current;
    let startState = false;
    // 滚轮控制三视图的缩放
    const handleWheel = (event: WheelEvent) => {
      if (!camera || startState) return;
      // shift + wheel 时触发旋转
      if (event.shiftKey) {
        // shift + wheel 会触发横向滚动，所以需要判断 deltaX
        if (event.deltaY > 0 || event.deltaX > 0) {
          onRotate(-Math.PI / 180);
        } else {
          onRotate(Math.PI / 180);
        }
        return;
      }

      const realScale = event.deltaY < 0 ? scale / SCALE_SPEED : scale * SCALE_SPEED;
      if (realScale > MAX_ZOOM || realScale < MIN_ZOOM) return;
      const { offsetX, offsetY } = event;
      const [x, y] = [(offsetX / viewWidth) * 2 - 1, -(offsetY / viewHeight) * 2 + 1];
      // 此处先转换到相机坐标系，然后再转换到 cameraContainer 坐标系下，方便相机的 position 移动计算
      mouseBefore.set(x, y, 0).applyMatrix4(camera.projectionMatrixInverse).applyMatrix4(camera.matrix);
      camera.zoom = realScale;
      camera.updateProjectionMatrix();
      mouseAfter.set(x, y, 0).applyMatrix4(camera.projectionMatrixInverse).applyMatrix4(camera.matrix);
      camera.position.sub(mouseAfter).add(mouseBefore);
      camera.updateMatrixWorld();
      setCameraUpdate((pre) => !pre);
      setScale(realScale);
    };

    const handleMouseDown = (event: MouseEvent) => {
      // 只管右键的拖动画布
      if (event.button === 2 && camera) {
        startState = true;
        const { offsetX, offsetY } = event;

        const [x, y] = [(offsetX / viewWidth) * 2 - 1, -(offsetY / viewHeight) * 2 + 1];
        mouseBefore.set(x, y, 0).applyMatrix4(camera.projectionMatrixInverse).applyMatrix4(camera.matrix);
      }
    };

    const handleMouseMove = (e: MouseEvent) => {
      if (startState && camera) {
        const { offsetX, offsetY } = e;
        const [x, y] = [(offsetX / viewWidth) * 2 - 1, -(offsetY / viewHeight) * 2 + 1];
        mouseAfter.set(x, y, 0).applyMatrix4(camera.projectionMatrixInverse).applyMatrix4(camera.matrix);
        camera.position.add(mouseBefore).sub(mouseAfter);
        camera.updateMatrixWorld();
        setCameraUpdate((pre) => !pre);
      }
    };

    const handleMouseUp = (e: MouseEvent) => {
      if (startState) {
        startState = false;
      }
    };

    if (el) {
      el.addEventListener('wheel', handleWheel, { passive: false });
      el.addEventListener('mousedown', handleMouseDown);
      el.addEventListener('mousemove', handleMouseMove);
      el.addEventListener('mouseup', handleMouseUp);
      el.addEventListener('pointercancel', handleMouseUp);
      el.addEventListener('pointerleave', handleMouseUp);
    }
    return () => {
      if (el) {
        el.removeEventListener('wheel', handleWheel);
        el.removeEventListener('mousedown', handleMouseDown);
        el.removeEventListener('mousemove', handleMouseMove);
        el.removeEventListener('mouseup', handleMouseUp);
        el.removeEventListener('pointercancel', handleMouseUp);
        el.removeEventListener('pointerleave', handleMouseUp);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ref, view, scale]);

  // 当切换页面后，页面的缩放应该变成原来的
  useEffect(() => {
    setScale(camera?.zoom ?? 1);
  }, [dataElementIndex, camera]);

  return { scale, cameraUpdate, handleResetCamera };
};
