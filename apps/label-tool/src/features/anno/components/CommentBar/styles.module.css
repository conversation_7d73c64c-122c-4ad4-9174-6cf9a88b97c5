.wrapper {
  width: 100%;
  font-size: 12px;
  padding-bottom: 4px;
  text-align: center;
}

.comment {
  position: relative;
  border-radius: 4px;
  background: rgba(50, 63, 75, 0.9);
  display: block;
  text-align: left;
  box-shadow: 0px 4px 4px 0px #00000040;
}

.header {
  display: flex;
  height: 24px;
  line-height: 24px;
  border-radius: 4px 4px 0 0;
  overflow: hidden;
}
.reason {
  flex-grow: 1;
  position: relative;
  background: #f54c46;
  padding-left: 8px;
}

.reason[data-status='pending'] {
  background: #f76600;
}

.reason[data-status='resolved'] {
  background: #00d18b;
}

.reason .title {
  font-weight: bold;
  color: #f5f7fa;
  pointer-events: none;
}

.comment .info {
  width: 100%;
  line-height: 18px;
  padding: 6px 8px;
  color: #cbd2d9;
  word-break: break-all;
  white-space: pre-line;
}

.button {
  width: 164px;
  height: 24px;
  margin: 8px 0 4px;
  line-height: 22px;
  text-align: center;
  font-size: 12px;
  border-radius: 4px;
  border: 1px solid #f5f7fa;
  cursor: pointer;
}

.button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.handlers {
  display: flex;
  position: absolute;
  top: 0;
  height: 24px;
  align-items: center;
  right: 4px;
  color: #f5f7fa;
}
.icon {
  font-size: 12px;
  border-radius: 2px;
  cursor: pointer;
  text-align: center;
  padding: 2px;
  line-height: 0;
}
.icon + .icon {
  margin-left: 4px;
}
.icon:hover {
  background: rgba(0, 0, 0, 0.06);
}
