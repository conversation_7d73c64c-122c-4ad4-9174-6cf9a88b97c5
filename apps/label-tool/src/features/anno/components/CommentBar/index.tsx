import { useMemo } from 'react';
import { RiDeleteBin5Line, RiEditBoxLine } from 'react-icons/ri';

import { Comment, LotPhase } from '@/types';

import styles from './styles.module.css';

/** 批注对应的操作：删除、编辑、标记为已解决、标记为错误、开始修改/新增修改 */
export type IOptType = 'delete' | 'edit' | 'resolve' | 'missed';

const HANDLERS_MAP: Record<
  Exclude<IOptType, 'resolve' | 'missed'>,
  {
    type: Exclude<IOptType, 'resolve'>;
    icon: JSX.Element;
  }
> = {
  delete: {
    type: 'delete',
    icon: <RiDeleteBin5Line />,
  },
  edit: {
    type: 'edit',
    icon: <RiEditBoxLine />,
  },
};

export interface CommentBarProps {
  commentTitle: string;
  commentReasons: string[];
  commentContent: string;
  status: Comment['status'];
  commentClass: string;
  onClickComment: (type: IOptType) => void;
  currentComment: Comment | null;
  currentPhase: LotPhase | null;
  resolvePhase: Comment['resolve_phase'];
}
export const CommentBar = ({
  commentTitle,
  commentReasons,
  commentClass,
  commentContent,
  status,
  onClickComment,
  currentComment,
  currentPhase,
  resolvePhase,
}: CommentBarProps) => {
  const COMMENT_STATUS_MAP: Record<
    Exclude<Comment['status'], undefined>,
    Array<{
      type: Exclude<IOptType, 'update'>;
      icon: JSX.Element;
    }>
  > = useMemo(() => {
    const isAuditPhase = currentPhase && currentPhase?.number > 1;
    return {
      pending: isAuditPhase ? [HANDLERS_MAP['edit'], HANDLERS_MAP['delete']] : [],
      unresolved: isAuditPhase ? [HANDLERS_MAP['edit'], HANDLERS_MAP['delete']] : [],
      resolved: [],
    };
  }, [currentPhase]);

  if (!status) return null;

  const renderButton = () => {
    if (commentClass === 'unspecified') {
      if (currentComment || (resolvePhase && currentPhase && currentPhase.number > resolvePhase)) return null;
      switch (status) {
        case 'unresolved':
          return (
            <div className={styles.button} onClick={() => onClickComment('missed')}>
              开始修改
            </div>
          );
        case 'resolved':
          return (
            <div className={styles.button} onClick={() => onClickComment('missed')}>
              新增修改
            </div>
          );
      }
      return null;
    }

    if (status === 'unresolved' && !resolvePhase) {
      return (
        <div className={styles.button} onClick={() => onClickComment('resolve')}>
          标记为已解决
        </div>
      );
    }
  };

  return (
    <div className={styles.wrapper}>
      <div className={styles.comment}>
        <div className={styles.header}>
          <div className={styles.reason} data-status={status}>
            <div className={styles.title}>{commentTitle}</div>
            <div className={styles.handlers}>
              {COMMENT_STATUS_MAP[status]?.map(({ type, icon }) => (
                <span
                  key={type}
                  className={styles.icon}
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    onClickComment(type);
                  }}
                >
                  {icon}
                </span>
              ))}
            </div>
          </div>
        </div>

        <div className={styles.info}>
          [{commentReasons.join('] [')}] {commentContent}
          {renderButton()}
        </div>
      </div>
    </div>
  );
};
