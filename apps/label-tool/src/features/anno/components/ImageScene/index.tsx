import { Button } from 'antd';
import { CSSProperties, useEffect, useMemo, useRef, useState } from 'react';
import { RiArrowDownDoubleLine, RiArrowUpDoubleLine } from 'react-icons/ri';

import { ResizeBox } from '@/components';
import type { Rawdata } from '@/types';
import { getImageKey } from '@/utils/transform';

import { ImageList } from './components';

import styles from './styles.module.css';

export interface ImageSceneProps {
  images: Array<Rawdata>;
  orders?: Array<string>;
  /** 融合数据中图片对应的点云文件 */
  relativePCD?: Rawdata;
  style?: CSSProperties;
}

export const ImageScene = ({ images: listData, orders, relativePCD, style }: ImageSceneProps) => {
  const [isOpen, setOpen] = useState(true);
  const [wrapperHeight, setWrapperHeight] = useState(0);

  const wrapperRef = useRef<HTMLDivElement>(null);

  const images = useMemo(() => {
    const imgs = listData.filter((img, i) => {
      img.originIndex = i;
      return img.type === 'image' && img.meta.image;
    });

    if (Array.isArray(orders) && orders.length && imgs.length > 1) {
      let unFindeIndex = orders.length;

      const orderMap = imgs.reduce((prev, img) => {
        const index = orders.indexOf(getImageKey(img));
        // 未找到的图片序号更大
        prev[img.name] = index > -1 ? index : unFindeIndex++;
        return prev;
      }, {} as Record<Rawdata['name'], number>);

      // 按配置顺序排序
      imgs.sort((a, b) => orderMap[a.name] - orderMap[b.name]);
    }

    return imgs;
  }, [listData, orders]);

  useEffect(() => {
    if (wrapperRef.current) {
      const observer = new ResizeObserver((entries) => {
        const { inlineSize, blockSize } = entries[0].borderBoxSize[0];
        if (inlineSize > 0 && blockSize > 0) {
          setWrapperHeight(blockSize);
        }
      });
      observer.observe(wrapperRef.current);

      return () => observer.disconnect();
    }
  }, []);

  return (
    <div ref={wrapperRef} className={styles.scene} style={style} hidden={images.length === 0}>
      <ResizeBox
        style={{ display: isOpen ? 'block' : 'none', pointerEvents: 'auto', maxHeight: wrapperHeight }}
        initialWidth={300}
        minWidth={200}
        maxWidth={400}
        renderChildren={(imageWidth, isResizing) => (
          <ImageList
            images={images}
            relativePCD={relativePCD}
            imageWidth={imageWidth}
            isResizing={isResizing}
          ></ImageList>
        )}
      />
      <div className={styles.button} data-open={isOpen}>
        <Button
          type="text"
          size="small"
          style={{ fontSize: 14 }}
          icon={isOpen ? <RiArrowUpDoubleLine /> : <RiArrowDownDoubleLine />}
          onClick={() => setOpen((prev) => !prev)}
        ></Button>
      </div>
    </div>
  );
};
