/* eslint-disable react-hooks/exhaustive-deps */
import { useConstant } from '@forest/hooks';
import { CSSProperties, Fragment, useEffect, useRef, useState } from 'react';
import { Matrix4, Scene, Vector3, <PERSON><PERSON><PERSON><PERSON><PERSON>, WebGLRenderTarget } from 'three';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer';
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass';
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass';
import { shallow } from 'zustand/shallow';

import { CAMERA_FAR_3D } from '@/config/render';
import { useContextStore, useStoreApi } from '@/features/anno/stores';
import { FusionParamsWithPartialCamera } from '@/features/anno/stores/slices';
import { MapCamera } from '@/lib/three';
import { Rawdata } from '@/types';
import { BaseCamera, createDistortionShader, ZOOM_FOR_DISTORTION_FACTOR } from '@/utils/camera';
import { roundDecimals } from '@/utils/math';
import { ISubView, Stage, SubViews } from '@/utils/render';

import { useSelectorInView } from '../../hooks';
import { LineDot2d, MapParams } from '../LineDot2d';

import styles from './styles.module.css';

const getRenderHeights = (images: Array<Rawdata>, imageWidth: number, gapY: number) => {
  let total = 0;
  const heights = images.map((img) => {
    const { width, height } = img.meta.image!;
    const rh = roundDecimals(height / width) * imageWidth;
    total += rh + gapY;
    return rh;
  });

  return { heights, total };
};

const createViewsFromImages = (
  images: Array<Rawdata>,
  imageWidth: number,
  heights: number[],
  totalHeight: number,
  gapY: number
) => {
  let bottomOffset = totalHeight;
  return images.map((item, i) => {
    const { width, height } = item.meta.image!;
    const renderHeight = heights[i];

    const view: ISubView = {
      name: '',
      viewport: [0, bottomOffset - renderHeight, imageWidth, renderHeight],
      camera: new MapCamera([], new Matrix4(), width, height, 0.1, CAMERA_FAR_3D / 6),
      zoom: 1,
    };
    bottomOffset -= renderHeight + gapY;

    return view;
  });
};

/**
 * 根据图片更新视图尺寸
 * @param mapViews
 * @param imageWidth
 * @param heights
 * @param totalHeight
 * @param gapY
 * @returns
 */
const updateViewSizeFromImages = (
  mapViews: SubViews,
  imageWidth: number,
  heights: number[],
  totalHeight: number,
  gapY: number
) => {
  if (mapViews.views.length !== heights.length) return;

  let bottomOffset = totalHeight;
  mapViews.views.forEach((view, i) => {
    const renderHeight = heights[i];
    SubViews.updateViewViewport(view, [0, bottomOffset - renderHeight, imageWidth, renderHeight]);
    bottomOffset -= renderHeight + gapY;
  });
};

/**
 * 根据图片更新视图的相机参数
 * @param images
 * @param mapViews
 * @param renderMatrix
 * @param getTransParams
 * @returns
 */
const updateViewCamera = (
  images: Array<Rawdata>,
  mapViews: SubViews,
  renderMatrix: Matrix4,
  getTransParams: (rawdata: Rawdata) => FusionParamsWithPartialCamera | undefined
) => {
  if (images.length !== mapViews.views.length) return;

  return mapViews.views.map((view, i) => {
    const params = images[i] ? getTransParams(images[i]) : undefined;

    if (params?.camera && view.camera instanceof MapCamera) {
      const {
        matrixs: [pcdMatrix],
        camera,
      } = params;

      const extrinsic = new Matrix4().copy(pcdMatrix).multiply(renderMatrix).invert();

      view.name = camera.name;
      view.camera.updateFromParams(extrinsic, camera);

      return { extrinsic, camera };
    }

    return null;
  });
};

/**
 * 根据图片的相机模型更新视图的畸变后处理数据
 * @param images
 * @param mapViews
 * @param renderMatrix
 * @param getTransParams
 * @returns
 */
export const updateViewComposer = (
  cameraModels: Array<MapParams | null>,
  mapViews: SubViews,
  renderer: WebGLRenderer,
  scene: Scene
) => {
  mapViews.views.forEach((view, i) => {
    const model = cameraModels[i];
    if (!model) return;

    const shader = model ? createDistortionShader(model.camera) : null;
    if (shader) {
      if (view.composer) {
        // 更新绑定的场景
        const renderPass = view.composer.passes.find((pass): pass is RenderPass => pass instanceof RenderPass);
        renderPass && (renderPass.scene = scene);

        // 查找畸变 Pass 的次序
        const index = view.composer.passes.findIndex((pass): pass is ShaderPass => pass instanceof ShaderPass);
        if (index < 0) return;

        // 若畸变 shader 与相机模型不匹配，为畸变 shader 重赋值
        if ((view.composer.passes[index] as ShaderPass).material.name !== view.name) {
          view.composer.passes[index] = new ShaderPass(shader);
        }
      } else {
        // 新建合成器
        const composer = new EffectComposer(renderer, new WebGLRenderTarget(view.viewport[2], view.viewport[3]));
        const renderPass = new RenderPass(scene, view.camera, undefined, undefined, 0);
        composer.addPass(renderPass);

        const distortionPass = new ShaderPass(shader);
        composer.addPass(distortionPass);

        view.composer = composer;
      }
      view.composer.setPixelRatio(window.devicePixelRatio / ZOOM_FOR_DISTORTION_FACTOR);
      // 更新场景尺寸
      view.composer.setSize(view.viewport[2], view.viewport[3]);
    } else {
      view.composer = null;
    }
  });
};

export interface IImages {
  images: Array<Rawdata>;
  imageWidth: number;
  isResizing: boolean;
  gapY: number;
  relativePCD?: Rawdata;
}

export type Focus = Record<string, { offset: Vector3; zoom: number }>;

interface ImageMappingProps extends IImages {
  focus?: Focus;
  interactive?: boolean;
  style?: CSSProperties;
}

export const ImageMapping = ({
  images,
  imageWidth,
  relativePCD,
  isResizing,
  gapY,
  focus,
  interactive = false,
  style,
}: ImageMappingProps) => {
  const [isShow, setShow] = useState(true);
  const [mapStage, setMapStage] = useState<Stage | null>(null);
  const [mapParams, setMapParams] = useState<{
    extrinsic: Matrix4;
    camera: BaseCamera;
  }>();

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationLoop = useRef<() => void>(() => {});
  const mapViews = useConstant<SubViews>(() => new SubViews([], false, 0));

  const { labelStage, isFusionEnabled, rawMeta, getTransParams } = useContextStore(
    ({ fusionLinkage: { isEnabled, getTransParams }, labelStage, rawMeta }) => ({
      labelStage,
      isFusionEnabled: isEnabled,
      rawMeta,
      getTransParams,
    }),
    shallow
  );

  const store = useStoreApi();

  useSelectorInView(labelStage, interactive ? mapStage : null, store, mapViews.views[0]);

  // 初始化 mapStage
  useEffect(() => {
    if (!mapStage && canvasRef.current) {
      const { total } = getRenderHeights(images, imageWidth, gapY);

      const stage = new Stage(imageWidth, Math.max(total, 10), canvasRef.current, {
        autoAnimate: false,
        backgroundAlpha: 0,
      });
      setMapStage(stage);
    }
  }, []);

  // 响应列表宽度变化
  useEffect(() => {
    if (!mapStage) return;

    if (isResizing) {
      if (isShow) {
        // 调整过程中，隐藏映射视图
        Stage.clearStage(mapStage);
        setShow(false);
      }
    } else {
      // 调整结束后，显示映射视图
      const { heights, total } = getRenderHeights(images, imageWidth, gapY);
      mapStage.updateStageSize(imageWidth, total);
      updateViewSizeFromImages(mapViews, imageWidth, heights, total, gapY);

      setShow(true);
      mapStage.renderer.setAnimationLoop(animationLoop.current);
    }
  }, [imageWidth, isResizing]);

  // 响应图片变化
  useEffect(() => {
    // 判断映射策略是否开启
    if (!isFusionEnabled || !mapStage) return;

    if (
      images.length > 0 &&
      relativePCD?.type === 'pointcloud' &&
      // 主场景已加载完成
      labelStage?.currentSight &&
      // 主场景点云与关联的点云一致
      relativePCD.name === rawMeta.pointcloud?.fileName
    ) {
      // 确保有图片显示 且 主场景的点云已加载完成

      const { heights, total } = getRenderHeights(images, imageWidth, gapY);

      mapStage.updateStageSize(imageWidth, total);

      // 创建映射视图
      if (mapViews.views.length === 0) {
        mapViews.views = createViewsFromImages(images, imageWidth, heights, total, gapY);
      } else {
        // 更新映射视图尺寸
        updateViewSizeFromImages(mapViews, imageWidth, heights, total, gapY);
      }
      mapViews.isShow = true;

      // 获取当前点云场景数据
      const { scene: pcdScene } = labelStage.currentSight;
      // 获取点云渲染矩阵，如无则不显示映射视图
      const renderMatrix = labelStage.currentSight.getMatrixs()[0];
      if (!renderMatrix) return;

      animationLoop.current = mapStage.renderViews.bind(mapStage, pcdScene, mapViews);
      mapStage.renderer.setAnimationLoop(animationLoop.current);

      const models = updateViewCamera(images, mapViews, renderMatrix, getTransParams);
      if (models) {
        updateViewComposer(models, mapViews, mapStage.renderer, pcdScene);
        models.length === 1 && models[0] && setMapParams(models[0]);
      }

      mapViews.views.forEach(({ camera, composer }, i) => {
        const name = images[i]?.name;
        camera instanceof MapCamera && camera.focusOn(name ? focus?.[name] : undefined, composer?.passes[1]);
      });
    } else {
      // 不匹配的情况下隐藏映射
      Stage.clearStage(mapStage);
      setMapParams(undefined);
    }
  }, [images, rawMeta]);

  useEffect(() => {
    mapViews.views.forEach(({ camera, composer }, i) => {
      const name = images[i]?.name;
      camera instanceof MapCamera && camera.focusOn(name ? focus?.[name] : undefined, composer?.passes[1]);
    });
  }, [focus]);

  return (
    <Fragment>
      <canvas className={styles.map} style={style} ref={canvasRef} data-visible={isShow}></canvas>
      {interactive && !isResizing && (
        <LineDot2d
          imageWidth={imageWidth}
          mapParams={mapParams}
          focus={images[0]?.name && focus ? focus[images[0].name] : undefined}
        />
      )}
    </Fragment>
  );
};
