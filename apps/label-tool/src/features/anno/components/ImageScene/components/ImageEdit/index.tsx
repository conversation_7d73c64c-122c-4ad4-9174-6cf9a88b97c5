import { Button } from 'antd';
import { FC, PropsWithChildren, useEffect, useMemo, useRef, useState } from 'react';
import { RiCloseLine } from 'react-icons/ri';
import { Vector3 } from 'three';
import { shallow } from 'zustand/shallow';

import { useContextStore } from '@/features/anno/stores';
import { OrbitControls } from '@/lib/three';
import { Rawdata } from '@/types';
import { Sight } from '@/utils/render';

import { getRoundOffset } from '../../utils';
import { ImageEnlarge } from '../ImageEnlarge';
import { IImages, ImageMapping } from '../ImageMapping';

import styles from './styles.module.css';

export interface ImageEditProps extends Omit<IImages, 'gapY' | 'images'> {
  index: number;
  image?: Rawdata;
  onClose?: () => void;
}

export const ImageEdit: FC<PropsWithChildren<ImageEditProps>> = ({
  image,
  index,
  imageWidth: canvasWidth,
  relativePCD,
  isResizing,
  children,
  onClose,
}) => {
  const [transform, setTransform] = useState<{ offset: Vector3; zoom: number } | null>(null);

  const controlsRef = useRef<HTMLDivElement>(null);
  const controls = useRef<OrbitControls>();

  const list = useMemo(() => {
    return image ? [image] : [];
  }, [image]);

  const { focusMode, setFocusImage } = useContextStore(
    ({ fusionLinkage: { focusMode, setFocusImage } }) => ({
      focusMode,
      setFocusImage,
    }),
    shallow
  );

  useEffect(() => {
    if (!controls.current && controlsRef.current) {
      const camera = Sight.createCamera(1, 1, { type: 'OrthographicCamera', eye: [0, 0, 0] }, 1);
      controls.current = Sight.createControls(camera, controlsRef.current, { enabled: true, enableRotate: false });

      controls.current.saveState();

      controls.current.minZoom = controls.current.zoom0 / 2;
      controls.current.maxZoom = controls.current.zoom0 * 20;

      controls.current.addEventListener('change', () => {
        setTransform({ offset: camera.position, zoom: camera.zoom });
      });
    }
  }, []);

  useEffect(() => {
    if (controls.current && isResizing) {
      // 当窗口尺寸发生变动时，重置控制器绑定的元素尺寸
      controls.current.domRect = undefined;
    }
  }, [isResizing]);

  useEffect(() => {
    if (!focusMode.isEnabled) {
      controls.current?.reset();
    }
    setFocusImage(image ? image.originIndex ?? 1 : -1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [image]);

  useEffect(() => {
    if (focusMode.mouseMapPoint && controls.current) {
      const { offsetX, offsetY } = focusMode.mouseMapPoint;
      // 若当前缩放比例被用户手动更改过，则不自动变更；否则默认设置为2倍放大
      const nextZoom =
        Math.abs(controls.current.object.zoom - controls.current.zoom0) < 1e-3
          ? controls.current.zoom0 * 2
          : controls.current.object.zoom;

      // 当前图片有放大时调整图片位置，使得聚焦点在视野范围内
      if (nextZoom > 1) {
        const fx = getRoundOffset(nextZoom, offsetX);
        const fy = getRoundOffset(nextZoom, offsetY);
        controls.current.object.position.setX(fx);
        controls.current.object.position.setY(fy);

        controls.current.target.set(fx, fy, 0);
      }

      controls.current.object.zoom = nextZoom;
      controls.current.update();
    }
  }, [focusMode.mouseMapPoint]);

  return (
    <div className={styles.wrapper} style={{ display: image ? 'flex' : 'none' }}>
      <div className={styles.title}>
        <div className={styles.name}>
          {index + 1}. {image?.title}
        </div>
        <div className={styles.tools}></div>
        <div className={styles.close}>
          <Button type="text" size="small" style={{ fontSize: 16 }} icon={<RiCloseLine />} onClick={onClose}></Button>
        </div>
      </div>
      <div ref={controlsRef} className={styles.content} style={{ width: canvasWidth }}>
        {/* 图片使用 相对定位 并撑起容器 */}
        <img
          className={styles['image-item']}
          style={{
            transform: transform
              ? `translate(${-transform.offset.x * transform.zoom * 100}%, ${
                  transform.offset.y * transform.zoom * 100
                }%) scale(${transform.zoom})`
              : undefined,
          }}
          alt="item"
          src={image?.url}
          crossOrigin="anonymous"
        />
        {/* canvas 使用绝对定位 便于缩放和移动交互 */}
        <ImageMapping
          images={list}
          imageWidth={canvasWidth}
          isResizing={isResizing}
          gapY={0}
          relativePCD={relativePCD}
          interactive={true}
          focus={image && transform ? { [image.name]: transform } : undefined}
          style={{ zIndex: 2 }}
        />
      </div>
      {children}
      {focusMode.isEnabled && transform && <ImageEnlarge zoom={transform.zoom}></ImageEnlarge>}
    </div>
  );
};
