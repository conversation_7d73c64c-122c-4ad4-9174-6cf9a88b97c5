.image-canvas-container {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.48);
  z-index: 1001;
}

.image-canvas-container .image-canvas-content {
  position: absolute;
  left: 12vw;
  width: 76vw;
  top: 10vh;
  height: 80vh;
  background: #323f4b;
  border: 1px solid #52606d;
  box-shadow: 0px 8px 11px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
  overflow: hidden;
  color: #cbd2d9;
  font-size: 14px;
}

.image-canvas-container .image-canvas-content .title {
  display: flex;
  padding: 0 10px 0 16px;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
}

.image-canvas-container .title .tools {
  display: flex;
  align-items: center;
}
.image-canvas-container .title .tool-button {
  height: 24px;
  width: 24px;
  text-align: center;
  cursor: pointer;
}
.image-canvas-container .title .tool-button > svg {
  width: 18px;
  height: 100%;
}
.image-canvas-container .title .tool-button + .tool-button {
  margin-left: 12px;
}
.image-canvas-container .title .tool-button:hover {
  color: #3aa691;
}
.image-canvas-container .title .tool-button[aria-selected='true'] {
  color: #3aa691;
}
.image-canvas-container .title .tool-button[aria-disabled='true'] {
  opacity: 0.5;
  cursor: not-allowed;
  color: #cbd2d9;
}

.image-canvas-container .image-canvas-content .close {
  cursor: pointer;
  padding: 0 4px;
  margin-left: 8px;
}
.image-canvas-container .image-canvas-content .close:hover {
  color: #f5f7fa;
}

.image-canvas-container .image-canvas-content .delete {
  cursor: pointer;
  position: absolute;
  padding: 5px 8px;
  background: #323f4b;
  font-size: 14px;
  line-height: 22px;
  color: #cbd2d9;
  border-radius: 4px;
}
.image-canvas-container .image-canvas-content .delete:hover {
  color: #f5f7fa;
}
.image-canvas-container .image-canvas-content .delete::after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border: 8px solid transparent;
  border-right-color: #323f4b;
  top: 8px;
  left: -15px;
}

.image-canvas-container .image-canvas-content canvas:focus {
  outline: none;
}
