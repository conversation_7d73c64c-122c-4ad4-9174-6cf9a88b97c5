/* eslint-disable react-hooks/exhaustive-deps */
import { CSSProperties, MouseEvent, PropsWithChildren, useEffect, useMemo, useRef, useState } from 'react';
import { RiCameraLine } from 'react-icons/ri';
import { Mesh, ShaderMaterial, ShapeGeometry, Vector3 } from 'three';
import { shallow } from 'zustand/shallow';

import { ResizeBox } from '@/components';
import { useContextStore } from '@/features/anno/stores';
import type { Rawdata } from '@/types';
import { AnnoFactory, AnnoInstanceItem, Point3d } from '@/utils/annos';
import { roundDecimals } from '@/utils/math';
import { imgRetry } from '@/utils/render';

import { useHotkeyInFullscreen } from '../../hooks';
import { createGradientMaterial, createSectorGeometry, getRoundOffset } from '../../utils';
import { ImageEdit } from '../ImageEdit';
import { type Focus, IImages, ImageMapping } from '../ImageMapping';

import styles from './styles.module.css';

const AREA_RADIUS = 80; // 扇形打光区域半径
const _position3d = new Vector3();
const _vertex = new Vector3();

export interface ImageListProps extends Omit<IImages, 'gapY'> {
  gapY?: number;
  style?: CSSProperties;
}

export const ImageList = ({
  images,
  imageWidth,
  isResizing,
  relativePCD,
  gapY = 1,
  style,
}: PropsWithChildren<ImageListProps>) => {
  const [fullIndex, setFullIndex] = useState(-1);
  const [shineImage, setShineImage] = useState<Rawdata>();
  // const [isDistortion, setDistortion] = useState(false);

  const lightArea = useRef<Mesh<ShapeGeometry, ShaderMaterial> | null>(null);

  const { labelStage, isFusionEnabled, selectedAnnos, editAnno, getLightAngleByFile, getTransParams } = useContextStore(
    ({ fusionLinkage: { isEnabled, getLightAngleByFile, getTransParams }, labelStage, selectedAnnos, editAnno }) => ({
      labelStage,
      isFusionEnabled: isEnabled,
      selectedAnnos,
      editAnno,
      getLightAngleByFile,
      getTransParams,
    }),
    shallow
  );

  // 切换选择或进入编辑态时，排序 & 放大
  const [sortedImages, focus, ratioList] = useMemo(() => {
    const defaultResults: [Rawdata[], Focus, number[]] = [
      images,
      {},
      images.map((img) => {
        const { width, height } = img.meta.image!;
        return roundDecimals(height / width);
      }),
    ];

    // 单选 或 编辑态
    if (selectedAnnos.length !== 1 && editAnno?.type !== 'widget') return defaultResults;

    // 只对 cuboid
    const anno = selectedAnnos[0] || editAnno?.anno;
    if (!(anno instanceof AnnoInstanceItem) || anno.widget.name !== 'cuboid') return defaultResults;

    // 使用文件坐标系（widget 的）
    const [x, y, z] = anno.widget.data; // cuboid 可以这样，如果扩展其他类型需要注意
    const vertexs = AnnoFactory.getVertexFromWidget(anno.widget);

    // 用于排序
    const includeImages: Array<Rawdata> = [];
    const otherImages: Array<Rawdata> = [];
    // 用于放大
    const focus: Focus = {};

    for (const image of images) {
      const name = image?.name;

      // 从图片获取相机参数
      const {
        matrixs: [pcdMatrix],
        camera,
      } = getTransParams(image) || { matrixs: [] };
      if (!pcdMatrix || !camera) break;

      // 根据相机参数，把 position 从 3D 文件坐标系转到图片的像素坐标系
      _position3d.set(x, y, z);
      Point3d.getVectorFusionMapped(_position3d, pcdMatrix, camera);

      // 把各顶点转到图片的像素坐标系，确认 anno 在图片中显示的大小
      // 原点在左上角，left 和 top 为最小，right 和 bottom 为最大
      let left = Number.MAX_SAFE_INTEGER,
        right = Number.MIN_SAFE_INTEGER,
        top = Number.MAX_SAFE_INTEGER,
        bottom = Number.MIN_SAFE_INTEGER;

      for (let i = 0; i < vertexs.length; i += 3) {
        _vertex.set(vertexs[i], vertexs[i + 1], vertexs[i + 2]);

        // 对于 z > 0 的顶点，检查是否为最左、最右、最上、最下的点
        if (Point3d.getVectorFusionMapped(_vertex, pcdMatrix, camera)) {
          left = Math.min(left, _vertex.x);
          right = Math.max(right, _vertex.x);
          top = Math.min(top, _vertex.y);
          bottom = Math.max(bottom, _vertex.y);
        }
      }

      // 在图片坐标系下，判断 anno 是否在图片范围内
      const { width, height } = camera;
      const { x: px, y: py, z: pz } = _position3d;

      if (pz > 0 && right >= 0 && left <= width && bottom >= 0 && top <= height) {
        // 可见的长边需要占图片 1/2 的空间，根据当前图片范围内看到的物体大小计算需要放大多少倍
        const targetObjectWidth = width / 2;
        const targetObjectHeight = height / 2;
        const wZoom = targetObjectWidth / (Math.min(right, width) - Math.max(0, left));
        const hZoom = targetObjectHeight / (Math.min(bottom, height) - Math.max(0, top));
        const targetZoom = Math.min(wZoom, hZoom); // 选择需要放大较少的
        const zoom = Math.max(targetZoom, 1); // zoom 至少为 1

        // 计算 offset
        // (px, py) 表示的是相对于图像左上角的位置偏移比例（原点在左上角）
        // 把 (px, py) 归一化并转到 offset 坐标系（原点在中间）
        const offsetX = getRoundOffset(zoom, px / width - 0.5);
        const offsetY = getRoundOffset(zoom, 0.5 - py / height);
        const offset = new Vector3(offsetX, offsetY, 0);
        focus[name] = { offset, zoom };
        includeImages.push(image);
      } else {
        otherImages.push(image);
      }
    }

    // 排序：把 includeImages 放到列表前面
    const newImages = [...includeImages, ...otherImages];

    return [
      newImages,
      focus,
      newImages.map((img) => {
        const { width, height } = img.meta.image!;
        return roundDecimals(height / width);
      }),
    ];
  }, [images, selectedAnnos, editAnno]);

  // 切帧时关闭探照灯
  useEffect(() => {
    if (lightArea.current?.visible) {
      lightArea.current.visible = false;
    }
    setShineImage(undefined);
  }, [images]);

  const onClickShine = (image: Rawdata, evt: MouseEvent) => {
    evt.stopPropagation();

    if (!isFusionEnabled || !labelStage?.currentSight) return;

    const newData = image.name === shineImage?.name ? undefined : image;
    if (newData) {
      // 获取投影区域的原点与角度范围，坐标系为点云文件
      const angleData = getLightAngleByFile(newData);
      const pcdToThreeMat = labelStage.currentSight.getMatrixs()[1];

      if (!angleData || !pcdToThreeMat) return;

      const { x, y, geometry } = createSectorGeometry(angleData, AREA_RADIUS, pcdToThreeMat);

      if (!lightArea.current) {
        lightArea.current = new Mesh(geometry, createGradientMaterial(0xb09e4b, [x, y, 0], AREA_RADIUS * 1.1));
        labelStage.currentSight.rawObject.object.add(lightArea.current);
      } else {
        if (lightArea.current.parent !== labelStage.currentSight.marker) {
          labelStage.currentSight.addMarker(lightArea.current);
        }
        lightArea.current.geometry = geometry;
        lightArea.current.material.uniforms.origin.value = [x, y, 0];
      }
      lightArea.current.visible = true;
    } else if (lightArea.current?.visible) {
      lightArea.current.visible = false;
    }
    setShineImage(newData);
  };

  useHotkeyInFullscreen(images.length, setFullIndex);

  return (
    <div className={styles['list-container']} style={style}>
      <div className={styles.content}>
        <ul style={{ width: imageWidth }}>
          {sortedImages.map((item, i) => (
            <li
              className={styles['image-container']}
              key={i}
              style={{
                height: imageWidth * ratioList[i],
                marginBottom: gapY,
              }}
              data-selected={fullIndex === i}
            >
              <img
                className={styles['image-item']}
                style={{
                  transform: focus[item.name]
                    ? `translate(${-focus[item.name].offset.x * focus[item.name].zoom * 100}%, ${
                        focus[item.name].offset.y * focus[item.name].zoom * 100
                      }%) scale(${focus[item.name].zoom})`
                    : undefined,
                }}
                alt="item"
                src={item.url}
                crossOrigin="anonymous"
                onError={(e) => {
                  imgRetry(e.target);
                }}
              />

              <div className={styles['image-cover']} onClick={() => setFullIndex(i)}>
                <label className={styles['image-id']}>
                  {i + 1}. {item.title ?? ' '}
                </label>
                <div
                  className={styles['image-opt']}
                  data-selected={shineImage?.name === item.name}
                  onClick={(evt) => onClickShine(item, evt)}
                >
                  <RiCameraLine />
                </div>
              </div>
            </li>
          ))}
        </ul>
        <div className={styles['image-last-line']}>已经是最后一张</div>
        <ImageMapping
          style={{ pointerEvents: 'none' }}
          images={sortedImages}
          relativePCD={relativePCD}
          imageWidth={imageWidth}
          isResizing={isResizing}
          gapY={gapY}
          focus={focus}
        ></ImageMapping>
      </div>
      <ResizeBox
        initialWidth={520}
        minWidth={400}
        maxWidth={800}
        style={{
          position: 'absolute',
          top: 0,
          left: imageWidth + 4,
        }}
        renderChildren={(width, isResizing) => {
          return (
            <ImageEdit
              relativePCD={relativePCD}
              index={fullIndex}
              image={sortedImages[fullIndex]}
              imageWidth={width}
              isResizing={isResizing}
              onClose={() => setFullIndex(-1)}
            ></ImageEdit>
          );
        }}
      />
    </div>
  );
};
