.list-container {
  max-height: inherit;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: #323f4b;
  border-radius: 4px;
  border: 1px solid #323f4b;
  padding-top: 32px;
  box-shadow: 0px 8px 11px 0px #00000040;
}

.mode {
  cursor: pointer;
  display: inline-block;
  color: #cbd2d9;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  text-align: center;
  padding: 4px 0;
  margin-right: 4px;
}
.mode:hover {
  background: #52606d;
}
.mode:active,
.mode[data-active='true'] {
  color: #28c89b;
}

.content {
  position: relative;
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
  background: #171f26;
}
.content::-webkit-scrollbar-thumb {
  background: rgba(150, 150, 150, 0.4);
  border: 1px solid rgba(225, 225, 225, 0.2);
  border-radius: 12px;
}
.content::-webkit-scrollbar-thumb:hover {
  background: rgba(150, 150, 150, 0.6);
}
.content::-webkit-scrollbar-button:vertical:start:increment,
.content::-webkit-scrollbar-button:vertical:end:increment {
  display: block;
  height: 0;
  background: transparent;
}

.content ul {
  padding: 0;
  margin: 0;
}

.content li {
  position: relative;
  width: 100%;
  list-style: none;
  cursor: pointer;
  overflow: hidden;
}

.image-container {
  background-color: #515c67;
}

.content .image-item {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: top;
}

.content li .image-cover {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
}
.content li[data-selected='true'] .image-cover {
  box-shadow: inset 0 0 1px 1px #ffee99;
}

.content li[data-focus='true'] .image-id {
  background: rgba(58, 166, 145, 0.8);
}
.content .image-id {
  position: absolute;
  height: 20px;
  left: 0px;
  top: 0px;
  padding: 0 4px;

  background: rgba(0, 0, 0, 0.3);
  border-radius: 0px 0px 2px 0px;
  color: #f3f3f3;
  font-size: 12px;
  line-height: 20px;

  display: block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content .image-opt {
  position: absolute;
  width: 16px;
  height: 16px;
  right: 2px;
  top: 2px;

  background: rgba(50, 63, 75, 0.3);
  border-radius: 2px;

  color: #cecece;
}

.content .image-opt:hover {
  background: rgba(50, 63, 75, 0.4);
  color: #f5f7fa;
}

.content .image-opt[data-selected='true'] {
  color: #ffee99;
}

.content .image-last-line {
  width: 100%;
  height: 22px;
  text-align: center;
  font-size: 10px;
  line-height: 22px;
  text-align: center;
  color: #52606d;
}
