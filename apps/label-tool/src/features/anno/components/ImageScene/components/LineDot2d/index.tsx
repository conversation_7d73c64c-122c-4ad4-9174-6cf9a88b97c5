import { useMemo } from 'react';
import { Matrix4, Vector3 } from 'three';
import { shallow } from 'zustand/shallow';

import { WIDGET_SHOW_DOT } from '@/features/anno/config';
import { useContextStore } from '@/features/anno/stores';
import { AnnoFactory, AnnoInstanceItem } from '@/utils/annos';
import { BaseCamera } from '@/utils/camera';

import styles from './styles.module.css';

const DOT_SIZE = [16, 16];
const DOT_OFFSET = [DOT_SIZE[0] / 2, 6];
const DEFAULT_FOCUS = {
  offset: new Vector3(0, 0, 0),
  zoom: 1,
};

export interface MapParams {
  /** 图像相对于【渲染出的点云】的外参矩阵 */
  extrinsic: Matrix4;
  /** 相机参数 */
  camera: BaseCamera;
}

interface Dot2d {
  /** 序号渲染颜色 */
  color: string;
  /** [序号次序, 水平百分比, 垂直百分比] */
  postion: Array<[number, number, number]>;
}

interface LineDot2dProps {
  /** 图像展示宽度 */
  imageWidth: number;
  /** 映射参数 */
  mapParams?: MapParams;
  /** 交互的偏移及缩放值 */
  focus?: { offset: Vector3; zoom: number };
}

export const LineDot2d = ({ imageWidth, mapParams, focus = DEFAULT_FOCUS }: LineDot2dProps) => {
  const { labelStage, selectedAnnos, trackIdsVisibleFlag, currentAnno } = useContextStore(
    ({ labelStage, selectedAnnos, trackIdsVisibleFlag, currentAnno }) => ({
      labelStage,
      selectedAnnos,
      trackIdsVisibleFlag,
      currentAnno,
    }),
    shallow
  );

  const imageHeight = useMemo(() => {
    return mapParams ? (imageWidth * mapParams.camera.height) / mapParams.camera.width : 0;
  }, [mapParams, imageWidth]);

  const dots = useMemo(() => {
    if (!labelStage || !mapParams || selectedAnnos.length === 0) {
      return null;
    }

    const newDots: Record<string, Dot2d> = {};

    const extMat = mapParams.extrinsic.clone().invert();
    const camera = mapParams.camera;
    const point3 = new Vector3();

    selectedAnnos.forEach((anno) => {
      if (anno instanceof AnnoInstanceItem && WIDGET_SHOW_DOT.includes(anno.widget.name)) {
        const line = labelStage.currentSight?.getObjectByName(anno.name);
        if (!line) return;

        const point2: Array<[number, number, number]> = [];
        const vertexs = AnnoFactory.getVertexFromObject3D(line.userData.type, line);

        const len = Math.floor(vertexs.length / 3);
        for (let i = 0; i < len; i++) {
          point3.fromArray(vertexs, i * 3).applyMatrix4(extMat);
          if (point3.z <= 0) continue;

          camera.project(point3);
          const rx = point3.x / point3.z / camera.width;
          const ry = point3.y / point3.z / camera.height;

          if (rx >= 0 && rx <= 1 && ry >= 0 && ry <= 1) {
            point2.push([i, rx, ry]);
          }
        }

        point2.length > 0 && (newDots[anno.name] = { color: line.userData.color, postion: point2 });
      }
    });

    return newDots;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [labelStage, mapParams, selectedAnnos, currentAnno]);

  const { offset, zoom } = focus;

  // 序号偏移的垂直方向分量：有畸变时使用后处理显示图像，点渲染尺寸会随缩放变化，故需要乘以缩放值
  const offsetY = mapParams?.camera.distortion ? DOT_OFFSET[1] * zoom : DOT_OFFSET[1];

  return (
    <div className={styles.wrapper}>
      {dots &&
        Object.keys(dots).map((name) => (
          <div key={name} style={{ display: trackIdsVisibleFlag[name] >= 0 ? 'none' : 'block' }}>
            {dots[name].postion.map((point) => (
              <div
                key={point[0]}
                className={styles.dot}
                style={{
                  left: ((point[1] - offset.x) * zoom + (1 - zoom) * 0.5) * imageWidth - DOT_OFFSET[0],
                  top: ((point[2] + offset.y) * zoom + (1 - zoom) * 0.5) * imageHeight - DOT_SIZE[1] - offsetY,
                  color: dots[name].color,
                }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width={DOT_SIZE[0]}
                  height={DOT_SIZE[1]}
                  viewBox="0 0 16 16"
                  fill="currentColor"
                >
                  <path
                    d="M0 2C0 0.89543 0.895431 0 2 0H14C15.1046 0 16 0.895431 16 2V12C16 13.1046 15.1046 14 14 14H10L8 16L6 14H2C0.89543 14 0 13.1046 0 12V2Z"
                    fill="currentColor"
                  />
                  <text
                    x="8"
                    y="8"
                    fontSize="11"
                    fill="white"
                    style={{
                      dominantBaseline: 'middle',
                      textAnchor: 'middle',
                    }}
                  >
                    {point[0] + 1}
                  </text>
                </svg>
              </div>
            ))}
          </div>
        ))}
    </div>
  );
};

export {};
