.wrapper {
  display: flex;
  flex-direction: column;
  background: #323f4b;
  border-radius: 4px;
  border: 1px solid #323f4b;
  box-shadow: 0px 8px 11px 0px #00000040;
  overflow: hidden;
}

.title {
  background: #323f4b;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #f5f7fa;
  font-size: 14px;
  padding: 0 4px 0 12px;
  position: relative;
  z-index: 9;
}

.loading {
  position: absolute;
  top: 200px;
  width: 100%;
  text-align: center;
  font-weight: bold;
  color: #f5f7fa;
}

.content {
  position: relative;
  font-size: 0;
  background: #171f26;
  overflow: hidden;
}

.image-item {
  width: 100%;
  object-fit: contain;
  position: relative;
  z-index: 1;
  pointer-events: none;
  user-select: none;
}

.image-map {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
}
