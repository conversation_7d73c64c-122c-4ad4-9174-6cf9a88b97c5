/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useRef } from 'react';
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass';

import { StoreType } from '@/features/anno/stores';
import { BaseCamera } from '@/utils/camera';
import { ISubView, Selector, Stage } from '@/utils/render';

export const useSelectorInView = (
  stage: Stage | null,
  element: Pick<Stage, 'canvas' | 'width' | 'height'> | null,
  store: StoreType,
  view?: ISubView
) => {
  const selector = useRef<Selector | null>(null);
  const canvasSize = useRef<[number, number]>([0, 0]);

  useEffect(() => {
    if (stage && element && view && !selector.current) {
      selector.current = new Selector(stage, { camera: view.camera, lineThreshold: 8 });

      const { getAnnoInstanceItem, updateSelectedAnno, setSelectedAnnos } = store.getState();

      const onPointerDown = (event: PointerEvent) => {
        const intersectObject = selector.current?.getHoverObject();

        if (intersectObject && event.button === 0 && intersectObject.visible) {
          // 左键点击选中对象
          const item = getAnnoInstanceItem(intersectObject.name);
          if (!item) return;

          if (event.metaKey || event.ctrlKey) {
            // 多选模式
            updateSelectedAnno(item);
          } else {
            // 单选模式
            setSelectedAnnos([item], 'ImageScene');
          }
        }
      };

      const onPointerMove = (evt: PointerEvent) => {
        if (canvasSize.current[0] > 0 && canvasSize.current[1] > 0) {
          // 判断当前场景是否经过畸变后处理
          const lastPass = view.composer?.passes[view.composer.passes.length - 1];
          if (lastPass instanceof ShaderPass) {
            // 获取鼠标位置点对应的 后处理之前 的坐标
            const changedEvt = processPointerEvent(evt, canvasSize.current, lastPass.material.uniforms);
            selector.current?.updateRaycaster(changedEvt, canvasSize.current[0], canvasSize.current[1]);
          } else {
            selector.current?.updateRaycaster(evt, canvasSize.current[0], canvasSize.current[1]);
          }
        } else {
          selector.current?.updateRaycaster(evt);
        }
      };

      element.canvas.addEventListener('pointerdown', onPointerDown);
      element.canvas.addEventListener('pointermove', onPointerMove);

      return () => {
        element.canvas.removeEventListener('pointerdown', onPointerDown);
        element.canvas.removeEventListener('pointermove', onPointerMove);
      };
    }
  }, [stage, element, view]);

  useEffect(() => {
    if (element) {
      canvasSize.current[0] = element.width;
      canvasSize.current[1] = element.height;
    }
  }, [element?.width, element?.height]);
};

/**
 * 将鼠标位置还原到 畸变后处理 之前的坐标，逻辑与畸变 shaderPass 中的 fragmentShader 一致
 * @param evt PointerEvent
 * @param size 当前元素尺寸 [width, height]
 * @param uniforms 畸变后处理参数
 * @returns PointerEvent 一个新的 PointerEvent 对象，其中 offsetX，offsetY 被修改为后处理之前的坐标
 */
function processPointerEvent(
  evt: PointerEvent,
  size: [number, number],
  uniforms: ShaderPass['material']['uniforms']
): PointerEvent {
  const { uDistortionLUT, uZoom, uImageOffset } = uniforms;

  const changedEvt = { ...evt, offsetX: 0, offsetY: 0 };

  if (!uDistortionLUT || !uZoom || !uImageOffset) return changedEvt;

  // 将点坐标转换到 uv 坐标系（以左下角为原点，两轴取值范围均为 [0, 1]）
  const u = evt.offsetX / size[0];
  const v = 1 - evt.offsetY / size[1];

  const zoomOffset = (1.0 - 1.0 / uZoom.value) / 2.0;

  const dx = u / uZoom.value + zoomOffset + uImageOffset.value.x;
  const dy = v / uZoom.value + zoomOffset + uImageOffset.value.y;

  const originCoord = BaseCamera.lookupInDistortionLUT(uDistortionLUT.value, dx, dy);

  if (originCoord) {
    changedEvt.offsetX = originCoord[0] * size[0];
    changedEvt.offsetY = (1 - originCoord[1]) * size[1];
  }
  return changedEvt;
}
