import { Color, Matrix4, Quaternion, ShaderMaterial, Shape, ShapeGeometry, Vector3 } from 'three';

/**
 * 创建单色渐变材质
 * @param color
 * @param origin
 * @param maxDistance
 * @returns
 */
export const createGradientMaterial = (color: number, origin = [0, 0, 0], maxDistance = 100.0) => {
  return new ShaderMaterial({
    transparent: true,
    uniforms: {
      startColor: { value: new Color(color) },
      origin: { value: origin },
      maxDistance: { value: maxDistance * maxDistance },
    },
    vertexShader: `
      varying float distFromCenter;
      uniform vec3 origin;
      uniform float maxDistance;
      void main(){
        vec3 tc = position - origin;
        distFromCenter = (tc.x * tc.x + tc.y * tc.y) / maxDistance;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `,
    fragmentShader: `
      varying float distFromCenter;
      uniform vec3 startColor;
      void main(){
        gl_FragColor = vec4(startColor.rgb, mix(0.4, 0.0, distFromCenter) );
      }
    `,
  });
};

/**
 * 创建扇形几何
 * @param angleData [扇形区域的原点，扇形弧线的起点，扇形弧线的终点]
 * @param radius 扇形半径
 * @param mat 变换矩阵
 * @returns
 */
export const createSectorGeometry = (angleData: [Vector3, Vector3, Vector3], radius: number, mat: Matrix4) => {
  const [origin, ...points] = angleData;
  const quater = new Quaternion().setFromRotationMatrix(mat);

  // 将扇形原点转换到threejs坐标系
  const { x, y } = new Vector3(...origin).applyMatrix4(mat);

  // 将扇形两侧端点应用旋转并生成扇形角度
  const [startAngle, endAngle] = points.map((vec) => {
    vec.applyQuaternion(quater);
    return Math.atan2(vec.y, vec.x);
  });

  const shape = new Shape().moveTo(x, y).arc(0, 0, radius, startAngle, endAngle, false).lineTo(x, y);
  return {
    x,
    y,
    geometry: new ShapeGeometry(shape),
  };
};
