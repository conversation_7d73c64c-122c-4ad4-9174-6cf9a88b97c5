import { Event, Object3D, Vector3 } from 'three';

import { EventHandler, HandlerOptions } from '@/features/anno/utils';
import { KeyPoint, type Stage } from '@/utils/render';

import { getNextColor } from './keyPointColorList';

const distance2DPoint = (point1: Vector3, point2: Vector3) => {
  const x1 = point1.x,
    y1 = point1.y,
    x2 = point2.x,
    y2 = point2.y;
  return (x1 - x2) * (x1 - x2) + (y1 - y2) * (y1 - y2);
};

export class EditKeyPoint extends EventHandler {
  private pointer: Vector3;
  // 标识鼠标操作，用于区分用户是不是点击
  private pointerStatus: 'none' | 'down';

  constructor(stage: Stage, options: HandlerOptions) {
    super('keypoint', stage, options);

    this.pointer = new Vector3();
    this.pointerStatus = 'none';
  }

  isInImageRange() {
    const position = this.stage.currentSight?.coord.position;
    if (!position) return true;
    const [rangeX, rangeY] = position,
      x = this.pointer.x,
      y = this.pointer.y;
    return x >= 0 && x <= rangeX * 2 && y <= 0 && y >= rangeY * 2;
  }

  onPointerDown(event: PointerEvent) {
    // 只响应左键
    if (event.button !== 0) return false;
    if (this.isInImageRange() && (!this.editObject || distance2DPoint(this.pointer, this.editObject.position) > 400)) {
      this.pointerStatus = 'down';
      return false;
    } else {
      this.pointerStatus = 'none';
      return true;
    }
  }
  onPointerMove(event: PointerEvent) {
    this.updatePointer(event);
  }
  onPointerUp(event: PointerEvent) {
    if (this.pointerStatus === 'down' && this.stage.currentSight) {
      this.editObject = KeyPoint.create(
        { coord2: [this.pointer.x, this.pointer.y], color: getNextColor() },
        {
          type: 'image',
          width: this.stage.currentSight?.coord.position[0],
        }
      );
      this.stage.currentSight.addAddition(this.editObject);

      this.onObjectMount(this.editObject);
    }
    this.pointerStatus = 'none';
  }

  updateEditVectors(obj?: Object3D<Event> | undefined): void {}

  private updatePointer(event: PointerEvent | MouseEvent) {
    this.pointer.set((event.offsetX / this.stage.width) * 2 - 1, -(event.offsetY / this.stage.height) * 2 + 1, 0);
    this.pointer.unproject(this.stage.currentSight?.camera!);
  }
}
