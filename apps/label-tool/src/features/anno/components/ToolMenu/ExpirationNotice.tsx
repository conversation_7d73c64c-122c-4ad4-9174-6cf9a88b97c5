import { KvTimer } from '@forest/icons';
import { Tooltip } from 'antd';
import dayjs from 'dayjs';
import { FC, useEffect, useMemo, useState } from 'react';

import { useRequest } from '@/hooks';
import { useMessage } from '@/provider';

import { updateJobExpiration } from '../../api';

import styles from './styles.module.css';

interface ExpirationNoticeProps {
  expirationTime: string;
  jobUid: string;
  lotUid: string;
  updateJobTime: (time: string) => void;
}

// 倒计时不同截止时间的警告类型，单位是秒
enum ExpirationLevel {
  // 10 分钟以内
  Error = 10 * 60,
  // 30 分钟以内
  Warning = 30 * 60,
  Normal,
}

enum ExpirationLevelColor {
  Error = '#F54C46',
  Warning = '#FA8C16',
  Normal = '#00D18B',
}

const BACKGROUND_ALPHA = '33';
// 倒计时的间隔，单位是毫秒
const EXPIRATION_INTERVAL_MINUTE = 1000 * 60;
const EXPIRATION_INTERVAL_SECOND = 1000;
const EXPIRATION_KEY = 'expiration-warning';

export const ExpirationNotice: FC<ExpirationNoticeProps> = ({ expirationTime, jobUid, lotUid, updateJobTime }) => {
  const [duration, setDuration] = useState<number>(dayjs(expirationTime).diff(dayjs(), 's'));
  const [open, setOpen] = useState<boolean>(false);

  const showTime = useMemo(() => {
    if (duration <= 0) return '00:00:00';
    const hours = String(Math.floor(duration / 3600)).padStart(2, '0');
    const minutes = String(Math.floor((duration % 3600) / 60)).padStart(2, '0');
    const seconds = String(Math.floor(duration % 60)).padStart(2, '0');
    return duration > ExpirationLevel.Warning ? `${hours}:${minutes}` : `${hours}:${minutes}:${seconds}`;
  }, [duration]);

  const messageApi = useMessage();

  // 添加弹窗提示
  const timeColor = useMemo(() => {
    if (duration <= ExpirationLevel.Warning) {
      if (!open) {
        setOpen(true);
      }
      return duration <= ExpirationLevel.Error ? ExpirationLevelColor.Error : ExpirationLevelColor.Warning;
    }
    return ExpirationLevelColor.Normal;
  }, [duration, open]);

  const { isLoading: updating, refetch: update } = useRequest(updateJobExpiration, {
    params: {
      job_uid: jobUid,
      lot_uid: lotUid,
    },
    onSuccess: (data, _, messageApi) => {
      updateJobTime(data.job?.updated_at!);
      setOpen(false);
      messageApi?.success('任务有效期已延长');
    },
    onError: (error, _, messageApi) => {
      messageApi?.error(`任务有效期延长失败，原因为【${error.message}】请检查标注结果或联系管理者`);
    },
  });

  const handleUpdateExpiration = () => {
    if (!updating && open) {
      update({});
    }
  };

  useEffect(() => {
    if (open) {
      messageApi?.open({
        key: EXPIRATION_KEY,
        type: 'warning',
        content: (
          <div className={styles['expiration-warning']}>
            <span>任务有效期即将结束，请点击按钮延长过期时间，否则可能会提交失败</span>
            <button onClick={handleUpdateExpiration}>延期</button>
          </div>
        ),
        className: styles['expiration-warning-wrapper'],
        duration: 0,
        style: {
          marginTop: '48px',
        },
      });
    } else {
      messageApi?.destroy(EXPIRATION_KEY);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  useEffect(() => {
    const updateDuration = () => {
      const duration = dayjs(expirationTime).diff(dayjs(), 's');
      setDuration(duration);
      if (duration <= 0) return;
      const timer = setInterval(
        () => {
          setDuration((prev) => {
            // 倒计时结束，就清除计时器
            if (prev <= 0) {
              clearInterval(timer);
              return -1;
            }
            return prev - 1;
          });
        },
        duration > ExpirationLevel.Warning ? EXPIRATION_INTERVAL_MINUTE : EXPIRATION_INTERVAL_SECOND
      );
      return timer;
    };
    let timer = updateDuration();

    const visibilitychange = () => {
      // 可见时，再重新开始计时
      if (document.visibilityState === 'visible') {
        timer = updateDuration();
      } else {
        // 不可见时清除计时器
        timer && clearInterval(timer);
      }
    };
    document.addEventListener('visibilitychange', visibilitychange);
    return () => {
      timer && clearInterval(timer);
      document.removeEventListener('visibilitychange', visibilitychange);
    };
    // 过期时间改变和打开延期弹窗后，都要更新倒计时
  }, [expirationTime, open]);

  return (
    <Tooltip title="任务有效期倒计时" placement="bottom">
      <div className={styles.expiration} style={{ color: timeColor, backgroundColor: timeColor + BACKGROUND_ALPHA }}>
        <KvTimer />
        {showTime}
      </div>
    </Tooltip>
  );
};
