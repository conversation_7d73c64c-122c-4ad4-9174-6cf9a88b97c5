.adjust-container {
  display: flex;
  align-items: center;
}

.adjust-container .toggle-tool {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 32px;
  background: #52606d;
  border-radius: 4px;
  margin-right: 8px;
}

.adjust-container .toggle-tool .toggle-btn {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  padding: 0;
  margin: 0;
  min-width: 28px;
  opacity: 0.5;
}

.adjust-container .toggle-tool .toggle-btn:hover {
  opacity: 1;
  background: #7f8c98;
}

.adjust-container .toggle-tool .toggle-btn[aria-selected='true'] {
  background: #323f4b;
  color: #cbd2d9;
  opacity: 1;
}

/* ExpirationNotice */
.expiration {
  display: inline-flex;
  align-items: center;
  height: 24px;
  border-radius: 4px;
  padding: 2px 4px;
  margin-left: 4px;
  font-size: 14px;
}

.expiration svg {
  font-size: 16px;
  margin-right: 4px;
}
.expiration-warning-wrapper :global(.ant-message-custom-content) {
  display: flex;
  align-items: flex-start;
}
.expiration-warning-wrapper :global(.ant-message-custom-content .anticon-exclamation-circle) {
  margin-top: 4px;
}
.expiration-warning {
  display: flex;
  height: 40px;
  align-items: center;
}

.expiration-warning span {
  width: 243px;
  margin-right: 8px;
  text-align: left;
}

.expiration-warning button {
  display: inline-block;
  width: 48px;
  border: 1px solid #f5f7fa;
  background-color: transparent;
  border-radius: 4px;
  color: #f5f7fa;
  height: 22px;
  line-height: 18px;
}

.expiration-warning button:hover {
  border-color: #f5f7f888;
  cursor: pointer;
}
