import { KvDetection, KvSegment, KvSettingLine } from '@forest/icons';
import { Divider, Tooltip } from 'antd';
import { isEqual, pick } from 'lodash';
import { PropsWithChildren, ReactNode, useEffect, useMemo, useState } from 'react';
import { RiArrowDropDownLine, RiContrastDrop2Line } from 'react-icons/ri';
import { shallow } from 'zustand/shallow';

import {
  IPointsValue,
  MenuButton,
  PointsAdjuster,
  PopoverWithTooltip,
  SimpleMenu,
  SimpleMenuItem,
  SwitchGroup,
} from '@/components';
import { POINTS_DEFAULT_BACKGROUND_COLOR } from '@/config/render';
import type { Rawdata } from '@/types';
import { RawPoints } from '@/utils/render';
import { getLocalValue, setLocalValue } from '@/utils/storage';
import { Tool } from '@/utils/tool';

import { useContextStore } from '../../stores';
import { SCENE_DISPLAY_TOOL_MAP, SCENE_TOOL_MAP } from './config';
import { ExpirationNotice } from './ExpirationNotice';

import '@/styles/index.css';
import styles from './styles.module.css';

const LOCAL_KEY = 'POINTS_SETTING';

const TYPE_MAPS: Array<{ name: string; text: string; icon: ReactNode }> = [
  {
    name: 'segment',
    text: '语义分割',
    icon: <KvSegment />,
  },
  {
    name: 'detection',
    text: '目标检测',
    icon: <KvDetection />,
  },
];

export interface ToolMenuProps {
  sceneType?: Rawdata['type'];
}
export const ToolMenu: React.FC<PropsWithChildren<ToolMenuProps>> = ({ sceneType = 'unspecified', children }) => {
  const [pointsValue, setPointsValue] = useState<IPointsValue>(() =>
    getLocalValue(LOCAL_KEY, {
      pointSize: 2,
      visibleZ: [-10, 30],
      colorTheme: 'binarization',
      groundZ: 0,
      background: POINTS_DEFAULT_BACKGROUND_COLOR,
    })
  );

  const {
    job,
    lot,
    updateJob,
    labelStage,
    currentType,
    rawMeta,
    currentTool,
    posePoints,
    ranges,
    displayValues,
    toolControls,
    setCurrentTool,
    setCurrentType,
    setSelectedAnnos,
    setEditAnno,
    fusionLinkage: { deleteAllKeyPoints },
    toggleSceneConfig,
  } = useContextStore(
    (state) =>
      pick(state, [
        'job',
        'lot',
        'updateJob',
        'labelStage',
        'rawMeta',
        'currentTool',
        'currentType',
        'posePoints',
        'ranges',
        'displayValues',
        'toolControls',
        'setCurrentTool',
        'setCurrentType',
        'setSelectedAnnos',
        'setEditAnno',
        'fusionLinkage',
        'toggleSceneConfig',
      ]),
    shallow
  );

  const sceneDisplayItems = useMemo(() => {
    return SCENE_DISPLAY_TOOL_MAP.filter((child) => {
      if (child.name === 'rangeVisible') {
        return ranges.length > 0;
      } else if (child.name === 'poseVisible') {
        return posePoints.length > 0;
      } else {
        return true;
      }
    });
  }, [posePoints, ranges]);

  const toolDisplayItems = useMemo(() => {
    const items: SimpleMenuItem[] = [];
    const tools = SCENE_TOOL_MAP[currentType === 'segment' ? 'segment' : sceneType];
    for (let i = 0, l = tools.length; i < l; i++) {
      const item = tools[i];
      if (item.type === 'divider') {
        items.push(item);
      } else if (item.type === 'button') {
        if (!Tool.isTool(item.name) || toolControls[item.name] !== false) {
          items.push(item);
        }
      } else {
        const children = item.children?.filter((child) => {
          return !Tool.isTool(child.name) || toolControls[child.name] !== false;
        });
        if (children && children.length > 0) {
          items.push({ ...item, children });
        }
      }
    }
    return items;
  }, [sceneType, toolControls, currentType]);

  useEffect(() => {
    if (labelStage) {
      labelStage.updateFileRenderParams('pointcloud', pointsValue);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [labelStage]);

  // tofix: 把渲染配置参数放到 renderSlice 中，避免当前组件和 labelStage 两套渲染配置
  useEffect(() => {
    if (pointsValue?.visibleZ && labelStage?.currentSight?.rawObject instanceof RawPoints) {
      const zRange = labelStage?.currentSight.rawObject.zRange;
      const newPointsValue = { ...pointsValue };

      if (pointsValue.visibleZ[1] < zRange[0] || pointsValue.visibleZ[0] > zRange[1]) {
        newPointsValue.visibleZ = zRange;
        handleAdjustPoints({ visibleZ: zRange });
      }

      if (pointsValue?.groundZ && (pointsValue.groundZ < zRange[0] || pointsValue.groundZ > zRange[1])) {
        const groundZ = Math.floor(zRange[0]);
        newPointsValue.groundZ = groundZ;
        handleAdjustPoints({ groundZ });
      }

      if (isEqual(newPointsValue, pointsValue)) {
        labelStage?.updateFileRenderParams('pointcloud', newPointsValue);
        setLocalValue(LOCAL_KEY, newPointsValue);
      }
    }

    if (pointsValue.background && labelStage) {
      labelStage.backgroundColor = pointsValue.background;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rawMeta]);

  const handleAdjustPoints = (value: Partial<IPointsValue>) => {
    setPointsValue((prev) => ({ ...prev, ...value }));
    if (labelStage && value.background) {
      labelStage.backgroundColor = value.background;
    }
    labelStage?.currentSight?.updateRenderOptions(value);
  };

  const onMouseLeave = () => {
    labelStage?.updateFileRenderParams('pointcloud', pointsValue);
    setLocalValue(LOCAL_KEY, pointsValue);
  };

  const handleMenuClick = (tool: string) => {
    if (tool === currentTool) return;

    switch (tool) {
      // 删除只是点击操作，不需要更换当前的编辑工具
      case 'delete-keypoints':
        deleteAllKeyPoints();
        break;
      default:
        if (Tool.isAvailableTool(tool, toolControls)) {
          setEditAnno(null);
          setCurrentTool(tool);
        }
        break;
    }
  };

  const handleCurrentType = (type: string) => {
    setSelectedAnnos([]);
    setEditAnno(null);
    setTimeout(() => {
      setCurrentType(type);
    }, 0);
  };

  return (
    <div className={styles['adjust-container']}>
      {lot?.tool_cfg?.segmentation_3d_enabled && lot?.data_type !== 'fusion4d' ? (
        <div className={styles['toggle-tool']}>
          {TYPE_MAPS.map((item) => (
            <Tooltip key={item.name} title={item.text} placement="bottom">
              <MenuButton
                className={styles['toggle-btn']}
                aria-selected={currentType === item.name}
                onClick={() => handleCurrentType(item.name)}
              >
                {item.icon}
              </MenuButton>
            </Tooltip>
          ))}
        </div>
      ) : null}
      <SimpleMenu items={toolDisplayItems} onSelect={handleMenuClick} selectedKey={currentTool} />
      {sceneType === 'pointcloud' && (
        <>
          <PopoverWithTooltip
            content={
              <PointsAdjuster
                value={pointsValue}
                config={rawMeta.pointcloud}
                onChange={handleAdjustPoints}
                onMouseLeave={onMouseLeave}
              />
            }
            title="点云设置"
            tooltipTitle="点云设置"
            placement="bottomLeft"
            tooltipPlacement="bottom"
            mouseLeaveDelay={0.2}
          >
            <MenuButton aria-selected="false">
              <RiContrastDrop2Line />
            </MenuButton>
          </PopoverWithTooltip>

          <Divider type="vertical" />

          <PopoverWithTooltip
            content={<SwitchGroup items={sceneDisplayItems} values={displayValues} onChange={toggleSceneConfig} />}
            tooltipTitle="显示设置"
            placement="bottomLeft"
            tooltipPlacement="bottom"
          >
            <MenuButton aria-selected="false">
              <KvSettingLine />
              <RiArrowDropDownLine fontSize="16px" />
            </MenuButton>
          </PopoverWithTooltip>
        </>
      )}
      {job?.expired_at && (
        <>
          <Divider type="vertical" />
          <ExpirationNotice
            expirationTime={job.expired_at}
            lotUid={job.lot_uid}
            jobUid={job.uid}
            updateJobTime={updateJob}
          />
        </>
      )}
      {children}
    </div>
  );
};
