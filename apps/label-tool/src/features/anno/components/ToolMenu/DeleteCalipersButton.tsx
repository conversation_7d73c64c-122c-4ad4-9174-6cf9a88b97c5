import { pick } from 'lodash';
import { FC, MouseEventHandler } from 'react';
import { shallow } from 'zustand/shallow';

import { useContextStore } from '@/features/anno/stores';
import { FatLine } from '@/lib/three';
import { Caliper3d, HorizontalCaliper3d, VerticalCaliper3d } from '@/utils/annos';
import type { AdditionHandlerType } from '@/utils/tool';

interface DeleteCalipersButtonProps {
  tool: AdditionHandlerType;
}

export const DeleteCalipersButton: FC<DeleteCalipersButtonProps> = ({ tool }) => {
  const { labelStage, currentTool, setCurrentTool } = useContextStore(
    (state) => pick(state, ['labelStage', 'currentTool', 'setCurrentTool']),
    shallow
  );
  const handleDeleteCalipers: MouseEventHandler<HTMLElement> = (e) => {
    e.stopPropagation();
    // 删除测距
    const filteredChildren = labelStage?.currentSight?.addition.children.filter((child) => {
      if (child.userData.type === tool) {
        switch (child.userData.type) {
          case 'caliper':
            Caliper3d.clear2DDom(child as FatLine);
            return false;
          case 'caliper-h':
            HorizontalCaliper3d.clear2DDom(child as FatLine);
            return false;
          case 'caliper-v':
            VerticalCaliper3d.clear2DDom(child as FatLine);
            return false;
        }
      }
      return true;
    });
    if (labelStage?.currentSight?.addition.children && filteredChildren) {
      labelStage.currentSight.addition.children = filteredChildren;
    }
    if (['caliper', 'caliper-h', 'caliper-v'].includes(currentTool)) {
      // 若当前工具为测距，则重置
      setCurrentTool('select');
    }
  };
  return (
    <div
      onClick={handleDeleteCalipers}
      style={{
        color: '#27CD9D',
        cursor: 'pointer',
        fontSize: 12,
      }}
    >
      清空
    </div>
  );
};
