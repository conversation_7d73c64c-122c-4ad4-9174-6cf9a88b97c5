import {
  Kv3D<PERSON>iewLine,
  KvBirdFill,
  KvDeleteKeyPoints,
  KvFoldLine,
  KvLabelLine,
  KvMissedLabel,
  KvPolygon2D,
  KvPolygon3D,
  KvRectLine,
  KvRulers,
  KvSplineLine,
} from '@forest/icons';
import {
  RiBox3Line,
  RiBriefcase2Line,
  RiGitCommitLine,
  RiGradienterLine,
  RiNavigationLine,
  RiSignalWifi2Line,
} from 'react-icons/ri';

import type { BasicSwitchItem, SimpleMenuItem } from '@/components';
import { HOTKEY_TOOL_CONFIG } from '@/features/hotkey';
import type { Rawdata } from '@/types';
import type { ToolType } from '@/utils/tool';

import { DeleteCalipersButton } from './DeleteCalipersButton';

const toolHotkeys = HOTKEY_TOOL_CONFIG.flat().reduce((prev, cur) => {
  const keystr = cur.keys.join('+');
  prev[cur.name] = keystr;

  return prev;
}, {} as Record<ToolType, string>);

const pointcloudTools: Record<string, SimpleMenuItem> = {
  select: {
    name: 'select',
    type: 'button',
    text: '选择 V',
    icon: <RiNavigationLine />,
  },
  helper: {
    name: 'helper',
    type: 'group',
    text: '辅助工具',
    icon: <RiBriefcase2Line />,
    children: [
      {
        name: 'caliper',
        text: `测距（${toolHotkeys.caliper}）`,
        icon: <KvRulers />,
        sideElement: <DeleteCalipersButton tool="caliper" />,
      },
      {
        name: 'caliper-h',
        text: `水平测距`,
        icon: <KvRulers />,
        sideElement: <DeleteCalipersButton tool="caliper-h" />,
      },
      {
        name: 'caliper-v',
        text: `垂直测距`,
        icon: <KvRulers />,
        sideElement: <DeleteCalipersButton tool="caliper-v" />,
      },
      {
        name: 'delete-keypoints',
        text: '清除所有观察点',
        icon: <KvDeleteKeyPoints />,
      },
      {
        name: 'pyramid',
        text: `指示器（${toolHotkeys.pyramid}）`,
        icon: <KvMissedLabel />,
      },
    ],
  },
  divider: {
    name: 'divider',
    type: 'divider',
  },
};

export const SCENE_TOOL_MAP: Record<Rawdata['type'] | 'segment', Array<SimpleMenuItem>> = {
  unspecified: [
    {
      name: 'select',
      type: 'button',
      text: '选择',
      icon: <RiNavigationLine />,
    },
  ],
  image: [
    {
      name: 'select',
      type: 'button',
      text: '选择',
      icon: <RiNavigationLine />,
    },
    {
      name: 'box2d',
      type: 'button',
      text: '矩形框',
      icon: <KvRectLine />,
    },
    {
      name: 'poly2d',
      type: 'button',
      text: '多边形框',
      icon: <KvPolygon2D />,
    },
    {
      name: 'line2d',
      type: 'button',
      text: '折线',
      icon: <KvFoldLine />,
    },
  ],
  pointcloud: [
    pointcloudTools.select,
    // 二级工具栏使用示例
    {
      name: 'tool3d',
      type: 'group',
      text: '3D工具',
      icon: <Kv3DViewLine />,
      children: [
        {
          name: 'cuboid',
          text: '立体框',
          icon: <RiBox3Line />,
          sideElement: toolHotkeys.cuboid,
        },
        {
          name: 'line3d',
          text: '折线',
          icon: <KvLabelLine />,
          sideElement: toolHotkeys.line3d,
        },
        {
          name: 'point3d',
          text: '点',
          icon: <RiGitCommitLine fontSize={24} />,
          sideElement: toolHotkeys.point3d,
        },
        {
          name: 'spline3d',
          text: '样条曲线',
          icon: <KvSplineLine />,
          sideElement: toolHotkeys.spline3d,
        },
        {
          name: 'poly3d',
          text: '多边形',
          icon: <KvPolygon3D />,
          sideElement: toolHotkeys.poly3d,
        },
      ],
    },
    // 辅助工具
    pointcloudTools.helper,
    pointcloudTools.divider,
  ],
  segment: [
    pointcloudTools.select,
    {
      name: 'tool3d',
      type: 'group',
      text: '3D工具',
      icon: <Kv3DViewLine />,
      children: [
        {
          name: 'poly3d',
          text: '多边形',
          icon: <KvPolygon3D />,
          sideElement: toolHotkeys.poly3d,
        },
      ],
    },
    pointcloudTools.helper,
    pointcloudTools.divider,
  ],
};

export const SCENE_DISPLAY_TOOL_MAP: Array<BasicSwitchItem> = [
  {
    name: 'poseVisible',
    text: '显示车姿',
    icon: <RiGradienterLine />,
  },
  {
    name: 'rangeVisible',
    text: '显示标注范围',
    icon: <RiSignalWifi2Line />,
  },
  {
    name: 'isBevModel',
    text: 'BEV模式',
    icon: <KvBirdFill />,
  },
];
