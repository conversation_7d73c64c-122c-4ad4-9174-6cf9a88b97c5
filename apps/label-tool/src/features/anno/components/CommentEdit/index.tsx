import { KvCommentEdit } from '@forest/icons';
import { pick } from 'lodash';
import { FC, useMemo } from 'react';

import { useContextStore } from '../../stores';
import { formatTrackIdDisplayName } from '../../utils';

import style from './styles.module.css';

export const CommentEdit: FC = () => {
  const {
    currentComment,
    commentConfigDisplay,
    resolveCurrentMissedComment,
    cancelCurrentMissedComment,
    createSnapshot,
  } = useContextStore((state) =>
    pick(state, [
      'currentComment',
      'commentConfigDisplay',
      'cancelCurrentMissedComment',
      'resolveCurrentMissedComment',
      'createSnapshot',
    ])
  );

  const displayName = useMemo(() => {
    if (!currentComment) return '';
    const { reasons, content, trackId } = currentComment;
    const reasonDetail = commentConfigDisplay[reasons.class];
    const classDisplayName = reasonDetail ? `/${reasonDetail.displayName}` : '';
    const displayContent = content ? `/${content}` : '';
    return `${formatTrackIdDisplayName(trackId)}${classDisplayName}${displayContent} 修改中...`;
  }, [commentConfigDisplay, currentComment]);

  const handleResolve = () => {
    resolveCurrentMissedComment();
    createSnapshot();
  };
  if (!currentComment) return null;
  return (
    <div className={style.wrapper}>
      <div className={style.name}>
        <span className={style.icon}>
          <KvCommentEdit />
        </span>
        <span className={style.text}>{displayName}</span>
      </div>
      <div className={style.opt}>
        <span className={style.button} onClick={cancelCurrentMissedComment}>
          取消
        </span>
        <span className={style.button} onClick={handleResolve}>
          完成修改
        </span>
      </div>
    </div>
  );
};
