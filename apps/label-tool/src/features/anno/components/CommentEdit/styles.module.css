.wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 400px;
  height: 40px;
  padding: 8px 16px;
  color: #f5f7fa;
  font-size: 12px;
  background-color: #f54c46;
  position: absolute;
  z-index: 3;
  top: 0px;
  margin-left: 50%;
  transform: translateX(-50%);
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
}
.name {
  display: inline-flex;
  align-items: center;
  position: relative;
}
.icon {
  line-height: 0;
  font-size: 24px;
}
.text {
  padding-inline-start: 4px;
  font-weight: 500;
}
.button {
  display: inline-block;
  height: 24px;
  margin-left: 8px;
  padding-inline: 12px;
  line-height: 22px;
  text-align: center;
  font-size: 12px;
  font-weight: 400;
  border-radius: 4px;
  border: 1px solid #f5f7fa;
  cursor: pointer;
}

.button:hover {
  background: rgba(255, 255, 255, 0.2);
}
