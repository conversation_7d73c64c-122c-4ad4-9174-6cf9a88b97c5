import { FormItem, Input, Select } from '@formily/antd-v5';
import { createForm } from '@formily/core';
import { createSchemaField, FormProvider } from '@formily/react';
import { pick } from 'lodash';
import { CSSProperties, FC, useMemo } from 'react';
import { shallow } from 'zustand/shallow';

import { ExpandModal } from '@/components';

import { useContextStore } from '../../stores';
import { formTpls } from '../../utils';

import styles from './styles.module.css';

type IAttrFormData = Record<string, string[]>;

const SchemaField = createSchemaField({
  components: {
    FormItem,
    Input,
    Select,
  },
});

interface FrameAttrsProps {
  onClose: () => void;
  style?: CSSProperties;
}

export const FrameAttrs: FC<FrameAttrsProps> = ({ onClose, style }) => {
  const { elementAttrs, elementAttrValues, dataElementIndex, setElementAttrValues } = useContextStore(
    (state) => pick(state, ['elementAttrs', 'elementAttrValues', 'dataElementIndex', 'setElementAttrValues']),
    shallow
  );

  const [form, schema] = useMemo(() => {
    const schema = {
      type: 'object',
      properties: elementAttrs.reduce((pre, attr) => {
        return {
          ...pre,
          [attr.name]: {
            'x-decorator': 'FormItem',
            'x-decorator-props': {
              colon: false,
              label: <div className={styles['frame-attrs-form-label']}>{attr.display_name}</div>,
              feedbackLayout: 'none',
              bordered: false,
              className: styles['frame-attrs-form-item'],
              wrapperStyle: {
                lineHeight: 'unset',
              },
            },
            ...formTpls(attr.type, attr.choices, 'default'),
          },
        };
      }, {}),
    };
    const form = createForm<IAttrFormData>({
      initialValues: elementAttrValues[dataElementIndex],
    });
    return [form, schema];
  }, [elementAttrs, elementAttrValues, dataElementIndex]);

  const handleSaveAttrs = () => {
    const values = form.values,
      attrs: Record<string, string[]> = {};
    if (values) {
      for (const key in values) {
        const attr = Reflect.get(values, key);

        if (typeof attr === 'string') {
          attrs[key] = [attr];
        } else {
          // 如果此处得到可能是 proxy<Array> 对象，通过 map 转化成普通的字符串对象
          attrs[key] = attr.map((item: string) => item);
        }
      }
      setElementAttrValues(dataElementIndex, attrs);
    }
    onClose();
  };

  return (
    <div style={style}>
      <ExpandModal title="帧属性" onOk={handleSaveAttrs} onCancel={onClose}>
        <FormProvider form={form}>
          <SchemaField schema={schema} />
        </FormProvider>
      </ExpandModal>
    </div>
  );
};
