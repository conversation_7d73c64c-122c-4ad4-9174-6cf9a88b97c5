/*Modal*/
.modal :global(.ant-modal-content) {
  border-radius: 6px;
  box-shadow: 0px 10px 15px -3px rgba(0, 0, 0, 0.1);
  overflow-x: hidden;
}

.reason-form {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  font-size: 12px;
  color: #f5f7fa;
  font-weight: 400;
  margin-top: 29px;
}

.reason-form-class {
  display: flex;
  margin-top: 10px;
  width: 100%;
}

.reason-form-class-label {
  /* width: 66px; */
  min-width: 40px;
  font-size: 12px;
  font-weight: 600;
  line-height: 30px;
  padding-inline-start: 8px;
  padding-inline-end: 8px;
  border: 1px solid #52606d;
  border-right: none;
  border-top-left-radius: 2px;
  border-bottom-left-radius: 2px;
  text-align: center;
}

.reason-form-class-select {
  flex: 1;
}
.reason-form-class-select :global(.ant-select-selector),
.reason-form-class-input:global(.ant-input) {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}

.reason-form-class .reason-form-class-select :global(.ant-select-selector),
.comment-textarea,
.reason-form-class-input {
  border-color: #52606d;
}

.reason-form-class-popup {
  padding: 0;
  border: 1px solid #52606d;
}

.reason-form-class-tip {
  background: #52606d;
  color: #ffffff80;
  height: 28px;
  padding: 0 16px;
  display: flex;
  align-items: center;
}

.elem-info {
  margin-bottom: -4px;
  font-size: 14px;
  color: #f5f7fa;
}

.elem-info span {
  color: #3aa691;
}

.footer {
  margin-top: 40px;
}
.footer .delete {
  float: left;
}
.footer .cancel {
  background: #edf2f7;
  color: #1a202c;
}
.reason-form .comment-textarea {
  margin-top: 10px;
  height: 120px;
  resize: none;
}
