import { ExclamationCircleFilled } from '@ant-design/icons';
import { Alert } from 'antd';

import { useContextStore } from '../../stores';

import styles from './styles.module.css';

export const OnlyReadAlter = () => {
  const currentPhase = useContextStore((state) => state.currentPhase);
  if (!currentPhase || currentPhase.editable) return null;

  return (
    <Alert
      banner
      closable
      className={styles['only-read-alter']}
      showIcon={false}
      message={
        <>
          <ExclamationCircleFilled className={styles['only-read-alter-icon']} color="F98533" />
          <span>只读模式，标注物的修改无法提交。如果标注物有修改，刷新页面即可复原。</span>
        </>
      }
    />
  );
};
