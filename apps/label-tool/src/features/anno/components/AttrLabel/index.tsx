import { pick } from 'lodash';
import { useEffect, useMemo, useRef, useState } from 'react';

import { ORDER_HOVER } from '@/config/render';
import { AnnoInstanceItem } from '@/utils/annos';

import { useContextStore } from '../../stores';
import { AttrBar } from '../AttrBar';

import styles from './styles.module.css';

export interface AttrLabelProps {
  isShow: boolean;
  currentPhase?: number;
  item: AnnoInstanceItem & { attrsStr?: string };
  selectedItem: AnnoInstanceItem | null;
  onClickAttrBar: (name: AnnoInstanceItem) => void;
  onChangeOrder: (item: AnnoInstanceItem, order?: number) => void;
}
export const AttrLabel = ({
  isShow,
  item,
  selectedItem,
  currentPhase,
  onClickAttrBar,
  onChangeOrder,
}: AttrLabelProps) => {
  const [isShowPin, setIsShowPin] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  const {
    lot,
    annoCommentMap,
    commentHoverInfo,
    commentConfigDisplay,
    labelMap,
    setCommentHoverInfo,
    clearCommentHoverTimeoutId,
    createCommentHoverTimeoutId,
  } = useContextStore((state) =>
    pick(state, [
      'lot',
      'annoCommentMap',
      'commentHoverInfo',
      'commentConfigDisplay',
      'labelMap',
      'setCommentHoverInfo',
      'clearCommentHoverTimeoutId',
      'createCommentHoverTimeoutId',
    ])
  );

  const [comment, displayName] = useMemo(() => {
    if (!lot || !lot.comment_reasons) return [null, null];
    const comment = annoCommentMap[item.name];
    if (!comment) return [null, null];
    const displayName = commentConfigDisplay[comment.reasons.class].displayName;

    return [comment, displayName];
  }, [annoCommentMap, commentConfigDisplay, item.name, lot]);

  const [onPinHover, onLabelHover] = useMemo(() => {
    const showComment = (type: 'pin' | 'label') => {
      clearCommentHoverTimeoutId();
      if (!ref.current) return;

      const current = ref.current;
      const { bottom, left, right } = current?.getBoundingClientRect() ?? {};
      if (!bottom || !left || !right) return;

      if (type === 'pin' && comment) {
        setIsShowPin(false);
        setCommentHoverInfo({
          anno: item,
          comment: comment,
          position: ['unset', 'unset', window.innerHeight - bottom + 26, left],
        });
      } else if (type === 'label' && !comment) {
        setCommentHoverInfo({
          anno: item,
          position: ['unset', 'unset', window.innerHeight - bottom + 28, left + 32],
        });
      }
    };
    return [() => showComment('pin'), () => showComment('label')];
  }, [clearCommentHoverTimeoutId, comment, item, setCommentHoverInfo]);

  useEffect(() => {
    if (comment && !commentHoverInfo) {
      setIsShowPin(true);
    } else if (!comment && isShowPin) {
      setIsShowPin(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [comment, commentHoverInfo, currentPhase]);

  const onLeave = () => {
    onChangeOrder(item);
    createCommentHoverTimeoutId();
  };

  const label = labelMap.get(item.label);

  return (
    <div
      className={styles.label}
      style={{ display: isShow ? 'block' : 'none' }}
      onMouseEnter={() => {
        onChangeOrder(item, ORDER_HOVER);
      }}
      onMouseLeave={onLeave}
      ref={ref}
    >
      {isShowPin && (
        <div className={styles.pin} onMouseEnter={onPinHover} data-status={comment?.status}>
          {displayName}
        </div>
      )}
      {label && (
        <AttrBar
          trackId={item.trackId}
          label={label}
          attrsStr={item.attrsStr}
          selected={selectedItem?.name === item.name}
          onOpenPanel={() => onClickAttrBar(item)}
          onHover={onLabelHover}
        />
      )}
    </div>
  );
};
