.label {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: auto;
}
.label::after {
  content: '';
  position: absolute;
  display: block;
  width: 5px;
  height: 5px;
  background: #ffffff;
  border-radius: 50%;
  margin-top: 6px;
  left: 50%;
  transform: translateX(-50%);
}
.pin {
  user-select: none;
  position: relative;
  display: inline-block;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  padding: 2px 8px;
  color: #f5f7fa;
  border-radius: 22px;
  background: #f54c46;
  margin-bottom: 5px;
  margin-left: 90px;
  transform: translateX(-50%);
}
.pin::after {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  border: 5px solid transparent;
  border-top-color: #f54c46;
  bottom: -9px;
  left: 50%;
  margin-left: -5px;
}
.pin:hover {
  display: none;
}

.pin[data-status='pending'] {
  background: #f76600;
}
.pin[data-status='pending']::after,
.pin[data-status='pending']::after {
  border-top-color: #f76600;
}

.pin[data-status='resolved'] {
  background: #00d18b;
}
.pin[data-status='resolved']::after {
  border-top-color: #00d18b;
}

.review {
  position: absolute;
  bottom: 0;
  right: -44px;
  width: 40px;
  height: 28px;
  border-radius: 4px;
  background-color: #f54c46;
  color: #f5f7fa;
  font-size: 12px;
  text-align: center;
  line-height: 28px;
  cursor: pointer;
}
.review:hover {
  background: #f54c46e5;
}
