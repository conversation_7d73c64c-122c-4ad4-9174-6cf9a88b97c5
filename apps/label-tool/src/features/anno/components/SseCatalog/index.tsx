import { Tabs } from 'antd';
import { pick } from 'lodash';
import { CSSProperties, FC, useEffect, useMemo, useState } from 'react';
import { shallow } from 'zustand/shallow';

import type { InstancesStatus } from '@/features/anno/stores/slices';
import type { Instance, Statistic } from '@/types';
import type { RawPoints } from '@/utils/render';

import { FrameAnnoStore, useContextStore } from '../../stores';
import { useExpandedKeys } from '../LabelCatalog/hooks';
import { LabelList, SseTool } from './components';
import { InstanceType } from './components/LabelTree';

import './index.css';

export interface SseCatalogProps {
  style?: CSSProperties;
}

const getDefaultNum = (pointsNum: number, labels: Array<Statistic>) => {
  if (!pointsNum) return 0;
  const totalNum = labels?.reduce((acc, label) => acc + (label?.num ?? 0), 0) ?? 0;
  return pointsNum - totalNum;
};

const formatLabelChildren = (list: Array<Instance>, label: { color: string; name: string }, status: InstancesStatus) =>
  list?.map((item) => ({ ...label, ...item, ...status[item?.instance_id] }));

const mergeByNameInArrays = (arrays: Statistic[][]) => {
  const result = arrays.reduce<Record<string, Statistic>>((accOuter, array) => {
    array.reduce((accInner, item) => {
      const { category_name, num } = item;
      if (accInner[category_name]) {
        accInner[category_name].num += num;
      } else {
        accInner[category_name] = { category_name, num };
      }
      return accInner;
    }, accOuter);
    return accOuter;
  }, {});

  return Object.values(result);
};

export const SseCatalog: FC<SseCatalogProps> = ({ style }) => {
  const [activeKey, setActiveKey] = useState(() => '3D');
  const {
    labelStage,
    pointsNum,
    segLabels,
    statistic,
    settingStatus,
    currentElementIndex,
    currentType,
    currentTool,
    overlayFrames,
    selectedSegment,
    selectSegLabel,
    selectSegment,
    resetCurSegLabel,
    setCurrentTool,
  } = useContextStore(
    (state) =>
      pick(state, [
        'labelStage',
        'pointsNum',
        'segLabels',
        'statistic',
        'currentElementIndex',
        'currentType',
        'currentTool',
        'overlayFrames',
        'selectedSegment',
        'settingStatus',
        'selectSegLabel',
        'selectSegment',
        'resetCurSegLabel',
        'setCurrentTool',
      ]),
    shallow
  ) as FrameAnnoStore;

  const instanceData = useMemo(() => {
    const formattedInstances: Array<InstanceType> = [];
    if (Object.keys(settingStatus).length === 0) return formattedInstances;
    if (overlayFrames) {
      const { startIndex, num } = overlayFrames;
      let totalNum = 0,
        overlayStatistic = [];
      for (let i = 0; i <= num; i++) {
        totalNum += pointsNum[startIndex + i] ?? 0;
        if (statistic[startIndex + i]) {
          overlayStatistic.push(statistic[startIndex + i]);
        }
      }
      const mergedStatistic = mergeByNameInArrays(overlayStatistic);
      const defaultPointsNum = getDefaultNum(totalNum, mergedStatistic);
      segLabels.forEach((label) => {
        const { name } = label;
        const labelStatistic = mergedStatistic.find((item) => item.category_name === name);

        formattedInstances.push({
          num: name === '' ? defaultPointsNum : labelStatistic?.num ?? 0,
          ...settingStatus[name],
          ...label,
          children: [],
        });
      });
    } else {
      const defaultPointsNum = getDefaultNum(pointsNum[currentElementIndex], statistic[currentElementIndex]);
      segLabels.forEach((label) => {
        const { name, has_instance } = label;
        const labelStatistic = statistic[currentElementIndex]?.find((item) => item.category_name === name);
        const children =
          has_instance && labelStatistic?.instances
            ? formatLabelChildren(labelStatistic.instances as Array<Instance>, label, settingStatus[name]?.instances)
            : [];

        formattedInstances.push({
          num: name === '' ? defaultPointsNum : labelStatistic?.num ?? 0,
          ...settingStatus[name],
          ...label,
          children,
        });
      });
    }

    return formattedInstances;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [segLabels, statistic, settingStatus, pointsNum, overlayFrames, currentElementIndex]);

  const [instanceExpandedKeys, handleInstanceExpand, handleInstanceExpandAll] = useExpandedKeys(instanceData, true);

  useEffect(() => {
    if (currentType === 'segment') {
      if (selectedSegment) {
        handleSelectLabel('');
      }
      if (currentTool !== 'select') {
        setCurrentTool('select');
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [overlayFrames]);

  // 点击标签
  const handleSelectLabel = (name: string) => {
    name ? selectSegLabel(name) : resetCurSegLabel();
    (labelStage?.currentSight?.getUsingRawObject() as RawPoints)?.scheduler?.setSseSelectInfo();
  };

  // 点击实例标签
  const handleSelectSegment = (category: string, instanceId: number) => {
    const label = instanceData.find((item) => item.name === category);
    label && selectSegment({ name: category, color: label?.color, instanceId: Number(instanceId) });
    (labelStage?.currentSight?.getUsingRawObject() as RawPoints)?.scheduler?.setSseSelectInfo();
  };

  return (
    <>
      <SseTool />
      <div className="editor-catalog" style={style}>
        <Tabs
          activeKey={activeKey}
          rootClassName="editor-catalog-container"
          centered
          type="card"
          size="large"
          onChange={(activeKey) => setActiveKey(activeKey)}
          items={[
            {
              key: '3D',
              label: '3D',
              children: (
                <div className="editor-catalog-container-list">
                  <LabelList
                    title="标注物"
                    stickyDirection="top"
                    labelTreeData={instanceData}
                    onClickLabel={handleSelectLabel}
                    expandedKeys={instanceExpandedKeys}
                    onExpand={handleInstanceExpand}
                    onExpandAll={handleInstanceExpandAll}
                    onSelectSegment={handleSelectSegment}
                    onRemove={(name, instanceId) =>
                      (labelStage?.currentSight?.getUsingRawObject() as RawPoints)?.scheduler?.delete(name, instanceId)
                    }
                    onToggleVisible={(name, instanceId, show) =>
                      (labelStage?.currentSight?.getUsingRawObject() as RawPoints)?.scheduler?.showOrHide(
                        name,
                        instanceId,
                        show
                      )
                    }
                    onOnlyShow={(name, instanceId, isCancel) =>
                      (labelStage?.currentSight?.getUsingRawObject() as RawPoints)?.scheduler?.showSelfOnly(
                        name,
                        instanceId,
                        isCancel
                      )
                    }
                  />
                </div>
              ),
            },
            {
              key: '2D',
              label: '2D',
              disabled: true,
            },
          ]}
        />
      </div>
    </>
  );
};
