.editor-catalog {
  width: 242px;
}
.editor-catalog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
}
.editor-catalog-container.ant-tabs-card .ant-tabs-nav {
  margin: 0;
}
.editor-catalog-container.ant-tabs-card .ant-tabs-nav::before {
  display: none;
}
.editor-catalog-container.ant-tabs-card .ant-tabs-nav .ant-tabs-nav-list {
  width: 100%;
  border: 1px solid #323f4b;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}
.editor-catalog-container.ant-tabs-card .ant-tabs-nav .ant-tabs-tab {
  border: none;
  background: transparent;
  font-size: 12px;
  font-weight: 600;
  padding: 8px 36px 6px;
  color: #52606d;
  flex: 1;
  justify-content: center;
}
.editor-catalog-container.ant-tabs-card .ant-tabs-nav .ant-tabs-tab-active {
  background-color: #323f4b;
  margin-top: -1px;
  border-top: 1px solid #323f4b;
}
.editor-catalog-container.ant-tabs-card .ant-tabs-nav .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #cbd2d9;
}
.editor-catalog-container.ant-tabs .ant-tabs-tab-btn:focus-visible {
  outline: 0;
}

.ant-tabs-content,
.ant-tabs-tabpane {
  height: 100%;
}

.editor-catalog-container-list {
  flex: 1;
  color: #ffffff;
  overflow: auto;
  background-color: #323f4b;
  height: 100%;
}

.editor-catalog-container .label-list {
  padding: 8px;
  font-size: 12px;
  user-select: none;
}

.editor-catalog-container .label-list .label-list-title {
  width: 226px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  padding: 4px;
  background-color: #323f4b;
  height: 28px;
  color: #cbd2d9;
  width: 226px;
}
.editor-catalog-container .label-list .label-list-title .text {
  margin-inline-start: 4px;
}

.editor-catalog-container .label-list .label-list-title .opt {
  display: inline-flex;
  align-items: center;
}

.editor-catalog-container .label-list .label-list-title .icon {
  font-size: 16px;
  line-height: 0;
  margin-inline-start: 4px;
  padding: 2px;
}

.editor-catalog-container .label-list .label-list-title .icon:hover,
.editor-catalog-container .label-list .label-list-title .icon:active {
  background-color: #cbd2d966;
  border-radius: 2px;
  cursor: pointer;
}

.editor-catalog-container .label-list .label-list-content {
  color: #f5f7fa;
}
.editor-catalog-container .label-list .label-list-item .label {
  width: 226px;
  height: 28px;
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  padding: 4px;
  border-radius: 2px;
  background-color: #171f26;
  font-size: 12px;
  overflow: hidden;
  position: relative;
}

.editor-catalog-container .label-list .label-list-item .label[aria-disabled='false']:hover {
  cursor: pointer;
  border: 1px solid #3aa691;
}
.editor-catalog-container .label-list .label-list-item .label[aria-selected='true'] {
  background-color: rgba(58, 166, 145, 0.8);
  border: none;
}
.editor-catalog-container .label-list .label-list-item .label[aria-disabled='true'] {
  opacity: 0.5;
}

.editor-catalog-container .label-list .label-list-item .label > .icon {
  display: none;
  width: 12px;
  text-align: center;
  line-height: 0;
  padding: 3px 0;
  margin-inline-end: 4px;
}

.editor-catalog-container .label-list .label-list-item .label:hover .icon {
  display: inline-block;
  cursor: pointer;
}

.editor-catalog-container .label-list .label-list-item .label[aria-disabled='true']:hover .icon {
  display: none;
}

.editor-catalog-container .label-list .label-list-item .label .text {
  flex: 1;
}

.editor-catalog-container .label-list .label-list-item .label .dot {
  content: ' ';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin: 0 4px;
}

.editor-catalog-container .label-list .segment-list-item {
  background-color: #171f2666;
  padding: 4px;
  margin-bottom: 4px;
  height: 28px;
  width: 226px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 2px;
  overflow: hidden;
}

.editor-catalog-container .label-list .segment-list-item .name {
  display: inline-flex;
  align-items: center;
  position: relative;
  width: 100%;
}
.editor-catalog-container .label-list .segment-list-item .square {
  content: ' ';
  display: inline-block;
  width: 8px;
  height: 8px;
  margin: 0 4px;
}
.editor-catalog-container .label-list .segment-list-item .index {
  display: inline-block;
  min-width: 12px;
  text-align: center;
  color: #cbd2d9;
  margin-inline-start: 6px;
  margin-inline-end: 2px;
}
.editor-catalog-container .label-list .segment-list-item .icon,
.editor-catalog-container .label-list .label .opt .icon {
  line-height: 0;
  margin-inline-start: 2px;
  font-size: 16px;
  margin-inline-end: 2px;
}

.editor-catalog-container .label-list .segment-list-item .icon[aria-hidden='true'],
.editor-catalog-container .label-list .segment-list-item:hover .icon[aria-hidden='false'] {
  display: none;
}

.editor-catalog-container .label-list .segment-list-item .text-wrap {
  flex: 1;
  display: flex;
  overflow: hidden;
}
.editor-catalog-container .label-list .segment-list-item .text {
  margin-inline-start: 2px;
}
.editor-catalog-container .label-list .segment-list-item .ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.editor-catalog-container .label-list .segment-list-item .opt,
.editor-catalog-container .label-list .label .opt {
  display: none;
  color: #cbd2d9;
}
.editor-catalog-container .label-list .segment-list-item .opt .icon,
.editor-catalog-container .label-list .label .opt .icon {
  padding: 2px;
}

.editor-catalog-container .label-list .segment-list-item .opt .icon[aria-disabled='true'],
.editor-catalog-container .label-list .label .opt .icon[aria-disabled='true'] {
  cursor: not-allowed;
  opacity: 0.4;
}

.editor-catalog-container .label-list .segment-list-item:hover {
  cursor: pointer;
  border: 1px solid #3aa691;
}

.editor-catalog-container .label-list .segment-list-item[aria-selected='true'] {
  background-color: rgba(58, 166, 145, 0.6);
  border: none;
}

.editor-catalog-container .label-list .segment-list-item.anno-selected-related {
  box-shadow: 0px 0px 4px 0px rgba(58, 166, 145, 0.8);
}

.editor-catalog-container .label-list .segment-list-item[aria-hidden='true'] .name,
.editor-catalog-container .label-list .segment-list-item[aria-hidden='true'] .opt {
  opacity: 0.4;
}

.editor-catalog-container .label-list .segment-list-item:hover .name {
  width: 144px;
}
.editor-catalog-container .label-list .segment-list-item:hover .opt,
.editor-catalog-container .label-list .label:hover .opt {
  display: inline-flex;
  align-items: center;
}

.editor-catalog-container .label-list .segment-list-item:hover .opt .icon:hover,
.editor-catalog-container .label-list .segment-list-item:hover .opt .icon:active,
.editor-catalog-container .label-list .label:hover .opt .icon:hover,
.editor-catalog-container .label-list .label:hover .opt .icon:active {
  cursor: pointer;
  background-color: #cbd2d966;
  border-radius: 2px;
}

.editor-catalog-container .label-list .segment-list-item:hover .opt .icon[aria-disabled='true']:hover,
.editor-catalog-container .label-list .segment-list-item:hover .opt .icon[aria-disabled='true']:active,
.editor-catalog-container .label-list .label:hover .opt .icon[aria-disabled='true']:hover,
.editor-catalog-container .label-list .label:hover .opt .icon[aria-disabled='true']:active {
  cursor: not-allowed;
}

.editor-catalog-container .button {
  display: inline-flex;
  background-color: #edf2f7;
  color: #1a202c;
  height: 24px;
  align-items: center;
  margin-inline: 4px;
  cursor: pointer;
  border-radius: 4px;
  border: none;
  padding: 0 10px;
}

.editor-catalog-container .button:hover,
.editor-catalog-container .button:active {
  color: #3aa691;
}

.editor-catalog-container .button span {
  margin-inline-end: 2px;
  line-height: 0;
  font-size: 16px;
  color: #52606d99;
}

.editor-catalog-container .button:hover span,
.editor-catalog-container .button:active span {
  color: #61b8a7;
}

.editor-catalog-container .button.primary {
  color: #ffffff;
  background-color: #3aa691;
}

.editor-catalog-container .button.primary:hover,
.editor-catalog-container .button.primary:active {
  background-color: #61b8a7;
}
