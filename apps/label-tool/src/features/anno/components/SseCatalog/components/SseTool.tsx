import { KvSseAdd, KvSseCover, KvSseDelete } from '@forest/icons';
import { pick } from 'lodash';
import { FC } from 'react';

import { SimpleMenu, SimpleMenuItem } from '@/components';
import { FrameAnnoStore, useContextStore } from '@/features/anno/stores';
import type { SegModeType } from '@/utils/tool';

import styles from './styles.module.css';

const MODE_MAPS: Array<SimpleMenuItem> = [
  {
    name: 'add',
    type: 'button',
    text: '添加',
    icon: <KvSseAdd />,
  },
  {
    name: 'cover',
    type: 'button',
    text: '覆盖',
    icon: <KvSseCover />,
  },
  {
    name: 'remove',
    type: 'button',
    text: '删除',
    icon: <KvSseDelete />,
  },
];

export const SseTool: FC = () => {
  const { curSegMode, setSegMode } = useContextStore((state) =>
    pick(state, ['curSegMode', 'setSegMode'])
  ) as FrameAnnoStore;

  const handleMenuClick = (tool: string) => setSegMode(tool as SegModeType);

  return (
    <div className={styles.container}>
      <SimpleMenu items={MODE_MAPS} onSelect={handleMenuClick} selectedKey={curSegMode} />
    </div>
  );
};
