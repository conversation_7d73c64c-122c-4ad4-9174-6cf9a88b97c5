import { KvSseDelete, KvSseShow } from '@forest/icons';
import { Tooltip } from 'antd';
import { pick } from 'lodash';
import { FC, memo, MouseEvent } from 'react';
import { RiArrowDownSFill, RiArrowLeftSFill, RiEyeCloseLine, RiEyeLine } from 'react-icons/ri';

import { FrameAnnoStore, useContextStore } from '@/features/anno/stores';
import type { InstanceStatus } from '@/features/anno/stores/slices';

export interface LabelToolCallbacks {
  onRemove: ((name: string, instanceId: number) => void) | undefined;
  onToggleVisible: ((name: string, instanceId: number, isHide: number) => void) | undefined;
  onOnlyShow: ((name: string, instanceId: number, isCancel: number) => void) | undefined;
}

interface LabelToolProps extends LabelToolCallbacks {
  name: string;
  id: number;
  isHide: number;
  isOnlyShow: number;
}

const LabelTool: FC<LabelToolProps> = memo(
  ({ name, id, isHide, isOnlyShow, onRemove, onToggleVisible, onOnlyShow }) => {
    const handleEvent = (e: MouseEvent<HTMLElement>, flag: boolean, callback?: (...args: any[]) => void) => {
      e.stopPropagation();
      const { name = '', id = '0' } = e.currentTarget.dataset;
      setTimeout(() => callback?.(name, Number(id), Number(flag)), 0);
    };

    return (
      <span className="opt">
        {name && (
          <Tooltip title="清除" placement="bottom">
            <span data-name={name} data-id={id} className="icon" onClick={(e) => handleEvent(e, false, onRemove)}>
              <KvSseDelete />
            </span>
          </Tooltip>
        )}
        <Tooltip title={isHide ? '显示' : '隐藏'} placement="bottom">
          <span
            data-name={name}
            data-id={id}
            className="icon"
            onClick={(e) => handleEvent(e, !isHide, onToggleVisible)}
          >
            {isHide ? <RiEyeLine /> : <RiEyeCloseLine />}
          </span>
        </Tooltip>
        <Tooltip title="唯显" placement="bottom">
          <span data-name={name} data-id={id} className="icon" onClick={(e) => handleEvent(e, !isOnlyShow, onOnlyShow)}>
            <KvSseShow />
          </span>
        </Tooltip>
      </span>
    );
  }
);

type BaseType = InstanceStatus & { color: string; name: string; num: number };

type ChildrenType = BaseType & { instance_id: number };

export type InstanceType = BaseType & {
  children: ChildrenType[];
  has_instance: boolean;
  display_name: string;
};

interface LabelTreeProps extends LabelToolCallbacks {
  labelTreeData: InstanceType[];
  expandedKeys: string[];
  onExpand: (expandedKeys: string[]) => void;
  onSelectSegment: (category: string, instanceId: number) => void;
  onClickLabel?: (labelName: string) => void;
}

export const LabelTree: FC<LabelTreeProps> = memo(
  ({ labelTreeData, expandedKeys, onExpand, onSelectSegment, onClickLabel, onRemove, onToggleVisible, onOnlyShow }) => {
    const { selectedSegment, curSegLabel, overlayFrames } = useContextStore((state) =>
      pick(state, ['selectedSegment', 'curSegLabel', 'overlayFrames'])
    ) as FrameAnnoStore;

    const handleExpand = (name: string) => {
      const index = expandedKeys.indexOf(name);
      let newKeys;
      if (index > -1) {
        newKeys = expandedKeys.slice(0, index).concat(expandedKeys.slice(index + 1));
      } else {
        newKeys = [...expandedKeys, name];
      }
      onExpand(newKeys);
    };

    const handleExpandLabel = (e: MouseEvent<HTMLElement>) => {
      e.stopPropagation();
      const key = e.currentTarget.dataset.name as string;
      handleExpand(key);
    };

    const handleClickLabel = (e: MouseEvent<HTMLElement>) => {
      let labelName = e.currentTarget.dataset.name as string;
      if (labelName === curSegLabel?.name) {
        labelName = '';
      }
      onClickLabel && onClickLabel(labelName);
    };

    const handleSelectSegment = (e: MouseEvent<HTMLElement>) => {
      const instanceId = e.currentTarget.dataset.id as string;
      const name = e.currentTarget.dataset.name as string;
      onSelectSegment(name, Number(instanceId));
    };

    return labelTreeData?.map(
      ({ name, children, display_name, color, has_instance, num, isHide, isOnlyShow }: InstanceType) => {
        const isOpen = expandedKeys.includes(name);
        const isDisabled = overlayFrames !== null && has_instance;

        return (
          <div className="label-list-item" key={`label-${name}`}>
            <div
              className="label"
              aria-selected={curSegLabel?.name === name}
              aria-disabled={isDisabled}
              data-name={name}
              onClick={(e) => {
                if (isDisabled) return;
                handleClickLabel(e);
              }}
            >
              {name && <span className="dot" style={{ backgroundColor: color }}></span>}
              <span className="text">
                {display_name} ({num})
              </span>
              <>
                <LabelTool
                  name={name}
                  id={0}
                  isHide={isHide}
                  isOnlyShow={isOnlyShow}
                  onRemove={onRemove}
                  onToggleVisible={onToggleVisible}
                  onOnlyShow={onOnlyShow}
                />
                {overlayFrames === null && has_instance ? (
                  <span className="icon" data-name={name} onClick={handleExpandLabel}>
                    {isOpen ? <RiArrowDownSFill /> : <RiArrowLeftSFill />}
                  </span>
                ) : null}
              </>
            </div>

            {isOpen &&
              children?.map((child) => {
                const { name, num, color, isHide, isOnlyShow, instance_id } = child;
                const isSelected = selectedSegment?.name === name && selectedSegment?.instanceId === instance_id;

                return (
                  <div
                    className="segment-list-item"
                    key={`segment-${name}-${instance_id}`}
                    data-name={name}
                    data-id={instance_id}
                    aria-selected={isSelected}
                    onClick={handleSelectSegment}
                  >
                    <span className="name">
                      <span className="index">{instance_id}</span>
                      <span className="square" style={{ backgroundColor: color }}></span>
                      <span className="text-wrap">
                        <span className="text">{num}</span>
                      </span>
                    </span>
                    <LabelTool
                      name={name}
                      id={instance_id}
                      isHide={isHide}
                      isOnlyShow={isOnlyShow}
                      onRemove={onRemove}
                      onToggleVisible={onToggleVisible}
                      onOnlyShow={onOnlyShow}
                    />
                  </div>
                );
              })}
          </div>
        );
      }
    );
  }
);
