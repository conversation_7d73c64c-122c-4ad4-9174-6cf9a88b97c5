import { KvLabelFoldAll, KvLabelUnfoldAll } from '@forest/icons';
import { Affix, Tooltip } from 'antd';
import { FC, useEffect, useMemo, useRef, useState } from 'react';

import { InstanceType, LabelToolCallbacks, LabelTree } from './LabelTree';

interface LabelListProps extends LabelToolCallbacks {
  stickyDirection: 'top' | 'bottom';
  title: string;
  labelTreeData: InstanceType[];
  expandedKeys: string[];
  onExpand: (expandedKeys: string[]) => void;
  onExpandAll: () => void;
  onSelectSegment: (category: string, instanceId: number) => void;
  onClickLabel?: (labelName: string) => void;
}

export const LabelList: FC<LabelListProps> = ({
  stickyDirection,
  title,
  labelTreeData,
  expandedKeys,
  onExpand,
  onExpandAll,
  onClickLabel,
  onOnlyShow,
  onRemove,
  onToggleVisible,
  onSelectSegment,
}) => {
  const [topDistance, setTopDistance] = useState<number | undefined>(undefined);
  // 控制全部的展开和收起
  const [allExpanded, setAllExpanded] = useState<boolean>(true);
  const wrapperRef = useRef<HTMLDivElement | null>(null);
  const isAffixed = useRef<boolean>(false);

  const allLabels = useMemo(() => labelTreeData.map(({ name }) => name), [labelTreeData]);

  const expandSetting = useMemo(
    () =>
      allExpanded
        ? { title: '全部收起', icon: <KvLabelFoldAll />, handler: () => onExpand([]) }
        : { title: '全部展开', icon: <KvLabelUnfoldAll />, handler: onExpandAll },
    [allExpanded, onExpand, onExpandAll]
  );

  useEffect(() => {
    if (expandedKeys.length === allLabels.length) {
      setAllExpanded(true);
    } else if (expandedKeys.length === 0) {
      setAllExpanded(false);
    }
  }, [allLabels, expandedKeys]);

  const handleClickSticky = () => {
    if (isAffixed.current === true && stickyDirection === 'top') {
      wrapperRef.current?.parentElement?.scroll(0, 0);
    }
  };

  const handleAffixChange = (affixed: boolean | undefined) => {
    if (affixed !== undefined) {
      isAffixed.current = affixed;
      if (stickyDirection === 'bottom' && affixed === false) {
        setTopDistance(32);
      }
    }
  };

  if (labelTreeData.length === 0) return null;

  return (
    <div className="label-list" ref={wrapperRef}>
      <Affix
        offsetTop={stickyDirection === 'top' ? 0 : topDistance}
        offsetBottom={stickyDirection === 'bottom' ? 0 : undefined}
        target={() => wrapperRef.current?.parentElement as HTMLElement}
        onChange={handleAffixChange}
      >
        <div className="label-list-title" onClick={handleClickSticky}>
          <span className="text">{title}</span>
          <span className="opt">
            <Tooltip placement="bottom" title={expandSetting.title}>
              <span onClick={expandSetting.handler} className="icon">
                {expandSetting.icon}
              </span>
            </Tooltip>
          </span>
        </div>
      </Affix>
      <div className="label-list-content">
        <LabelTree
          labelTreeData={labelTreeData}
          expandedKeys={expandedKeys}
          onClickLabel={onClickLabel}
          onExpand={onExpand}
          onRemove={onRemove}
          onToggleVisible={onToggleVisible}
          onOnlyShow={onOnlyShow}
          onSelectSegment={onSelectSegment}
        />
      </div>
    </div>
  );
};
