.middle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  user-select: none;
}

.loading-icon-wrap {
  position: relative;
  margin-bottom: 16px;
  width: 80px;
  height: 80px;
}

@keyframes spin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.spinner {
  width: 80px;
  height: 80px;
  animation: spin 0.75s linear infinite;
}

.loading-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.loading-text {
  color: #f5f7fa;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  text-wrap: nowrap;
  margin: 0;
}

.layout {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 5000;
  height: 100%;
}

.layout-part {
  top: 0;
  left: 0;
  z-index: 5000;
  height: 100%;
}

.container {
  width: 100vw;
  flex: 1;
  overflow: hidden;
}

.container-part {
  overflow: hidden;
}

.header-part {
  position: absolute;
  top: 0;
  left: 68px;
  background: #323f4b;
  height: 48px;
  display: flex;
  align-items: center;
  width: fit-content;
  /* 此处的值是：实际的menu长度 - HeaderSkeletonIcons 的长度 */
  padding-right: 136px;
  z-index: 5000;
}

.wrapper {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #f5f7fa;
}

.header-help-container {
  display: flex;
  align-items: center;
}

.content {
  position: static;
}

.image-scene {
  position: absolute;
  top: 56px; /* header 为 48px，距离 header 8px */
  left: 8px;
  z-index: 2;
  width: 310px;
  height: 100%;
}

.list-container {
  height: calc(100% - 112px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: #323f4b;
  border-radius: 4px;
  border: 1px solid #323f4b;
  padding-top: 32px;
  box-shadow: 0px 8px 11px 0px #00000040;
}

.label-scene {
  position: absolute;
  overflow: visible;
  width: 164px;
  height: 196px;
  top: 50vh;
  left: 50vw;
  transform: translate(-50%, -50%);
  z-index: 1;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.5);
}

.label-catalog {
  width: 242px;
  display: flex;
  flex-direction: column;

  position: absolute;
  height: calc(100% - 112px);
  top: 56px; /* header 为 48px，距离 header 8px */
  right: 8px;
  z-index: 2;
}

.label-catalog-container {
  flex: 1;
  color: #ffffff;
  overflow: auto;
  background-color: #323f4b;
}

.label-list {
  padding: 8px;
  font-size: 12px;
  user-select: none;
}

.label-list-title {
  width: 226px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  padding: 4px;
  background-color: #323f4b;
  height: 28px;
  color: #cbd2d9;
  width: 226px;
}

.label-list-title .text {
  margin-inline-start: 4px;
}

.label-list-title .opt {
  display: inline-flex;
  align-items: center;
}

.frame-list-container {
  position: absolute;
  top: calc(100vh - 48px);
  width: 100vw;
  height: 48px;
  left: 0;
  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #323f4b;
  user-select: none;
}

.footer-opt {
  position: absolute;
  color: #cbd2d9;
  font-size: 20px;
  right: 20px;
  display: inline-flex;
  align-items: center;
}
