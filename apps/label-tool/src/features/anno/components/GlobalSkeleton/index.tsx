import { Skeleton } from 'antd';
import { FC, PropsWithChildren } from 'react';
import { shallow } from 'zustand/shallow';

import { LabelHeader } from '@/components';
import { useAuth } from '@/features/auth';

import { useContextStore } from '../../stores';
import loadingIconSrc from './loading.png';

import styles from './styles.module.css';

const { Avatar, Button, Node } = Skeleton;

const SkeletonIcon: FC<{
  width?: number;
  margin?: string;
}> = ({ width = 24, margin = '0 8px' }) => {
  return (
    <Avatar
      shape={'square'}
      active
      style={{
        width: `${width}px`,
        height: `${width}px`,
        margin,
        borderRadius: '4px',
      }}
    />
  );
};

const SkeletonButton: FC<{
  width?: number;
  height?: number;
  margin?: string;
}> = ({ width = 50, height = 16, margin = '0' }) => {
  return (
    <Button
      shape={'square'}
      active
      style={{
        width: `${width}px`,
        height: `${height}px`,
        margin,
        borderRadius: '2px',
      }}
    />
  );
};

const SkeletonImage: FC<{
  width?: number;
  height?: number;
  margin?: string;
}> = ({ width = 310, height = 168, margin = '0' }) => {
  return (
    <Node
      active
      style={{
        width: `${width}px`,
        height: `${height}px`,
        margin,
      }}
    >
      {/* <img alt="item" src="/img-load-error.svg"></img> */}
      <svg xmlns="http://www.w3.org/2000/svg" width="240" height="140" viewBox="0 0 240 140" fill="none">
        <path
          opacity="0.2"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M162 38H78L78 102H162V38ZM78 30C73.5817 30 70 33.5817 70 38V102C70 106.418 73.5817 110 78 110H162C166.418 110 170 106.418 170 102V38C170 33.5817 166.418 30 162 30H78ZM104 55C104 59.9706 99.9706 64 95 64C90.0294 64 86 59.9706 86 55C86 50.0294 90.0294 46 95 46C99.9706 46 104 50.0294 104 55ZM154 94V72L134 51L109 77L101 69L86 85V94H154Z"
          fill="#323F4B"
        />
      </svg>
    </Node>
  );
};

const LoadingIcon: FC = () => {
  return (
    <div className={styles['loading-icon-wrap']}>
      <div className={styles.middle}>
        <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 120 120" fill="none">
          <path d="M96.6719 9.99951H110.005V23.3328H103.339V16.6662H96.6719V9.99951Z" fill="#008DFF" />
          <path
            d="M23.3281 110L9.99479 110L9.99479 96.6667L16.6615 96.6667L16.6615 103.333L23.3281 103.333L23.3281 110Z"
            fill="#008DFF"
          />
          <path d="M23.3281 9.99951H9.99479V23.3328H16.6615V16.6662H23.3281V9.99951Z" fill="#27CD9D" />
          <path
            d="M96.6719 110L110.005 110L110.005 96.6667L103.339 96.6667L103.339 103.333L96.6719 103.333L96.6719 110Z"
            fill="#27CD9D"
          />
        </svg>
        <img className={`${styles.middle} ${styles.spinner}`} src={loadingIconSrc} alt="" />
      </div>
    </div>
  );
};

const HeaderSkeletonIcons: FC<PropsWithChildren<{ nIcons?: number }>> = ({ nIcons = 6 }) => {
  return (
    <div className={styles['header-help-container']}>
      {new Array(nIcons).fill(1).map((_, idx) => (
        <SkeletonIcon key={idx} />
      ))}
    </div>
  );
};

export const GlobalSkeleton: FC = () => {
  const { loadingProgressInfo, labelStage, dataElementIndex } = useContextStore(
    ({ loadingProgressInfo, labelStage, dataElementIndex }) => ({
      loadingProgressInfo,
      labelStage,
      dataElementIndex,
    }),
    shallow
  );
  const { user } = useAuth();

  const loadingStr =
    loadingProgressInfo && labelStage?.currentSight?.id ? loadingProgressInfo[labelStage.currentSight.id] : '请求中';

  if (!loadingStr || loadingStr === '初始化完成') {
    return null;
  }

  const showFullLoading = dataElementIndex === 0 && loadingStr !== '优化中';

  return (
    <div className={showFullLoading ? styles.layout : styles['layout-part']}>
      <div className={showFullLoading ? styles.container : styles['container-part']}>
        {showFullLoading ? (
          <LabelHeader username={user?.name} height={48}>
            <div className={styles.wrapper}>
              <HeaderSkeletonIcons />
              <HeaderSkeletonIcons nIcons={2} />
            </div>
          </LabelHeader>
        ) : (
          <div className={styles['header-part']}>
            <HeaderSkeletonIcons />
          </div>
        )}

        <div className={styles.content}>
          {showFullLoading && (
            <div className={styles['image-scene']}>
              <div className={styles['list-container']}>
                {new Array(5).fill(1).map((_, idx) => (
                  <SkeletonImage key={idx} margin={'0 0 1px'} />
                ))}
              </div>
            </div>
          )}

          <div className={styles['label-scene']}>
            <div className={styles['loading-wrap']}>
              <LoadingIcon />
              <p className={styles['loading-text']}>{loadingStr}...</p>
            </div>
          </div>

          {showFullLoading && (
            <>
              <div className={styles['label-catalog']}>
                <div className={styles['label-catalog-container']}>
                  {new Array(2).fill(1).map((_, idx) => (
                    <div className={styles['label-list']} key={idx}>
                      <div className={styles['label-list-title']}>
                        <span className={styles['text']}>
                          <SkeletonButton />
                        </span>
                        <span className={styles['opt']}>
                          {new Array(2).fill(1).map((_, idx) => (
                            <SkeletonIcon key={idx} width={16} margin={'0 6px'} />
                          ))}
                        </span>
                      </div>
                      {new Array(10).fill(1).map((_, idx) => (
                        <SkeletonButton key={idx} height={28} width={226} margin={'0 0 4px'} />
                      ))}
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}
        </div>

        {showFullLoading && (
          <div className={styles['frame-list-container']}>
            <SkeletonButton height={24} width={400} margin={'0 16px'} />
            <SkeletonButton height={24} width={100} />
            <div className={styles['footer-opt']}>
              {new Array(3).fill(1).map((_, idx) => (
                <SkeletonIcon key={idx} margin={'0 4px'} />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
