import { Modal, Select as AntSelect } from 'antd';
import { pick } from 'lodash';
import { FC, useEffect, useMemo, useRef, useState } from 'react';
import { shallow } from 'zustand/shallow';

import { AttrForm, ExpandModal, IAttrFormMethods } from '@/components';
import { SERIES_OPT_SCOPE_MAP } from '@/features/anno/config';
import { useContextStore } from '@/features/anno/stores';
import { formatTrackIdDisplayName, labelTemplate } from '@/features/anno/utils';
import { Label, SeriesOptScope } from '@/types';
import { AnnoCompoundItem, AnnoInstanceItem } from '@/utils/annos';
import { annoTrackIdGenerator } from '@/utils/annos/GenerateTrackId';

interface AttrsModalProps {
  onUpdateAnno?: () => void;
}

export const AttrsModal: FC<AttrsModalProps> = ({ onUpdateAnno }) => {
  const [scope, setScope] = useState<SeriesOptScope>(SeriesOptScope.ALL);
  const [labelName, setLabelName] = useState<string>();
  const [trackId, setTrackId] = useState<string>();
  const attrFormRef = useRef<IAttrFormMethods>(null);

  const [modal, contextHolder] = Modal.useModal();

  const {
    currentAnno,
    job,
    editAnno,
    labelMap,
    seriesLinkage,
    dataElementIndex,
    setEditAnno,
    setSelectedAnnos,
    updateAnnoLabelAndAttrs,
  } = useContextStore(
    (state) =>
      pick(state, [
        'currentAnno',
        'job',
        'editAnno',
        'labelMap',
        'seriesLinkage',
        'dataElementIndex',
        'setEditAnno',
        'setSelectedAnnos',
        'updateAnnoLabelAndAttrs',
      ]),
    shallow
  );

  // trackId 的修改会改变 labelOptions  的选择
  const [isCompound, labelOptions] = useMemo(() => {
    if (!editAnno || editAnno.type !== 'label' || !trackId) return [];
    const isCompound = editAnno.anno instanceof AnnoCompoundItem;
    let options: Label[] = [];
    // 当 trackId 发生改变，不允许修改成其他标签类型
    if (editAnno.anno.trackId !== trackId) {
      const label = seriesLinkage.trackTypeMap.get(trackId)?.label;
      if (label) {
        setLabelName(label);
        options = [];
        const option = labelMap.get(label);
        if (option) {
          options.push(option);
        }
      } else {
        options = labelTemplate.instanceLabels;
      }
    } else {
      options = isCompound ? labelTemplate.compoundLabels : labelTemplate.instanceLabels;
    }

    return [isCompound, options.map((item) => ({ label: item.display_name, value: item.name }))];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [trackId, seriesLinkage]);

  const showDivider = useMemo(() => {
    if (!editAnno || editAnno.type !== 'label' || !labelName) return false;
    const attrs = labelMap.get(labelName)?.fullAttrs;
    return Array.isArray(attrs) && attrs.length > 0;
  }, [editAnno, labelMap, labelName]);

  const trackIdOptions = useMemo(() => {
    if (!editAnno || editAnno.type !== 'label') return undefined;
    // 组合标注物不能修改 trackId
    let disabled = editAnno.anno instanceof AnnoCompoundItem;
    // 非组合标注物，如果这个 trackId 已经在是某个组合标注物的子 trackId，那么也不能修改
    if (!disabled) {
      seriesLinkage.trackTypeMap.forEach((value) => {
        if (!disabled && value.compoundTrackIds?.includes(editAnno.anno.trackId)) {
          disabled = true;
        }
      });
    }
    if (disabled || !seriesLinkage.isEnabled || !job || !labelName) return undefined;
    // 列表不能有当前帧已有的 trackId，需要保证 trackId 在每一帧的唯一性
    const trackId = editAnno.anno.trackId,
      trackIds = new Set<string>();
    const { widgetName } = seriesLinkage.trackTypeMap.get(trackId) || {};
    for (let i = 0; i < job.elements.length; i++) {
      seriesLinkage.trackAnnoMap.forEach((map, key) => {
        const { widgetName: newWidgetName, label } = seriesLinkage.trackTypeMap.get(key) || {};
        // 显示选中的类别下，同一工具创建的标注物
        if (
          widgetName === newWidgetName &&
          labelName === label &&
          key !== trackId &&
          map.has(i) &&
          !map.has(dataElementIndex)
        ) {
          trackIds.add(key);
        }
      });
    }
    return [{ label: '新建', value: 'add' }, ...[...trackIds].map((key) => ({ label: key, value: key }))];
  }, [editAnno, seriesLinkage, job, dataElementIndex, labelName]);

  const seriesSettings = useMemo(() => {
    if (!seriesLinkage?.isEnabled) return undefined;
    let seriesOptScopeOptions: Partial<Record<SeriesOptScope, string>> = {};
    if (editAnno?.type === 'label') {
      // 如果没改变 trackId，那么如果修改 label，必须是全量修改
      if (editAnno.anno.label !== labelName) {
        seriesOptScopeOptions = {
          [SeriesOptScope.ALL]: SERIES_OPT_SCOPE_MAP[SeriesOptScope.ALL],
        };
      } else {
        seriesOptScopeOptions = SERIES_OPT_SCOPE_MAP;
      }
    }
    return {
      scope,
      changeScope: (scope: string) => setScope(scope as SeriesOptScope),
      scopeOptions: seriesOptScopeOptions,
    };
  }, [scope, seriesLinkage, labelName, editAnno]);

  // 设置初始化的值
  useEffect(() => {
    if (editAnno?.type === 'label') {
      setLabelName(editAnno.anno.label);
      setTrackId(editAnno.anno.trackId);
      setScope(SeriesOptScope.ALL);
    } else {
      setLabelName(undefined);
      setTrackId(undefined);
      setScope(SeriesOptScope.ALL);
    }
  }, [editAnno]);

  useEffect(() => {
    const trackId = editAnno?.anno.trackId;
    const trackAnnoMap = trackId ? seriesLinkage.trackAnnoMap.get(trackId) : undefined;
    if (trackAnnoMap && !trackAnnoMap.has(dataElementIndex)) {
      handleClosePanel();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataElementIndex]);

  const handleChangeLabelName = (value: string) => {
    setLabelName(value);
    // 如果类别发生变化，则处理范围重置为全部
    if (value !== editAnno?.anno?.label) {
      setScope(SeriesOptScope.ALL);
    }
  };

  const handleChangeTrackId = (value: string) => {
    setTrackId(value === 'add' ? annoTrackIdGenerator.createTrackId() : value);
  };

  const handleClosePanel = () => {
    const anno = editAnno?.anno;
    setEditAnno(null);
    // 新建标注物关闭属性面板时选中
    if (currentAnno?.opt === 'add' && anno?.name === currentAnno.anno.name) {
      setSelectedAnnos([anno]);
    }
  };

  const getConflicts = (editTrackId: string, trackId: string) => {
    const editAnnoMap = seriesLinkage.trackAnnoMap.get(editTrackId);
    const curAnnoMap = seriesLinkage.trackAnnoMap.get(trackId);
    if (!editAnnoMap || !curAnnoMap || !job) {
      return undefined;
    }
    let start = 0,
      end = job.elements.length - 1;
    switch (scope) {
      case SeriesOptScope.CURRENT:
        start = end = dataElementIndex;
        break;
      case SeriesOptScope.FORWARD:
        end = dataElementIndex - 1;
        break;
      case SeriesOptScope.BACKWARD:
        start = dataElementIndex + 1;
        break;
    }
    start = Math.min(Math.max(start, 0), job.elements.length - 1);
    end = Math.max(0, Math.min(end, job.elements.length - 1));
    const conflicts = Array.from(editAnnoMap.keys()).filter(
      (frame) => frame >= start && frame <= end && curAnnoMap.has(frame)
    );
    return conflicts.length > 0 ? conflicts : undefined;
  };

  const handleSaveAttrs = () => {
    if (
      editAnno?.anno instanceof AnnoInstanceItem &&
      trackId &&
      trackId !== editAnno.anno.trackId &&
      seriesLinkage?.isEnabled
    ) {
      const oldTrackId = editAnno.anno.trackId;
      const conflicts = getConflicts(oldTrackId, trackId);
      if (conflicts) {
        // 校验有冲突
        const prevKey = seriesLinkage.trackTypeMap.get(oldTrackId)?.label;
        if (prevKey && labelName) {
          const prevName = labelMap.get(prevKey)?.display_name ?? '未知标签';
          const curName = labelMap.get(labelName)?.display_name ?? '未知标签';
          modal.error({
            zIndex: 5001,
            content: `在第${conflicts.map((value) => value + 1).join('，')}帧中同时存在
            [${prevName} - ${oldTrackId}]和[${curName} - ${trackId}]。`,
            okText: '确定',
          });
        }
        return;
      }
    }

    // 获取表单数据
    const data = attrFormRef.current?.submit();
    // 保存更新
    if (data) {
      updateAnnoLabelAndAttrs(data.label, data.attrs);
      // 如果是连续帧，则需要更新到范围内的所有帧
      if (editAnno?.anno && seriesLinkage.isEnabled && scope) {
        // 更新标签
        seriesLinkage.onTrackIdLabelAndAttrUpdate(
          scope,
          editAnno.anno.trackId,
          dataElementIndex,
          data.label.name,
          data.attrs
        );
      }
    }
    // 处理 trackId
    if (
      editAnno?.anno instanceof AnnoInstanceItem &&
      trackId &&
      trackId !== editAnno.anno.trackId &&
      seriesLinkage?.isEnabled
    ) {
      const oldTrackId = editAnno.anno.trackId;
      editAnno.anno.trackId = trackId;
      seriesLinkage.onTrackIdChange(scope, oldTrackId, dataElementIndex, trackId);
    }
    onUpdateAnno && onUpdateAnno();
    handleClosePanel();
  };

  if (editAnno?.type !== 'label') return null;

  return (
    <ExpandModal
      title={formatTrackIdDisplayName(editAnno.anno.trackId)}
      onCancel={handleClosePanel}
      onOk={handleSaveAttrs}
      width={240}
      expandedSize={{
        height: 400,
        width: 400,
      }}
      positionStyle={isCompound ? { bottom: 0, right: 250 } : { top: 0, right: 250, bottom: 'unset' }}
      scopeSettings={seriesSettings}
    >
      <div className="select-label">
        <div className="tag">类别</div>
        <AntSelect
          className="selector"
          onChange={handleChangeLabelName}
          variant="borderless"
          options={labelOptions}
          disabled={labelOptions?.length === 0}
          value={labelName}
          placeholder="请选择类别"
        />
      </div>
      <div className="select-label">
        <div className="tag">编号</div>
        <AntSelect
          className="selector"
          variant="borderless"
          onChange={handleChangeTrackId}
          options={trackIdOptions ?? []}
          // 组合标注物是不能修改 trackId 的
          disabled={trackIdOptions?.length === 0}
          value={trackId}
          placeholder="请选择编号"
        />
      </div>

      {showDivider && <div className="divider"></div>}
      <AttrForm labelMap={labelMap} anno={editAnno.anno} labelName={labelName} ref={attrFormRef} />
      {contextHolder}
    </ExpandModal>
  );
};
