.comment-wrap {
  position: fixed;
  z-index: 1000;
  width: 100%;
  height: 100vh;
  bottom: 0;
  left: 0;
  overflow: hidden;
  pointer-events: none;
}
.comment {
  position: absolute;
  width: 180px;
  height: fit-content;
  font-size: 12px;
  pointer-events: auto;
}

.comment::before {
  content: ' ';
  position: absolute;
  right: -16px;
  width: 50px;
  height: 100%;
  min-height: 28px;
}

.error-button {
  position: absolute;
  right: -12px;
  width: 40px;
  height: 28px;
  background-color: #f54c46;
  border-radius: 4px;
  text-align: center;
  line-height: 28px;
  color: #f5f7fa;
  cursor: pointer;
}
.error-button:hover {
  background: #f54c46e5;
}
