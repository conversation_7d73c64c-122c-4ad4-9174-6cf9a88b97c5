import { pick } from 'lodash';
import { FC } from 'react';

import { useContextStore } from '@/features/anno/stores';

import { THREE_VIEW_RIGHT, THREE_VIEW_WIDTH } from '../../config';
import { CommentBar, IOptType } from '../CommentBar';

import styles from './styles.module.css';

export const AnnoComment: FC = () => {
  const {
    currentPhase,
    commentHoverInfo,
    commentConfigDisplay,
    currentComment,
    labelStage,
    selectedComment,
    setSelectedComment,
    addResolveComment,
    deleteComment,
    setCommentModal,
    createSnapshot,
    setCommentHoverInfo,
    setCurrentComment,
    clearCommentHoverTimeoutId,
  } = useContextStore((state) =>
    pick(state, [
      'currentPhase',
      'commentHoverInfo',
      'commentConfigDisplay',
      'currentComment',
      'labelStage',
      'selectedComment',
      'setSelectedComment',
      'addResolveComment',
      'deleteComment',
      'setCommentModal',
      'createSnapshot',
      'setCommentHoverInfo',
      'setCurrentComment',
      'clearCommentHoverTimeoutId',
    ])
  );

  const handleAddComment = () => {
    if (!commentHoverInfo || !commentHoverInfo.anno) return;

    setCommentModal({
      type: 'add',
      scope: 'object',
      obj_uuids: [commentHoverInfo.anno.name],
    });
  };

  const handleClickComment = (type: IOptType) => {
    if (!comment) return;

    setCommentHoverInfo(null);
    switch (type) {
      case 'resolve':
        addResolveComment([comment.uuid]);
        createSnapshot();
        break;

      case 'delete':
        if (comment.scope === 'unspecified') {
          labelStage?.currentSight?.removeObjectByName(comment.uuid);
          if (selectedComment?.uuid === comment.uuid) setSelectedComment(null);
        }
        deleteComment(comment.uuid);
        createSnapshot();
        break;

      case 'edit':
        if (comment.scope === 'unspecified' && comment.extra_info?.position) {
          setCommentModal({
            type: 'missed',
            uuid: comment.uuid,
            position: comment.extra_info?.position,
            trackId: comment.trackId,
            reasons: comment.reasons,
            content: comment.content,
          });
        } else {
          setCommentModal({
            type: 'edit',
            uuid: comment.uuid,
            reasons: comment.reasons,
            content: comment.content,
          });
        }
        break;
      case 'missed':
        setCurrentComment(comment);
        break;
    }
  };

  const handleHideComment = () => {
    setCommentHoverInfo(null);
  };

  if (!commentHoverInfo) return null;

  const { anno, comment, position } = commentHoverInfo;

  let commentTitle = '',
    commentReasons: string[] = [];
  if (comment) {
    const displayInfo = commentConfigDisplay[comment.reasons.class];
    if (displayInfo) {
      commentTitle = displayInfo.displayName;
      commentReasons = comment.reasons.reasons.map((reason) => displayInfo.reasons[reason]);
    }
  }

  const [top, right, bottom, left] = position;

  return (
    <div
      className={styles['comment-wrap']}
      style={{
        width: labelStage?.currentSight?.subViews?.isShow
          ? `calc(100% - ${THREE_VIEW_WIDTH + THREE_VIEW_RIGHT}px)`
          : '100%',
        right: right === 'unset' ? 'unset' : 0,
        left: left === 'unset' ? 'unset' : 0,
      }}
    >
      <div
        className={styles.comment}
        style={{
          top,
          right,
          bottom,
          left,
        }}
        onMouseEnter={clearCommentHoverTimeoutId}
        onMouseLeave={handleHideComment}
      >
        {comment ? (
          <CommentBar
            commentTitle={commentTitle}
            commentReasons={commentReasons}
            commentClass={comment.scope ?? ''}
            commentContent={comment.content ?? ''}
            status={comment.status}
            onClickComment={handleClickComment}
            currentComment={currentComment}
            currentPhase={currentPhase}
            resolvePhase={comment.resolve_phase}
          />
        ) : (
          currentPhase &&
          currentPhase.number > 1 &&
          anno && (
            <div className={styles['error-button']} onClick={handleAddComment}>
              错误
            </div>
          )
        )}
      </div>
    </div>
  );
};
