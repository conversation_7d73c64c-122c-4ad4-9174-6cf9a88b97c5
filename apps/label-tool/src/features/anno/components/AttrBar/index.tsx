import { Label } from '@/types';

import './styles.css';

export interface AttrBarProps {
  trackId: string;
  label: Label;
  attrsStr?: string;
  selected: boolean;
  status?: 'collapse' | 'expand';
  onOpenPanel?: () => void;
  onHover: () => void;
}
export const AttrBar = ({
  trackId,
  label,
  attrsStr,
  selected,
  status = 'collapse',
  onOpenPanel,
  onHover,
}: AttrBarProps) => {
  return (
    <div
      className={`attr-bar-content attr-bar-${status}`}
      aria-selected={selected}
      onClick={onOpenPanel}
      onMouseEnter={onHover}
      style={{ background: label.color }}
    >
      <div className="attr-text">
        <span>
          #{trackId}: {label.display_name}
        </span>
        {attrsStr ? <span>/{attrsStr}</span> : null}
      </div>
    </div>
  );
};
