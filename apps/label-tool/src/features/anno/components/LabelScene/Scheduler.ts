import { EventDispatcher } from 'three';

import { StoreType } from '@/features/anno/stores';
import { CurrentAnnoType } from '@/features/anno/stores/slices';
import { WidgetObject } from '@/types';
import { AnnoFactory, AnnoInstanceItem } from '@/utils/annos';
import { Selector, Stage } from '@/utils/render';
import type { ToolType } from '@/utils/tool';

import { DOUBLE_CLICK_INTERVAL, ROTATION_DISTANCE_THRESHOLD } from '../../config';
import {
  Caliper3dHandler,
  checkDeleteAnnos,
  CuboidHandler,
  DirectionCaliper3dHandler,
  EventHandler,
  HandlerOptions,
  IInteractEvent,
  labelTemplate,
  Line3dHandler,
  MoveHandler,
  Point3dHandler,
  Poly3dHandler,
  Pyramid3dHandler,
  Spline3dHandler,
} from '../../utils';

// 为了实现双击记录上次的点击时间
let lastClickTime = 0;
let isDoubleClick = false;

// 为了区分选择和旋转，如果位移的平方超过阈值，判断为旋转
let mouseDownClientX = 0;
let mouseDownClientY = 0;
let mouseMovedOverThreshold = false;

// 允许使用放大镜的工具
const FOCUS_MODE_TOOL: Array<ToolType> = ['line3d', 'spline3d', 'caliper', 'point3d', 'poly3d'];
export const POINTER_STATUS = 'pointerStatus';
export enum PointerStatus {
  Rotate = 'rotate',
  Move = 'move',
  Using = 'using',
  None = 'none',
}

export class Scheduler implements IInteractEvent {
  public dispatcher: EventDispatcher<{ type: string; data: string }>;

  private pointerStatus: string;
  // 可能是其他工具的 handler（EventHandler 类型），也可能是选择工具下的 MoveHandler
  private childHandler: EventHandler | MoveHandler | null;
  private stage: Stage;
  private store: StoreType;
  private selector: Selector;
  private isFusion4d: boolean | undefined;
  private handlerOptionDefaults: HandlerOptions;

  constructor(stage: Stage, store: StoreType, onChange?: HandlerOptions['onInfoChange']) {
    this.stage = stage;
    this.store = store;
    this.childHandler = null;
    this.pointerStatus = PointerStatus.Rotate;
    this.dispatcher = new EventDispatcher();

    // TODO: 线条感应范围需改成根据sight变化
    this.selector = new Selector(this.stage, { lineThreshold: 8, range: ['container'] });
    this.isFusion4d = undefined;

    const { fusionLinkage } = store.getState();

    this.handlerOptionDefaults = {
      onObjectBegin: (isClearSelected = true) => {
        this.clearEditObject();
        if (isClearSelected) {
          this.store.getState().setSelectedAnnos([]);
        }
        return true;
      },
      onObjectMount: (target, opt) => {
        const { isClone = false, allowKeepAdd = false, indicateLine = '' } = opt ?? {};
        const { currentLabel, selectedAnnos, setCurrentAnno, setEditAnno, setSelectedAnnos } = this.store.getState();
        if (target && this.stage.currentSight) {
          let labelName;
          let attrs;
          if (isClone && selectedAnnos.length === 1) {
            const anno = selectedAnnos[0];
            labelName = anno.label;
            attrs = { ...anno.attrs };
          } else {
            // 1、可以在此处进行校验，如果不符合要求，可以调用 onFail
            // 2、计算得到标注物标签
            let label;
            if (currentLabel) {
              label = currentLabel;
            } else {
              label = labelTemplate.matchLabelByWidget(target.userData.type, target.scale.toArray());
              // TODO: 待讨论：会有匹配不到标签的情况吗
              if (!label) {
                this.stage.currentSight?.removeObject(target);
                console.error('未找到匹配的类别');
                return false;
              }
            }
            labelName = label.name;
            attrs = { ...label.defaultAttrValues };
            AnnoFactory.changeColor(target, label.color);
          }

          const widget = AnnoFactory.transOutWidget(target, this.stage.currentSight.getMatrixs()[0]);
          if (!widget) return false;

          const anno = new AnnoInstanceItem(target.name, labelName, widget, {
            attrs,
            ...(indicateLine ? { seriesPoint: { isAdsorb: true, indicateLine } } : {}),
          });

          // 添加到数据层
          setCurrentAnno({ anno, opt: 'add' });

          if (allowKeepAdd) {
            AnnoFactory.setStatus(target, 'default');
            return true;
          }

          if (isClone) {
            setSelectedAnnos([anno]);
          } else {
            setEditAnno({ anno, type: 'label' });
          }
        } else {
          setEditAnno(null);
        }
        return true;
      },
      onObjectUpdate: (target) => {
        const { editAnno, selectedAnnos, setCurrentAnno, getAnnoInstanceItem } = this.store.getState();

        const anno = editAnno?.type === 'widget' ? editAnno?.anno : selectedAnnos[0];
        if (target && anno?.name === target.name && this.stage.currentSight) {
          const oldWidget = target.userData.widget;
          const newWidget = AnnoFactory.transOutWidget(target, this.stage.currentSight.getMatrixs()[0]);

          if (newWidget) {
            // 用于保证通过 object 改动的数据层，不会重新修改 object
            // 使用 onObjectUpdate 回调更新数据层时，将返回的数据绑定到当前的 handler 中，用于检查编辑对象是否需要重新执行 setEditObjectEffects
            this.childHandler instanceof EventHandler && this.childHandler.setWidget(newWidget);

            // 处理回溯时添加点需要的数据
            let options: CurrentAnnoType = { anno, opt: 'update', widget: newWidget, prevWidget: oldWidget };
            if (anno instanceof AnnoInstanceItem) {
              switch (anno.widget.name) {
                case 'line3d':
                  const seriesPoints: Record<string, WidgetObject> = {};
                  const prevSeriesPoints: Record<string, WidgetObject> = {};
                  const matrixs = this.stage.currentSight?.getMatrixs()[0];
                  target?.children
                    ?.filter((obj) => obj.userData?.seriesPoint)
                    ?.forEach((obj) => {
                      const point = getAnnoInstanceItem(obj.name);
                      point && (prevSeriesPoints[obj.name] = point.widget);
                      if (this.stage.currentSight) {
                        const widget = AnnoFactory.transOutWidget(obj, matrixs);
                        if (widget) seriesPoints[obj.name] = widget;
                      }
                    });
                  options = { ...options, seriesPoints, prevSeriesPoints };
                  break;
                case 'point3d':
                  if (anno.seriesPoint)
                    options = {
                      ...options,
                      seriesPoints: {
                        ...anno.seriesPoint,
                        isAdsorb: target?.parent?.name === anno.seriesPoint.indicateLine,
                      },
                      prevSeriesPoints: anno.seriesPoint,
                    };
                  break;
                default:
                  break;
              }
            }
            setCurrentAnno(options);

            return true;
          }
        }

        return false;
      },

      onObjectRemove: (target) => {
        const { commentMap, annotations, setCurrentAnno } = this.store.getState();
        if (target) {
          const anno = annotations[target.name];
          if (!anno) return true;
          // 检查能否删除，只有这种情况下有错误信息返回，会无法删除
          const errMsg = checkDeleteAnnos([anno], commentMap);
          if (errMsg) return false;
          setCurrentAnno({ anno, opt: 'remove' });
        }
        return true;
      },

      onPointerChange: (point3, source = 'move') => {
        if (source === 'click') {
          fusionLinkage.updateFoucsPoint(point3);
        }
      },
      onInfoChange: onChange,
    };
  }

  get handler() {
    return this.childHandler;
  }

  public onClick(event: MouseEvent) {}

  // 参考编辑流程文档：https://konverydata.feishu.cn/wiki/J4MEwDd9mio1rvkjTJTcbzbAng5
  public onPointerDown(event: PointerEvent | MouseEvent) {
    const { selectedAnnos, currentTool, getAnnoInstanceItem } = this.store.getState();
    const intersectObject = this.selector.getHoverObject();

    // 编辑工具
    if (currentTool !== 'select') {
      // 旋转模式下，左键失效
      if (this.pointerStatus === PointerStatus.Rotate) {
        return;
      }

      // 未获取到 hover 对象 或 强制新建
      if ((!intersectObject || event.shiftKey) && this.childHandler?.onPointerDown(event)) {
        this.pointerStatus = PointerStatus.Using;
        this.dispatchPointerEvent();
      }
      return;
    }

    // 选择工具
    // 以下逻辑仅响应鼠标左键
    if (event.button !== 0) return;

    // 多选模式
    if (event.metaKey || event.ctrlKey) return;

    // 用于双击判断
    const clickTime = Date.now();
    const clickTimeInterval = clickTime - lastClickTime;
    lastClickTime = clickTime;

    if (selectedAnnos.length === 1) {
      // 在 pointerUp 的时候进入标注物编辑态
      if (intersectObject?.visible && clickTimeInterval < DOUBLE_CLICK_INTERVAL) {
        isDoubleClick = true;
        return;
      }

      // 是否点击当前唯一选中的标注物（包括选择框范围）
      if (
        this.childHandler instanceof MoveHandler &&
        this.childHandler.intersectCurrentObject(event.offsetX, event.offsetY)
      ) {
        const item = getAnnoInstanceItem(selectedAnnos[0].name);
        if (item) {
          // 移动标注物 或者 拖动复制
          this.pointerStatus = PointerStatus.Move;
          this.childHandler.onPointerDown(event);

          // 处理 pointerStatus 更新（禁用旋转画布）
          this.dispatchPointerEvent();

          return;
        }
      }
    }

    // 旋转画布，记录按下的位置
    mouseDownClientX = event.clientX;
    mouseDownClientY = event.clientY;
  }

  public onPointerMove(event: PointerEvent | MouseEvent) {
    const { currentTool, lot } = this.store.getState();

    // 编辑工具
    if (currentTool !== 'select') {
      // 鼠标按下时，如果是旋转模式，失效
      if (event.buttons > 0 && this.pointerStatus === PointerStatus.Rotate) {
        return;
      }
      this.childHandler?.onPointerMove(event);

      // 禁用射线感知
      this.selector.setIntersects([]);

      return;
    }

    // 选择工具

    if (event.buttons > 0) {
      if (this.pointerStatus === PointerStatus.Move) {
        // 移动标注物
        this.childHandler?.onPointerMove(event);
        return;
      }

      // 当之前最大移动距离的平方未超过阈值时，计算并判断此时是否达到阈值。用于旋转判断。
      if (!mouseMovedOverThreshold) {
        // 开方运算成本较高，阈值用平方值判断
        const travelDistanceSquare =
          Math.pow(mouseDownClientX - event.clientX, 2) + Math.pow(mouseDownClientY - event.clientY, 2);
        mouseMovedOverThreshold = travelDistanceSquare >= ROTATION_DISTANCE_THRESHOLD;
      }
      // 旋转画布不用更新射线感知
      if (mouseMovedOverThreshold) {
        return;
      }
    }

    if (this.isFusion4d === undefined) {
      this.isFusion4d = lot?.data_type === 'fusion4d';
    }

    // 更新射线感知
    this.selector.updateRaycaster(event, undefined, undefined, this.isFusion4d);
  }

  public onPointerUp(event: PointerEvent | MouseEvent) {
    const {
      selectedAnnos,
      currentTool,
      commentMap,
      currentComment,
      getAnnoInstanceItem,
      updateSelectedAnno,
      setSelectedAnnos,
      setEditAnno,
      setSelectedComment,
    } = this.store.getState();

    // 编辑工具
    if (currentTool !== 'select') {
      // 旋转模式下，左键失效
      if (this.pointerStatus === PointerStatus.Rotate) {
        return;
      }

      if (this.pointerStatus === PointerStatus.Using) {
        this.pointerStatus = PointerStatus.None;
        this.dispatchPointerEvent();
        // 跟 childHandler.onPointerDown 对应
        this.childHandler?.onPointerUp(event);
      }

      return;
    }

    // 选择工具
    // 以下逻辑仅响应鼠标左键
    if (event.button !== 0) return;

    if (this.pointerStatus === PointerStatus.Move) {
      // 结束移动标注物
      this.childHandler?.onPointerUp(event);
      // 允许旋转画布
      this.pointerStatus = PointerStatus.Rotate;
      this.dispatchPointerEvent();
      return;
    }

    // 旋转画布判断（按下鼠标左键并且位移的平方超过阈值）
    if (mouseMovedOverThreshold) {
      mouseMovedOverThreshold = false;
      return;
    }

    const intersectObject = this.selector.getHoverObject();
    // 未获取到 hover 对象
    if (!intersectObject) {
      // 清除编辑对象和选中注解
      setSelectedAnnos([]);
      return;
    }

    if (intersectObject.visible) {
      const item = getAnnoInstanceItem(intersectObject.name),
        comment = commentMap[intersectObject.name];
      if (item) {
        // 判断是否是双击
        if (isDoubleClick && item === selectedAnnos[0]) {
          setEditAnno({ anno: item, type: 'widget' });
          isDoubleClick = false;
          return;
        }

        if (event.metaKey || event.ctrlKey) {
          // 多选模式
          updateSelectedAnno(item);
        } else {
          // 单选模式
          setSelectedAnnos([item], 'LabelScene');
        }
      } else if (comment && comment.scope === 'unspecified' && !currentComment) {
        // 在修改漏标时，无法选中其他漏标
        setSelectedComment(comment);
      }
    }
  }

  public onMouseEnter(evt: MouseEvent) {
    this.childHandler?.onMouseEnter(evt);
  }

  public onMouseLeave(evt: MouseEvent) {
    this.childHandler?.onMouseLeave(evt);
  }

  public onKeyDown(event: KeyboardEvent) {
    event.preventDefault();

    if (event.code === 'Space' && this.pointerStatus === PointerStatus.Rotate) return;

    // 当 childHandler 为 MoveHandler 的时候不响应 Space（移动标注物时不支持按空格旋转）
    if (
      event.code === 'Space' &&
      this.pointerStatus === PointerStatus.None &&
      this.childHandler instanceof EventHandler
    ) {
      this.pointerStatus = PointerStatus.Rotate;
      this.childHandler.muteContorls(true);
      this.dispatchPointerEvent();
      return;
    }

    if (this.childHandler?.onKeyDown(event)) {
      event.stopPropagation();
      return;
    }
  }
  public onKeyUp(event: KeyboardEvent) {
    event.preventDefault();

    // 当 childHandler 为 MoveHandler 的时候不响应 Space（移动标注物时不支持按空格旋转）
    if (
      event.code === 'Space' &&
      this.pointerStatus === PointerStatus.Rotate &&
      this.childHandler instanceof EventHandler
    ) {
      this.pointerStatus = PointerStatus.None;
      this.childHandler.muteContorls(false);
      this.dispatchPointerEvent();
      return;
    }

    if (this.childHandler?.onKeyUp(event)) {
      event.stopPropagation();
      return;
    }
  }

  /**
   * 设置工具对应的 childHandler
   * @param toolName 工具名
   * @returns 是否设置成功
   */
  public setChildHandler(toolName: ToolType) {
    // 跳过不需要切换的情况
    if (this.childHandler?.type === toolName) {
      return;
    }

    const { setCurrentTool } = this.store.getState();

    this.store.getState().fusionLinkage.setFocusMode(FOCUS_MODE_TOOL.includes(toolName));

    // 设置选择工具
    if (toolName === 'select') {
      this.clearEditObject(false);
      this.childHandler?.destroy();
      this.childHandler = new MoveHandler(this.stage, this.handlerOptionDefaults);

      if (this.pointerStatus !== PointerStatus.Rotate) {
        this.pointerStatus = PointerStatus.Rotate;
        this.dispatchPointerEvent();
      }

      return true;
    }

    // 设置重置工具
    if (toolName === 'reset') {
      if (!this.childHandler) {
        // 当前没有 handler，则将工具切换到选择
        setCurrentTool('select');
        return;
      }

      // 记录当前的 handlerType
      const currentTool = this.childHandler.type;
      // 通过销毁后新建来重置当前handler
      this.clearEditObject(false);
      this.childHandler.destroy();
      this.childHandler = null;
      // 新建
      setCurrentTool(currentTool);

      return true;
    }

    // 设置标注或辅助工具
    this.pointerStatus = PointerStatus.None;
    this.dispatchPointerEvent();
    if (this.childHandler?.type !== toolName) {
      this.clearEditObject(false);
      this.childHandler?.destroy();

      const newHandler = this.createEventHandler(toolName);

      // 创建失败，默认恢复到选择工具
      if (newHandler === null) {
        setCurrentTool('select');
        return false;
      }

      this.childHandler = newHandler;
    }

    return true;
  }

  // 根据传入的数据来创建 handler
  private createEventHandler(tool: ToolType, options: Partial<HandlerOptions> = {}) {
    // 确保当前有 currentSight
    if (!this.stage.currentSight) return null;
    const that = this;
    const { setCommentModal } = this.store.getState();

    // TODO: 检查当前场景类型与要创建的工具是否匹配
    const opts = {
      ...this.handlerOptionDefaults,
      ...options,
    };
    switch (tool) {
      case 'cuboid':
        return new CuboidHandler('cuboid', this.stage, opts);
      case 'line3d':
        return new Line3dHandler('line3d', this.stage, opts);
      case 'spline3d':
        return new Spline3dHandler('spline3d', this.stage, opts);
      case 'point3d':
        return new Point3dHandler('point3d', this.stage, opts);
      case 'series-point3d':
        return new Point3dHandler('series-point3d', this.stage, opts);
      case 'caliper':
        return new Caliper3dHandler('caliper', this.stage, {});
      case 'caliper-h':
        return new DirectionCaliper3dHandler('caliper-h', this.stage, {});
      case 'caliper-v':
        return new DirectionCaliper3dHandler('caliper-v', this.stage, {});
      case 'poly3d':
        return new Poly3dHandler('poly3d', this.stage, opts);
      case 'pyramid':
        return new Pyramid3dHandler('pyramid', this.stage, {
          onObjectMount(obj) {
            if (!obj || !that.stage.currentSight) return false;
            setCommentModal({
              type: 'missed',
              position: obj.position.toArray(),
              uuid: obj.name,
            });
          },
        });
      default:
        return null;
    }
  }

  public setSelects(selects: Set<string>) {
    this.stage.currentSight?.selects.flush(selects);
  }

  public setEditObject(name: string) {
    const editObject = this.stage.currentSight?.getObjectByName(name);

    if (editObject) {
      if (this.childHandler?.type !== 'select') {
        // point3d 需要进入编辑态后立刻清空鼠标找点，为 pointerDown 做准备
        this.selector.setIntersects([]);
      }
      this.childHandler?.setEditObject(editObject);
    }
  }

  /**
   * 清除主视图当前编辑对象
   * @param triggerUpdate 是否触发 currentAnno 更新
   */
  public clearEditObject(triggerUpdate: boolean = true) {
    this.childHandler?.setEditObject(undefined);

    if (triggerUpdate) {
      this.store.getState().setCurrentAnno(null);
    }
  }

  /**
   * 重置状态，常用于 stage.currentSight 变更时
   */
  public reset() {
    this.selector.reset();
    this.childHandler?.setEditObject(undefined);
  }

  private dispatchPointerEvent() {
    this.dispatcher.dispatchEvent({
      type: POINTER_STATUS,
      data: this.pointerStatus,
    });
  }
}
