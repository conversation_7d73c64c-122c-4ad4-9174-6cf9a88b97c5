import { MessageInstance } from 'antd/es/message/interface';
import { EventDispatcher } from 'three';

import { frameAnnoStore, StoreType } from '@/features/anno/stores';
import { RawPoints, Selector, Stage } from '@/utils/render';
import type { ToolType } from '@/utils/tool';

import { ROTATION_DISTANCE_THRESHOLD } from '../../config';
import {
  EventSelector,
  HandlerOptions,
  IInteractEvent,
  MoveHandler,
  Poly3dSelector,
  Pyramid3dHandler,
} from '../../utils';
import { POINTER_STATUS, PointerStatus } from './Scheduler';

// 为了区分选择和旋转，如果位移的平方超过阈值，判断为旋转
let mouseDownClientX = 0;
let mouseDownClientY = 0;
let mouseMovedOverThreshold = false;

export class SseScheduler implements IInteractEvent {
  public dispatcher: EventDispatcher<{ type: string; data: string }>;

  private pointerStatus: string;
  private stage: Stage;
  private canvas: HTMLCanvasElement | null;
  private store: typeof frameAnnoStore;
  private messageApi: MessageInstance | null;
  private childHandler: EventSelector | MoveHandler | Pyramid3dHandler | null;
  private selector: Selector | null;
  private handlerOptionDefaults: HandlerOptions;

  constructor(stage: Stage, store: StoreType, messageApi: MessageInstance | null, canvas: HTMLCanvasElement | null) {
    this.stage = stage;
    this.canvas = canvas;
    this.store = store as typeof frameAnnoStore;
    this.messageApi = messageApi;
    this.childHandler = null;
    this.pointerStatus = PointerStatus.None;
    this.dispatcher = new EventDispatcher();

    // TODO: 线条感应范围需改成根据sight变化
    this.selector = new Selector(stage, { lineThreshold: 8, range: ['container'] });

    this.handlerOptionDefaults = {
      onObjectMount: (_, opt) => {
        if (!opt?.selectionPoints) return false;
        (stage.currentSight?.getUsingRawObject() as RawPoints)?.scheduler?.initPaint(opt.selectionPoints);
        return true;
      },
    };
  }

  public onClick = () => {};

  public onPointerDown = (event: PointerEvent) => {
    const { currentTool, currentType, selectedSegment } = this.store.getState();
    const intersectObject = this.selector?.getHoverObject();

    if (currentTool !== 'select') {
      // 旋转模式下，左键失效
      if (this.pointerStatus === PointerStatus.Rotate) {
        return;
      }

      if (currentType === 'segment') {
        // 点击其他 dom 时 canvas 会失焦
        if (document.activeElement !== this.canvas) {
          this.canvas?.focus();
        }

        if (currentTool === 'poly3d' && !selectedSegment) {
          this.messageApi?.error('请先选择标签');
          return;
        }
      }

      // 未获取到 hover 对象 或 强制新建
      if ((!intersectObject || event.shiftKey) && this.childHandler?.onPointerDown(event)) {
        this.pointerStatus = PointerStatus.Using;
        this.dispatchPointerEvent();
      }
      return;
    }

    // 选择工具,以下逻辑仅响应鼠标左键
    if (event.button !== 0) return;

    // 旋转画布，记录按下的位置
    mouseDownClientX = event.clientX;
    mouseDownClientY = event.clientY;
  };

  public onPointerMove = (event: PointerEvent) => {
    const { currentTool } = this.store.getState();

    if (currentTool !== 'select') {
      // 鼠标按下时，如果是旋转模式，失效
      if (event.buttons > 0 && this.pointerStatus === PointerStatus.Rotate) {
        return;
      }
      this.childHandler?.onPointerMove(event);

      // 禁用射线感知
      this.selector?.setIntersects([]);

      return;
    }

    // 选择工具
    if (event.buttons > 0) {
      // 当之前最大移动距离的平方未超过阈值时，计算并判断此时是否达到阈值。用于旋转判断。
      if (!mouseMovedOverThreshold) {
        // 开方运算成本较高，阈值用平方值判断
        const travelDistanceSquare =
          Math.pow(mouseDownClientX - event.clientX, 2) + Math.pow(mouseDownClientY - event.clientY, 2);
        mouseMovedOverThreshold = travelDistanceSquare >= ROTATION_DISTANCE_THRESHOLD;
      }
      // 旋转画布不用更新射线感知
      if (mouseMovedOverThreshold) {
        return;
      }
    }

    // 更新射线感知
    this.selector?.updateRaycaster(event);
  };

  public onPointerUp = (event: PointerEvent) => {
    const { currentTool } = this.store.getState();

    if (currentTool !== 'select') {
      // 旋转模式下，左键失效
      if (this.pointerStatus === PointerStatus.Rotate) {
        return;
      }

      if (this.pointerStatus === PointerStatus.Using) {
        this.pointerStatus = PointerStatus.None;
        this.dispatchPointerEvent();
        // 跟 childHandler.onPointerDown 对应
        this.childHandler?.onPointerUp(event);
      }

      return;
    }
  };

  public onMouseEnter = (evt: MouseEvent) => {
    this.childHandler?.onMouseEnter(evt);
  };

  public onMouseLeave = (evt: MouseEvent) => {
    this.childHandler?.onMouseLeave(evt);
  };

  public onKeyDown = (event: KeyboardEvent) => {
    event.preventDefault();

    if (event.code === 'Space') {
      if (this.pointerStatus === PointerStatus.Rotate) return;

      if (this.pointerStatus === PointerStatus.None && this.childHandler instanceof EventSelector) {
        this.pointerStatus = PointerStatus.Rotate;
        this.dispatchPointerEvent();
        return;
      }
    }

    if (this.childHandler?.onKeyDown(event)) {
      event.stopPropagation();
      return;
    }
  };

  public onKeyUp = (event: KeyboardEvent) => {
    event.preventDefault();

    if (
      event.code === 'Space' &&
      this.pointerStatus === PointerStatus.Rotate &&
      this.childHandler instanceof EventSelector
    ) {
      this.pointerStatus = PointerStatus.None;
      this.dispatchPointerEvent();
      return;
    }

    if (this.childHandler?.onKeyUp(event)) {
      event.stopPropagation();
      return;
    }
  };

  private destroyHandler = () => {
    if (!this.childHandler) return;
    this.childHandler.destroy();
    this.childHandler = null;
  };

  // 通用的设置 pointerStatus 和 dispatch 事件
  private setPointerStatusAndDispatch = (status: PointerStatus) => {
    if (this.pointerStatus !== status) {
      this.pointerStatus = status;
      this.dispatchPointerEvent();
    }
  };

  public createChildHandler = (toolName: ToolType) => {
    const { setCurrentTool } = this.store.getState();

    this.setPointerStatusAndDispatch(PointerStatus.None);
    this.destroyHandler();

    const newHandler = this.createEventHandler(toolName);

    // 创建失败，默认恢复到选择工具
    if (newHandler === null) {
      setCurrentTool('select');
      return false;
    }

    this.childHandler = newHandler;
    return true;
  };

  /**
   * 设置工具对应的 childHandler
   * @param toolName 工具名
   * @returns 是否设置成功
   */
  public setChildHandler = (toolName: ToolType) => {
    // 跳过不需要切换的情况
    if (this.childHandler?.type === toolName) return;

    const { setCurrentTool } = this.store.getState();

    switch (toolName) {
      case 'select':
        if (this.stage) {
          this.destroyHandler();
          this.childHandler = new MoveHandler(this.stage, this.handlerOptionDefaults);
          this.setPointerStatusAndDispatch(PointerStatus.Rotate);
        }
        break;
      case 'reset':
        if (!this.childHandler) {
          setCurrentTool('select');
          return;
        } else {
          const currentTool = this.childHandler.type;
          this.destroyHandler();
          setCurrentTool(currentTool);
        }
        break;
      default:
        return this.createChildHandler(toolName);
    }

    return true;
  };

  // 根据传入的数据来创建 handler
  private createEventHandler = (tool: ToolType, options: Partial<HandlerOptions> = {}) => {
    // 确保当前有 currentSight
    if (!this.stage?.currentSight) return null;
    const that = this;
    const { setCommentModal } = this.store.getState();
    const opts = {
      ...this.handlerOptionDefaults,
      ...options,
    };
    switch (tool) {
      case 'poly3d':
        return new Poly3dSelector('poly3d', this.stage, opts);
      case 'pyramid':
        return new Pyramid3dHandler('pyramid', this.stage, {
          onObjectMount(obj) {
            if (!obj || !that.stage.currentSight) return false;
            setCommentModal({
              type: 'missed',
              position: obj.position.toArray(),
              uuid: obj.name,
            });
          },
        });
      default:
        return null;
    }
  };

  /**
   * 重置状态，常用于 stage.currentSight 变更时
   */
  public reset = () => {
    this.selector?.reset();
    this.destroyHandler();
  };

  private dispatchPointerEvent = () => {
    this.dispatcher.dispatchEvent({
      type: POINTER_STATUS,
      data: this.pointerStatus,
    });
  };
}
