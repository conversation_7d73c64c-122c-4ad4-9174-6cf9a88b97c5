/* eslint-disable react-hooks/exhaustive-deps */
import { useConstant } from '@forest/hooks';
import { pick } from 'lodash';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Stats from 'stats.js';
import { Matrix4, Vector3 } from 'three';
import { shallow } from 'zustand/shallow';

import { DescInfo, IDescItem, RotationCenterMarker } from '@/components';
import { ContextMenu } from '@/components/ContextMenu';
import { CAMERA_FAR_3D, CAMERA_TYPE_MAP, CONTROLS_OPTIONS_MAP, IMG_RENDER_WIDTH_MAX } from '@/config/render';
import { useLinePoint } from '@/features/anno/hooks';
import { useContextStore, useStoreApi } from '@/features/anno/stores';
import {
  formatHotkeys,
  HOTKEY_ANNO_CONFIG,
  HOTKEY_PCD_CONFIG,
  HOTKEY_TOOL_CONFIG,
  HOTKEY_VIEW_CONFIG,
  useHotkeyConfigWithScope,
  useHotkeyWithScope,
} from '@/features/hotkey';
import { useLineDot, usePointLabel, useRange, useStage } from '@/hooks';
import { useMessage } from '@/provider';
import type { Rawdata } from '@/types';
import { AnnoCompoundItem, AnnoFactory, AnnoInstanceItem, IAnnoItem } from '@/utils/annos';
import { Extrinsic } from '@/utils/math';
import { CameraType, RawImage, RawObject, RawPoints, Sight } from '@/utils/render';
import type { ToolType } from '@/utils/tool';
import { getTransformMatrix } from '@/utils/transform';

import {
  PCD_THREE_VIEWS_OPTIONS,
  THREE_VIEW_BOTTOM,
  THREE_VIEW_HEIGHT_RATIO,
  THREE_VIEW_RIGHT,
  THREE_VIEW_TOP,
  THREE_VIEW_WIDTH,
} from '../../config';
import { useRemoveAnno } from '../../hooks';
import { VisibleStatus } from '../../stores/slices';
import { CuboidHandler, generateKeepFocus } from '../../utils';
import { BarContainer } from '../BarContainer';
import { DeleteModal } from '../DeleteModal';
import { ThreeViewer } from '../ThreeViewer';
import { AUTO_MOVE_SOURCE } from './config';
import { useClipboard, useContextMenu, useUndo } from './hooks';
import { POINTER_STATUS, PointerStatus, Scheduler } from './Scheduler';
import { SseScheduler } from './SseScheduler';

const _a = new Vector3(),
  _b = new Vector3();

interface CameraInfoType {
  // 相机的位置
  position: [number, number, number];
  // 相机的缩放
  zoom: number;
  // 相机的目标点
  target?: [number, number, number];
}
export interface LabelSceneProps {
  sceneData?: Rawdata;
  rangeCenter?: [number, number, number];
}

let preFrameCameraInfo: (CameraInfoType & { relativePose?: [number, number, number] }) | null = null;
// 进入 Bev 模式时的相机信息
let enterBevCameraInfo: CameraInfoType | null = null;
// 离开 Bev 模式时的相机信息
let leaveBevCameraInfo: CameraInfoType | null = null;

// const POSITION_ESP = 0.001;

const HOTKEY_TOOL_MAP = HOTKEY_TOOL_CONFIG.flat().reduce((prev, cur) => {
  const keystr = formatHotkeys(cur.keys);
  prev[keystr] = cur.name;

  return prev;
}, {} as Record<string, ToolType>);

export const LabelScene: React.FC<LabelSceneProps> = ({ sceneData, rangeCenter }) => {
  const [wrapperHeight, setWrapperHeight] = useState(300);
  const [sceneId, setSceneId] = useState<string>();
  const [annoDesc, setAnnoDesc] = useState<{ type: string; items: Array<IDescItem> }>();

  const scheduler = useRef<Scheduler | null>(null);
  const sseScheduler = useRef<SseScheduler | null>(null);
  const toolKeys = useConstant(() => [
    'select',
    'cuboid',
    'line3d',
    'spline3d',
    'poly3d',
    'point3d',
    'caliper',
    'pyramid',
    'series-point3d',
  ]);

  const {
    lot,
    job,
    currentAnno,
    currentTool,
    selectedAnnos,
    selectedSource,
    selectedComment,
    editAnno,
    currentComment,
    labelStage,
    labelMap,
    trackIdsVisibleFlag,
    commentsVisibleFlag,
    seriesLinkage,
    annoCommentMap,
    missedCommentMap,
    dataElementIndex,
    currentElementIndex,
    annotations,
    displayValues: { isBevModel, rangeVisible },
    ranges,
    devMode,
    toolControls,
    currentType,
    setLabelStage,
    renderAnnosAndComments,
    updateRawMeta,
    setEditAnno,
    getAnnoInstanceNames,
    updateLoadingProgressInfo,
    toggleSceneConfig,
    getCurrentMissedComments,
    setCurrentTool,
  } = useContextStore(
    (state) =>
      pick(state, [
        'lot',
        'job',
        'currentAnno',
        'currentTool',
        'selectedAnnos',
        'selectedSource',
        'selectedComment',
        'editAnno',
        'currentComment',
        'labelStage',
        'labelMap',
        'trackIdsVisibleFlag',
        'commentsVisibleFlag',
        'annoCommentMap',
        'missedCommentMap',
        'dataElementIndex',
        'currentElementIndex',
        'annotations',
        'displayValues',
        'ranges',
        'devMode',
        'toolControls',
        'currentType',
        'overlayFrames',
        'setOverlayNumber',
        'setLabelStage',
        'renderAnnosAndComments',
        'updateRawMeta',
        'seriesLinkage',
        'getAnnoInstanceNames',
        'setEditAnno',
        'toggleSceneConfig',
        'updateLoadingProgressInfo',
        'getCurrentMissedComments',
        'setCurrentTool',
      ]),
    shallow
  );
  const store = useStoreApi();

  const [wrapperRef, canvasRef] = useStage(
    {
      autoAnimate: true,
      labelRendererDepth: 2,
      fileRenderMethods: {
        image: RawImage.createRenderMethod(IMG_RENDER_WIDTH_MAX),
        pointcloud: (data) => {
          // 变换矩阵仅支持平移与旋转，无缩放参数
          const { viewpoint } = data.meta.pcd!;

          const mat =
            viewpoint && !Extrinsic.equals(viewpoint.data, Extrinsic.IDENTITY, 1e-10)
              ? getTransformMatrix(viewpoint)
              : new Matrix4();

          return {
            matrixs: [mat.clone().invert(), mat],
          };
        },
      },
    },
    (stage) => {
      setLabelStage(stage);
      scheduler.current = new Scheduler(stage, store, (type, data) => {
        if (type === 'desc') {
          setAnnoDesc(data);
        }
      });
    },
    (width, height) => {
      setWrapperHeight(height);
    }
  );

  const { clearOperations } = useUndo(scheduler);
  const { clearClipboard, handleCopy, handlePaste, clipboardContent } = useClipboard(scheduler);
  const { contextMenuItem, handleContextMenuItemClick } = useContextMenu(clipboardContent, handleCopy, handlePaste);

  const selectedCompoundAnnoNames = useMemo(() => {
    const selectedNames: Set<string> = new Set();
    selectedAnnos.forEach((anno) => {
      if (anno instanceof AnnoCompoundItem) {
        getAnnoInstanceNames(anno).forEach((name) => selectedNames.add(name));
      }
    });
    return selectedNames;
  }, [selectedAnnos]);

  const pointLabelAnno = useMemo(
    () => (editAnno && ['label', 'widget'].includes(editAnno.type) ? [editAnno.anno] : selectedAnnos),
    [editAnno, selectedAnnos]
  );
  const lineDotAnno = useMemo(
    () => (editAnno?.type === 'label' ? [editAnno.anno] : selectedAnnos),
    [editAnno, selectedAnnos]
  );

  // 更新被标注的场景数据
  useEffect(() => {
    setSceneId(undefined);
    clearOperations();
    if (!lot?.is_frame_series) {
      clearClipboard();
    }
    scheduler.current?.reset();
    sseScheduler.current?.reset();

    if (sceneData && labelStage) {
      const { name, type } = sceneData;

      leaveBevCameraInfo = null;
      const cameraType: CameraType = CAMERA_TYPE_MAP[lot?.data_type as keyof typeof CAMERA_TYPE_MAP];
      // 在改变场景的时候，需要根据实际情况来判断控制器的状态
      const options =
        type === 'pointcloud'
          ? {
              subViewOptions: PCD_THREE_VIEWS_OPTIONS,
              cameraOptions: {
                type: cameraType,
                eye: [0, 0, CAMERA_FAR_3D / 2] as [number, number, number],
                far: CAMERA_FAR_3D,
              },
              controlsOptions: CONTROLS_OPTIONS_MAP[cameraType],
            }
          : {};

      const prevSight = labelStage.currentSight;
      // 如果之前的场景的 controls 设置过 _domElementKeyEvents，说明之前场景已经完全加载好了
      const hasResetCamera = Boolean(prevSight?.controls?._domElementKeyEvents);
      prevSight?.controls?.stopListenToKeyEvents();

      if (seriesLinkage.isEnabled && hasResetCamera && prevSight) {
        const cameraInfo = prevSight.getCameraInfo();
        const widget = seriesLinkage.getFocusedTrackIdWidgetByFileName(prevSight.id);
        let relativePose: [number, number, number] | undefined;

        if (widget) {
          const renderWidget = AnnoFactory.getTransformedWidget(widget, prevSight.getMatrixs()[1]);
          if (renderWidget && cameraInfo.target) {
            // 得到相机的目标物的位置 减去 标注物位置 得到相对平移位置
            relativePose = _a.fromArray(cameraInfo.target).sub(_b.fromArray(renderWidget.data)).toArray();
          }
        }

        preFrameCameraInfo = {
          ...prevSight.getCameraInfo(),
          relativePose,
        };
      }

      labelStage
        .changeSight(sceneData, {
          ...options,
          autoLoad: true,
          onLoadProgress: updateLoadingProgressInfo.bind(null, name),
        })
        .then((newSight) => {
          // 以下只用在当前展示的场景中生效
          // 因为是异步的，有可能此处的 newSight 和当前页面正在展示的不一致，如果不一样，就不需要修改
          if (labelStage?.currentSightId === name) {
            renderAnnosAndComments();
            setSceneId(newSight.id);

            if (newSight.rawObject instanceof RawPoints) {
              updateRawMeta('pointcloud', {
                fileName: name,
                zRange: newSight.rawObject.zRange,
                intensityRange: newSight.rawObject.intensityRange,
              });
            }

            newSight.controls?.listenToKeyEvents(canvasRef.current!);
            if (isBevModel) {
              newSight.disableControlsVerticalRotate();
            } else {
              newSight.enableControlsVerticalRotate();
            }

            // 是选择工具时，需要设置为可以旋转
            if (currentTool === 'select') {
              newSight.enableControlsRotate();
            } else {
              newSight.disableControlsRotate();
            }

            // 连续帧需要保持视角
            if (preFrameCameraInfo) {
              let { position, zoom, target, relativePose } = preFrameCameraInfo;
              const widget = seriesLinkage.getFocusedTrackIdWidgetByFileName(newSight.id);
              if (widget) {
                const renderWidget = AnnoFactory.getTransformedWidget(widget, newSight.getMatrixs()[1]);
                if (renderWidget && relativePose && target) {
                  const oldTarget = target;
                  // 通过相对平移位置，计算出当前帧的相机目标点
                  target = _a.fromArray(renderWidget.data).add(_b.fromArray(relativePose)).toArray();
                  // 新的相机目标点位置 减去 旧的相机目标点位置，得到目标点的平移量
                  _a.sub(_b.fromArray(oldTarget));
                  // 相机位置 加上 目标点的平移量，得到当前帧的相机位置
                  position = _b.fromArray(position).add(_a).toArray();
                }
              }

              newSight.setCameraInfo({
                position: position,
                zoom: zoom,
                target: target,
              });
            }

            // 处理分割
            if (currentType === 'segment') {
              handleSegment(newSight);

              // 切换帧时需重置工具
              if (currentTool !== 'select') {
                sseScheduler.current?.createChildHandler(currentTool);
              }
            }
          }
        });
    }
  }, [sceneData]);

  // 更新标注物选择态与编辑态
  useEffect(() => {
    if (currentType === 'segment') {
      sseScheduler.current?.setChildHandler(currentTool);
      return;
    }

    scheduler.current?.setChildHandler(currentTool);
    if (selectedAnnos.length === 1 && selectedAnnos[0] instanceof AnnoInstanceItem) {
      scheduler.current?.setEditObject(selectedAnnos[0].name);
    } else if (editAnno?.type === 'widget' && editAnno.anno instanceof AnnoInstanceItem) {
      scheduler.current?.setEditObject(editAnno.anno.name);
    } else {
      scheduler.current?.clearEditObject(false);
    }

    const newSelects: Set<string> = new Set();

    if (editAnno?.type === 'label') {
      // 编辑属性时，进入选择态
      newSelects.add(editAnno.anno.name);
    } else {
      // 选中组合标注物时，组合标注物内的标注物进入选择态
      selectedCompoundAnnoNames.forEach((annoName) => {
        newSelects.add(annoName);
      });
      // 多选时的标注物进入选择态
      selectedAnnos.forEach((anno) => {
        if (anno instanceof AnnoInstanceItem) {
          newSelects.add(anno.name);
        }
      });
      // 选中的漏标进入选择态
      if (selectedComment) {
        newSelects.add(selectedComment.uuid);
      }
    }
    scheduler.current?.setSelects(newSelects);
  }, [editAnno, selectedAnnos, selectedComment, currentTool]);

  // 如果立体框发生更新，则三视图上的数据也跟着更新
  useEffect(() => {
    if (
      currentAnno?.anno instanceof AnnoInstanceItem &&
      currentAnno.anno.widget.name === 'cuboid' &&
      scheduler.current?.handler instanceof CuboidHandler
    ) {
      const object = labelStage?.currentSight?.getObjectByName(currentAnno.anno.name);
      // 更新和添加
      if (object) {
        scheduler.current.handler.updatePointsCount(object);
        scheduler.current.handler.updateInfo(object);
      } else {
        // 删除
        setAnnoDesc(undefined);
      }
    }
  }, [currentAnno]);

  useEffect(() => {
    if (currentComment) {
      const obj = labelStage?.currentSight?.getObjectByName(currentComment.uuid);
      if (obj) {
        AnnoFactory.setStatus(obj, 'adjust');
        labelStage?.currentSight?.moveToObject(obj.name);
      }
    }
  }, [currentComment]);

  useEffect(() => {
    if (
      labelStage?.currentSight &&
      selectedSource &&
      selectedAnnos[0]?.name &&
      AUTO_MOVE_SOURCE.includes(selectedSource)
    ) {
      // 如果是组合标注物，移动到第一个标注物
      labelStage.currentSight.moveToObject(getAnnoInstanceNames(selectedAnnos[0])[0]);
    }
  }, [selectedAnnos]);

  // 挂载的函数
  useEffect(() => {
    if (currentType === 'segment' && labelStage) {
      if (!sseScheduler.current) {
        sseScheduler.current = new SseScheduler(labelStage, store, messageApi, canvasRef.current);
      }

      Object.values(labelStage?.sights || {})?.forEach((sight) => {
        sight.container.children.forEach((child) => (child.visible = false));
      });
      setCurrentTool('select');
    }

    const dom = canvasRef.current,
      handler = currentType === 'segment' ? sseScheduler.current : scheduler.current;

    if (!dom || !handler) return;

    const [resetLastFocus, saveCurrentFocus] = generateKeepFocus();
    const eventHandlers = {
      pointerdown: handler.onPointerDown.bind(handler),
      pointermove: handler.onPointerMove.bind(handler),
      pointerup: handler.onPointerUp.bind(handler),
      keydown: handler.onKeyDown.bind(handler),
      keyup: handler.onKeyUp.bind(handler),
      mouseenter: [handler.onMouseEnter.bind(handler), resetLastFocus],
      mouseleave: [handler.onMouseLeave.bind(handler), saveCurrentFocus],
    };

    const modifyEventListeners = (action: 'add' | 'remove') => {
      const eventActions = {
        add: 'addEventListener' as const,
        remove: 'removeEventListener' as const,
      };

      const act = eventActions[action];

      Object.entries(eventHandlers).forEach(([event, funcs]) => {
        const handlers = Array.isArray(funcs) ? funcs : [funcs];
        handlers.forEach((func) => dom[act](event, func as EventListener));
      });
    };

    modifyEventListeners('add');

    const handlePointerStatusChange = (event: { data: string }) => {
      // Rotate 可以移动；None、 Using 和 Move 不可移动。
      if (event.data === PointerStatus.Rotate) {
        labelStage?.currentSight?.enableControlsRotate();
      } else {
        labelStage?.currentSight?.disableControlsRotate();
      }
    };

    handler?.dispatcher.addEventListener<string>(POINTER_STATUS, handlePointerStatusChange);

    return () => {
      if (!dom || !handler) return;

      modifyEventListeners('remove');

      handler?.dispatcher.removeEventListener(POINTER_STATUS, handlePointerStatusChange);

      if (currentType !== 'segment') return;
      Object.values(labelStage?.sights || {})?.forEach((sight) => {
        (sight?.rawObject as RawPoints)?.setLabelEnable(false);
        (sight?.overlays?.background as RawPoints)?.setLabelEnable(false);
        sight.container.children.forEach((child) => (child.visible = true));
      });
    };
  }, [labelStage, currentType]);

  useEffect(() => {
    if (
      labelStage?.currentSight &&
      job?.elements[currentElementIndex].datas[0].name === labelStage?.currentSight.id &&
      currentType === 'segment'
    ) {
      handleSegment(labelStage.currentSight);
    }
  }, [currentType]);

  // 根据更改的批注，更改当前标注物的颜色
  useEffect(() => {
    if (!labelStage?.currentSight) return;
    labelStage.currentSight.updateObjectsByComments(
      annoCommentMap,
      missedCommentMap[dataElementIndex],
      annotations,
      labelMap
    );
  }, [annoCommentMap, missedCommentMap]);

  useEffect(() => {
    canvasRef.current?.focus();
  }, [currentTool]);

  // 标注物的显隐
  useEffect(() => {
    const showObjectNames = new Set<IAnnoItem['name']>();
    Object.values(annotations).forEach((anno) => {
      if (!anno) return;
      const visible = trackIdsVisibleFlag[anno.trackId] ?? VisibleStatus.Show;
      if (visible === VisibleStatus.Show) {
        showObjectNames.add(anno.name);
      }
    });
    const missedComments = getCurrentMissedComments();
    if (missedComments && missedComments.length > 0) {
      missedComments.forEach((comment) => {
        const visible = commentsVisibleFlag[comment.uuid] ?? VisibleStatus.Show;
        if (visible === VisibleStatus.Show) {
          showObjectNames.add(comment.uuid);
        }
      });
    }

    labelStage?.currentSight?.updateObjectsVisible(showObjectNames);
  }, [trackIdsVisibleFlag, commentsVisibleFlag]);

  useEffect(() => {
    if (labelStage?.currentSight?.controls && labelStage?.currentSight?.container.visible) {
      // 进入 BEV 模式时，记录当前相机信息
      if (isBevModel) {
        enterBevCameraInfo = labelStage.currentSight.getCameraInfo();
        labelStage.currentSight.disableControlsVerticalRotate();
        if (leaveBevCameraInfo) {
          labelStage.currentSight.setCameraInfo(leaveBevCameraInfo);
        }
      } else {
        leaveBevCameraInfo = labelStage.currentSight.getCameraInfo();

        labelStage.currentSight.enableControlsVerticalRotate();
        if (enterBevCameraInfo) {
          labelStage.currentSight.setCameraInfo(enterBevCameraInfo);
        }
      }
    }
  }, [isBevModel]);

  useEffect(() => {
    let dom = labelStage?.devTools.stats?.dom;
    if (devMode) {
      if (!dom) {
        // 添加 fps
        const stats = new Stats();
        stats.dom.style.position = 'absolute';
        stats.dom.style.top = '8px';
        stats.dom.style.right = '250px';
        stats.dom.style.left = 'unset';

        wrapperRef.current?.appendChild(stats.dom);
        labelStage?.updateDevTools({ stats });
        dom = stats.dom;
      }
      dom.style.display = 'block';
    } else {
      dom && (dom.style.display = 'none');
    }
  }, [devMode, labelStage]);

  const handleSegment = useCallback((sight: Sight) => {
    const rawObjects: RawObject[] = [sight?.rawObject];
    if (sight?.overlays?.background) {
      rawObjects.push(sight?.overlays?.background);
    }

    rawObjects.forEach((rawObject) => {
      if (!(rawObject instanceof RawPoints)) return;

      if (rawObject.scheduler) {
        rawObject.setLabelEnable(true);
      } else {
        rawObject.initialSegment(store);
      }
    });
  }, []);

  const onClickBar = (item: AnnoInstanceItem) => {
    if (item) {
      setEditAnno({ anno: item, type: 'label' });
    }
  };

  useHotkeyConfigWithScope(HOTKEY_PCD_CONFIG, 'bev', () => {
    toggleSceneConfig('isBevModel');
  });

  useHotkeyConfigWithScope(HOTKEY_ANNO_CONFIG, 'prev', () => {
    const { getPrevOrNextAnno } = store.getState();
    getPrevOrNextAnno('prev');
  });

  useHotkeyConfigWithScope(HOTKEY_ANNO_CONFIG, 'next', () => {
    const { getPrevOrNextAnno } = store.getState();
    getPrevOrNextAnno('next');
  });

  useHotkeyConfigWithScope(HOTKEY_ANNO_CONFIG, 'edit', () => {
    const { selectedAnnos, setEditAnno } = store.getState();

    if (selectedAnnos.length === 1 && selectedAnnos[0] instanceof AnnoInstanceItem) {
      const anno = selectedAnnos[0];
      setEditAnno({
        anno,
        type: 'widget',
      });
    }
  });

  useHotkeyConfigWithScope(HOTKEY_VIEW_CONFIG, 'attr-panel', () => {
    const { editAnno, selectedAnnos, setEditAnno } = store.getState();
    if (editAnno?.type === 'widget') {
      setEditAnno({ anno: editAnno.anno, type: 'label' });
    } else if (selectedAnnos.length === 1) {
      setEditAnno({ anno: selectedAnnos[0], type: 'label' });
    }
  });

  useHotkeyConfigWithScope(HOTKEY_VIEW_CONFIG, 'object-visible', () => {
    const {
      selectedAnnos,
      annotations,
      trackIdsVisibleFlag,
      commentsVisibleFlag,
      selectedComment,
      changeAnnoListVisible,
      getCurrentMissedComments,
      changeCommentListVisible,
    } = store.getState();
    // 处理 Annos
    const selectedInstanceAnnos = selectedAnnos.filter(
      (anno): anno is AnnoInstanceItem => anno instanceof AnnoInstanceItem
    );
    const unSelectedInstanceAnnos = Object.values(annotations).filter(
      (anno): anno is AnnoInstanceItem => anno instanceof AnnoInstanceItem && !selectedInstanceAnnos.includes(anno)
    );
    let hiddenCount = 0;
    unSelectedInstanceAnnos.forEach((anno) => {
      const visible = trackIdsVisibleFlag[anno.trackId] ?? VisibleStatus.Show;
      if (visible !== VisibleStatus.Show) {
        hiddenCount++;
      }
    });
    // 如果没有被选中的标注物全部都是隐藏的，那么接下来就应该变成 显示
    if (hiddenCount === unSelectedInstanceAnnos.length) {
      changeAnnoListVisible(unSelectedInstanceAnnos, true);
    } else {
      changeAnnoListVisible(unSelectedInstanceAnnos, false);
    }

    // 处理漏标
    const missedComments = getCurrentMissedComments() ?? [];
    hiddenCount = 0;
    const unSelectedComments = missedComments.filter((comment) => comment !== selectedComment).map(({ uuid }) => uuid);
    unSelectedComments.forEach((uuid) => {
      const visible = commentsVisibleFlag[uuid] ?? VisibleStatus.Show;
      if (visible !== VisibleStatus.Show) {
        hiddenCount++;
      }
    });
    if (hiddenCount === unSelectedComments.length) {
      changeCommentListVisible(unSelectedComments, true);
    } else {
      changeCommentListVisible(unSelectedComments, false);
    }
  });

  useHotkeyConfigWithScope(HOTKEY_TOOL_CONFIG, toolKeys, function () {
    const { selectedAnnos, setCurrentTool } = store.getState();
    const helper = ['shift', 'alt', 'ctrl', 'mod', 'meta'].find((key) => {
      return arguments[1][key];
    });
    const tool = HOTKEY_TOOL_MAP[(helper ? helper + '+' : '') + arguments[1].keys[0]];

    if (toolControls[tool] !== false) {
      setEditAnno(null);
      // 线上加点工具 或者 是其他工具
      if (
        (tool === 'series-point3d' &&
          selectedAnnos.length === 1 &&
          selectedAnnos[0] instanceof AnnoInstanceItem &&
          selectedAnnos[0].widget.name === 'line3d') ||
        tool !== 'series-point3d'
      ) {
        setCurrentTool(tool);
      }
    }
  });

  // dev，shift + ctrl + d 切换展示 dev 模式
  useHotkeyWithScope('shift+ctrl+d', () => {
    const { toggleDevMode } = store.getState();
    toggleDevMode();
  });

  // 支持快捷键删除
  const { handleRemoveAnnos, checkRemoveDuringCommentEdit } = useRemoveAnno();
  const messageApi = useMessage();

  useHotkeyConfigWithScope(HOTKEY_ANNO_CONFIG, 'remove', () => {
    if (!editAnno && selectedAnnos.length === 0) return;
    const annos = editAnno ? [editAnno.anno] : selectedAnnos;

    // 编辑漏标不能删除，除非这个标注物在漏标的编辑物中
    if (annos.some(() => !checkRemoveDuringCommentEdit())) {
      messageApi?.error('请退出修改模式后再删除标注物');
      return;
    }

    const { setDeleteModalAnnos } = store.getState();
    // 连续帧删除需要弹窗确认
    if (seriesLinkage.isEnabled) {
      // 弹窗，暂不执行后续删除逻辑
      setDeleteModalAnnos(annos);
      return;
    }

    handleRemoveAnnos(annos);
  });

  useRange(job, currentElementIndex, labelStage, ranges, rangeVisible, sceneId, rangeCenter);

  useLineDot(labelStage, lineDotAnno, labelStage?.labelRenderer?.domElement, sceneId, currentAnno);
  usePointLabel(labelStage, pointLabelAnno, annotations, labelMap, labelStage?.labelRenderer?.domElement, sceneId);
  useLinePoint(lot?.data_type === 'fusion4d');

  return (
    <div
      ref={wrapperRef}
      style={{
        position: 'relative',
        width: '100%',
        height: '100%',
        zIndex: 1,
      }}
    >
      <canvas ref={canvasRef} tabIndex={-1} style={{ position: 'absolute', left: 0, top: 0, outline: 0 }}></canvas>
      <ThreeViewer
        width={THREE_VIEW_WIDTH}
        height={wrapperHeight - THREE_VIEW_TOP - THREE_VIEW_BOTTOM}
        clipboard={clipboardContent.current}
        subViewHeightRatio={THREE_VIEW_HEIGHT_RATIO}
        style={{
          top: THREE_VIEW_TOP,
          right: THREE_VIEW_RIGHT,
        }}
      />
      <DescInfo items={annoDesc?.items} type={annoDesc?.type} />
      <BarContainer sceneId={sceneId} onClickBar={onClickBar} />
      <RotationCenterMarker controls={labelStage?.currentSight?.controls} />
      <DeleteModal />
      <ContextMenu
        items={contextMenuItem}
        onItemClick={handleContextMenuItemClick}
        triggerElement={wrapperRef.current}
      />
    </div>
  );
};
