import { pick, throttle } from 'lodash';
import { MutableRefObject, useEffect, useMemo, useRef } from 'react';
import { MathUtils, Vector3Tuple } from 'three';

import { ContextMenuItem } from '@/components/ContextMenu';
// import { ContextMenuItem } from '@/components/ContextMenu';
import { HOTKEY_ANNO_CONFIG, useHotkeyConfigWithScope } from '@/features/hotkey';
import { useMessage } from '@/provider';
import { WidgetObject } from '@/types';
import { AnnoCompoundItem, AnnoFactory, AnnoInstanceItem } from '@/utils/annos';
import { projectMouseToPlane } from '@/utils/render';

import { EventHandler } from '../..';
import { useRemoveAnno } from '../../hooks';
import { useContextStore, useStoreApi, useTemporalStore } from '../../stores';
import { ElementState } from '../../stores/slices';
import { Scheduler } from './Scheduler';

export const useUndo = (scheduler: MutableRefObject<Scheduler | null>) => {
  const seriesLinkage = useContextStore((state) => state.seriesLinkage);
  const { undo, redo, skipUndo, clear, pastStates, futureStates } = useTemporalStore((state) => state);

  const messageApi = useMessage();

  useHotkeyConfigWithScope(HOTKEY_ANNO_CONFIG, 'undo', () => {
    // 创建过程不可打断
    if (scheduler.current?.handler instanceof EventHandler && scheduler.current?.handler?.isUpdateForbidden) {
      messageApi?.warning({ content: '请完成绘制后再执行撤销' });
      return;
    }
    if (pastStates.length === 0) {
      messageApi?.warning({ content: '已达到最大撤销次数' });
      return;
    }

    // 连续帧增删不允许撤销
    const opt = pastStates[pastStates.length - 1].currentAnno?.opt;
    if (seriesLinkage.isEnabled && opt !== 'update') {
      // 跳过
      skipUndo();

      // 提示
      messageApi?.warning({
        content: opt === 'remove' ? '连续帧的删除暂不支持撤销，已跳过此撤销' : '连续帧的新增暂不支持撤销，请手动删除',
      });
    } else {
      undo();
    }
  });

  useHotkeyConfigWithScope(HOTKEY_ANNO_CONFIG, 'redo', () => {
    // 创建过程不可打断
    if (scheduler.current?.handler instanceof EventHandler && scheduler.current?.handler?.isUpdateForbidden) {
      messageApi?.warning({ content: '请完成绘制后再执行重做' });
      return;
    }
    if (futureStates.length === 0) {
      messageApi?.warning({ content: '没有可重做的操作' });
      return;
    }

    redo();
  });

  return {
    clearOperations: clear,
  };
};

export interface ClipboardContentType {
  widget: WidgetObject;
  label: string;
  attrs: Record<string, string[]>;
  position: Vector3Tuple;
}

export const useClipboard = (scheduler: MutableRefObject<Scheduler | null>) => {
  const { labelStage, selectedAnnos, currentTool, setCurrentAnno, setSelectedAnnos } = useContextStore((state) =>
    pick(state, ['labelStage', 'selectedAnnos', 'currentTool', 'setCurrentAnno', 'setSelectedAnnos'])
  );
  const store = useStoreApi();

  const messageApi = useMessage();

  const clipboardContent = useRef<ClipboardContentType | null>(null);
  const pastePosition = useRef<[number, number] | null>(null);

  const handleCopy = () => {
    if (currentTool !== 'select') {
      messageApi?.warning({ content: '请切换到选择工具再复制' });
      return;
    }
    if (selectedAnnos?.length > 1) {
      messageApi?.warning({ content: '不支持复制多个标注物' });
      return;
    }
    const anno = selectedAnnos?.[0];
    const target = labelStage?.currentSight?.getObjectByName(anno?.name);
    if (!target || !(anno instanceof AnnoInstanceItem)) {
      messageApi?.warning({ content: '请先选中标注物再复制' });
      return;
    }

    // 数据保存到剪切板
    const { widget, label, attrs } = anno;
    clipboardContent.current = {
      widget: { ...widget },
      label,
      attrs: { ...attrs },
      // 粘贴位置默认是在 target 附近
      position: (AnnoFactory.getAnnoCenter(target) || target.position).toArray(),
    };
    pastePosition.current = null;
  };

  /**
   * 粘贴内容
   * @returns
   */
  const handlePaste = () => {
    if (currentTool !== 'select') {
      messageApi?.warning({ content: '请切换到选择工具再粘贴' });
      return;
    }
    if (!clipboardContent.current) {
      messageApi?.warning({ content: '当前剪切板为空，请先复制标注物' });
      return;
    }
    if (!labelStage?.currentSight || !pastePosition.current) return;

    const camera = labelStage?.currentSight?.camera;

    const x = (pastePosition.current[0] / labelStage.width) * 2 - 1;
    const y = -(pastePosition.current[1] / labelStage.height) * 2 + 1;
    const pastePosition3D = projectMouseToPlane(camera, x, y, clipboardContent.current.position[2]);

    if (!pastePosition3D) return;

    const { widget, label, attrs } = clipboardContent.current;

    const newWidget = AnnoFactory.changeWidgetPosition(
      widget,
      pastePosition3D,
      labelStage.currentSight.getMatrixs()[0]
    );

    const uuid = MathUtils.generateUUID();
    const anno = new AnnoInstanceItem(uuid, label, newWidget, {
      attrs,
    });
    setCurrentAnno({ anno, opt: 'add' });
    // 选中
    setSelectedAnnos([anno]);
  };
  const handleClear = () => {
    clipboardContent.current = null;
  };

  useHotkeyConfigWithScope(HOTKEY_ANNO_CONFIG, 'copy', handleCopy);
  useHotkeyConfigWithScope(HOTKEY_ANNO_CONFIG, 'paste', handlePaste);

  useEffect(() => {
    const dom = labelStage?.canvas;
    if (!dom) return;
    const handleMove = throttle((event: PointerEvent | MouseEvent) => {
      const { currentTool } = store.getState();
      // 必须是选择工具且没有按下的状态, 剪贴板有内容
      if (currentTool === 'select' && event.buttons === 0 && clipboardContent.current) {
        pastePosition.current = [event.offsetX, event.offsetY];
      } else {
        pastePosition.current = null;
      }
    }, 200);

    dom.addEventListener('pointermove', handleMove);

    return () => {
      if (!dom) return;

      dom.removeEventListener('pointermove', handleMove);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [labelStage]);

  return {
    handleCopy,
    handlePaste,
    clearClipboard: handleClear,
    clipboardContent,
  };
};

// 用于右键菜单
export const useContextMenu = (
  clipboardContent: MutableRefObject<ClipboardContentType | null>,
  handleCopy: () => void,
  handlePaste: (x?: number, y?: number) => void
) => {
  const {
    lot,
    currentElementIndex,
    seriesLinkage,
    selectedAnnos,
    editAnno,
    labelStage,
    setCurrentAnno,
    setCurrentTool,
    setDeleteModalAnnos,
    setEditAnno,
    setElementsState,
  } = useContextStore((state) =>
    pick(state, [
      'lot',
      'currentElementIndex',
      'seriesLinkage',
      'selectedAnnos',
      'editAnno',
      'labelStage',
      'setCurrentAnno',
      'setCurrentTool',
      'setDeleteModalAnnos',
      'setEditAnno',
      'setElementsState',
    ])
  );
  const { handleRemoveAnnos, checkRemoveDuringCommentEdit } = useRemoveAnno();

  function handleToggleScaleMode() {
    const anno = selectedAnnos[0] || editAnno?.anno;
    if (anno instanceof AnnoInstanceItem && anno.widget.name === 'cuboid') {
      if (anno.isScaleIndependent) {
        // 恢复至统一尺寸
        anno.setIsScaleIndependent(false);

        if (labelStage?.currentSight) {
          const scale = seriesLinkage.trackScale.get(anno.trackId);
          if (scale) {
            // 数据层更新
            const targetData = anno.widget.data;
            anno.widget.data = targetData.slice(0, 3).concat(scale, targetData.slice(6));

            // 视图更新
            const fileToThreeMat = labelStage.currentSight.getMatrixs()[1];
            const target = labelStage.currentSight.getObjectByName(anno.name);
            if (target) {
              AnnoFactory.updateFromWidget(target, anno.widget, fileToThreeMat);
            }
          }
        }
      } else {
        // 仅修改此帧尺寸
        anno.setIsScaleIndependent(true);
      }
      // 更新 ElementState
      const elementsState = seriesLinkage.trackElementsState.get(anno.trackId);
      if (elementsState) {
        elementsState[currentElementIndex] = anno.isScaleIndependent
          ? ElementState.INDEPENDENT
          : anno.source === 'manual'
          ? ElementState.KEY
          : ElementState.INTERPOLATION;
        setElementsState([...elementsState]);
      }

      // 更新到数据层
      setCurrentAnno({ anno, opt: 'update' });
    }
  }

  const contextMenuItem = useMemo<ContextMenuItem>(() => {
    function getEditMenu(): ContextMenuItem {
      const res: ContextMenuItem = [];
      if (selectedAnnos?.length !== 1 && editAnno?.type !== 'widget') return res;

      if (
        lot?.data_type === 'fusion4d' &&
        selectedAnnos[0] instanceof AnnoInstanceItem &&
        selectedAnnos[0]?.widget?.name === 'line3d'
      ) {
        res.push(
          { name: 'divider' },
          {
            name: 'series-point3d',
            text: '添加点',
            hotkey: 'Shift +',
            disabled:
              selectedAnnos?.length !== 1 ||
              selectedAnnos[0] instanceof AnnoCompoundItem ||
              selectedAnnos[0].widget.name !== 'line3d',
          }
        );
      }

      const currentAnno = selectedAnnos[0] || editAnno?.anno;
      if (currentAnno instanceof AnnoInstanceItem && currentAnno.widget.name === 'cuboid') {
        if (!res.length) {
          res.push({ name: 'divider' });
        }
        res.push({
          name: 'scale-mode',
          text: currentAnno.isScaleIndependent ? '恢复至统一尺寸' : '仅修改此帧尺寸',
        });
      }

      return res;
    }

    return [
      {
        name: 'copy',
        text: '复制',
        hotkey: 'Ctrl C',
        disabled: selectedAnnos?.length !== 1,
      },
      {
        name: 'paste',
        text: '粘贴',
        hotkey: 'Ctrl V',
        disabled: !clipboardContent.current,
      },
      ...getEditMenu(),
      { name: 'divider' },
      {
        name: 'delete',
        text: '删除',
        hotkey: 'Del',
        disabled: selectedAnnos?.length !== 1 || !checkRemoveDuringCommentEdit(),
      },
    ];
  }, [selectedAnnos, clipboardContent, checkRemoveDuringCommentEdit, editAnno, lot]);

  const handleContextMenuItemClick = (key: string) => {
    switch (key) {
      case 'copy':
        handleCopy();
        return true;
      case 'paste':
        handlePaste();
        return true;
      case 'series-point3d':
        setEditAnno(null);
        setCurrentTool('series-point3d');
        return true;
      case 'scale-mode':
        handleToggleScaleMode();
        return true;
      case 'delete':
        // 连续帧删除需要弹窗确认
        if (seriesLinkage.isEnabled) {
          // 弹窗，暂不执行后续删除逻辑
          setDeleteModalAnnos(selectedAnnos);
        } else {
          handleRemoveAnnos(selectedAnnos);
        }
        return true;
    }
    return false;
  };

  return {
    contextMenuItem,
    handleContextMenuItemClick,
  };
};
