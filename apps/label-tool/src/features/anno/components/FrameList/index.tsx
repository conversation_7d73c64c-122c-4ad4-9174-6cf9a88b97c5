import { KvLockCloseLine, KvLockOpenLine } from '@forest/icons';
import { Tooltip } from 'antd';
import { pick } from 'lodash';
import { ChangeEvent, CSSProperties, KeyboardEvent, PropsWithChildren, useState } from 'react';
import { shallow } from 'zustand/shallow';

import { HOTKEY_VIEW_CONFIG, useHotkeyConfigWithScope, useHotkeyWithScope } from '@/features/hotkey';
import { useMessage } from '@/provider';

import { useContextStore } from '../../stores';
import { Pagination } from './Pagination';

import './styles.css';

export interface FrameListProps {
  selectFrame: (id: number) => void;
  style: CSSProperties;
  hideNumber?: boolean;
}

export const FrameList: React.FC<PropsWithChildren<FrameListProps>> = ({
  selectFrame,
  style: frameListStyle,
  hideNumber,
  children,
}) => {
  const {
    job,
    currentElementIndex: currentIndex,
    fusionLinkage: { focusMode, toggleAutoSwitchFrame },
    labelStage,
    posePoints,
    overlayFrames,
    currentType,
  } = useContextStore(
    (state) =>
      pick(state, [
        'job',
        'currentElementIndex',
        'fusionLinkage',
        'labelStage',
        'posePoints',
        'overlayFrames',
        'currentType',
      ]),
    shallow
  );
  const messageApi = useMessage();

  const [currentInputValue, setCurrentInputValue] = useState(currentIndex + 1);

  const totalPage = job?.elements.length ?? 0;

  const handleChangeFrame = (index: number) => {
    if (overlayFrames && currentType === 'segment') {
      messageApi?.warning('叠帧模式下无法切帧，请关闭叠帧后重试');
      return;
    }
    index !== currentInputValue && setCurrentInputValue(index);
    selectFrame(index - 1);
    if ((!focusMode.isEnabled || focusMode.autoSwitchFrame) && Array.isArray(posePoints[index - 1])) {
      // 标注员可能会在标注中手动跳帧，故点云跳帧时定位到中心暂时关闭，后续再讨论
      labelStage?.currentSight?.setCameraToward(...posePoints[index - 1]);
    }
  };

  const getValidValue = (e: any): number => {
    const inputValue = e.target.value;
    let value: number;
    if (inputValue === '') {
      value = inputValue;
      // eslint-disable-next-line no-restricted-globals
    } else if (Number.isNaN(Number(inputValue))) {
      value = currentInputValue;
    } else if (inputValue >= totalPage) {
      value = totalPage;
    } else if (inputValue <= 1) {
      value = 1;
    } else {
      value = Number(inputValue);
    }
    return value;
  };

  const handleKeyUp = (e: KeyboardEvent<HTMLInputElement> | ChangeEvent<HTMLInputElement>) => {
    const value = getValidValue(e);
    if (value !== currentInputValue) {
      setCurrentInputValue(value);
    }
    if ((e as React.KeyboardEvent<HTMLInputElement>).code === 'Enter') {
      handleChangeFrame(value);
    } else if ((e as React.KeyboardEvent<HTMLInputElement>).code === 'ArrowUp') {
      handleChangeFrame(Math.max(value - 1, 1));
    } else if ((e as React.KeyboardEvent<HTMLInputElement>).code === 'ArrowDown') {
      handleChangeFrame(Math.min(value + 1, totalPage));
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.code === 'ArrowUp' || e.code === 'ArrowDown') {
      e.preventDefault();
    }
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement, Element>) => {
    const value = getValidValue(e);
    handleChangeFrame(value);
  };

  useHotkeyWithScope(
    ',&shift+<',
    () => {
      handleChangeFrame(Math.max(currentIndex, 1));
    },
    {
      splitKey: '&',
    }
  );

  useHotkeyWithScope('.,shift+>', () => {
    handleChangeFrame(Math.min(currentIndex + 2, totalPage));
  });

  useHotkeyConfigWithScope(HOTKEY_VIEW_CONFIG, 'element-lock', () => {
    toggleAutoSwitchFrame();
  });

  return (
    <div className="frame-list-container" style={frameListStyle}>
      <Tooltip
        title={
          focusMode.autoSwitchFrame ? (
            <pre style={{ margin: 0 }}>帧解锁&nbsp;&nbsp;Ctrl L</pre>
          ) : (
            <pre style={{ margin: 0 }}>帧锁定&nbsp;&nbsp;Ctrl L</pre>
          )
        }
        placement="top"
      >
        <div className="frame-lock-btn" role="button" hidden={!focusMode.isEnabled} onClick={toggleAutoSwitchFrame}>
          {focusMode.autoSwitchFrame ? <KvLockOpenLine /> : <KvLockCloseLine />}
        </div>
      </Tooltip>

      <Pagination
        prefixCls="frame-pagination"
        total={totalPage}
        current={currentIndex + 1}
        onChange={handleChangeFrame}
        disabled={false}
        hideNumber={hideNumber}
      />
      <div className="frame-page-show">
        <span className="title">总帧数</span>
        <input
          value={currentInputValue}
          onKeyUp={handleKeyUp}
          onChange={handleKeyUp}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
        />
        <span>/ {totalPage}</span>
      </div>
      {children}
    </div>
  );
};
