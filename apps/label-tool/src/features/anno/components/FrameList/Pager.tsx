import { pick } from 'lodash';
import React from 'react';

import { useContextStore } from '../../stores';

export interface PagerProps {
  last?: boolean;
  locale?: any;
  rootPrefixCls: string;
  page: number;
  active?: boolean;
  className?: string;
  showTitle: boolean;
  hideNumber?: boolean;
  onClick: (page: number) => void;
  onKeyDown?: (e: React.KeyboardEvent<HTMLLIElement>, onClick: PagerProps['onClick']) => void;
  itemRender?: (
    page: number,
    type: 'page' | 'prev' | 'next' | 'jump-prev' | 'jump-next',
    element: React.ReactNode,
  ) => React.ReactNode;
}

const FRAME_TYPE = ['NORMAL', 'KEY', 'INTERPLOATION', 'INVALID', 'INDEPENDENT'];

const Pager: React.FC<PagerProps> = (props) => {
  const { rootPrefixCls, page, active, showTitle, hideNumber, onClick, onKeyDown } = props;

  const { elementsState, isInOverlay } = useContextStore((state) => pick(state, ['elementsState', 'isInOverlay']));

  const prefixCls = `${rootPrefixCls}-item`;
  const cls = `${prefixCls} ${prefixCls}-${page} ${active ? `${prefixCls}-active` : ''} ${
    !page ? `${prefixCls}-disabled` : ''
  }`;

  const handleClick = () => {
    onClick && onClick(page);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLLIElement>) => {
    onKeyDown && onKeyDown(e, onClick);
  };

  return (
    <li
      title={showTitle ? page.toString() : undefined}
      className={cls}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
    >
      {!hideNumber && page}
      <span className="frame-button" data-is-overlay={isInOverlay(page - 1)}>
        <span className="frame-button-state" data-type={FRAME_TYPE[elementsState[page - 1]]}></span>
      </span>
    </li>
  );
};

export default Pager;
