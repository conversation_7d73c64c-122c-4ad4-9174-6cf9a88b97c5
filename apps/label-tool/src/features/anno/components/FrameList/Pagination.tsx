import { KvDoubleLeftOut, KvDoubleRightOut, KvLeftOut, KvRightOut } from '@forest/icons';
import { cloneElement, FC, isValidElement, ReactElement, useEffect, useMemo, useRef, useState } from 'react';

import type { PagerProps } from './Pager';
import Pager from './Pager';

const LOCALE = {
  prev_page: '上一帧',
  next_page: '下一帧',
  prev_5: '向前5帧',
  next_5: '向后5帧',
};

export interface PaginationLocale {
  // Options
  items_per_page?: string;
  jump_to?: string;
  jump_to_confirm?: string;
  page?: string;

  // Pagination
  prev_page?: string;
  next_page?: string;
  prev_5?: string;
  next_5?: string;
}

export interface PaginationData {
  className: string;
  prefixCls: string;

  current: number;
  defaultCurrent: number;
  total: number;
  pageSize: number;

  showPrevNextJumpers: boolean;
  showTitle: boolean;
  disabled: boolean;
  hideNumber?: boolean;

  locale: PaginationLocale;

  style: React.CSSProperties;

  prevIcon: React.ComponentType | React.ReactNode;
  nextIcon: React.ComponentType | React.ReactNode;
  jumpPrevIcon: React.ComponentType | React.ReactNode;
  jumpNextIcon: React.ComponentType | React.ReactNode;
}

export interface PaginationProps extends Partial<PaginationData>, React.AriaAttributes {
  onChange?: (page: number) => void;
  itemRender?: (
    page: number,
    type: 'page' | 'prev' | 'next' | 'jump-prev' | 'jump-next',
    element: React.ReactNode
  ) => React.ReactNode;
}

function noop() {}

function isInteger(v: number) {
  const value = Number(v);
  return typeof value === 'number' && !Number.isNaN(value) && isFinite(value) && Math.floor(value) === value;
}

export const Pagination: FC<PaginationProps> = (props) => {
  const {
    // cls
    prefixCls = 'pagination',
    className,

    // control
    current: currentProp,
    defaultCurrent = 1,
    total = 0,
    onChange = noop,

    // config
    showPrevNextJumpers = true,
    showTitle = true,
    hideNumber = false,
    locale = LOCALE,
    style,
    disabled,
  } = props;

  const [current, setCurrent] = useState<number>(() => {
    const page = currentProp ?? defaultCurrent ?? 1;
    return Math.max(1, Math.min(page, total));
  });

  const paginationRef = useRef<HTMLUListElement>(null);

  const iconsProps = useMemo<Record<PropertyKey, React.ReactNode>>(() => {
    const ellipsis = <span className={`${prefixCls}-item-ellipsis`}>•••</span>;
    const prevIcon = (
      <button className={`${prefixCls}-item-link`} type="button" tabIndex={-1}>
        <KvLeftOut />
      </button>
    );
    const nextIcon = (
      <button className={`${prefixCls}-item-link`} type="button" tabIndex={-1}>
        <KvRightOut />
      </button>
    );
    const jumpPrevIcon = (
      <span className={`${prefixCls}-item-link`}>
        <div className={`${prefixCls}-item-container`}>
          <KvDoubleLeftOut className={`${prefixCls}-item-link-icon`} />
          {ellipsis}
        </div>
      </span>
    );
    const jumpNextIcon = (
      <span className={`${prefixCls}-item-link`}>
        <div className={`${prefixCls}-item-container`}>
          <KvDoubleRightOut className={`${prefixCls}-item-link-icon`} />
          {ellipsis}
        </div>
      </span>
    );
    return { prevIcon, nextIcon, jumpPrevIcon, jumpNextIcon };
  }, [prefixCls]);

  useEffect(() => {
    if (currentProp && currentProp !== current) {
      setCurrent(Math.max(1, Math.min(currentProp, total)));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentProp, total]);

  const jumpPrevPage = Math.max(1, current - 5);
  const jumpNextPage = Math.min(total, current + 5);

  function isValid(page: number) {
    return isInteger(page) && page !== current && isInteger(total) && total > 0;
  }

  function handleChange(page: number) {
    if (isValid(page) && !disabled) {
      let newPage = page;
      if (page > total) {
        newPage = total;
      } else if (page < 1) {
        newPage = 1;
      }

      setCurrent(newPage);
      onChange?.(newPage);

      return newPage;
    }

    return current;
  }

  const hasPrev = current > 1;
  const hasNext = current < total;

  function prevHandle() {
    if (hasPrev) handleChange(current - 1);
  }

  function nextHandle() {
    if (hasNext) handleChange(current + 1);
  }

  function jumpPrevHandle() {
    handleChange(jumpPrevPage);
  }

  function jumpNextHandle() {
    handleChange(jumpNextPage);
  }

  function runIfEnter(event: React.KeyboardEvent<HTMLLIElement>, callback: (page: number) => void) {
    if (event.key === 'Enter' || event.keyCode === 13) {
      callback(current);
    }
  }

  function runIfEnterPrev(event: React.KeyboardEvent<HTMLLIElement>) {
    runIfEnter(event, prevHandle);
  }

  function runIfEnterNext(event: React.KeyboardEvent<HTMLLIElement>) {
    runIfEnter(event, nextHandle);
  }

  function runIfEnterJumpPrev(event: React.KeyboardEvent<HTMLLIElement>) {
    runIfEnter(event, jumpPrevHandle);
  }

  function runIfEnterJumpNext(event: React.KeyboardEvent<HTMLLIElement>) {
    runIfEnter(event, jumpNextHandle);
  }

  function renderPrev(prevPage: number) {
    const prevButton = iconsProps.prevIcon;
    return isValidElement<HTMLButtonElement>(prevButton)
      ? cloneElement(prevButton, { disabled: !hasPrev })
      : prevButton;
  }

  function renderNext(nextPage: number) {
    const nextButton = iconsProps.nextIcon;
    return isValidElement<HTMLButtonElement>(nextButton)
      ? cloneElement(nextButton, { disabled: !hasNext })
      : nextButton;
  }

  const allPages = total;

  // ================== Render ==================

  const pagerList: React.ReactElement<PagerProps>[] = [];

  const pagerProps: PagerProps = {
    rootPrefixCls: prefixCls,
    onClick: handleChange,
    onKeyDown: runIfEnter,
    showTitle: !!hideNumber,
    page: 1,
    hideNumber,
  };

  const prevPage = current - 1 > 0 ? current - 1 : 0;
  const nextPage = current + 1 < allPages ? current + 1 : allPages;

  // ====================== Normal ======================
  let jumpPrev: ReactElement;
  let jumpNext: ReactElement;
  const pageBufferSize = 8;
  if (allPages <= 3 + pageBufferSize * 2) {
    if (!allPages) {
      pagerList.push(<Pager {...pagerProps} key="noPager" page={1} className={`${prefixCls}-item-disabled`} />);
    }

    for (let i = 1; i <= allPages; i += 1) {
      pagerList.push(<Pager {...pagerProps} key={i} page={i} active={current === i} />);
    }
  } else {
    const prevItemTitle = locale.prev_5;
    const nextItemTitle = locale.next_5;

    const jumpPrevContent = iconsProps.jumpPrevIcon;
    const jumpNextContent = iconsProps.jumpNextIcon;

    if (showPrevNextJumpers) {
      jumpPrev = (
        <li
          title={showTitle ? prevItemTitle : undefined}
          key="prev"
          onClick={jumpPrevHandle}
          tabIndex={0}
          onKeyDown={runIfEnterJumpPrev}
          className={`${prefixCls}-jump-prev  ${prefixCls}-jump-prev`}
        >
          {jumpPrevContent}
        </li>
      );

      jumpNext = (
        <li
          title={showTitle ? nextItemTitle : undefined}
          key="next"
          onClick={jumpNextHandle}
          tabIndex={0}
          onKeyDown={runIfEnterJumpNext}
          className={`${prefixCls}-jump-next ${prefixCls}-jump-next-custom-icon`}
        >
          {jumpNextContent}
        </li>
      );
    }

    let left = Math.max(1, current - pageBufferSize);
    let right = Math.min(current + pageBufferSize, allPages);

    if (current - 1 <= pageBufferSize) {
      right = 1 + pageBufferSize * 2;
    }
    if (allPages - current <= pageBufferSize) {
      left = allPages - pageBufferSize * 2;
    }

    for (let i = left; i <= right; i += 1) {
      pagerList.push(<Pager {...pagerProps} key={i} page={i} active={current === i} />);
    }

    if (current - 1 >= pageBufferSize + 2) {
      pagerList[0] = cloneElement<PagerProps>(pagerList[0], {
        className: `${prefixCls}-item-after-jump-prev ${pagerList[0].props.className}`,
      });
      // @ts-ignore
      pagerList.unshift(jumpPrev);
    }

    if (allPages - current >= pageBufferSize + 2) {
      const lastOne = pagerList[pagerList.length - 1];
      pagerList[pagerList.length - 1] = cloneElement(lastOne, {
        className: `${prefixCls}-item-before-jump-next ${lastOne.props.className}`,
      });

      // @ts-ignore
      pagerList.push(jumpNext);
    }

    if (left !== 1) {
      pagerList.unshift(<Pager {...pagerProps} key={1} page={1} />);
    }
    if (right !== allPages) {
      pagerList.push(<Pager {...pagerProps} key={allPages} page={allPages} />);
    }
  }

  let prev = renderPrev(prevPage);
  if (prev) {
    const prevDisabled = !hasPrev || !allPages;
    prev = (
      <li
        title={showTitle ? locale.prev_page : undefined}
        onClick={prevHandle}
        tabIndex={prevDisabled ? undefined : 0}
        onKeyDown={runIfEnterPrev}
        className={`${prefixCls}-prev ${prevDisabled ? `${prefixCls}-disabled` : ''}`}
      >
        {prev}
      </li>
    );
  }

  let next = renderNext(nextPage);
  if (next) {
    let nextDisabled: boolean, nextTabIndex: number | undefined;

    nextDisabled = !hasNext || !allPages;
    nextTabIndex = nextDisabled ? undefined : 0;

    next = (
      <li
        title={showTitle ? locale.next_page : undefined}
        onClick={nextHandle}
        tabIndex={nextTabIndex}
        onKeyDown={runIfEnterNext}
        className={`${prefixCls}-next ${nextDisabled ? `${prefixCls}-disabled` : ''}`}
      >
        {next}
      </li>
    );
  }

  return (
    <ul
      className={`${prefixCls} ${className ?? ''} ${disabled ? `${prefixCls}-disabled` : ''}`}
      style={style}
      ref={paginationRef}
    >
      {prev}
      {pagerList}
      {next}
    </ul>
  );
};
