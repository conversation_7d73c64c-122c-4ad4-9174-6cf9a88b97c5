.frame-list-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #323f4b;
  user-select: none;
}

.frame-list-container .frame-button {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.frame-list-container .frame-button[data-is-overlay='true']::before {
  display: inline-block;
  content: ' ';
  position: absolute;
  height: 4px;
  width: 100%;
  background-color: #27cd9d;
  top: 0;
}

.frame-list-container .frame-button-state {
  display: none;
  width: 8px;
  height: 8px;
  border-radius: 1px;
  transform: rotateZ(45deg);
}

.frame-list-container .frame-button-state[data-type='KEY'] {
  display: block;
  background-color: #27cd9d;
}

.frame-list-container .frame-button-state[data-type='INDEPENDENT'] {
  display: block;
  background-color: #27cd9d;
}
.frame-list-container .frame-button-state[data-type='INDEPENDENT']::before {
  position: absolute;
  content: ' ';
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  background-color: #27cd9d;
  border: solid 1px #52606d;
}

.frame-list-container .frame-button-state[data-type='INTERPLOATION'] {
  display: block;
  border: 1px solid #27cd9d;
}

.frame-list-container .frame-lock-btn {
  width: 32px;
  height: 32px;
  box-sizing: border-box;
  padding-top: 6px;
  color: #cbd2d9;
  font-size: 20px;
  text-align: center;
  cursor: pointer;
}
.frame-list-container .frame-lock-btn:hover {
  background: #52606d;
  border-radius: 4px;
}

.frame-list-container .ant-pagination.ant-pagination-mini .ant-pagination-item {
  box-sizing: border-box;
  width: 20px;
  min-width: 20px;
  background-color: #52606d;
  border-radius: 2px;
  margin: 0px 1px;
  border: 0;
  overflow: hidden;
}

.frame-list-container
  .ant-pagination
  .ant-pagination-jump-next
  .ant-pagination-item-container
  .ant-pagination-item-ellipsis {
  color: #cbd2d9;
}

.frame-list-container .ant-pagination.ant-pagination-mini .ant-pagination-item-active {
  border: #f5f7fa 1px solid;
}

.frame-list-container .ant-pagination.ant-pagination-mini .ant-pagination-next,
.frame-list-container .ant-pagination.ant-pagination-mini .ant-pagination-next,
.frame-list-container .ant-pagination.ant-pagination-mini .ant-pagination-prev,
.frame-list-container .ant-pagination.ant-pagination-mini .ant-pagination-prev {
  /* background-color: transparent; */
  width: 20px;
  border-radius: 2px;
}

.frame-list-container .frame-page-show {
  font-size: 12px;
  color: #cbd2d9;
  margin-left: 20px;
}

.frame-list-container .frame-page-show .title {
  font-size: 14px;
}

.frame-list-container input {
  width: 32px;
  height: 24px;
  margin: 0 6px;
  background-color: #171f26;
  box-shadow: none;
  text-overflow: ellipsis;
  color: #f5f7fa;
  border: 1px solid #52606d;
  border-radius: 2px;
  text-align: center;
  outline: 0;
}

.frame-panel-button {
  position: absolute;
  right: 17px;
  width: 74px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  color: #f5f7fa;
  font-size: 14px;
  border-radius: 6px;
  cursor: pointer;
}

.frame-panel-button.close {
  background-color: #52606d;
}

.frame-panel-button.open {
  background-color: #3aa691;
}

/* copy ant-design */
.frame-pagination {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(255, 255, 255, 0.85);
  font-size: 12px;
  line-height: 1.6666666666666667;
  list-style: none;
}
.frame-pagination-prev,
.frame-pagination-next {
  border-radius: 2px;
  min-width: 24px;
  height: 24px;
  margin: 0;
  outline: 0;
}
.frame-pagination-prev,
.frame-pagination-next,
.frame-pagination-jump-prev,
.frame-pagination-jump-next {
  display: inline-block;
  color: #cbd2d9;
  text-align: center;
  vertical-align: middle;
  transition: all 0.2s;
  list-style: none;
}

.frame-pagination-jump-prev,
.frame-pagination-jump-next {
  min-width: 32px;
}

.frame-pagination-item {
  display: inline-block;
  box-sizing: border-box;
  width: 20px;
  min-width: 20px;
  background-color: #52606d;
  border-radius: 2px;
  margin: 0px 1px;
  border: 0;
  overflow: hidden;
  height: 24px;
  line-height: 22px;
  text-align: center;
  vertical-align: middle;
  list-style: none;
  outline: 0;
  cursor: pointer;
  user-select: none;
}

.frame-pagination-item:hover {
  background-color: rgba(255, 255, 255, 0.12);
}

.frame-pagination-item-active {
  font-weight: 600;
  border: #f5f7fa 1px solid;
}

.frame-pagination-item-active .frame-button[data-is-overlay='true']::before {
  height: 3px;
}

.frame-pagination-item-link {
  background-color: transparent;
  border-color: transparent;
  display: block;
  width: 100%;
  height: 100%;
  padding: 0;
  font-size: 16px;
  text-align: center;
  border-radius: 2px;
  outline: none;
  transition: border 0.2s;
  color: #cbd2d9;
  cursor: pointer;
  user-select: none;
  border: none;
}
.frame-pagination-next:hover .frame-pagination-item-link,
.frame-pagination-prev:hover .frame-pagination-item-link {
  background-color: rgba(255, 255, 255, 0.12);
}
.frame-pagination-disabled .frame-pagination-item-link {
  color: rgba(255, 255, 255, 0.25);
  cursor: not-allowed;
}

.frame-pagination-disabled:hover .frame-pagination-item-link {
  background-color: transparent;
}

.frame-pagination-jump-next:hover .frame-pagination-item-link-icon,
.frame-pagination-jump-prev:hover .frame-pagination-item-link-icon {
  opacity: 1;
}
.frame-pagination-jump-next:hover .frame-pagination-item-ellipsis,
.frame-pagination-jump-prev:hover .frame-pagination-item-ellipsis {
  opacity: 0;
}

.frame-pagination-item-ellipsis {
  position: absolute;
  top: 0;
  inset-inline-end: 0;
  bottom: 0;
  inset-inline-start: 0;
  display: block;
  margin: auto;
  font-family: Arial, Helvetica, sans-serif;
  letter-spacing: 2px;
  text-align: center;
  text-indent: 0.13em;
  opacity: 1;
  transition: all 0.2s;
  color: #cbd2d9;
}

.frame-pagination-item-container {
  position: relative;
}

.frame-pagination-item-link-icon {
  color: #24a78e;
  font-size: 16px;
  opacity: 0;
  transition: all 0.2s;
}
