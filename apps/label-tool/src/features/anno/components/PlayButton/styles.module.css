.frame-list-tool {
  position: absolute;
  right: 12px;
  top: 5px;
  z-index: 1;
}

/* PlayButton */
.play-button {
  display: flex;
  align-items: center;
}

.play-interval-time {
  width: 50px;
  margin-left: 6px;
  margin-right: 6px;
}
.play-interval-time :global(.ant-input-number-handler-wrap) {
  width: 16px;
}
.icon {
  width: 24px;
  height: 24px;
  font-size: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  margin: 0 4px;
  cursor: pointer;
}
.icon[aria-disabled='true'] {
  opacity: 0.4;
  cursor: not-allowed;
}

.icon:hover {
  background: #52606d;
}

.play-back {
  transform: rotate(-180deg);
}
