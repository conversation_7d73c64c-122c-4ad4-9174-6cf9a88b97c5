import { KvOverlayFrame } from '@forest/icons';
import { InputNumber } from 'antd';
import { FC } from 'react';

import { MenuButton, PopoverWithTooltip } from '@/components';

import styles from './styles.module.css';

interface OverlayFramePanelProps {
  value?: number;
  maxValue: number;
  onChange: (num?: number) => void;
}

const OverlayFrameSettingPanel: FC<OverlayFramePanelProps> = ({ value: number, maxValue, onChange }) => {
  const handleClear = () => {
    onChange(undefined);
  };
  const handleMinus = () => {
    if (!number || number < 1) return;
    onChange(number - 1);
  };
  const handlePlus = () => {
    if (number && number >= maxValue) return;
    const val = (number || 0) + 1;
    onChange(val);
  };
  const handleChange = (val?: number | null) => {
    val = Number(val);
    onChange(Number.isNaN(val) ? undefined : val - 1);
  };

  return (
    <div className={styles['overlay-frame-panel']}>
      <div className={styles['row']}>
        <span>叠帧</span>
        <button className={styles['clear-btn']} onClick={handleClear}>
          清除
        </button>
      </div>
      <div>
        <InputNumber
          addonBefore={
            <button
              aria-disabled={!number || number < 1}
              className={styles['control-btn']}
              style={{ right: '-1px', zIndex: 1 }}
              onClick={handleMinus}
            >
              {'-'}
            </button>
          }
          addonAfter={
            <button
              aria-disabled={!!number && number >= maxValue}
              className={styles['control-btn']}
              style={{ borderRadius: '0 2px 2px 0', left: '-1px' }}
              onClick={handlePlus}
            >
              {'+'}
            </button>
          }
          className={styles['number-input']}
          value={number ? number + 1 : 1}
          onChange={handleChange}
          controls={false}
          precision={0}
          min={1}
          max={maxValue}
        />
      </div>
    </div>
  );
};

export const OverlayFrameButton: FC<
  OverlayFramePanelProps & {
    disabled?: boolean;
  }
> = ({ disabled, ...props }) => {
  if (disabled) {
    return (
      <MenuButton aria-disabled={true}>
        <KvOverlayFrame fontSize={24} />
      </MenuButton>
    );
  }
  return (
    <PopoverWithTooltip
      content={<OverlayFrameSettingPanel {...props} />}
      placement="topRight"
      tooltipPlacement="top"
      tooltipTitle="叠帧"
    >
      <MenuButton>
        <KvOverlayFrame fontSize={24} />
      </MenuButton>
    </PopoverWithTooltip>
  );
};
