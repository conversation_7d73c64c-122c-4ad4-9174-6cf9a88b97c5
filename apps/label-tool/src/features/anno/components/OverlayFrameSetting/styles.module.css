.overlay-frame-panel {
  padding: 8px 12px;
  font-size: 14px;
}

.row {
  display: flex;
  justify-content: space-between;
}

.overlay-frame-panel button {
  cursor: pointer;
  background: transparent;
  user-select: none;
}
.clear-btn {
  color: #27cd9d;
  font-size: 12px;
  border: 0;
  padding: 0;
}

.number-input {
  margin-top: 8px;
  width: 146px;
  height: 32px;
}

.number-input :global(.ant-input-number-group-addon) {
  border: 0;
  padding: 0;
}
.number-input :global(.ant-input-number) {
  border-color: #52606d;
}
.number-input :global(.ant-input-number):hover {
  border-color: #27cd9d;
}
.number-input :global(.ant-input-number):focus-within {
  box-shadow: none;
}
.number-input :global(.ant-input-number-input) {
  font-size: 14px;
  text-align: center;
  /* 这是边框内的元素，所以要扣掉边框 */
  line-height: 30px;
  height: 30px;
}
.control-btn {
  width: 32px;
  height: 32px;
  font-size: 16px;
  box-sizing: border-box;
  border: solid 1px #52606d;
  border-radius: 2px 0px 0px 2px;
  padding: 0;
  position: relative;
}
.control-btn[aria-disabled='false']:hover {
  border-color: #27cd9d;
  color: #27cd9d;
}
.control-btn[aria-disabled='true'] {
  opacity: 0.4;
  cursor: not-allowed;
}
