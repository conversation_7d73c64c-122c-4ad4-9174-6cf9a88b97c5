import { KvRejectLine, KvSubmitLine } from '@forest/icons';
import { Button, Divider, message, Modal, Popconfirm, Tooltip } from 'antd';
import { JointContent, MessageInstance } from 'antd/es/message/interface';
import dayjs from 'dayjs';
import { pick } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import { RiCloudLine, RiCloudOffLine, RiFileCloseLine } from 'react-icons/ri';
import { shallow } from 'zustand/shallow';

import { ADMIN_ORIGIN } from '@/config';
import { HOTKEY_NORMAL_CONFIG, useHotkeyConfigWithScope } from '@/features/hotkey';
import { useRequest } from '@/hooks/use-request';
import { Comment, LabelDataSaveType } from '@/types';

import { reviewJob, ReviewJobRequest, saveJob, submitJob } from '../../api';
import { useContextStore, useStoreApi } from '../../stores';
import { checkAnnotations } from './checkAnnotations';

import './styles.css';

export const ServerHandler: React.FC<FrameHandlerProps> = ({ uid, serverTime }) => {
  const {
    currentType,
    jobDraftTime,
    setJobDraftTime,
    diffSnapshotHash,
    exportSnapshot,
    clearSnapshot,
    getSnapshot,
    setJobLabelDataInfo,
  } = useContextStore(
    (state) =>
      pick(state, [
        'currentType',
        'jobDraftTime',
        'setJobDraftTime',
        'diffSnapshotHash',
        'exportSnapshot',
        'clearSnapshot',
        'getSnapshot',
        'setJobLabelDataInfo',
      ]),
    shallow
  );

  const draftTime = useMemo(
    () => (jobDraftTime ? `最近保存 ${dayjs(jobDraftTime).format('YYYY/MM/DD HH:mm:ss')}` : '未保存'),
    [jobDraftTime]
  );

  const handleSuccess = (version: string = '', messageApi: MessageInstance | null, message: JointContent) => {
    messageApi?.success(message);
    setJobLabelDataInfo(LabelDataSaveType.DRAFT, version);
    setJobDraftTime(version);
  };

  const { isLoading: saving, refetch: save } = useRequest(saveJob, {
    onSuccess: (data, _, messageApi) => {
      handleSuccess(data.version, messageApi, '保存成功 🎉');
    },
    onError: (error, _, messageApi) => {
      messageApi?.error(`保存失败，原因为【${error.message}】请联系管理者`);
    },
  });
  const { isLoading: autoSaving, refetch: autoSave } = useRequest(saveJob, {
    onSuccess: (data, _, messageApi) => {
      handleSuccess(data.version, messageApi, {
        content: '标注结果已自动保存到远程。您也可以在标注作业中手动保存，以防任务结果丢失。',
        duration: 10,
      });
    },
    onError: (error, _, messageApi) => {
      messageApi?.error(`保存失败，原因为【${error.message}】请联系管理者`);
    },
  });
  const { isLoading: clearing, refetch: clear } = useRequest(saveJob, {
    onSuccess: (data, _, messageApi) => {
      messageApi?.success('清除成功 🎉');
      const localSnapshot = getSnapshot();
      // 本地有缓存，说明本地缓存最新
      if (localSnapshot) {
        setJobLabelDataInfo(LabelDataSaveType.LOCAL, serverTime);
      } else if (serverTime) {
        // 有 label 的保存时间，说明服务端有数据
        setJobLabelDataInfo(LabelDataSaveType.SERVER, serverTime);
      } else {
        setJobLabelDataInfo(LabelDataSaveType.NONE, '');
      }
    },
    onError: (error, _, messageApi) => {
      messageApi?.error(`清除失败，原因为【${error.message}】请联系管理者`);
    },
  });

  const onClickSave = () => {
    const snapshot = exportSnapshot();
    save({
      params: {
        uid,
        draft: JSON.stringify(snapshot),
      },
    });
  };

  const onAutoSave = () => {
    diffSnapshotHash().then((snapshot) => {
      if (!snapshot) return;
      autoSave({
        params: {
          uid,
          draft: JSON.stringify(snapshot),
        },
      });
    });
  };

  const onClearServer = () => {
    clear({
      params: {
        uid,
        version: '',
      },
    });
  };

  const onCleanLocal = () => {
    clearSnapshot();
    setTimeout(() => {
      window.location.reload();
    }, 500);
  };

  useEffect(() => {
    let interval: NodeJS.Timer | undefined;

    interval = setInterval(() => {
      onAutoSave();
    }, 10 * 60 * 1000);

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      {currentType === 'segment' ? (
        <>{draftTime}</>
      ) : (
        <>
          <Popconfirm
            title="清除缓存"
            description="您确定要清除本地缓存数据吗，清除后不可恢复"
            placement="bottom"
            onConfirm={onCleanLocal}
            okText="是"
            cancelText="否"
          >
            <div>
              <Tooltip title={'清除缓存并刷新页面'} placement="bottom">
                <Button className="optBtn" type="text" icon={<RiFileCloseLine />}></Button>
              </Tooltip>
            </div>
          </Popconfirm>

          <Tooltip title={'清除远程数据'} placement="bottom">
            <Button
              className="optBtn"
              type="text"
              icon={<RiCloudOffLine />}
              loading={clearing}
              onClick={onClearServer}
            ></Button>
          </Tooltip>
        </>
      )}
      <Tooltip title={'保存到远程'} placement="bottom">
        <Button
          className="optBtn"
          type="text"
          icon={<RiCloudLine />}
          loading={saving || autoSaving}
          onClick={onClickSave}
        ></Button>
      </Tooltip>
      <Divider type="vertical" />
    </>
  );
};

export interface FrameHandlerProps {
  uid: string;
  serverTime?: string;
}

const initialConfirm = {
  open: false,
  shouldSolve: [],
};

export const FrameHandler: React.FC<FrameHandlerProps> = ({ uid }) => {
  const [confirm, setConfirm] = useState<{
    open: boolean;
    shouldSolve: Array<string>;
  }>(initialConfirm);
  const [reconfirm, setReconfirm] = useState<{
    open: boolean;
    mode: ReviewJobRequest['decision'];
  }>({
    open: false,
    mode: 'accept',
  });

  const { job, currentPhase, commentMap, labelMap, exportAnnotations, addResolveComment, clearSnapshot } =
    useContextStore(
      (state) =>
        pick(state, [
          'job',
          'commentMap',
          'labelMap',
          'currentPhase',
          'exportAnnotations',
          'addResolveComment',
          'clearSnapshot',
        ]),
      shallow
    );
  const { isLoading: submitting, refetch: submit } = useRequest(submitJob, {
    onSuccess: (data, _, messageApi) => {
      messageApi?.success('提交成功 🎉');
      clearSnapshot();
      window.location.reload();
    },
    onError: (error, _, messageApi) => {
      messageApi?.error(`提交失败，原因为【${error.message}】请检查标注结果或联系管理者`);
    },
  });
  const { isLoading: reviewing, refetch: review } = useRequest(reviewJob, {
    onSuccess: (data, _, messageApi) => {
      messageApi?.success('已提交审核结果');
      clearSnapshot();
      window.location.reload();
    },
    onError: (error, _, messageApi) => {
      messageApi?.error(`提交失败，原因为【${error.message}】请检查标注结果或联系管理者`);
    },
  });

  const store = useStoreApi();

  const unresolveCommentCount = useMemo(() => {
    let count = 0;
    Object.values(commentMap).forEach((comment) => {
      if (!comment || (comment.status && ['resolved', 'pending'].includes(comment.status))) return;
      count++;
    });
    return count;
  }, [commentMap]);

  useHotkeyConfigWithScope(HOTKEY_NORMAL_CONFIG, 'home', () => {
    onClickBack();
  });

  useHotkeyConfigWithScope(
    HOTKEY_NORMAL_CONFIG,
    'reject',
    () => {
      if (currentPhase?.number === 1) return;
      onClickReview('reject');
    },
    { preventDefault: true }
  );
  useHotkeyConfigWithScope(HOTKEY_NORMAL_CONFIG, 'resolve', () => {
    if (currentPhase?.number === 1) {
      onClickSubmit();
    } else {
      onClickReview('accept');
    }
  });

  const [messageApi, contextHolder] = message.useMessage();
  const [modal, contextModalHolder] = Modal.useModal();

  const confirmSolve = (decision: ReviewJobRequest['decision']) => {
    const [checkResult, checkModal] = checkAnnotations(store, labelMap);
    if (!checkResult) {
      modal.error(checkModal);
      return true;
    }

    if (decision === 'accept') {
      let shouldSolve: string[] = [],
        shouldAddResolve: Array<string | number> = [];

      Object.values(commentMap).forEach((comment) => {
        if (!comment) return;
        const { status, uuid } = comment;
        if (status === 'unresolved') {
          shouldSolve.push(uuid);
        } else if (status === 'pending') {
          shouldAddResolve.push(uuid);
        }
      });

      // 若批注未修改完，打开提示
      if (shouldSolve?.length > 0) {
        if (currentPhase && currentPhase?.number > 1) {
          messageApi.warning({
            content: '当前有添加批注，不能提交只能驳回',
          });
          return true;
        }
        setConfirm({
          open: true,
          shouldSolve,
        });
        return true;
      } else {
        addResolveComment(shouldAddResolve);
        return false;
      }
    }
    return false;
  };

  const onClickSubmit = () => {
    console.log('🚀 onClickSubmit 开始执行:', { timestamp: Date.now() });
    if (!job || confirmSolve('accept')) return;

    if (reconfirm.open === false) {
      setReconfirm({
        open: true,
        mode: 'accept',
      });
      return;
    }

    console.log('📊 开始收集提交数据:', { timestamp: Date.now() });
    const annotations = exportAnnotations();
    const resolveComments: ReviewJobRequest['resolves'] = [];

    console.log('🔍 当前commentMap状态:', Object.fromEntries(
      Object.entries(commentMap).map(([uuid, comment]) => [
        uuid,
        comment ? { status: comment.status, scope: comment.scope, obj_uuids: comment.obj_uuids } : null
      ])
    ));

    Object.values(commentMap).forEach((comment) => {
      if (comment && comment.status === 'pending') {
        console.log('✅ 发现pending批注:', { uuid: comment.uuid, scope: comment.scope, obj_uuids: comment.obj_uuids });
        const { uuid, obj_uuids, elem_idx, scope } = comment;
        if (scope === 'object') {
          const newUuids = obj_uuids.filter((uuid) =>
            annotations.element_annos[elem_idx].rawdata_annos[0].objects.find((obj) => obj.uuid === uuid)
          );
          console.log('🔗 检查标注物存在性:', {
            comment_uuid: uuid,
            obj_uuids,
            existing_uuids: newUuids,
            elem_idx
          });
          if (newUuids.length) {
            resolveComments.push({
              uuid,
              obj_uuids: newUuids,
            });
            console.log('➕ 添加到resolves:', { uuid, obj_uuids: newUuids });
          } else {
            console.warn('⚠️ 批注关联的标注物不存在，跳过:', { uuid, obj_uuids });
          }
        } else {
          resolveComments.push({
            uuid,
            obj_uuids,
          });
          console.log('➕ 添加非object类型批注到resolves:', { uuid, scope });
        }
      } else if (comment) {
        console.log('❌ 跳过非pending批注:', { uuid: comment.uuid, status: comment.status });
      }
    });

    console.log('📤 最终提交数据:', {
      uid,
      resolves_count: resolveComments.length,
      resolves: resolveComments,
      annotations_count: annotations.element_annos.length,
      timestamp: Date.now()
    });

    submit({
      params: {
        uid,
        annotations,
        resolves: resolveComments,
      },
    });

    setConfirm({
      ...confirm,
      open: false,
    });
    setReconfirm({
      ...reconfirm,
      open: false,
    });
  };

  const onClickReview = (decision: ReviewJobRequest['decision']) => {
    if (!job || confirmSolve(decision)) return;

    if (reconfirm.open === false) {
      setReconfirm({
        open: true,
        mode: decision,
      });
      return;
    }

    const annotations = exportAnnotations();
    const addComments: Comment[] = [],
      updateComments: Comment[] = [],
      resolveComments: ReviewJobRequest['resolves'] = [],
      deleteComments: ReviewJobRequest['deleted_comments'] = [];

    // 处理新增、删除、修改
    Object.values(commentMap).forEach((comment) => {
      if (!comment) return;
      const { status, uuid, obj_uuids, scope, elem_idx } = comment;
      if (status === 'pending') {
        if (scope === 'object') {
          const newUuids = obj_uuids.filter((uuid) =>
            annotations.element_annos[elem_idx].rawdata_annos[0].objects.find((obj) => obj.uuid === uuid)
          );
          if (!newUuids.length) return;
        }
        resolveComments.push({ uuid, obj_uuids });
      } else if (status === 'unresolved') {
        const initComment = job.comments?.find((jobComment) => uuid === jobComment.uuid);
        if (initComment) {
          updateComments.push(comment);
        } else {
          addComments.push(comment);
        }
      }
    });

    // 处理删除
    job.comments?.forEach((jobComment) => {
      const { uuid, obj_uuids } = jobComment;
      if (!commentMap[uuid]) {
        deleteComments.push({ uuid, obj_uuids });
      }
    });

    // 只有允许修改才提交 annotations
    review({
      params:
        decision === 'accept'
          ? {
            uid,
            decision,
            annotations: !currentPhase || currentPhase.editable ? annotations : undefined,
            resolves: resolveComments,
            deleted_comments: deleteComments,
          }
          : {
            uid,
            decision,
            annotations: !currentPhase || currentPhase.editable ? annotations : undefined,
            comments: addComments,
            resolves: resolveComments,
            updated_comments: updateComments,
            deleted_comments: deleteComments,
          },
    });

    setReconfirm({
      ...reconfirm,
      open: false,
    });
  };

  const onClickBack = () => {
    const url = `${ADMIN_ORIGIN}/admin/tasks/hall`;
    window.open(url, '_self');
  };

  return (
    <div className="opt-container">
      {contextHolder}
      {contextModalHolder}
      <ServerHandler uid={uid} serverTime={job?.last_modify} />
      {unresolveCommentCount > 0 && (
        <div
          style={{
            color: '#b7c2da',
            fontSize: 14,
            marginRight: 3,
          }}
        >
          {'当前包共有 '}
          {unresolveCommentCount}
          {currentPhase?.number === 1 ? ' 个问题待解决' : ' 个批注'}
        </div>
      )}

      {currentPhase?.number === 1 ? (
        <>
          <Tooltip title={'提交'} placement="bottom">
            <Button
              className="optBtn"
              type="text"
              icon={<KvSubmitLine />}
              loading={submitting}
              onClick={onClickSubmit}
            ></Button>
          </Tooltip>
        </>
      ) : (
        <>
          <Tooltip title={'驳回'} placement="bottom">
            <Button
              className="optBtn"
              type="text"
              icon={<KvRejectLine />}
              loading={reviewing}
              onClick={() => onClickReview('reject')}
            ></Button>
          </Tooltip>
          <Tooltip title={'通过'} placement="bottom">
            <Button
              className="optBtn"
              type="text"
              icon={<KvSubmitLine />}
              loading={reviewing}
              onClick={() => onClickReview('accept')}
            ></Button>
          </Tooltip>
        </>
      )}

      <Modal
        open={confirm.open}
        title="提交"
        okText="确定"
        cancelText="取消"
        onCancel={() =>
          setConfirm({
            ...confirm,
            open: false,
          })
        }
        onOk={() => {
          console.log('⚡ 确认框点击确定，开始处理批注:', {
            shouldSolve: confirm.shouldSolve,
            timestamp: Date.now()
          });
          addResolveComment(confirm.shouldSolve);
          console.log('⏰ addResolveComment 调用完成，准备提交:', { timestamp: Date.now() });
          if (currentPhase?.number === 1) {
            onClickSubmit();
          } else {
            onClickReview('accept');
          }
        }}
      >
        此文件还有 <strong style={{ color: '#3AA691' }}>{confirm.shouldSolve.length}</strong> 个问题待解决，确定提交？
      </Modal>

      <Modal
        open={reconfirm.open}
        title={`您确定${reconfirm.mode === 'accept' ? '提交' : '驳回'}当前任务吗？`}
        okText="确定"
        cancelText="取消"
        onOk={() => {
          if (currentPhase?.number === 1) {
            onClickSubmit();
          } else {
            onClickReview(reconfirm.mode);
          }
        }}
        onCancel={() => {
          setReconfirm({
            ...reconfirm,
            open: false,
          });
        }}
      ></Modal>
    </div>
  );
};
