import { ModalFuncProps } from 'antd';
import { Fragment } from 'react';
import { RiCloseLine } from 'react-icons/ri';

import { Label } from '@/types';
import { AnnoInstanceItem } from '@/utils/annos';

import { StoreType } from '../../stores';

export const checkAnnotations: (
  store: StoreType,
  labelMap: Map<Label['name'], Label>
) => [true, null] | [false, ModalFuncProps] = (store, labelMap) => {
  const { elementAttrs, elementAttrValues, elementAnnos } = store.getState();

  const errElement: Array<string[] | undefined> = Array(elementAttrValues.length).fill(undefined);
  let hasElementErr = false;
  const requiredElementAttrs = elementAttrs.filter((attr) => attr.required);
  for (let i = 0; i < elementAttrValues.length; i++) {
    const currentAttrs = elementAttrValues[i];
    for (let j = 0; j < requiredElementAttrs.length; j++) {
      const attr = requiredElementAttrs[j];
      if (!currentAttrs[attr.name]) {
        !hasElementErr && (hasElementErr = true);
        if (!errElement[i]) {
          errElement[i] = [];
        }
        errElement[i]!.push(attr.display_name);
      }
    }
  }

  // 第几帧，第几个标注物，有多少属性没有填写
  let hasAnnoErr = false;
  const errAnno: Array<Record<AnnoInstanceItem['trackId'], string[]> | undefined> = Array(
    elementAttrValues.length
  ).fill(undefined);
  for (let i = 0; i < Object.keys(elementAnnos).length; i++) {
    const annotations = elementAnnos[i].annotations,
      itemNames = Object.keys(annotations);
    for (let j = 0; j < itemNames.length; j++) {
      const item = annotations[itemNames[j]];
      if (!item) continue;
      const label = labelMap.get(item.label);
      if (!label) continue;
      const allAttrs = label.fullAttrs;
      if (!allAttrs) continue;

      const requiredLabelAttrs = allAttrs.filter((attr) => attr.required);
      for (let k = 0; k < requiredLabelAttrs.length; k++) {
        const attr = requiredLabelAttrs[k];
        if (!item.attrs[attr.name]) {
          !hasAnnoErr && (hasAnnoErr = true);
          if (!errAnno[i]) {
            errAnno[i] = {};
          }
          if (!errAnno[i]![item.trackId]) {
            errAnno[i]![item.trackId] = [];
          }
          errAnno[i]![item.trackId].push(attr.display_name);
        }
      }
    }
  }

  if (!hasElementErr && !hasAnnoErr) return [true, null];

  const errorContent: ModalFuncProps = {
    title: '提交',
    closable: true,
    okText: '我知道了',
    style: {
      width: '600px',
    },
    closeIcon: <RiCloseLine style={{ width: '22px', height: '22px', color: '#F5F7FA' }} />,
    icon: null,
    content: (
      <div className="attrs-check-container">
        <div className="attrs-check-title">属性数据校验失败，以下位置属性未填写</div>
        {errElement.map((errElement, index) => {
          if (!errElement && !errAnno[index]) return '';
          const group = errAnno[index];
          return (
            <div key={`element-error-${index}`} className="attrs-check-element">
              <div className="element-index">{index + 1}帧</div>
              <div className="element-error">
                {errElement && (
                  <div className="element-error-part">
                    <div>帧属性：</div>
                    <div>
                      {errElement.map((name, index) => (
                        <Fragment key={`element-error-${name}-${index}`}>
                          <span>{name}</span>
                          {index < errElement.length - 1 ? ', ' : ''}
                        </Fragment>
                      ))}
                    </div>
                  </div>
                )}
                {group && (
                  <div className="element-error-part">
                    <div>属性面板：</div>
                    <div>
                      {Object.keys(group).map((trackId) => {
                        const names = group[trackId];
                        return (
                          <div key={`anno-error-${trackId}`}>
                            <span>#{trackId}-</span>
                            {names.map((name, index) => (
                              <Fragment key={`anno-error-${name}-${index}`}>
                                <span>{name}</span>
                                {index < names.length - 1 ? ', ' : '; '}
                              </Fragment>
                            ))}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    ),
  };

  return [false, errorContent];
};
