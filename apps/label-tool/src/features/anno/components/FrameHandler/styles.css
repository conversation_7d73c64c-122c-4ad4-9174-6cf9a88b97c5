.opt-container {
  display: flex;
  align-items: center;
}

.opt-container .optBtn {
  font-size: 22px;
  padding: 0;
  margin-left: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.attrs-check-container {
  min-height: 96px;
  max-height: 400px;
  overflow: auto;
  margin-right: -48px;
  padding-right: 16px;
}
.attrs-check-title {
  font-size: 12px;
  font-weight: 600;
  line-height: 22px;
  letter-spacing: 0em;
  color: #f5f7fa;
}
.attrs-check-element {
  border-radius: 4px;
  overflow: hidden;
  margin: 2px 0;
  display: flex;
  flex-direction: row;
}
.attrs-check-element .element-index {
  width: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #f5f7fa;
  background-color: rgba(82, 96, 109, 0.5);
  font-size: 14px;
  font-weight: 600;
  line-height: 22px;
  margin-right: 2px;
}
.attrs-check-element .element-error {
  flex: 1;
}
.attrs-check-element .element-error-part {
  display: flex;
  flex-direction: row;
  background-color: rgba(82, 96, 109, 0.5);
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  color: #cbd2d9;
  padding: 8px;
}
.attrs-check-element .element-error-part:not(:last-child) {
  margin-bottom: 2px;
}
.attrs-check-element .element-error-part:only-child {
  margin-bottom: 0;
}
