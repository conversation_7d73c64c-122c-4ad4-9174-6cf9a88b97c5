/* eslint-disable react-hooks/exhaustive-deps */
import { pick } from 'lodash';
import { Fragment, useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { shallow } from 'zustand/shallow';

import { ORDER_ACTIVE, ORDER_DEFAULT } from '@/config/render';
import { HOTKEY_VIEW_CONFIG, useHotkeyConfigWithScope } from '@/features/hotkey';
import { AnnoInstanceItem } from '@/utils/annos';

import { useContextStore } from '../stores';
import { addAttrsStrToAnno } from '../utils';
import { AttrLabel } from './AttrLabel';

export interface BarContainerProps {
  sceneId?: string;
  onClickBar: (item: AnnoInstanceItem) => void;
}
export const BarContainer = ({ sceneId, onClickBar }: BarContainerProps) => {
  const [hotkeyShowBar, setHotkeyShowBar] = useState(false);
  const [selectedItem, setSelectedItem] = useState<AnnoInstanceItem | null>(null);
  const [elementList, setElementList] = useState<Element[]>([]);

  const { annotations, labelStage, labelMap, editAnno, currentPhase } = useContextStore(
    (state) => pick(state, ['annotations', 'labelStage', 'labelMap', 'editAnno', 'currentPhase']),
    shallow
  );

  useEffect(() => {
    if (sceneId) {
      // 增删标注物时需等待 labelRenderer.domElement 子元素渲染，故在当前帧渲染完成后再获取子元素列表
      requestIdleCallback(
        () => {
          let list: Element[] = [];
          if (labelStage?.labelRenderer) {
            const children = labelStage?.labelRenderer.domElement.children;
            for (let i = 0; i < children.length; i++) {
              if (annotations[children[i].id]) {
                list.push(children[i]);
              }
            }
          }
          setElementList(list);
        },
        {
          timeout: 1000,
        }
      );
    }
  }, [labelStage, sceneId, annotations]);

  useEffect(() => {
    if (editAnno?.type === 'label') {
      const anno = editAnno.anno;
      if (anno instanceof AnnoInstanceItem) {
        setSelectedItem(anno);
        labelStage?.currentSight?.setObjectLabelOrder(anno.name, ORDER_ACTIVE);
        return;
      }
    }
    if (selectedItem) {
      setSelectedItem(null);
      labelStage?.currentSight?.setObjectLabelOrder(selectedItem.name, ORDER_DEFAULT);
    }
  }, [editAnno]);

  useHotkeyConfigWithScope(
    HOTKEY_VIEW_CONFIG,
    'attr-bar-visible',
    () => {
      setHotkeyShowBar((prev) => !prev);
    },
    {
      preventDefault: true,
    }
  );

  const onChangeLabelOrder = (item: AnnoInstanceItem, order = ORDER_DEFAULT) => {
    if (item.name !== selectedItem?.name) {
      labelStage?.currentSight?.setObjectLabelOrder(item.name, order);
    }
  };

  return (
    <Fragment>
      {elementList.map((ele) => {
        if (!annotations[ele.id]) return null;
        return createPortal(
          <AttrLabel
            isShow={hotkeyShowBar}
            item={addAttrsStrToAnno(annotations[ele.id]!, labelMap) as AnnoInstanceItem}
            selectedItem={selectedItem}
            currentPhase={currentPhase?.number}
            onClickAttrBar={onClickBar}
            onChangeOrder={onChangeLabelOrder}
          />,
          ele,
          ele.id
        );
      })}
    </Fragment>
  );
};
