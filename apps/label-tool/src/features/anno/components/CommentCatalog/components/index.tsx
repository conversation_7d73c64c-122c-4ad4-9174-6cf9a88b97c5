import { KvLabelFoldAll, KvLabelUnfoldAll } from '@forest/icons';
import { Affix, Tabs, Tooltip } from 'antd';
import { pick } from 'lodash';
import { FC, MouseEvent, useEffect, useMemo, useRef, useState } from 'react';
import { RiArrowDownSFill, RiArrowLeftSFill } from 'react-icons/ri';

import { COMMENT_STATUS_MAP } from '@/features/anno/config';
import { useContextStore } from '@/features/anno/stores';
import { Label } from '@/types';

import { useExpandedKeys } from '../../LabelCatalog/hooks';
import { type CatalogComment, ListComment } from './ListComment';

const itemDefinitions = [
  { key: 'unresolved', labelTemplate: '未解决' },
  { key: 'pending', labelTemplate: '待确认' },
  { key: 'resolved', labelTemplate: '已解决' },
];

export type CatalogLabel = {
  name: string;
  display_name: string;
  children?: CatalogComment[];
};

export interface CommentListProps {
  stickyDirection: 'top' | 'bottom';
  title: string;
  labelTreeData: CatalogLabel[];
  onSelectAnno: (selectedKey: string, multiple: boolean) => void;
  onClickLabel?: (labelName: Label['name']) => void;
  onRemoveComment: (comment: CatalogComment) => void;
}

export const CommentList: FC<CommentListProps> = ({
  stickyDirection,
  title,
  labelTreeData,
  onClickLabel,
  onRemoveComment,
  onSelectAnno,
}) => {
  const [topDistance, setTopDistance] = useState<number | undefined>(undefined);
  // 控制全部的展开和收起
  const [allExpanded, setAllExpanded] = useState<boolean>(true);
  const [activeKey, setActiveKey] = useState<'unresolved' | 'pending' | 'resolved'>('unresolved');

  const wrapperRef = useRef<HTMLDivElement | null>(null);
  const isAffixed = useRef<boolean>(false);
  const { currentLabel, selectedAnnos } = useContextStore((state) => pick(state, ['currentLabel', 'selectedAnnos']));

  const [instanceData, instanceCounts] = useMemo(() => {
    const result: Record<string, CatalogLabel[]> = {
      unresolved: [],
      pending: [],
      resolved: [],
    };
    const counts: Record<string, number> = {
      unresolved: 0,
      pending: 0,
      resolved: 0,
    };

    labelTreeData.forEach((item) => {
      const categorizedComments =
        item.children?.reduce(
          (acc: Record<string, CatalogComment[]>, child: CatalogComment) => {
            const statusKey = child.commentStatus ?? '';
            if (!acc[statusKey]) acc[statusKey] = [];
            acc[statusKey].push(child);
            return acc;
          },
          { unresolved: [], pending: [], resolved: [] }
        ) || {};

      Object.keys(categorizedComments)?.forEach((status) => {
        if (categorizedComments[status].length) {
          result[status].push({ ...item, children: categorizedComments[status] });
          counts[status] += categorizedComments[status].length;
        }
      });
    });

    return [result, counts];
  }, [labelTreeData]);

  const selectedKeys = useMemo(() => selectedAnnos.map((anno) => anno.name), [selectedAnnos]);

  const [expandedKeys, handleInstanceExpand, handleInstanceExpandAll] = useExpandedKeys(instanceData[activeKey], true);

  useEffect(() => {
    if (expandedKeys.length === instanceData[activeKey].length) {
      setAllExpanded(true);
    } else if (expandedKeys.length === 0) {
      setAllExpanded(false);
    }
  }, [activeKey, expandedKeys, instanceData]);

  const handleExpand = (name: string) => {
    const index = expandedKeys.indexOf(name);
    let newKeys;
    if (index > -1) {
      newKeys = expandedKeys.slice(0, index).concat(expandedKeys.slice(index + 1));
    } else {
      newKeys = [...expandedKeys, name];
    }
    handleInstanceExpand(newKeys);
  };

  const handleExpandLabel = (e: MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    const key = e.currentTarget.dataset.name as string;
    handleExpand(key);
  };

  const handleClickLabel = (e: MouseEvent<HTMLElement>) => {
    let labelName = e.currentTarget.dataset.name as string;
    if (labelName === currentLabel?.name) {
      labelName = '';
    }
    onClickLabel && onClickLabel(labelName);
  };

  const handleClickSticky = () => {
    if (isAffixed.current === true && stickyDirection === 'top') {
      wrapperRef.current?.parentElement?.scroll(0, 0);
    }
  };

  const handleAffixChange = (affixed: boolean | undefined) => {
    if (affixed !== undefined) {
      isAffixed.current = affixed;
      if (stickyDirection === 'bottom' && affixed === false) {
        setTopDistance(32);
      }
    }
  };

  const renderLabel = (label: CatalogLabel) => {
    const { name, children, display_name } = label;
    const isOpen = expandedKeys?.includes(label.name);
    return (
      <div className="label-list-item" key={`label-${name}`}>
        <div className="label" aria-selected={currentLabel?.name === name} data-name={name} onClick={handleClickLabel}>
          <span className="dot" style={{ backgroundColor: COMMENT_STATUS_MAP[activeKey] }}></span>
          <span className="text">
            {display_name} ({children?.length})
          </span>
          <span className="icon" data-name={name} onClick={handleExpandLabel}>
            {isOpen ? <RiArrowDownSFill /> : <RiArrowLeftSFill />}
          </span>
        </div>
        {isOpen &&
          children?.map((child: CatalogComment, index: number) => (
            <ListComment
              key={`list-comment-${child.uuid}-${index}`}
              comment={child}
              index={index}
              selectedKeys={selectedKeys}
              onSelectAnno={onSelectAnno}
              onRemoveComment={onRemoveComment}
            />
          ))}
      </div>
    );
  };

  const expandSetting = allExpanded
    ? {
        title: '全部收起',
        icon: <KvLabelFoldAll />,
        handler: () => {
          handleInstanceExpand([]);
        },
      }
    : { title: '全部展开', icon: <KvLabelUnfoldAll />, handler: handleInstanceExpandAll };

  return (
    <div className="label-list" ref={wrapperRef}>
      <Affix
        offsetTop={stickyDirection === 'top' ? 0 : topDistance}
        offsetBottom={stickyDirection === 'bottom' ? 0 : undefined}
        target={() => wrapperRef.current?.parentElement as HTMLElement}
        onChange={handleAffixChange}
      >
        <div className="label-list-title" onClick={handleClickSticky}>
          <span className="text">{title}</span>
          <span className="opt">
            <Tooltip placement="bottom" title={expandSetting.title}>
              <span onClick={expandSetting.handler} className="icon">
                {expandSetting.icon}
              </span>
            </Tooltip>
          </span>
        </div>
      </Affix>
      <Tabs
        activeKey={activeKey}
        centered
        type="card"
        size="large"
        onChange={(activeKey) => setActiveKey(activeKey as 'unresolved' | 'pending' | 'resolved')}
        items={itemDefinitions.map(({ key, labelTemplate }) => ({
          key,
          label: `${labelTemplate}(${instanceCounts[key]})`,
          children: <div className="label-list-content">{instanceData[key]?.map(renderLabel)}</div>,
        }))}
      />
    </div>
  );
};
