import { pick } from 'lodash';
import { FC, MouseEvent, useMemo } from 'react';
import { RiDeleteBinLine, RiEditBoxLine, RiEyeCloseLine, RiEyeLine } from 'react-icons/ri';

import { useContextStore } from '@/features/anno/stores';
import { VisibleStatus } from '@/features/anno/stores/slices';
import { formatTrackIdDisplayName, getAnnoWidgetIcon } from '@/features/anno/utils';
import { Comment } from '@/types';
import { AnnoInstanceItem } from '@/utils/annos';

export type CatalogComment = Comment & { commentStatus?: string };

interface ListCommentProps {
  comment: CatalogComment;
  index: number;
  selectedKeys: string[];
  onSelectAnno: (selectedKey: string, multiple: boolean) => void;
  onRemoveComment: (comment: CatalogComment) => void;
}

export const ListComment: FC<ListCommentProps> = ({ comment, index, selectedKeys, onSelectAnno, onRemoveComment }) => {
  const {
    labelStage,
    selectedComment,
    currentPhase,
    currentComment,
    annotations,
    commentMap,
    commentConfigDisplay,
    commentsVisibleFlag,
    commentHoverInfo,
    setSelectedComment,
    setCommentHoverInfo,
    createCommentHoverTimeoutId,
    clearCommentHoverTimeoutId,
    changeCommentListVisible,
  } = useContextStore((state) =>
    pick(state, [
      'labelStage',
      'selectedComment',
      'currentPhase',
      'currentComment',
      'annotations',
      'commentMap',
      'commentConfigDisplay',
      'commentsVisibleFlag',
      'commentHoverInfo',
      'setSelectedComment',
      'setCommentHoverInfo',
      'createCommentHoverTimeoutId',
      'clearCommentHoverTimeoutId',
      'changeCommentListVisible',
    ])
  );

  const { content, trackId = '', reasons, scope, obj_uuids, uuid } = comment;
  const anno = useMemo(() => annotations[obj_uuids[0]] ?? undefined, [annotations, obj_uuids]);
  const visibilityStatus = useMemo(() => commentsVisibleFlag[uuid] ?? VisibleStatus.Show, [commentsVisibleFlag, uuid]);
  const isSelected = useMemo(() => {
    if (scope === 'object' && anno) {
      return selectedKeys.includes(anno.name);
    } else {
      return selectedComment?.uuid === uuid || currentComment?.uuid === uuid;
    }
  }, [anno, currentComment?.uuid, scope, selectedComment?.uuid, selectedKeys, uuid]);
  const icon = useMemo(() => (scope === 'object' && anno ? getAnnoWidgetIcon(anno) : <RiEditBoxLine />), [anno, scope]);
  const displayName = useMemo(() => {
    const reasonDetail = commentConfigDisplay[reasons.class];
    const classDisplayName = reasonDetail ? `/${reasonDetail.displayName}` : '';
    const displayContent = content ? `/${content}` : '';
    return `${formatTrackIdDisplayName(trackId)}${classDisplayName}${displayContent}`;
  }, [commentConfigDisplay, reasons.class, content, trackId]);

  const handleSelectComment = (event: MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    if (scope === 'object') {
      anno && onSelectAnno(anno.name, event.ctrlKey || event.metaKey);
      commentHoverInfo && setCommentHoverInfo(null);
      // 移到视野中间
      if (anno instanceof AnnoInstanceItem && labelStage) {
        labelStage.currentSight?.moveToObject(anno.name);
      }
    } else {
      const uuid = event.currentTarget.dataset.name as string;
      labelStage?.currentSight?.moveToObject(uuid);
      // 在漏标编辑中，无法选中其他批注
      if (currentComment) return;
      setSelectedComment(commentMap[uuid]);
    }
  };

  const handleRemoveComment = (e: MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    commentHoverInfo && setCommentHoverInfo(null);
    !currentComment && onRemoveComment(comment);
  };

  const handleShowComment = (event: MouseEvent<HTMLElement>) => {
    clearCommentHoverTimeoutId();
    const { top, left } = event.currentTarget.getBoundingClientRect();
    if (!top || !left || !comment) return;

    const position = [top, window.innerWidth - left + 16, 'unset', 'unset'];

    setCommentHoverInfo({
      anno,
      comment,
      position,
    });
  };

  const handleToggleVisible = (e: MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    const uuid = e.currentTarget.dataset.name as string;
    changeCommentListVisible([uuid], visibilityStatus === VisibleStatus.Hidden ? true : false);
  };

  return (
    <div
      className={'comment-list-item'}
      id={`comment-${uuid}`}
      key={`list-comment-${uuid}-${index}`}
      data-name={uuid}
      aria-selected={isSelected}
      aria-hidden={visibilityStatus !== VisibleStatus.Show}
      onClick={handleSelectComment}
      onMouseEnter={handleShowComment}
      onMouseLeave={createCommentHoverTimeoutId}
    >
      <span className="name">
        <span className="index">{index + 1}</span>
        <span className="icon">{icon}</span>
        <span className="text-wrap">
          <span className="text ellipsis">{displayName}</span>
        </span>
        <span className="icon" aria-hidden={visibilityStatus === VisibleStatus.Show}>
          <RiEyeCloseLine />
        </span>
      </span>
      {currentPhase && currentPhase?.number > 1 ? (
        <span className="opt">
          {scope === 'unspecified' ? (
            <span
              data-name={uuid}
              className="icon"
              onClick={handleToggleVisible}
              aria-disabled={visibilityStatus >= VisibleStatus.DisableMin}
            >
              {visibilityStatus === VisibleStatus.Show ? <RiEyeLine /> : <RiEyeCloseLine />}
            </span>
          ) : null}
          {comment.status !== 'resolved' ? (
            <span data-name={uuid} className="icon" onClick={handleRemoveComment}>
              <RiDeleteBinLine />
            </span>
          ) : null}
        </span>
      ) : null}
    </div>
  );
};
