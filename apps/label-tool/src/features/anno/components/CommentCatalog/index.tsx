import { pick } from 'lodash';
import { CSSProperties, FC, useMemo } from 'react';
import { shallow } from 'zustand/shallow';

import { AnnoInstanceItem } from '@/utils/annos';

import { useContextStore } from '../../stores';
import { AttrsModal } from '../AttrsModal';
import { CommentList } from './components';
import { type CatalogLabel } from './components/index';
import { type CatalogComment } from './components/ListComment';

import './index.css';

export interface CommentCatalogProps {
  style?: CSSProperties;
}

const SOURCE = 'CommentCatalog';

export const CommentCatalog: FC<CommentCatalogProps> = ({ style }) => {
  const {
    labelStage,
    editAnno,
    labels,
    annotations,
    labelAnnoMap,
    dataElementIndex,
    missedCommentMap,
    annoCommentMap,
    selectedComment,
    selectedAnnos,
    deleteComment,
    setSelectedComment,
    setCurrentLabel,
    setSelectedAnnos,
    resetCurrentLabel,
  } = useContextStore(
    (state) =>
      pick(state, [
        'labelStage',
        'editAnno',
        'labels',
        'annotations',
        'labelAnnoMap',
        'annoCommentMap',
        'dataElementIndex',
        'missedCommentMap',
        'selectedComment',
        'deleteComment',
        'setSelectedComment',
        'selectedAnnos',
        'setCurrentLabel',
        'setSelectedAnnos',
        'resetCurrentLabel',
      ]),
    shallow
  );

  const instanceData = useMemo(() => {
    const missedLabels = missedCommentMap?.[dataElementIndex]
      ? Object.values(missedCommentMap[dataElementIndex]).map((comment) => ({
          ...comment,
          commentStatus: comment.status,
        }))
      : [];

    const misses = missedLabels.length
      ? [
          {
            name: 'points',
            display_name: '点云批注',
            children: missedLabels,
          },
        ]
      : [];

    const { instances, compounds } = labels.reduce(
      (acc: { instances: CatalogLabel[]; compounds: CatalogLabel[] }, label) => {
        const children =
          labelAnnoMap[label.name]?.flatMap(({ name }) =>
            annoCommentMap[name] ? { ...annoCommentMap[name], commentStatus: annoCommentMap[name].status } : []
          ) || [];

        if (children.length) {
          const item = { ...label, children };
          label.compound ? acc.compounds.push(item) : acc.instances.push(item);
        }

        return acc;
      },
      { instances: [], compounds: [] }
    );
    return [...misses, ...instances, ...compounds];
  }, [missedCommentMap, dataElementIndex, labels, labelAnnoMap, annoCommentMap]);

  const handleSelectAnnos = (name: string, isMultiple: boolean) => {
    const anno = annotations[name];
    const index = selectedAnnos.findIndex((val) => val.name === name);
    if (anno) {
      if (isMultiple) {
        if (index > -1) {
          selectedAnnos.splice(index, 1);
        } else {
          selectedAnnos.push(anno);
        }
        setSelectedAnnos([...selectedAnnos], SOURCE);
      } else {
        setSelectedAnnos([anno], SOURCE);
      }
    }
  };

  const handleRemoveComment = (comment: CatalogComment) => {
    if (comment.scope === 'unspecified') {
      labelStage?.currentSight?.removeObjectByName(comment.uuid);
      if (selectedComment?.uuid === comment.uuid) setSelectedComment(null);
    }
    deleteComment(comment.uuid);
  };

  const handleSelectLabel = (name: string) => {
    if (name) {
      setCurrentLabel(name);
    } else {
      resetCurrentLabel();
    }
  };

  // 新增 / 编辑 单个 标注物时隐藏标签列表
  if (editAnno?.type === 'widget' && editAnno?.anno instanceof AnnoInstanceItem) return null;
  return (
    <div className="comment-catalog" style={style}>
      <div className="comment-catalog-container">
        <div className="label-catalog-container-list">
          <CommentList
            title="批注列表"
            stickyDirection="top"
            labelTreeData={instanceData}
            onClickLabel={handleSelectLabel}
            onSelectAnno={handleSelectAnnos}
            onRemoveComment={handleRemoveComment}
          />
        </div>
        <AttrsModal />
      </div>
    </div>
  );
};
