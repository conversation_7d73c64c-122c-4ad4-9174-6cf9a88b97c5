.comment-catalog {
  width: 242px;
}
.comment-catalog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
}
.comment-catalog-container-list {
  flex: 1;
  color: #ffffff;
  overflow: auto;
  background-color: #323f4b;
}

.comment-catalog-container .label-list {
  font-size: 12px;
  user-select: none;
}

.comment-catalog-container .ant-tabs-nav {
  margin: 0;
}
.comment-catalog-container .ant-tabs-nav::before {
  display: none;
}
.comment-catalog-container .ant-tabs-nav .ant-tabs-nav-list {
  padding: 0 8px;
  border-bottom: 1px solid #52606d;
  width: 100%;
}
.comment-catalog-container .ant-tabs-nav .ant-tabs-tab {
  border: none;
  background: transparent;
  font-size: 12px;
  color: rgba(245, 247, 250, 0.5);
  flex: 1;
  justify-content: center;
  align-items: center;
  height: 28px;
  padding: 0 !important;
}
.comment-catalog-container .ant-tabs-nav .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #28c89b;
  height: 28px;
  line-height: 28px;
  font-weight: 600;
  border-bottom: 1px solid #28c89b;
}
.comment-catalog-container .ant-tabs-nav .ant-tabs-nav-operations {
  display: none !important;
}
.comment-catalog-container .ant-tabs-tab-btn:focus-visible {
  outline: 0;
}
.comment-catalog-container .ant-tabs-content-holder {
  padding: 0 8px;
  margin-top: 12px;
}

.comment-catalog-container .label-list .label-list-title {
  display: flex;
  align-items: center;
  margin: 4px 0;
  padding: 10px;
  background-color: #323f4b;
  height: 28px;
  color: #cbd2d9;
}

.comment-catalog-container .label-list .label-list-title .opt {
  display: inline-flex;
  align-items: center;
  margin-left: auto;
}

.comment-catalog-container .label-list .label-list-title .icon {
  font-size: 16px;
  line-height: 0;
  margin-inline-start: 4px;
  padding: 2px;
}

.comment-catalog-container .label-list .label-list-title .icon:hover,
.comment-catalog-container .label-list .label-list-title .icon:active {
  background-color: #cbd2d966;
  border-radius: 2px;
  cursor: pointer;
}

.comment-catalog-container .label-list .label-list-content {
  color: #f5f7fa;
}

.comment-catalog-container .label-list .label-list-item .label {
  width: 226px;
  height: 28px;
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  padding: 4px;
  border-radius: 2px;
  background-color: #171f26;
  font-size: 12px;
  overflow: hidden;
  position: relative;
}

.comment-catalog-container .label-list .label-list-item .label:hover {
  cursor: pointer;
  border: 1px solid #3aa691;
}
.comment-catalog-container .label-list .label-list-item .label[aria-selected='true'] {
  background-color: rgba(58, 166, 145, 0.8);
  border: none;
}

.comment-catalog-container .label-list .label-list-item .label .icon {
  display: none;
  width: 12px;
  text-align: center;
  line-height: 0;
  padding: 3px 0;
  margin-inline-end: 4px;
}

.comment-catalog-container .label-list .label-list-item .label:hover .icon {
  display: inline-block;
  cursor: pointer;
}

.comment-catalog-container .label-list .label-list-item .label .text {
  flex: 1;
}

.comment-catalog-container .label-list .label-list-item .label .dot {
  content: ' ';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin: 0 4px;
}

.comment-catalog-container .label-list .comment-list-item {
  background-color: #171f2666;
  padding: 4px;
  margin-bottom: 4px;
  height: 28px;
  width: 226px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 2px;
  overflow: hidden;
}

.comment-catalog-container .label-list .comment-list-item .name {
  display: inline-flex;
  align-items: center;
  position: relative;
  width: 100%;
}
.comment-catalog-container .label-list .comment-list-item .index {
  display: inline-block;
  min-width: 12px;
  text-align: center;
  color: #cbd2d9;
  margin-inline-start: 6px;
  margin-inline-end: 2px;
}
.comment-catalog-container .label-list .comment-list-item .icon {
  line-height: 0;
  margin-inline-start: 2px;
  font-size: 16px;
  margin-inline-end: 2px;
}

.comment-catalog-container .label-list .comment-list-item .icon[aria-hidden='true'],
.comment-catalog-container .label-list .comment-list-item:hover .icon[aria-hidden='false'] {
  display: none;
}

.comment-catalog-container .label-list .comment-list-item .text-wrap {
  flex: 1;
  display: flex;
  overflow: hidden;
}
.comment-catalog-container .label-list .comment-list-item .text {
  margin-inline-start: 2px;
}
.comment-catalog-container .label-list .comment-list-item .ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.comment-catalog-container .label-list .comment-list-item .opt {
  display: none;
  color: #cbd2d9;
}
.comment-catalog-container .label-list .comment-list-item .opt .icon {
  padding: 2px;
}

.comment-catalog-container .label-list .comment-list-item .opt .icon[aria-disabled='true'] {
  cursor: not-allowed;
  opacity: 0.4;
}

.comment-catalog-container .label-list .comment-list-item:hover {
  cursor: pointer;
  border: 1px solid #3aa691;
}

.comment-catalog-container .label-list .comment-list-item[aria-selected='true'] {
  background-color: rgba(58, 166, 145, 0.6);
  border: none;
}

.comment-catalog-container .label-list .comment-list-item.anno-selected-related {
  box-shadow: 0px 0px 4px 0px rgba(58, 166, 145, 0.8);
}

.comment-catalog-container .label-list .comment-list-item[aria-hidden='true'] .name,
.comment-catalog-container .label-list .comment-list-item[aria-hidden='true'] .opt {
  opacity: 0.4;
}

.comment-catalog-container .label-list .comment-list-item:hover .name {
  width: 144px;
}
.comment-catalog-container .label-list .comment-list-item:hover .opt {
  display: inline-flex;
  align-items: center;
}

.comment-catalog-container .label-list .comment-list-item:hover .opt .icon:hover,
.comment-catalog-container .label-list .comment-list-item:hover .opt .icon:active {
  cursor: pointer;
  background-color: #cbd2d966;
  border-radius: 2px;
}

.comment-catalog-container .label-list .comment-list-item:hover .opt .icon[aria-disabled='true']:hover,
.comment-catalog-container .label-list .comment-list-item:hover .opt .icon[aria-disabled='true']:active {
  cursor: not-allowed;
}

.comment-catalog-container .button {
  display: inline-flex;
  background-color: #edf2f7;
  color: #1a202c;
  height: 24px;
  align-items: center;
  margin-inline: 4px;
  cursor: pointer;
  border-radius: 4px;
  border: none;
  padding: 0 10px;
}

.comment-catalog-container .button:hover,
.comment-catalog-container .button:active {
  color: #3aa691;
}

.comment-catalog-container .button span {
  margin-inline-end: 2px;
  line-height: 0;
  font-size: 16px;
  color: #52606d99;
}

.comment-catalog-container .button:hover span,
.comment-catalog-container .button:active span {
  color: #61b8a7;
}

.comment-catalog-container .button.primary {
  color: #ffffff;
  background-color: #3aa691;
}

.comment-catalog-container .button.primary:hover,
.comment-catalog-container .button.primary:active {
  background-color: #61b8a7;
}

.comment-catalog-container .divider {
  height: 1px;
  left: 0;
  background-color: #171f26;
  margin: 8px -8px;
}
.comment-catalog-container .select-label {
  display: flex;
  border-radius: 2px;
  border: 1px solid #52606d;
  margin-bottom: 4px;
  overflow: hidden;
}
.comment-catalog-container .select-label .tag {
  height: 32px;
  line-height: 32px;
  min-width: 41px;
  background: #323f4b;
  color: #f5f7fa;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  border-right: 1px solid #52606d;
  flex-shrink: 0;
  padding-inline: 8px;
}
.comment-catalog-container .select-label .ant-formily-item-colon {
  display: none;
}
.comment-catalog-container .select-label .selector {
  width: 100%;
}
.comment-catalog-container .select-label .ant-input {
  line-height: 22px;
}
