import { request as __request } from '@forest/fetch';
import pako from 'pako';
import { forkJoin, from, map, mergeMap, of, tap } from 'rxjs';

import { ADMIN_ORIGIN } from '@/config';
import {
  anno_v1_ClaimJobRequest as ClaimJobRequest,
  anno_v1_ReviewJobRequest as ReviewJobRequest,
  anno_v1_SaveJobDraftRequest as SaveJobDraftRequest,
  anno_v1_SkipAnnotationRequest as SkipAnnotationRequest,
  anno_v1_SubmitJobRequest as SubmitJobRequest,
  Configs as ConfigService,
  Jobs as JobService,
  Lots as LotService,
} from '@/service/anno/v1';
import { Datas as DataService } from '@/service/annofeed/v1';
import { Stats as StatsService } from '@/service/annostat/v1';
import { S3APIConfig } from '@/service/config';

export type { ReviewJobRequest };

export const getFromUrl = (url: string) => {
  return from(
    __request<string | Uint8Array>(S3APIConfig, {
      method: 'GET',
      url,
    })
  ).pipe(
    map((response) => {
      if (response instanceof Uint8Array) {
        const pakoArr = pako.ungzip(response, { to: 'string' });
        return JSON.parse(pakoArr);
      }
      return JSON.parse(response as string);
    })
  );
};

export const getFromUrls = (urls: Array<string>) => {
  const observables = urls.map((url) => getFromUrl(url));
  return forkJoin(observables);
};

export const claimMyJob = (requestBody: ClaimJobRequest) =>
  from(
    JobService.jobsClaimJob({
      requestBody: {
        ...requestBody,
      },
    })
  ).pipe(
    tap(({ job }) => {
      if (job === null) {
        window.open(`${ADMIN_ORIGIN}/admin/tasks/hall`, '_self');
      }
    }),
    mergeMap(({ job }) => {
      return forkJoin({
        draft: JobService.jobsGetJobDraft({ uid: job!.uid }),
        lastModifiedPhase: JobService.jobsGetJobLastCommitLog({
          uid: job!.uid,
          phase: 0,
        }),
      }).pipe(
        mergeMap(({ draft, lastModifiedPhase }) => {
          const { draft_url, version } = draft;
          // job 的最后的修改时间
          const jobLastModify = lastModifiedPhase.log?.created_at;

          // 如果远程缓存的时间比 job 的最后修改时间新，那么就获取远程缓存
          if (draft_url && version) {
            const lastModify = jobLastModify && version > jobLastModify ? jobLastModify : version;
            return getFromUrl(draft_url).pipe(
              map(({ draft }) => {
                const snapshot = JSON.parse(draft);
                snapshot.version = version;
                return {
                  ...job!,
                  snapshot,
                  last_modify: lastModify,
                };
              })
            );
          } else {
            return of({ ...job!, last_modify: jobLastModify });
          }
        })
      );
    }),
    mergeMap((job) => {
      const observables = [];

      if (job!.elements.length === 0 && job!.elements_urls && job!.elements_urls.length > 0) {
        const observable = getFromUrls(job!.elements_urls!).pipe(
          map((res) => {
            job!.elements = res.flatMap((data) => data.elements);

            if (res[0]?.cam_params && Object.keys(res[0].cam_params).length) {
              job!.cam_params = res[0].cam_params;
            }
            return job;
          })
        );
        observables.push(observable);
      }

      if (job!.annotations.length === 0 && job!.annotations_url) {
        const observable = getFromUrl(job!.annotations_url).pipe(
          map((res) => {
            job!.annotations = res.element_annos;
            return job;
          })
        );
        observables.push(observable);
      }

      if (job!.comments.length === 0 && job!.comments_url) {
        const observable = getFromUrl(job!.comments_url).pipe(
          map((res) => {
            job!.comments = res.comments;
            return job;
          })
        );
        observables.push(observable);
      }

      if (observables.length > 0) {
        return forkJoin(observables).pipe(map((jobs) => jobs[0]));
      } else {
        return of(job);
      }
    }),
    mergeMap((job) => {
      // 处理 transform 的问题
      job.elements.forEach(({ datas: [pcdRawData, ...imageRawData] }) => {
        // 如果第一帧不是点云，或者没有  transform
        if (pcdRawData?.type !== 'pointcloud' || pcdRawData?.transform?.length === 0) return;
        // 把点云的 pcd 添加到每个 image 的 transform
        imageRawData.forEach((raw) => {
          raw.transform = [...pcdRawData.transform, ...raw.transform];
        });
      });

      return forkJoin({
        lot: LotService.lotsGetLot({ uid: job!.lot_uid }),
        comment_reasons: ConfigService.configsListCommentReasons(),
      }).pipe(
        map(({ lot, comment_reasons }) => {
          // 没有 coment_reasons 或者 comment_reasons 为空数组时，需要设置默认值
          if (lot && (!lot.comment_reasons || lot.comment_reasons.length === 0)) {
            // 为了保证批注原因的顺序，得到的顺序是按照批注原因的数量排序的，前面是批注原因数量多的
            lot.comment_reasons = comment_reasons.classes?.sort((a, b) => b.reasons.length - a.reasons.length);
          }
          return { lot, job: job! };
        })
      );
    })
  );

export const submitJob = (requestBody: SubmitJobRequest) =>
  from(JobService.jobsSubmitJob({ uid: requestBody.uid, requestBody }));

export const reviewJob = (requestBody: ReviewJobRequest) =>
  from(
    JobService.jobsReviewJob({
      uid: requestBody.uid,
      requestBody,
    })
  );

export const saveJob = (requestBody: SaveJobDraftRequest) =>
  from(
    JobService.jobsSaveJobDraft({
      uid: requestBody.uid!,
      requestBody,
    })
  );

export const getLotLabelTemplate = (params: { uid: string }) => from(StatsService.statsGetLotLabelStat(params));

export const jobsGetSkipAnnotation = (params: { uid: string }) => from(JobService.jobsGetSkipAnnotation(params));

export const jobsSkipAnnotation = (requestBody: SkipAnnotationRequest) =>
  from(JobService.jobsSkipAnnotation({ uid: requestBody.uid ?? '', requestBody }));

/**
 * 更新 job 的过期时间
 * @param requestBody
 * @returns
 */
export const updateJobExpiration = (requestBody: ClaimJobRequest) =>
  from(
    JobService.jobsClaimJob({
      requestBody: {
        ...requestBody,
        renew: true,
      },
    })
  );

/**
 * 获取数据名和前一个流程操作人的信息
 * @param dataUid lot.data_uid
 * @param jobUid job.uid
 * @param prevPhase job.phase - 1，也就是前一个流程的 phase
 * @returns
 */
export const getLotDataAndPrevPhaseOperator = (dataUid: string, jobUid: string, prevPhase: number) => {
  return from(
    DataService.datasGetData({
      uid: dataUid,
      simple: true,
    })
  ).pipe(
    mergeMap(({ name: dataName }) => {
      const info: {
        dataName: string;
        prevPhaseOperator?: string;
      } = { dataName };
      if (prevPhase && prevPhase > 0) {
        return from(
          JobService.jobsGetJobLastCommitLog({
            uid: jobUid,
            phase: prevPhase,
            direction: 'up',
          })
        ).pipe(
          map(({ log }) => {
            if (log) {
              info.prevPhaseOperator = log.operator.name + '/' + log.operator.uid;
            }
            return info;
          })
        );
      } else {
        return of(info);
      }
    })
  );
};
