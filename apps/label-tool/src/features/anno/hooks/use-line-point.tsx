/* eslint-disable react-hooks/exhaustive-deps */
import { CloseOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import { pick } from 'lodash';
import { useEffect } from 'react';
import { MathUtils } from 'three';

import { useContextStore } from '@/features/anno/stores';
import { formatTrackIdDisplayName, labelTemplate } from '@/features/anno/utils';
import { useMessage } from '@/provider';
import type { ISeriesPoint, WidgetObject } from '@/types';
import { AnnoCompoundItem, AnnoFactory, AnnoInstanceItem } from '@/utils/annos';
import { Sight } from '@/utils/render';

const COMPOUND_KEY = 'compound-warning';

export const useLinePoint = (enable: boolean = false) => {
  const { selectedAnnos, labelStage, annotations, currentAnno, getAnnoInstanceItem, setCurrentAnno, updateAnnoObject } =
    useContextStore((state) =>
      pick(state, [
        'editAnno',
        'selectedAnnos',
        'labelStage',
        'annotations',
        'currentAnno',
        'getAnnoInstanceItem',
        'setCurrentAnno',
        'updateAnnoObject',
      ])
    );
  const messageApi = useMessage();

  // 类型守卫
  const isRecord = (obj: any): obj is Record<string, WidgetObject> =>
    typeof obj === 'object' && !('isAdsorb' in obj && 'indicateLine' in obj);
  const isISeriesPoint = (obj: any): obj is ISeriesPoint =>
    typeof obj === 'object' && 'isAdsorb' in obj && 'indicateLine' in obj;

  const destroyMessage = (key: string) => messageApi?.destroy(key);

  const handleMessage = (key: string, id: string) => {
    destroyMessage(key);
    messageApi?.open({
      key,
      content: (
        <div className={'compound-warning'}>
          <ExclamationCircleFilled className={'compound-warning-icon'} />
          <span className={'compound-warning-text'}>添加的点已自动加入 {id} 组</span>
          <button onClick={() => destroyMessage(key)}>我知道了</button>
          <CloseOutlined className={'compound-warning-close'} onClick={() => destroyMessage(key)} />
        </div>
      ),
      className: 'compound-warning-wrapper',
      duration: 10,
    });
  };

  // 更新引导线标识
  const updateIndicateLine = (sight: Sight, indicateLine: string) => {
    const parent = sight.getObjectByName(indicateLine);
    if (parent) {
      const hasSeriesPoints = parent.children?.some((child) => child?.userData?.seriesPoint);
      parent.userData.isIndicateLine = hasSeriesPoints;
      parent.userData.needsUpdate = true;
    }
  };

  useEffect(() => {
    if (!enable || !currentAnno || !labelStage?.currentSight) return;
    const { anno, opt, source, seriesPoints } = currentAnno;
    if (anno instanceof AnnoInstanceItem && source !== 'undo') {
      const { name, seriesPoint, widget } = anno;
      if (widget?.name === 'point3d' && seriesPoint) {
        const { indicateLine } = seriesPoint;
        switch (opt) {
          case 'add':
            updateIndicateLine(labelStage.currentSight, seriesPoint.indicateLine);
            // 添加点和引导线打组
            let annoCompound = Object.values(annotations)?.find(
              (item) => item instanceof AnnoCompoundItem && item.indicateLine === indicateLine
            ) as AnnoCompoundItem;
            if (annoCompound) {
              annoCompound.compound = [...annoCompound.compound, name];
              updateAnnoObject(annoCompound);
            } else {
              const label = labelTemplate.matchLabelByCompound({ lane: 2 });
              if (!label) return;
              // 组合类型存在
              annoCompound = new AnnoCompoundItem(
                MathUtils.generateUUID(),
                label.name,
                [selectedAnnos[0]?.name, name],
                {
                  attrs: { ...label.defaultAttrValues },
                  indicateLine,
                }
              );
              setCurrentAnno({ anno: annoCompound, opt: 'add' });
            }
            handleMessage(COMPOUND_KEY, formatTrackIdDisplayName(annoCompound?.trackId));
            break;
          case 'update':
            // 更新 point 和 children
            anno.seriesPoint = seriesPoints as ISeriesPoint;
            updateAnnoObject(anno);
            break;
          case 'remove':
            updateIndicateLine(labelStage.currentSight, seriesPoint.indicateLine);
            break;
          default:
            break;
        }
      } else if (widget?.name === 'line3d') {
        switch (opt) {
          case 'update':
            seriesPoints &&
              Object.keys(seriesPoints)?.forEach((key: string) => {
                const item = getAnnoInstanceItem(key);
                if (!item) return;
                if (isRecord(seriesPoints)) {
                  item.updateWidget(seriesPoints[key]);
                  updateAnnoObject(item);
                }
              });
            break;
          default:
            break;
        }
      }
    }
  }, [currentAnno]);

  const updateSeriesPoint = (
    sight: Sight,
    seriesPoints: { isAdsorb: boolean; indicateLine: string },
    anno: AnnoInstanceItem
  ) => {
    const { isAdsorb, indicateLine } = seriesPoints;
    const parent = sight.getObjectByName(indicateLine);
    const target = sight.getObjectByName(anno.name);
    if (target && parent) {
      target.userData.seriesPoint = seriesPoints;
      const item = getAnnoInstanceItem(anno.name);
      if (item) {
        item.seriesPoint = seriesPoints;
        updateAnnoObject(item);
      }
      sight.addObjectFromTool(anno.widget.name, target, isAdsorb ? indicateLine : undefined);
    }
  };

  // 撤销时，可能需要更新点的位置
  useEffect(() => {
    if (!enable || !labelStage?.currentSight || !currentAnno || currentAnno.source !== 'undo') return;
    const { anno, opt, seriesPoints } = currentAnno;
    if (anno instanceof AnnoInstanceItem) {
      if (anno.widget.name === 'line3d') {
        // 引导线
        switch (opt) {
          case 'update':
            // 更新添加点坐标
            seriesPoints &&
              Object.keys(seriesPoints)?.forEach((key) => {
                const item = getAnnoInstanceItem(key);
                const matrixs = labelStage.currentSight?.getMatrixs()[1];
                if (item && matrixs && isRecord(seriesPoints)) {
                  item.updateWidget(seriesPoints[key]);
                  updateAnnoObject(item);
                  const target = labelStage.currentSight?.getObjectByName(key);
                  target && AnnoFactory.updateFromWidget(target, seriesPoints[key], matrixs);
                }
              });
            break;
          default:
            break;
        }
      } else if (anno.widget.name === 'point3d' && anno.seriesPoint) {
        // 添加点
        switch (opt) {
          case 'add':
            // 撤销需要加入引导线及同步 seriesPoint
            updateSeriesPoint(labelStage.currentSight, anno.seriesPoint, anno);
            updateIndicateLine(labelStage.currentSight, anno.seriesPoint.indicateLine);
            break;
          case 'update':
            isISeriesPoint(seriesPoints) && updateSeriesPoint(labelStage.currentSight, seriesPoints, anno);
            break;
          case 'remove':
            updateIndicateLine(labelStage.currentSight, anno.seriesPoint.indicateLine);
            break;
          default:
            break;
        }
      }
    }
  }, [currentAnno]);
};
