import { pick } from 'lodash';

import { checkDeleteAnnos } from '@/features/anno';
import { useContextStore } from '@/features/anno/stores';
import { SelectedSource, VisibleStatus } from '@/features/anno/stores/slices';
import { useMessage } from '@/provider';
import { AnnoCompoundItem, AnnoInstanceItem, IAnnoItem } from '@/utils/annos';

export const useRemoveAnno = () => {
  const {
    labelStage,
    selectedAnnos,
    commentMap,
    dataElementIndex,
    trackIdsVisibleFlag,
    seriesLinkage,
    currentComment,
    getAnnoInstanceItem,
    changeAnnoListVisible,
    setCurrentAnno,
    setSelectedAnnos,
  } = useContextStore((state) =>
    pick(state, [
      'labelStage',
      'selectedAnnos',
      'commentMap',
      'dataElementIndex',
      'trackIdsVisibleFlag',
      'seriesLinkage',
      'currentComment',
      'getAnnoInstanceItem',
      'changeAnnoListVisible',
      'setCurrentAnno',
      'setSelectedAnnos',
    ])
  );

  const messageApi = useMessage();

  // 如果正在编辑漏标时，是不能删除的，但是如果这个标注物在漏标的编辑物中，就可以删除
  const checkRemoveDuringCommentEdit = () => !currentComment;

  // 检测能否删除
  const checkRemoveAnnos = (annos: IAnnoItem[]): boolean => {
    const errMsg = seriesLinkage.isEnabled
      ? seriesLinkage.canTrackIdsRemove(
          annos.map((anno) => anno.trackId),
          dataElementIndex
        )
      : checkDeleteAnnos(annos, commentMap);
    if (errMsg) {
      messageApi?.error(errMsg);
      return false;
    }
    return true;
  };

  // 删除前准备
  const prepareRemoveAnnos = (annos: IAnnoItem[]): boolean => {
    annos.forEach((anno) => {
      // 隐藏的组合标注物，先恢复可见再删除
      if (anno instanceof AnnoCompoundItem && trackIdsVisibleFlag[anno.name] === VisibleStatus.Hidden) {
        changeAnnoListVisible([anno], true);
      }
    });
    return true;
  };

  // 删除线上的添加点
  const handleRemovePoint = (anno: IAnnoItem) => {
    if (anno instanceof AnnoInstanceItem && anno.widget.name === 'line3d') {
      const points: IAnnoItem[] = [];
      const target = labelStage?.currentSight?.getObjectByName(anno.name);
      target?.children
        ?.filter((obj) => obj.userData?.seriesPoint)
        ?.forEach((obj) => {
          const item = getAnnoInstanceItem(obj.name);
          item && points.push(item);
        });
      points?.length && handleRemoveAnnos(points);
    }
  };

  // 批量删除
  const handleRemoveAnnos = (annos: IAnnoItem[]) => {
    if (!checkRemoveAnnos(annos)) return;
    if (!prepareRemoveAnnos(annos)) return;

    // 批量删除
    // TODO: (yingqian) 支持批量撤销，目前撤销只是一个一个恢复
    annos.forEach((anno) => {
      handleRemovePoint(anno);
      setCurrentAnno({ anno, opt: 'remove' });
    });

    // 选中的已经全部删除，清空选中
    setSelectedAnnos([]);
  };

  // 根据 name 删除单个标注物
  const handleRemoveAnno = (anno: IAnnoItem, SOURCE?: SelectedSource) => {
    if (anno) {
      if (!checkRemoveAnnos([anno])) return;
      if (!prepareRemoveAnnos([anno])) return;

      // 删除
      handleRemovePoint(anno);
      setCurrentAnno({ anno, opt: 'remove' });
      // 更新一下 selectedAnnos，防止选中的标注物被删除
      const index = selectedAnnos.findIndex((val) => val.name === anno.name);
      if (index > -1) {
        setSelectedAnnos([...selectedAnnos.slice(0, index), ...selectedAnnos.slice(index + 1)], SOURCE);
      }
    }
  };

  return {
    checkRemoveDuringCommentEdit,
    handleRemoveAnnos,
    handleRemoveAnno,
  };
};
