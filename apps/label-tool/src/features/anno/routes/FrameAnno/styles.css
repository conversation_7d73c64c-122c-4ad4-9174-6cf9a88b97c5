.frame-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.frame-container {
  position: relative;
  width: 100vw;
  flex: 1;
  overflow: hidden;
  display: grid;
  grid-template-rows: 48px 1fr 48px;
  grid-template-columns: 1fr;
  grid-template-areas:
    'header'
    'content'
    'footer';
}

.frame-layout .wrapper {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #f5f7fa;
}

.frame-layout .header-title {
  position: relative;
}

.wrapper .header-bev {
  font-size: 16px;
  line-height: 20px;
}

.wrapper .header-bev[aria-selected='true'] {
  color: #64d9c3;
  background-color: rgba(255, 255, 255, 0.12);
}

.frame-layout .header-help-container {
  display: flex;
  align-items: center;
}

.frame-layout .footer-select {
  position: absolute;
  left: 20px;
}

.frame-layout .footer-data {
  position: absolute;
  color: #f5f7fa;
  left: 160px;
  font-size: 12px;
  display: inline-flex;
  align-items: center;
}

.frame-layout .footer-opt {
  position: absolute;
  color: #cbd2d9;
  font-size: 20px;
  right: 20px;
  display: inline-flex;
  align-items: center;
}

.frame-layout .footer-opt > span {
  line-height: 0;
  margin-inline-start: 8px;
  padding: 4px;
  border-radius: 4px;
}

.frame-layout .footer-opt > span:hover {
  background-color: #52606d;
  cursor: pointer;
}

.frame-layout .footer-opt > span.active {
  color: #28c89b;
}

.frame-layout .footer-opt > span[aria-disabled='true'] {
  cursor: not-allowed;
  background-color: transparent;
  color: #cbd2d988;
}
