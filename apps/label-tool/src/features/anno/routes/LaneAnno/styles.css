.lane-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.lane-container {
  position: relative;
  width: 100vw;
  flex: 1;
  overflow: hidden;
  display: grid;
  grid-template-rows: 48px 1fr 48px;
  grid-template-columns: 1fr;
  grid-template-areas:
    'header'
    'content'
    'footer';
}

.lane-layout .wrapper {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #f5f7fa;
}

.lane-layout .header-title {
  position: relative;
}

.lane-layout .header-help-container {
  display: flex;
  align-items: center;
}

.frame-layout .footer-select {
  position: absolute;
  left: 20px;
}

.lane-layout .footer-data {
  position: absolute;
  color: #f5f7fa;
  left: 160px;
  font-size: 12px;
  display: inline-flex;
  align-items: center;
}

.lane-layout .footer-opt {
  position: absolute;
  color: #cbd2d9;
  font-size: 20px;
  right: 20px;
  display: inline-flex;
  align-items: center;
}

.lane-layout .footer-opt > span {
  line-height: 0;
  margin-inline-start: 8px;
  padding: 4px;
  border-radius: 4px;
}

.lane-layout .footer-opt > span:hover {
  background-color: #52606d;
  cursor: pointer;
}

.lane-layout .footer-opt > span.active {
  color: #28c89b;
}

.lane-layout .footer-opt > span[aria-disabled='true'] {
  cursor: not-allowed;
  background-color: transparent;
  color: #cbd2d988;
}
