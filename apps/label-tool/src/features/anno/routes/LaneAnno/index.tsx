import { Kv<PERSON>om<PERSON>, KvFrameAttrs, KvShortcutPanelLine } from '@forest/icons';
import { Checkbox, Modal, Select } from 'antd';
import dayjs from 'dayjs';
import { omit, pick } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { shallow } from 'zustand/shallow';

import { LabelHeader, SimpleMenu, SimpleMenuItem } from '@/components';
import { ADMIN_ORIGIN } from '@/config';
import { useAuth } from '@/features/auth';
import {
  HOTKEY_NORMAL_CONFIG,
  HOTKEY_VIEW_CONFIG,
  HotkeyPanel,
  useHotkeyConfigWithScope,
  useHotkeyWithScope,
} from '@/features/hotkey';
import { usePose, useRequest } from '@/hooks';
import { LabelDataSaveType, type Rawdata } from '@/types';

import { claimMyJob, jobsGetSkipAnnotation, jobsSkipAnnotation } from '../../api';
import {
  CommentCatalog,
  CommentEdit,
  FrameAttrs,
  FrameHandler,
  FrameList,
  GlobalSkeleton,
  ImageScene,
  LabelCatalog,
  LabelScene,
  OnlyReadAlter,
  PlayButton,
  ToolMenu,
} from '../../components';
import { AnnoComment } from '../../components/AnnoComment';
import { CommentModal } from '../../components/CommentModal';
import { JobInfo } from '../../components/JobInfo';
import { HOTKEY_CONFIG } from '../../config';
import { formatLaneAnnoState, laneAnnoStore, StoreProvider, useContextStore, useStoreApi } from '../../stores';
import { CuboidHandler } from '../../utils';

import './styles.css';

const COMMENT_MAPS: Array<SimpleMenuItem> = [
  {
    name: 'comment',
    type: 'button',
    text: '批注列表',
    icon: <KvComment />,
  },
  {
    name: 'divider',
    type: 'divider',
  },
];

const HELP_MAPS: Array<SimpleMenuItem> = [
  {
    name: 'hotkey',
    type: 'button',
    text: '快捷键',
    icon: <KvShortcutPanelLine />,
  },
  {
    name: 'divider',
    type: 'divider',
  },
];

const style: React.CSSProperties = {
  position: 'absolute',
  height: 'calc(100% - 16px)',
  top: 8,
  right: 8,
  zIndex: 2,
};

const InnerLaneAnno = () => {
  const [currentRawdata, setCurrentRawdata] = useState<Rawdata | undefined>();
  const [isOpenHotkeyPanel, setIsOpenHotkeyPanel] = useState(false);
  const [isOpenComment, setIsOpenComment] = useState(false);
  const [openFrameAttrsModal, setOpenFrameAttrsModal] = useState(false);
  const [isSkip, setIsSkip] = useState(false);
  const [preBox, setPreBox] = useState<string>();

  const [searchParams] = useSearchParams();

  const {
    lot,
    currentPhase,
    job,
    jobLabelDataInfo,
    dataList,
    labelStage,
    currentElementIndex,
    poseVisible,
    posePoints,
    rawMeta,
    elementAttrs,
    selectedComment,
    setSelectedComment,
    initJobData,
    setElementIndex,
  } = useContextStore(formatLaneAnnoState, shallow);

  const store = useStoreApi();
  const { currentTool } = useContextStore((state) => pick(state, ['currentTool']));

  const labelDataInfoText = useMemo(() => {
    if (!jobLabelDataInfo) return '';
    const { latestSaveType, remoteLatestSaveTime } = jobLabelDataInfo;
    if (!remoteLatestSaveTime) return '暂无远程数据';
    if (latestSaveType === LabelDataSaveType.DRAFT) return `已经保存到远程`;
    const localTimeStr = dayjs(remoteLatestSaveTime).format('M月DD日 HH:ss');
    return `${latestSaveType === LabelDataSaveType.LOCAL ? '最近保存到远程' : '最近更新'}:  ${localTimeStr}`;
  }, [jobLabelDataInfo]);

  const selectOptions = useMemo(
    () =>
      lot?.tool_cfg?.pre_box?.map((config, inx) => {
        const { name, length, width, height } = config;
        return {
          label: `${name}: ${length}*${width}*${height}`,
          value: name + '-' + inx,
        };
      }) ?? [],
    [lot]
  );

  const { user } = useAuth();
  const [modal, contextHolder] = Modal.useModal();

  const { refetch: claimJob } = useRequest(claimMyJob, {
    params: {
      lot_uid: searchParams.get('lid') || '',
      prefer: searchParams.get('prefer') === '1' ? 'rejected_first' : 'unspecified',
    },
    onSuccess: ({ lot, job }) => {
      initJobData(job, lot, user?.uid || '');
      setCurrentRawdata(job.elements[0].datas[0]);
      getSkip({ params: { uid: job.uid } });
    },
    onError: (error) => {
      modal.error({
        zIndex: 5001,
        content: error.message,
        okText: '返回大厅',
        onOk: () => window.open(`${ADMIN_ORIGIN}/admin/tasks/hall`, '_self'),
      });
    },
  });

  const { refetch: skip } = useRequest(jobsSkipAnnotation, {
    onSuccess: ({ skip_annotation }) => skip_annotation !== undefined && setIsSkip(skip_annotation),
  });

  const { refetch: getSkip } = useRequest(jobsGetSkipAnnotation, {
    onSuccess: ({ skip_annotation }) => skip_annotation !== undefined && setIsSkip(skip_annotation),
  });

  usePose(labelStage, posePoints, poseVisible, Boolean(rawMeta.pointcloud), currentElementIndex, setElementIndex);

  useEffect(() => {
    if (labelStage) {
      claimJob({});
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [labelStage]);

  useEffect(() => {
    if (preBox) {
      // 每次选择工具会重新创建实例，需要初始化预置框配置
      updatePreBox(preBox);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentTool]);

  const handleMenuClick = (name: string) => {
    if (name === 'hotkey') {
      setIsOpenHotkeyPanel((pre) => !pre);
    } else if (name === 'comment') {
      setIsOpenComment((pre) => !pre);
    }
  };

  const handleToggleFrameAttrsModal = () => {
    if (elementAttrs.length === 0) return;
    setOpenFrameAttrsModal((pre) => !pre);
  };

  useHotkeyConfigWithScope(HOTKEY_NORMAL_CONFIG, 'exit', () => {
    const { selectedAnnos, currentTool, editAnno, setCurrentTool, setSelectedAnnos } = store.getState();
    if (isOpenHotkeyPanel) {
      setIsOpenHotkeyPanel(false);
    } else if (openFrameAttrsModal) {
      setOpenFrameAttrsModal(false);
    } else if (currentTool !== 'select') {
      setCurrentTool('select');
      editAnno && setSelectedAnnos([editAnno.anno]);
    } else if (selectedAnnos.length > 0) {
      setSelectedAnnos([]);
    } else if (selectedComment) {
      setSelectedComment(null);
    }
  });

  useHotkeyConfigWithScope(HOTKEY_VIEW_CONFIG, 'element-attr-panel', () => {
    if (elementAttrs.length === 0) return;
    setOpenFrameAttrsModal(true);
  });

  useHotkeyWithScope(
    'mod+backslash',
    () => {
      setIsOpenHotkeyPanel((pre) => !pre);
    },
    {
      preventDefault: true,
    }
  );

  const handleSkip = (isSkip: boolean) => {
    skip({
      params: {
        lot_id: searchParams.get('lid') || '',
        uid: job?.uid,
        skip_annotation: isSkip,
      },
    });
  };

  const updatePreBox = (value: string) => {
    if (currentTool === 'cuboid') {
      if (value !== undefined) {
        const inx = Number(value.split('-')[1]);
        CuboidHandler.preBox = omit(lot?.tool_cfg?.pre_box?.[inx], ['name']);
      } else {
        CuboidHandler.preBox = undefined;
      }
    }
  };

  const onSelectChange = (value: string) => {
    setPreBox(value);
    updatePreBox(value);
  };

  return (
    <>
      {contextHolder}
      <LabelHeader username={user?.name} height={48} style={{ gridArea: 'header' }}>
        <div className="wrapper">
          <ToolMenu sceneType={currentRawdata?.type}></ToolMenu>
          <div className="header-title">
            {lot ? `${lot.name}：${currentPhase?.name}` : ''} <JobInfo />
          </div>

          <div className="header-help-container">
            <Checkbox onChange={(e) => handleSkip(e.target.checked)} checked={isSkip}>
              无需标注
            </Checkbox>
            <SimpleMenu items={COMMENT_MAPS} onSelect={handleMenuClick} selectedKey={isOpenComment ? 'comment' : ''} />
            <SimpleMenu items={HELP_MAPS} onSelect={handleMenuClick} selectedKey={isOpenHotkeyPanel ? 'hotkey' : ''} />
            {job?.uid ? <FrameHandler uid={job.uid} /> : <></>}
          </div>
        </div>
      </LabelHeader>

      <div
        style={{
          position: 'relative',
          gridArea: 'content',
          backgroundColor: '#1a202c',
        }}
      >
        <ImageScene
          images={dataList}
          orders={lot?.tool_cfg?.image_order}
          relativePCD={job?.elements[0]?.datas[0]}
          style={{ position: 'absolute', top: 8, left: 8, bottom: 8, zIndex: 2 }}
        />
        <LabelScene sceneData={currentRawdata} rangeCenter={posePoints[currentElementIndex]} />

        {isOpenComment ? <CommentCatalog style={style} /> : <LabelCatalog style={style} />}

        {openFrameAttrsModal && (
          <FrameAttrs
            onClose={() => setOpenFrameAttrsModal(false)}
            style={{ position: 'absolute', bottom: 8, right: 18, zIndex: 2 }}
          />
        )}

        <HotkeyPanel config={HOTKEY_CONFIG} isOpen={isOpenHotkeyPanel} onClose={() => setIsOpenHotkeyPanel(false)} />
        <AnnoComment />
        <CommentModal />
        <CommentEdit />
      </div>

      <FrameList style={{ gridArea: 'footer', position: 'relative', zIndex: 999 }} selectFrame={setElementIndex}>
        {selectOptions ? (
          <div className="footer-select">
            <Select
              placeholder="预置框"
              value={preBox}
              allowClear
              options={selectOptions}
              onChange={onSelectChange}
              style={{ width: 130 }}
            />
          </div>
        ) : null}
        <div className="footer-data">{labelDataInfoText}</div>
        <div className="footer-opt">
          <PlayButton />
          <span
            className={openFrameAttrsModal ? 'active' : ''}
            aria-disabled={elementAttrs.length === 0}
            onClick={handleToggleFrameAttrsModal}
          >
            <KvFrameAttrs />
          </span>
        </div>
      </FrameList>
    </>
  );
};

export const LaneAnno = () => {
  return (
    <StoreProvider value={laneAnnoStore}>
      <div className="lane-layout">
        <OnlyReadAlter />
        <div className="lane-container">
          <InnerLaneAnno />
          <GlobalSkeleton />
        </div>
      </div>
    </StoreProvider>
  );
};
