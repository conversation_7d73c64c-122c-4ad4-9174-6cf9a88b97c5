import { Navigate, Route, Routes } from 'react-router-dom';

import { FrameAnno } from './FrameAnno';
import { LaneAnno } from './LaneAnno';

export const AnnoRoutes = () => {
  return (
    <Routes>
      <Route path="frame" element={<FrameAnno />}></Route>
      <Route path="lane" element={<LaneAnno />}></Route>

      <Route path="*" element={<Navigate to="/annotate/frame" />}></Route>
    </Routes>
  );
};
