import { DebouncedFunc, throttle } from 'lodash';
import { Line3, Mesh, Object3D, Vector3 } from 'three';

import { OBJECT_EDIT_YELLOW } from '@/config/color';
import { FatLine } from '@/lib/three';
import { AnnoFactory, Caliper3d, ClosestElement, Line3d, Poly3d, Spline3d } from '@/utils/annos';
import { PointPicker, Sight, Stage } from '@/utils/render';

import { CubeDot } from '../handlers/CubeDot';
import { EventHandlerType, HandlerOptions } from '../handlers/EventHandler';
import { EventSelector } from './EventSelector';

const WAIT_TIME = 50;
const INDICATE_WHITE = '#FFFFFF';
const ACTIVE_WHITE = '#FFFFFF';

export class Poly3dSelector extends EventSelector {
  protected Line: typeof Line3d | typeof Spline3d | typeof Caliper3d | typeof Poly3d;
  protected oldEditObject?: FatLine | null;
  public declare editObject?: FatLine;

  protected pointer3: Vector3;
  protected vectors: Vector3[];
  protected lines: Line3[];
  protected pointsPosition: Record<string, ClosestElement>;
  protected pointPicker: PointPicker;

  protected indicatePoint: Mesh;
  protected indicateLine: FatLine;
  protected axisCtrlHelper: Object3D;

  // 响应鼠标的移动和鼠标移动前的按下操作
  protected hoverPointId: number;
  // 响应鼠标从按下、移动到抬起的过程，结束后会清空
  protected dragPointId: number;
  // 响应鼠标按下后的抬起操作，表示选中
  protected activePointId: number;

  protected hoverLineId: number;
  protected hoverLineClosest: Vector3;

  protected debounceSetIndicate: DebouncedFunc<(start: Vector3 | undefined, end: Vector3) => void>;

  protected selectionPoints: number[][];

  /**
   * 初始化折线工具
   * @param type 类型
   * @param stage 需确保当前 currentSight 不为空
   * @param handlerOptions
   * @param lineEditOptions
   */
  constructor(type: EventHandlerType, stage: Stage, handlerOptions: HandlerOptions) {
    super(type, stage, handlerOptions);
    this.pointer3 = new Vector3();
    this.vectors = [];
    this.lines = [];
    this.pointsPosition = {};
    this.pointPicker = new PointPicker(
      stage.currentSight!.getUsingRawObject(),
      stage.currentSight!.camera,
      CubeDot.POINT_SIZE * 1.1,
    );

    this.hoverPointId = -1;
    this.dragPointId = -1;
    this.activePointId = -1;

    this.hoverLineId = -1;
    this.hoverLineClosest = new Vector3();

    // 创建引导点与引导线
    this.indicatePoint = CubeDot.create(0, 0, 0, INDICATE_WHITE);
    this.indicatePoint.scale.set(1.8, 1.8, 1.8);

    this.indicateLine = Line3d.create([0, 0, 0, 0, 0, 0, 0, 0, 0], {
      color: INDICATE_WHITE,
      vertexColors: [1, 1, 1, 0.112, 0.125, 0.147, 0.1, 0.1, 0.1],
      canvasSize: [this.stage.width, this.stage.height],
    });

    this.indicatePoint.visible = false;
    this.indicateLine.visible = false;

    // 设置引导元素的图层
    const layer = Sight.getLayerFromTool(type);
    this.indicatePoint.layers.set(layer);

    this.axisCtrlHelper = new Object3D();

    this.stage.currentSight?.scene.add(this.indicatePoint, this.indicateLine, this.axisCtrlHelper);

    this.debounceSetIndicate = throttle(this.setIndicate, WAIT_TIME);

    this.Line = Poly3d;

    this.selectionPoints = [];
  }

  onPointerDown(event: PointerEvent) {
    const { button } = event;

    let needsUpdate = false;

    //  左键用来新增点，中键用来退出，右键用来拖拽画布
    switch (button) {
      case 0:
        needsUpdate = this.handlePointerDownLeft(event);
        if (needsUpdate && this.indicatePoint.visible) {
          this.onPointerChange(this.indicatePoint.position, 'click');
        }
        break;
      case 1:
        // 处理中键按下：隐藏端点引导线
        this.disableIndicateLine();
        needsUpdate = true;
        break;
    }

    if (this.editObject) {
      this.editObject.userData.needsUpdate = needsUpdate;
    }

    return needsUpdate;
  }

  onPointerMove(event: PointerEvent) {
    this.updatePointer(event);

    if (event.buttons <= 0) {
      // 鼠标处于松开状态

      // 尚未创建线条
      if (!this.editObject || this.vectors.length === 0) {
        this.debounceSetIndicate(undefined, this.pointer3);
        return false;
      }

      if (this.isEndPoint(this.activePointId) && this.mode === 'create') {
        this.debounceSetIndicate(this.vectors[this.activePointId], this.pointer3);
        return true;
      }
    }
  }

  onPointerUp(event: PointerEvent) {
    // 非左键抬起事件，不处理
    if (event.button !== 0) return false;

    if (this.editObject) {
      this.activePointId = this.hoverPointId;
      Line3d.changePointColor(this.editObject, this.activePointId, ACTIVE_WHITE, OBJECT_EDIT_YELLOW);
    }

    return true;
  }

  onMouseLeave() {
    this.onPointerChange(null);
  }

  onKeyDown(event: KeyboardEvent) {
    switch (event.code) {
      case 'Escape':
        if (this.indicateLine.visible) {
          this.disableIndicateLine();
          return true;
        }
        break;
      case 'Delete':
      case 'Backspace':
        // 没有选中的点
        if (this.activePointId === -1) return false;
        this.deletePoint(this.activePointId);
        break;
      default:
        break;
    }
  }

  updateEditVectors(object?: Object3D) {
    if (object && object instanceof FatLine && object.userData.type === this.type) {
      const positions = AnnoFactory.getVertexFromObject3D(this.type, object);
      const len = Math.floor(positions.length / 3);
      this.vectors = new Array(len);
      this.lines = [];

      // 创建顶点集合
      for (let i = 0; i < len; i++) {
        this.vectors[i] = new Vector3().fromArray(positions, i * 3);
      }

      // 创建线段集合
      for (let i = 1; i < this.vectors.length; i++) {
        this.lines.push(new Line3(this.vectors[i - 1], this.vectors[i]));
      }
    } else {
      this.lines = [];
      this.vectors = [];
    }
    this.activePointId = -1;
  }

  updatePointer(event: PointerEvent | MouseEvent) {
    this.pointer3
      .set((event.offsetX / this.stage.width) * 2 - 1, -(event.offsetY / this.stage.height) * 2 + 1, 0)
      .unproject(this.stage.currentSight!.camera);
  }

  // 开始创建线
  beginCreateLine() {
    this.mode = 'create';

    this.editObject = this.Line.create(this.indicatePoint.position.toArray(), {
      color: OBJECT_EDIT_YELLOW,
      maxPoints: 32,
      growable: true,
      withDot: true,
      canvasSize: [this.stage.width, this.stage.height],
    });
    this.vectors = [new Vector3().copy(this.indicatePoint.position)];
    this.hoverPointId = 0;
    this.activePointId = this.hoverPointId;
    this.stage.currentSight?.addObjectFromTool(this.type, this.editObject);
  }

  // 处理左键按下事件：添加点，移动点
  handlePointerDownLeft(event: PointerEvent) {
    this.debounceSetIndicate.cancel();
    this.updatePointer(event);
    this.selectionPoints.push([event.offsetX, event.offsetY]);

    // 可以新建线的判断条件：当前没有编辑物 或者 当前点击的范围没有在当前的编辑物上形成引导线或点
    if (!this.editObject || (this.hoverPointId === -1 && !this.indicateLine.visible && !this.indicatePoint.visible)) {
      // 创建折线
      this.setIndicate(undefined, this.pointer3);

      if (this.indicatePoint.visible) {
        this.beginCreateLine();
        return true;
      } else {
        return false;
      }
    }

    if (this.indicateLine.visible || this.vectors.length === 0) {
      // 引导线出现 ，在线条首尾添加点 / 所有点均被删除，重新绘制
      this.setIndicate(this.vectors[this.hoverPointId], this.pointer3);

      if (this.indicatePoint.visible) {
        this.addEndPoint(this.indicatePoint.position);
      }
      return true;
    }

    if (!this.indicatePoint.visible) return false;

    // 准备 hover 的地方创建新的点
    if (event.shiftKey && this.onObjectBegin()) {
      this.beginCreateLine();
      return true;
    }

    return false;
  }

  /**
   * 处理删除点
   * @returns
   */
  deletePoint(pointId: number) {
    // 无编辑对象时，不支持删除
    if (!this.editObject) return false;

    if (pointId >= 0 && pointId < this.vectors.length && this.vectors.length > 1) {
      // 删除当前选中点
      this.Line.partialUpdate(this.editObject, [], { startIndex: pointId, mode: 'remove' });

      // 更新点集、边集和当前预选中点
      this.activePointId = this.removeVector(pointId);

      // 删除点前，并没有出现引导线，说明点击之后没有移动，则只是单纯的选中点
      if ((this.activePointId === 0 || this.activePointId === this.vectors.length - 1) && !this.indicateLine.visible) {
        this.activePointId = -1;
        this.hoverPointId = -1;
        Line3d.changePointColor(this.editObject, this.activePointId, ACTIVE_WHITE, OBJECT_EDIT_YELLOW);
        return true;
      }

      Line3d.changePointColor(this.editObject, this.activePointId, ACTIVE_WHITE, OBJECT_EDIT_YELLOW);
      this.hoverPointId = this.activePointId;
      // 删除的是中间的点或者没有点了
      if (this.activePointId === -1) {
        this.indicateLine.visible = false;
        this.indicatePoint.visible = false;
      } else {
        // 当前预选中点为端点，重置引导线起点
        const v = this.vectors[this.activePointId];
        this.updateIndicateLine(v, this.indicatePoint.position);
      }

      return true;
    }
    return false;
  }

  /**
   * 设置引导线点坐标
   * @param start
   * @param end
   */
  setIndicate(start: Vector3 | undefined, end: Vector3) {
    this.onPointerChange(end);

    // 更新引导点
    if (end) {
      this.indicatePoint.position.copy(end);
      if (!this.indicatePoint.visible) {
        this.indicatePoint.visible = true;
      }
    } else if (this.indicatePoint.visible || this.indicateLine.visible) {
      this.indicatePoint.visible = false;
      this.indicateLine.visible = false;
    }

    if (start && end) {
      this.updateIndicateLine(start, end);
    }
  }

  updateIndicateLine(start: Vector3, end: Vector3) {
    if (!this.indicateLine.visible) {
      this.indicateLine.visible = true;
    }
    // 更新引导线，如果点 >= 2个，就需要两段引导线，如果减少 < 2，则只有一段引导线
    const updatePosition = [start.x, start.y, start.z, end.x, end.y, end.z];
    if (this.vectors.length + 1 >= Poly3d.MIN_POINTS_COUNT) {
      Line3d.update(this.indicateLine, updatePosition.concat(this.vectors[0].toArray()));
      this.indicateLine.geometry.instanceCount = 2;
    } else {
      Line3d.update(this.indicateLine, updatePosition);
      this.indicateLine.geometry.instanceCount = 1;
    }
  }

  isEndPoint(pointId: number) {
    return this.vectors.length > 0 && (pointId === 0 || pointId === this.vectors.length - 1);
  }

  /**
   * 取消引导线
   */
  disableIndicateLine() {
    // 点数小于指定数量，点击 esc 就直接删除了
    if (this.vectors.length < Poly3d.MIN_POINTS_COUNT) {
      this.editObject?.parent?.remove(this.editObject);
      this.mode = 'wait';
      this.editObject = undefined;
      this.debounceSetIndicate.cancel();
      this.indicateLine.visible = false;
      this.indicatePoint.visible = false;
      this.updateEditVectors();
      return;
    }

    if (this.editObject && this.indicateLine.visible) {
      // 取消 debounce 调用，避免在隐藏引导线后再次显示引导线
      this.debounceSetIndicate.cancel();
      this.indicateLine.visible = false;
      this.indicatePoint.visible = false;
      this.hoverPointId = -1;
      this.activePointId = -1;

      // 为了避免直接把类型为 Mesh<BoxGeometry, MeshBasicMaterial> 的点赋值给 this.editObject，
      // 引入的新类型 Mesh<BoxGeometry, MeshBasicMaterial> 需要在所有使用 Line3dHandler 的地方过滤，
      // 如 Caliper3dHandler,Spline3dHandler

      if (this.mode === 'create') {
        // 结束创建阶段
        this.mode = 'wait';

        if (this.onObjectMount(this.editObject, { selectionPoints: this.selectionPoints })) {
          this.selectionPoints.length = 0;
          this.editObject.parent?.remove(this.editObject);
        }
        // 清空当前编辑对象，便于重设标注物的编辑状态样式
        this.editObject = undefined;
        this.updateEditVectors();
      }
    }
  }

  addEndPoint(pos: Vector3) {
    if (!this.editObject) return;

    const id = this.hoverPointId === this.vectors.length - 1 ? this.vectors.length : 0;
    // 处理引导线出现，点击在头部或尾部添加点事件
    this.Line.partialUpdate(this.editObject, pos.toArray(), {
      startIndex: id,
      mode: 'add',
    });

    this.addVector(new Vector3().copy(pos), id);

    // 更新下一个添加的点位置
    this.hoverPointId = id;
    this.activePointId = id;
  }

  addVector(vec: Vector3, id: number) {
    if (id >= 0 && id <= this.vectors.length) {
      const changedVecs =
        id === 0
          ? [vec, this.vectors[0]]
          : id === this.vectors.length
            ? [this.vectors[this.vectors.length - 1], vec]
            : [this.vectors[id - 1], vec, this.vectors[id]];

      const newLines: Line3[] = [];
      for (let i = 1; i < changedVecs.length; i++) {
        newLines.push(new Line3(changedVecs[i - 1], changedVecs[i]));
      }
      this.lines = this.lines.slice(0, Math.max(0, id - 1)).concat(newLines, this.lines.slice(id));
      this.vectors = this.vectors.slice(0, id).concat([vec], this.vectors.slice(id));
    }
  }
  /**
   * @param id
   * @returns nextHoverPointId 删除当前点后应该渲染的点ID
   */
  removeVector(id: number) {
    let nextHoverPointId = -1;
    if (id >= 0 && id < this.vectors.length) {
      let deleteLineIds: number[] = [];
      if (id === 0) {
        deleteLineIds = [id, id + 1];
        nextHoverPointId = 0;
      } else if (id === this.vectors.length - 1) {
        deleteLineIds = [id - 1, id];
        nextHoverPointId = id - 1;
      } else {
        deleteLineIds = [id - 1, id];
        // 更新线段的起点索引地址
        this.lines[id].start = this.vectors[id - 1];
      }

      this.lines = this.lines.slice(0, deleteLineIds[0]).concat(this.lines.slice(deleteLineIds[1]));
      this.vectors = this.vectors.slice(0, id).concat(this.vectors.slice(id + 1));
    }
    // 处理删除所有点的情况
    nextHoverPointId = this.vectors.length === 0 ? -1 : nextHoverPointId;
    return nextHoverPointId;
  }

  destroy() {
    if (this.mode === 'create' && this.editObject) {
      this.editObject.parent?.remove(this.editObject);
    }
    this.stage.currentSight?.scene.remove(this.indicatePoint, this.indicateLine, this.axisCtrlHelper);

    // TODO: add dispose
  }
}
