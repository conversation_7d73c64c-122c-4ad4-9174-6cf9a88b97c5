import { Object3D } from 'three';

import type { Stage } from '@/utils/render';

import type { EventHandlerType, HandlerOptions, IInteractEvent } from '../handlers/EventHandler';

export abstract class EventSelector implements IInteractEvent {
  public type: EventHandlerType;
  public stage: Stage;

  public editObject?: Object3D;

  /**
   * 不同的类型，分别代表标注物不同的编辑状态
   * create：完全新建，在数据层也没有该数据
   * wait：无标注物，等待中
   */
  public mode: 'create' | 'wait';
  /**
   * 开始创建标注物的回调
   * @returns 是否允许：false 阻止创建，true 允许创建
   */
  public onObjectBegin: Required<HandlerOptions>['onObjectBegin'];
  /**
   * 结束创建标注物的回调
   * @param obj 创建对象
   * @param fromSelect 是否来自 selectedAnnos：true 来自所选，false 直接创建（默认值为 false）
   * @returns 对象是否符合要求：true 符合要求，false 不符合要求
   */
  public onObjectMount: Required<HandlerOptions>['onObjectMount'];

  /**
   * 删除标注物的回调
   * @param obj 删除的标注物
   * @returns 是否成功删除标注物
   */
  public onObjectRemove: Required<HandlerOptions>['onObjectRemove'];

  /**
   * 文件坐标系的点变更
   */
  public onPointerChange: Required<HandlerOptions>['onPointerChange'];

  public onInfoChange: Required<HandlerOptions>['onInfoChange'];

  constructor(type: EventHandlerType, stage: Stage, options: HandlerOptions) {
    const { onPointerChange, onObjectBegin, onObjectMount, onObjectRemove, onInfoChange } = options;
    this.type = type;
    this.stage = stage;
    this.mode = 'wait';
    this.onObjectBegin = onObjectBegin ?? (() => true);
    this.onObjectMount = onObjectMount ?? (() => true);
    this.onObjectRemove = onObjectRemove ?? (() => true);

    this.onPointerChange = onPointerChange ?? (() => {});
    this.onInfoChange = onInfoChange ?? (() => {});
  }

  onClick(event: MouseEvent) {}
  onPointerDown(event: PointerEvent | MouseEvent) {}
  onPointerMove(event: PointerEvent | MouseEvent) {}
  onPointerUp(event: PointerEvent | MouseEvent) {}

  onMouseEnter(event: MouseEvent) {}
  onMouseLeave(event: MouseEvent) {}

  onKeyDown(event: KeyboardEvent) {}
  onKeyUp(event: KeyboardEvent) {}
  /**
   * 废弃当前 handler
   */
  destroy() {}
}
