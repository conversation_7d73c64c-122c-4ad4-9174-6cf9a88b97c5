import { Label, WidgetType } from '@/types';

import { getLotLabelTemplate } from '../api';

type OtherWidget = Exclude<WidgetType, 'cuboid'>;

type LabelWidgetTemplate = {
  [key in OtherWidget]?: string[];
} & { cuboid?: { [key: string]: number[] } };

export class LabelTemplate {
  private labels: Label[] = [];
  private _instanceLabels: Label[] = [];
  private _compoundLabels: Label[] = [];
  // 实例标签的 Widget 模版
  private labelWidgetTemplate: LabelWidgetTemplate = {};

  private labelCompoundTemplate: {
    [key: string]: Record<Label['name'], [number, number] | null>;
  } = {};

  // 从后端的数据中初始化模板
  initTemplateFromLot(uid: string) {
    const labelWidgetTemplate = this.labelWidgetTemplate;
    getLotLabelTemplate({ uid }).subscribe({
      next: (res) => {
        const cuboids = res.cuboids;
        if (!cuboids) return;
        const cuboidSizeMap = labelWidgetTemplate['cuboid'] || {};
        // 对返回值的顺序大小进行排序
        for (const labelName in cuboids) {
          cuboidSizeMap[labelName] = cuboids[labelName].scales.sort((a, b) => a - b);
        }
        labelWidgetTemplate['cuboid'] = cuboidSizeMap;
      },
      error: (err) => {
        console.error(err);
      },
    });
  }

  // 从标签配置中初始化模板
  initTemplateFromLabelSettings(labels: Label[]) {
    this.labels = labels;
    const compoundTemplate = this.labelCompoundTemplate;
    for (let i = 0; i < labels.length; i++) {
      const label = labels[i];
      if (label.compound) {
        this._compoundLabels.push(label);
        compoundTemplate[label.name] = {};
        label.compound.parts.forEach((part) => {
          if (part.occurs) {
            compoundTemplate[label.name][part.label] = [part.occurs.min[0], part.occurs.max[0]];
          } else {
            compoundTemplate[label.name][part.label] = null;
          }
        });
      } else {
        // 此处只是根据 widget 的名字来分类，不是根据尺寸来分类
        if (label.widgets_v2 && label.widgets_v2.length > 0) {
          const widgetTemplate = this.labelWidgetTemplate;
          label.widgets_v2.forEach((widget) => {
            const widgetType = widget.name as WidgetType;
            // cuboid 的模版此处先不处理
            if (widgetType === 'cuboid') return;
            const widgetList = widgetTemplate[widgetType];
            if (!widgetList) {
              widgetTemplate[widgetType] = [label.name];
              return;
            }
            widgetList.push(label.name);
          });
        }
        this._instanceLabels.push(label);
      }
    }

    // this.setLabelSettingSizeTemplate(labels);
  }
  // 使用设置的标签大小的模板
  // private labelSettingSizeTemplate: {
  //   [key in WidgetType]?: {
  //     [key: string]: [number[], number[]];
  //   };
  // } = {};
  // 根据标签上的 widget 大小来更新
  // setLabelSettingSizeTemplate(labels: Label[]) {
  //   if (labels.length === 0) return;
  //   if (labels[0].widgets_v2.length === 0 || !labels[0].widgets_v2[0].scale) return;
  //   this.isUsingHistoryTemplate = false;
  //   labels.forEach((label) => {
  //     const labelName = label.name;
  //     label.widgets_v2.forEach((widget) => {
  //       const widgetType = widget.name as WidgetType;
  //       const size = widget.scale!;
  //       const sizeTemplate = this.labelSettingSizeTemplate[widgetType];
  //       if (!sizeTemplate) {
  //         this.labelSettingSizeTemplate[widgetType] = {
  //           [labelName]: [size.min, size.max],
  //         };
  //         return;
  //       }
  //       sizeTemplate[labelName] = [size.min, size.max];
  //     });
  //   });
  // }

  matchLabelByWidget(type: WidgetType, size?: number[]) {
    const templates = this.labelWidgetTemplate[type];
    // 没有历史就默认返回第一个
    if (!templates) return this._instanceLabels[0];
    //  没有尺寸就根据 type 来返回
    if (!size) {
      if (Array.isArray(templates)) return this.getLabelByName(templates[0]);
      return this._instanceLabels[0];
    }
    if (type === 'cuboid') {
      const sizeList = Object.entries(templates);
      let temp = size!.slice().sort((a, b) => a - b);
      let minDiff = Number.MAX_SAFE_INTEGER,
        labelName = undefined;
      for (let i = 0; i < sizeList.length; i++) {
        const [label, sizeTemplate] = sizeList[i] as [string, number[]];
        const diff = sizeTemplate.reduce((prev, cur, index) => {
          return prev + Math.abs(cur - temp[index]);
        }, 0);
        if (diff < minDiff) {
          minDiff = diff;
          labelName = label;
        }
      }
      return labelName ? this.getLabelByName(labelName) : this._instanceLabels[0];
    }
    return this._instanceLabels[0];
  }

  private getLabelByName = (name: Label['name']) => {
    return this.labels.find((label) => label.name === name);
  };

  matchLabelByCompound(info: Record<Label['name'], number>) {
    const templates = this.labelCompoundTemplate;
    const labelList = Object.keys(templates);
    for (let i = 0; i < labelList.length; i++) {
      const labelName = labelList[i];
      const template = templates[labelName];
      const match = Object.keys(info).every((key) => {
        const occurs = template[key];
        // 说明没有这个子标签
        if (occurs === undefined) return false;
        // 说明没有子标签的数量限制
        if (occurs === null) return true;
        const [min, max] = occurs,
          count = info[key];
        if (count <= max && count >= min) return true;
        return false;
      });
      if (match) {
        return this.getLabelByName(labelName);
      }
    }
    return this._compoundLabels[0];
  }

  get instanceLabels() {
    return this._instanceLabels;
  }

  get compoundLabels() {
    return this._compoundLabels;
  }
}

export const labelTemplate = new LabelTemplate();
