import { anno_v1_AttrValue } from '@/service/anno/v1';
import type { Comment, Label } from '@/types';
import { AnnoFactory, IAnnoItem } from '@/utils/annos';

import { WIDGET_INFO_MAP } from '../../config';
import { AnnoItemWithAttrsStr } from '../../types';

/**
 * 是否有待解决的批注，返回结果不考虑漏标
 * @param anno
 * @param commentMap
 * @returns
 */
export const hasComment = (anno: IAnnoItem, commentMap: Record<Comment['uuid'], Comment | null>) => {
  const comment = Object.values(commentMap).find((comment) => comment?.obj_uuids.includes(anno.name));

  if (comment?.status === 'unresolved') {
    return true;
  }

  return false;
};

export const checkDeleteAnnos = (annos: IAnnoItem[], commentMap: Record<string, Comment | null>) => {
  if (annos.some((anno) => hasComment(anno, commentMap))) {
    return '删除失败，当前标注物上有未解决的批注，请解决批注后再删除';
  }
};

export const formatTrackIdDisplayName = (trackId: string = '') => trackId.padStart(3, '0');

export const getAnnoWidgetIcon = (anno: IAnnoItem) => {
  const widget = 'compound' in anno ? 'compound' : AnnoFactory.getWidgetType(anno.widget);
  return WIDGET_INFO_MAP[widget];
};

export const addAttrsStrToAnno = (anno: IAnnoItem, labelMap: Map<Label['name'], Label>, separator: string = '/') => {
  const { label: labelName, attrs } = anno;
  const label = labelMap.get(labelName);
  if (!label) return anno;
  const needAttr = label.attrs ? label.attrs.required_v2.length + label.attrs.optional_v2.length > 0 : false;

  const getDisplayName = (attrValues: string[], choices: anno_v1_AttrValue[]) =>
    attrValues.map((attrValue) => choices.find((choice) => choice.name === attrValue)?.display_name || attrValue);

  const attrsStr = needAttr
    ? Object.keys(attrs)
        .filter((attrName) => !!attrs[attrName])
        .map((attrName) => {
          const attr = attrs[attrName];
          const attrsList = label.fullAttrs;
          if (!attrsList || attrsList.length === 0) return attr;
          // 先找到属性值，再从属性值中找到匹配的选项
          const choices = attrsList.find((attr) => attr.name === attrName)?.choices;
          const attrValues = Array.isArray(attr) ? attr : [attr];
          const displayNames = choices ? getDisplayName(attrValues, choices) : attrValues;
          return displayNames.join(',');
        })
        .join(separator)
    : undefined;

  (anno as AnnoItemWithAttrsStr).attrsStr = attrsStr;

  return anno;
};
