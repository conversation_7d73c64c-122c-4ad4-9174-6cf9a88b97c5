import { ISchema } from '@formily/react';

import type { Attr } from '@/types';

export const formTpls = (type: string, choices: Attr['choices'], size: string = 'small') => {
  switch (type) {
    case 'input':
      return {
        type: 'string',
        'x-component': 'Input',
        'x-component-props': {
          size,
        },
      };
    case 'bool':
      return {
        type: 'boolean',
        'x-component': 'Switch',
        'x-component-props': {
          size,
        },
      };
    case 'radiobox':
      return {
        type: 'string',
        'x-component': 'Select',
        'x-component-props': {
          size,
          options: choicesTransEnum(choices),
          // style: {
          //   color: 'white',
          // },
        },
      };
    case 'checkbox':
      return {
        type: 'array',
        'x-component': 'Select',
        'x-component-props': {
          mode: 'multiple',
          size,
          options: choicesTransEnum(choices),
          // style: {
          //   color: 'white',
          // },
        },
      };
    default:
      throw new Error(`Unsupported attr-type: ${type}`);
  }
};

export const attrsTransProperties = (attrs: Array<Attr>) => {
  const properties: ISchema['properties'] = {};
  attrs.forEach((attr) => {
    const { name, display_name, type, required = true, choices } = attr;

    properties.colName = {
      type: 'void',
      'x-component': 'ArrayTable.Column',
      'x-component-props': { title: '属性' },
      properties: {
        colName: {
          type: 'string',
          default: ' ',
          'x-read-pretty': true,
          'x-decorator': 'FormItem',
          'x-component': 'Input',
        },
      },
    };

    properties[name] = {
      type: 'void',
      'x-component': 'ArrayTable.Column',
      'x-component-props': {
        title: display_name,
      },
      properties: {
        [name]: {
          'x-decorator': 'FormItem',
          required,
          ...formTpls(type, choices),
        },
      },
    };
  });
  return properties;
};

export const choicesTransEnum = (choices: Attr['choices']) => {
  return choices.map((chi) => ({
    label: chi.display_name,
    value: chi.name,
  }));
};
