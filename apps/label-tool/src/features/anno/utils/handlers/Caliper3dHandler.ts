import { Vector3 } from 'three';

import { CSS2DObject } from '@/lib/three';
import { Caliper3d, Line3d } from '@/utils/annos';
import { Sight, Stage } from '@/utils/render';

import { EventHandlerType, HandlerOptions } from './EventHandler';
import { Line3dHandler } from './Line3dHandler';

const INDICATE_WHITE = '#FFFFFF';
const COMPLETE_COLOR = '#6fc6f9';

export class Caliper3dHandler extends Line3dHandler {
  private label;

  /**
   * 量尺
   * @param type
   * @param stage 需确保当前 currentSight 不为空
   * @param options
   */
  constructor(type: EventHandlerType, stage: Stage, options: HandlerOptions) {
    super(type, stage, options, {
      canDelete: false,
      canAdjust: false,
      canAdd: false,
    });

    const text = document.createElement('div');
    text.className = 'tool-caliper-label-cursor-div';
    this.label = new CSS2DObject(text);
    text.id = Caliper3d.getScopedId(this.label);

    this.stage.currentSight?.scene.remove(this.indicateLine);
    this.indicateLine = Caliper3d.create([0, 0, 0, 0, 0, 0], { color: INDICATE_WHITE });
    this.indicateLine.add(this.label);
    this.indicateLine.visible = false;

    // 设置引导元素的图层
    const layer = Sight.getLayerFromTool(type);
    this.indicatePoint.layers.set(layer);
    this.indicateLine.layers.set(layer);

    this.stage.currentSight?.scene.add(this.indicatePoint, this.indicateLine);
    this.Line = Caliper3d;
    this.stage.setCursor('ruler');
  }

  updateIndicateLine(start: Vector3, end: Vector3) {
    if (!this.indicateLine.visible) {
      this.indicateLine.visible = true;
    }
    Line3d.update(this.indicateLine, [start.x, start.y, start.z, end.x, end.y, end.z]);
    this.label.position.set(end.x, end.y, end.z);
    const distance = start.distanceTo(end);
    const totalDistance = (this.editObject?.userData.totalDistance + distance).toFixed(1);
    this.label.element.innerText = `总距离：${totalDistance}m`;
  }

  /**
   * 取消引导线
   */
  disableIndicateLine() {
    if (this.editObject) {
      Line3d.changeColor(this.editObject, COMPLETE_COLOR);
    }
    super.disableIndicateLine();
  }

  destroy() {
    if (this.editObject) {
      Caliper3d.clear2DDom(this.editObject);
      this.editObject.parent?.remove(this.editObject);
    }
    Caliper3d.clear2DDom(this.indicateLine);
    this.stage.currentSight?.scene.remove(this.indicatePoint, this.indicateLine);
    this.stage.setCursor('default');
  }
}
