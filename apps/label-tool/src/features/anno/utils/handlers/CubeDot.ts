import { BoxGeometry, Color, Mesh, MeshBasicMaterial, SphereGeometry } from 'three';

export class CubeDot {
  static POINT_SIZE = 0.1;

  /**
   * 创建立方体点，含有中心
   * @param x x-coordinate
   * @param y y-coordinate
   * @param z z-coordinate
   * @param color 点颜色
   * @returns
   */
  static create(x: number, y: number, z: number, color: string | Color) {
    const geometry = new BoxGeometry(this.POINT_SIZE, this.POINT_SIZE, this.POINT_SIZE);
    const material = new MeshBasicMaterial({ color, opacity: 0.6, transparent: true, depthTest: false });
    const dot = new Mesh(geometry, material);

    dot.position.set(x, y, z);

    const center = new Mesh(new SphereGeometry(this.POINT_SIZE / 16, 4, 4), material);
    dot.add(center);

    return dot;
  }
}
