import { DebouncedFunc, throttle } from 'lodash';
import {
  BoxGeometry,
  Line3,
  Mesh,
  MeshBasicMaterial,
  Object3D,
  OrthographicCamera,
  PerspectiveCamera,
  Ray,
  Vector3,
} from 'three';

import { OBJECT_EDIT_YELLOW } from '@/config/color';
import { FatLine, LineMaterial } from '@/lib/three';
import {
  AnnoFactory,
  Caliper3d,
  ClosestElement,
  HorizontalCaliper3d,
  Line3d,
  Point3d,
  Poly3d,
  Spline3d,
  VerticalCaliper3d,
} from '@/utils/annos';
import { roundDecimals } from '@/utils/math';
import { PointPicker, Sight, Stage } from '@/utils/render';

import { CubeDot } from './CubeDot';
import { EventHandler, EventHandlerType, HandlerOptions } from './EventHandler';

const WAIT_TIME = 50;
const INDICATE_WHITE = '#FFFFFF';
const ACTIVE_WHITE = '#FFFFFF';
const MOVE_DETECTION = 0.06;
// 新点落在已有点的这个距离范围内会被判定为重合
const MIN_POINT_RANGE = 0.001;

const _point = new Vector3();
const _ray = new Ray();

interface LineEditOptions {
  // 是否允许编辑的过程中删除
  canDelete: boolean;
  // 是否允许调整点位置
  canAdjust: boolean;
  // 是否允许进入端点延长编辑模式
  canAdd: boolean;
}

type AnnoOpt = 'update' | 'remove';

export class Line3dHandler extends EventHandler {
  protected Line:
    | typeof Line3d
    | typeof Spline3d
    | typeof Caliper3d
    | typeof Poly3d
    | typeof HorizontalCaliper3d
    | typeof VerticalCaliper3d;
  protected oldEditObject?: FatLine | null;
  public declare editObject?: FatLine;

  protected pointer3: Vector3;
  protected vectors: Vector3[];
  protected lines: Line3[];
  protected pointsPosition: Record<string, ClosestElement>;
  protected pointPicker: PointPicker;

  protected indicatePoint: Mesh;
  protected indicateLine: FatLine;
  protected axisCtrlHelper: Object3D;

  // 响应鼠标的移动和鼠标移动前的按下操作
  protected hoverPointId: number;
  // 响应鼠标从按下、移动到抬起的过程，结束后会清空
  protected dragPointId: number;
  // 响应鼠标按下后的抬起操作，表示选中
  protected activePointId: number;

  protected hoverLineId: number;
  protected hoverLineClosest: Vector3;

  protected debounceSetIndicate: DebouncedFunc<(start: Vector3 | undefined, end: Vector3) => void>;
  protected debounceDragPoint: DebouncedFunc<(point: Vector3) => void>;

  // 编辑限制
  protected canDelete: boolean;
  protected canAdjust: boolean;
  protected canAdd: boolean;

  /**
   * 初始化折线工具
   * @param type 类型
   * @param stage 需确保当前 currentSight 不为空
   * @param handlerOptions
   * @param lineEditOptions
   */
  constructor(
    type: EventHandlerType,
    stage: Stage,
    handlerOptions: HandlerOptions,
    lineEditOptions: LineEditOptions = { canDelete: true, canAdjust: true, canAdd: true }
  ) {
    super(type, stage, handlerOptions);

    this.pointer3 = new Vector3();
    this.vectors = [];
    this.lines = [];
    this.pointsPosition = {};
    this.pointPicker = new PointPicker(
      stage.currentSight!.rawObject,
      stage.currentSight!.camera,
      CubeDot.POINT_SIZE * 1.1
    );

    this.hoverPointId = -1;
    this.dragPointId = -1;
    this.activePointId = -1;

    this.hoverLineId = -1;
    this.hoverLineClosest = new Vector3();

    // 创建引导点与引导线
    this.indicatePoint = CubeDot.create(0, 0, 0, INDICATE_WHITE);
    this.indicatePoint.scale.set(1.8, 1.8, 1.8);

    this.indicateLine = Line3d.create([0, 0, 0, 0, 0, 0], {
      color: INDICATE_WHITE,
      canvasSize: [this.stage.width, this.stage.height],
    });

    this.indicatePoint.visible = false;
    this.indicateLine.visible = false;

    // 设置引导元素的图层
    const layer = Sight.getLayerFromTool(type);
    this.indicatePoint.layers.set(layer);
    this.indicateLine.layers.set(layer);

    this.axisCtrlHelper = new Object3D();

    this.stage.currentSight?.scene.add(this.indicatePoint, this.indicateLine, this.axisCtrlHelper);

    this.debounceSetIndicate = throttle(this.setIndicate, WAIT_TIME);
    this.debounceDragPoint = throttle(this.dragPoint, WAIT_TIME);

    this.canDelete = lineEditOptions.canDelete;
    this.canAdjust = lineEditOptions.canAdjust;
    this.canAdd = lineEditOptions.canAdd;

    this.Line = Line3d;
  }

  onPointerDown(event: PointerEvent) {
    const { button } = event;

    let needsUpdate = false;

    //  左键用来新增点，中键用来退出，右键用来拖拽画布
    switch (button) {
      case 0:
        needsUpdate = this.handlePointerDownLeft(event);
        if (needsUpdate && !this.transformControl.isActive && this.indicatePoint.visible) {
          this.onPointerChange(this.indicatePoint.position, 'click');
        }
        break;
      case 1:
        // 处理中键按下：隐藏端点引导线
        this.disableIndicateLine();
        needsUpdate = true;
        break;
    }

    if (this.editObject) {
      this.editObject.userData.needsUpdate = needsUpdate;
    }

    return needsUpdate;
  }

  onPointerMove(event: PointerEvent) {
    this.updatePointer(event);

    // 鼠标处于按下状态
    if (event.buttons > 0) {
      if (this.canAdjust && this.editObject) {
        // 判断是否开始拖动轴控制器
        if (this.transformControl?.dragging) {
          this.movePoint(this.activePointId, this.axisCtrlHelper.position);
          if (this.indicatePoint.visible) {
            this.indicatePoint.visible = false;
          }
        } else if (this.dragPointId >= 0 && this.dragPointId < this.vectors.length) {
          // 判断是否开始拖动点
          this.debounceDragPoint(this.pointer3);
        }
      }
      return true;
    } else {
      // 鼠标处于松开状态

      // 尚未创建线条
      if (!this.editObject || this.vectors.length === 0) {
        this.debounceSetIndicate(undefined, this.pointer3);
        return false;
      }

      // 如果当前显示轴控制器且有响应
      if (this.transformControl.isActive) {
        return true;
      }

      if (
        this.isEndPoint(this.activePointId) &&
        (this.mode === 'create' || this.mode === 'add' || (this.mode === 'adjust' && this.canAdd))
      ) {
        if (this.mode === 'adjust') {
          this.mode = 'add';
          // 如果在 'add' 状态下，切换编辑工具，需要退回到原来的状态
          this.oldEditObject = this.editObject.clone();
          this.oldEditObject.name = this.editObject.name;
          this.oldEditObject.geometry = this.editObject.geometry.clone();
          this.oldEditObject.material = (this.editObject.material as LineMaterial).clone();

          const oldPoints = Line3d.getPoints(this.oldEditObject);
          const points = Line3d.getPoints(this.editObject);
          if (oldPoints && points) {
            oldPoints.geometry = points.geometry.clone();
          }
        }

        this.debounceSetIndicate(this.vectors[this.activePointId], this.pointer3);
        return true;
      }

      // 查找该折线上距离光标最近的可响应元素
      return this.findClosestElement(this.pointer3);
    }
  }

  updatePointVector(pointId: number) {
    const positions = Line3d.getVertexFromObject3D(this.editObject!);
    this.vectors[pointId].fromArray(positions, pointId * 3);
  }

  onPointerUp(event: PointerEvent) {
    // 非左键抬起事件，不处理
    if (event.button !== 0) return false;

    // 判断是否为轴移动响应
    if (this.transformControl?.dragging) {
      this.updatePointVector(this.activePointId);
      this.onObjectUpdate(this.editObject);
      return true;
    }

    if (this.editObject) {
      if (this.canAdjust) {
        // 选中拖拽
        if (this.dragPointId >= 0 && this.dragPointId < this.vectors.length) {
          this.updatePointVector(this.dragPointId);
          // 显示轴移动
          const pos = this.vectors[this.dragPointId];
          this.axisCtrlHelper.position.copy(pos);
          this.updateTransformControls(this.axisCtrlHelper);
          this.updateInfo(this.dragPointId, pos);

          this.dragPointId = -1;
        }
        // 处理点拖动后、线上添加点数据层
        if (this.mode === 'adjust' && this.editObject.userData.needsUpdate) {
          this.onObjectUpdate(this.editObject);
          this.editObject.userData.needsUpdate = false;
        }
      }
      this.activePointId = this.hoverPointId;
      Line3d.changePointColor(this.editObject, this.activePointId, ACTIVE_WHITE, OBJECT_EDIT_YELLOW);
    }

    // 防止 activePointId 被意外覆盖
    if (this.activePointId < 0 && this.transformControl?.visible) {
      this.updateTransformControls();
    }

    return true;
  }

  onMouseLeave() {
    this.onPointerChange(null);
  }

  onKeyDown(event: KeyboardEvent) {
    switch (event.code) {
      case 'Escape':
        if (this.indicateLine.visible) {
          this.disableIndicateLine();
          return true;
        }
        break;
      case 'Delete':
      case 'Backspace':
        // 无法删除或者没有选中的点
        if (!this.canDelete || this.activePointId === -1) return false;
        const hasDeleted = this.deletePoint(this.activePointId);
        // 已经删除单个点
        if (hasDeleted) {
          this.mode === 'adjust' && this.onObjectUpdate(this.editObject);
          return true;
        }
        // 需要删除整个标注物
        if (this.onObjectRemove(this.editObject)) {
          this.updateTransformControls();
          this.editObject?.parent?.remove(this.editObject);
          this.editObject = undefined;
          this.indicateLine.visible = false;
          this.mode = 'wait';
          this.activePointId = -1;
          this.hoverPointId = -1;
          // 避免重复删除
          return true;
        }
        break;
      default:
        break;
    }
  }

  updateEditVectors(object?: Object3D) {
    if (object && object instanceof FatLine && object.userData.type === this.type) {
      const positions = AnnoFactory.getVertexFromObject3D(this.type, object);
      const len = Math.floor(positions.length / 3);
      this.vectors = new Array(len);
      this.lines = [];

      // 创建顶点集合
      for (let i = 0; i < len; i++) {
        this.vectors[i] = new Vector3().fromArray(positions, i * 3);
      }

      // 创建线段集合
      for (let i = 1; i < this.vectors.length; i++) {
        this.lines.push(new Line3(this.vectors[i - 1], this.vectors[i]));
      }
    } else {
      this.lines = [];
      this.vectors = [];
    }
    this.updatePointsPosition();
    this.activePointId = -1;
    this.updateTransformControls(undefined);
  }

  updateInfo(index: number, pos: Vector3) {
    if (!this.editObject) return;
    const total = this.Line.getVertexCount(this.editObject);
    const z = roundDecimals(pos.z, 3);
    const g = this.pointPicker.updateGroundByPoint(pos);
    const ground = roundDecimals(g, 3);
    this.onInfoChange('desc', {
      type: 'line',
      items: [
        {
          label: 'X',
          value: roundDecimals(pos.x, 3),
        },
        {
          label: 'Y',
          value: roundDecimals(pos.y, 3),
        },
        {
          label: 'Z',
          value: z,
          color: Math.abs(z - ground) < 0.1 ? '#28C89B' : '#E22525',
        },
        {
          label: '总点数',
          value: total,
        },
        {
          label: '点序号',
          value: index + 1,
        },
        {
          label: '拟合高度',
          value: ground,
        },
      ],
    });
  }

  /**
   * 更新添加点坐标
   * @param startIndex
   * @param mode
   * @returns
   */
  updatePointsWidget(startIndex: number, mode: AnnoOpt) {
    if (!this.editObject) return;
    const positions = Line3d.getVertexFromObject3D(this.editObject);
    this.editObject?.children?.forEach((obj) => {
      if (!obj.userData?.seriesPoint?.isAdsorb) return;
      const element = this.pointsPosition[obj.name];
      if (typeof element !== 'object') return;
      if (element.type === 'line') {
        const { inx: lineInx, at: distanceAt } = element;
        if (startIndex !== lineInx && startIndex !== lineInx + 1) return;
        let inx = lineInx;
        if (mode === 'remove') {
          if (startIndex === 0 || startIndex === positions.length / 3) {
            this.onObjectRemove(obj);
            this.editObject?.remove(obj);
            return;
          } else if (startIndex === lineInx && startIndex !== 0) {
            inx = lineInx - 1;
          }
        }
        const lineStart = new Vector3().fromArray(positions, inx * 3);
        const lineEnd = new Vector3().fromArray(positions, (inx + 1) * 3);
        const line = new Line3(lineStart, lineEnd);
        const widget = new Vector3();
        line.at(distanceAt, widget);
        Point3d.update(obj as Mesh<BoxGeometry, MeshBasicMaterial>, widget.toArray());
      } else if (element.type === 'vector') {
        // 绘制点和添加点重合
        const { inx } = element;
        if (startIndex !== inx) return;
        if (mode === 'remove') {
          this.onObjectRemove(obj);
          this.editObject?.remove(obj);
          return;
        }
        const widget = new Vector3().fromArray(positions, inx * 3).toArray();
        Point3d.update(obj as Mesh<BoxGeometry, MeshBasicMaterial>, widget);
      }
    });
  }

  /**
   * 记录添加点位置
   */
  updatePointsPosition() {
    this.editObject?.children?.forEach((obj) => {
      if (obj.userData?.seriesPoint?.isAdsorb) {
        this.pointsPosition[obj.name] = Point3d.findClosestElement(
          this.stage.currentSight!.camera,
          obj.position,
          this.vectors,
          this.lines,
          MOVE_DETECTION
        );
      }
    });
  }

  setEditObjectEffects(object?: Object3D) {
    this.updateEditVectors(object);
  }

  updatePointer(event: PointerEvent | MouseEvent) {
    this.pointer3.set((event.offsetX / this.stage.width) * 2 - 1, -(event.offsetY / this.stage.height) * 2 + 1, 0);
  }

  // 开始创建线
  beginCreateLine() {
    this.mode = 'create';

    this.editObject = this.Line.create(this.indicatePoint.position.toArray(), {
      color: OBJECT_EDIT_YELLOW,
      maxPoints: 32,
      growable: true,
      withDot: true,
      canvasSize: [this.stage.width, this.stage.height],
    });
    this.vectors = [new Vector3().copy(this.indicatePoint.position)];
    this.hoverPointId = 0;
    this.activePointId = this.hoverPointId;
    this.stage.currentSight?.addObjectFromTool(this.type, this.editObject);
  }

  // 处理左键按下事件：添加点，移动点
  handlePointerDownLeft(event: PointerEvent) {
    this.debounceSetIndicate.cancel();
    this.updatePointer(event);

    // 当前显示轴移动控制器（优先于新建判断）
    if (this.transformControl?.visible) {
      if (this.transformControl.hasAxis) {
        // 若轴移动有响应，则优先响应
        return true;
      } else {
        // 隐藏轴移动
        this.updateTransformControls();
      }
    }

    // 可以新建线的判断条件：当前没有编辑物 或者 当前点击的范围没有在当前的编辑物上形成引导线或点
    if (!this.editObject || (this.hoverPointId === -1 && !this.indicateLine.visible && !this.indicatePoint.visible)) {
      // 创建折线
      this.setIndicate(undefined, this.pointer3);

      // 判断第一个点是否吸附到点云中
      if (this.indicatePoint.visible && this.onObjectBegin()) {
        this.beginCreateLine();
        return true;
      } else {
        return false;
      }
    }

    if (this.indicateLine.visible || this.vectors.length === 0) {
      // 引导线出现 ，在线条首尾添加点 / 所有点均被删除，重新绘制
      this.setIndicate(this.vectors[this.hoverPointId], this.pointer3);

      if (this.indicatePoint.visible) {
        this.addEndPoint(this.indicatePoint.position);
      }
      return true;
    }

    if (!this.canAdjust || !this.indicatePoint.visible) return false;

    // 准备 hover 的地方创建新的点
    if (event.shiftKey && this.onObjectBegin()) {
      this.beginCreateLine();
      return true;
    }

    // 移动已有的点
    if (this.hoverPointId >= 0 && this.hoverPointId < this.vectors.length) {
      this.dragPointId = this.hoverPointId;
      Line3d.changePointColor(this.editObject, this.dragPointId, ACTIVE_WHITE, OBJECT_EDIT_YELLOW);
      this.pointPicker.updateGroundByPoint(this.vectors[this.dragPointId]);

      return true;
    }

    // 处理在线段上添加点事件
    if (this.hoverLineId >= 0) {
      const id = this.hoverLineId + 1;

      this.Line.partialUpdate(this.editObject!, this.indicatePoint.position.toArray(), {
        startIndex: id,
        mode: 'add',
      });

      this.addVector(new Vector3().copy(this.indicatePoint.position), id);

      this.hoverPointId = id;
      this.dragPointId = id;
      this.hoverLineId = -1;

      Line3d.changePointColor(this.editObject, this.dragPointId, ACTIVE_WHITE, OBJECT_EDIT_YELLOW);

      return true;
    }
    return false;
  }

  /**
   * 处理删除点
   * @returns
   */
  deletePoint(pointId: number) {
    // 无编辑对象时，不支持删除
    if (!this.editObject) return false;

    // 此处需要删除整个标注物
    if (this.mode === 'adjust' && this.vectors.length === Line3d.MIN_POINTS_COUNT) return false;

    if (pointId >= 0 && pointId < this.vectors.length && this.vectors.length > 1) {
      // 隐藏轴控制器
      this.updateTransformControls();
      // 删除当前选中点
      this.Line.partialUpdate(this.editObject, [], { startIndex: pointId, mode: 'remove' });
      // 删除对应添加点
      this.updatePointsWidget(pointId, 'remove');

      // 更新点集、边集和当前预选中点
      this.activePointId = this.removeVector(pointId);

      // 删除点前，并没有出现引导线，说明点击之后没有移动，则只是单纯的选中点
      if ((this.activePointId === 0 || this.activePointId === this.vectors.length - 1) && !this.indicateLine.visible) {
        this.activePointId = -1;
        this.hoverPointId = -1;
        Line3d.changePointColor(this.editObject, this.activePointId, ACTIVE_WHITE, OBJECT_EDIT_YELLOW);
        return true;
      }

      Line3d.changePointColor(this.editObject, this.activePointId, ACTIVE_WHITE, OBJECT_EDIT_YELLOW);
      this.hoverPointId = this.activePointId;
      // 删除的是中间的点或者没有点了
      if (this.activePointId === -1) {
        this.indicateLine.visible = false;
        this.indicatePoint.visible = false;
      } else {
        // 当前预选中点为端点，重置引导线起点
        const v = this.vectors[this.activePointId];
        this.updateIndicateLine(v, this.indicatePoint.position);
      }

      return true;
    }
    return false;
  }

  /**
   * 设置引导线点坐标
   * @param start
   * @param end
   */
  setIndicate(start: Vector3 | undefined, end: Vector3) {
    let realEnd = this.pointPicker.findNearestPoint(end);

    if (!realEnd && start) {
      // 非第一个点时可悬空
      realEnd = this.pointPicker.getPointInPlane(end, _point);
    }

    this.onPointerChange(realEnd);

    // 更新引导点
    if (realEnd) {
      this.indicatePoint.position.copy(realEnd);
      if (!this.indicatePoint.visible) {
        this.indicatePoint.visible = true;
      }
    } else if (this.indicatePoint.visible || this.indicateLine.visible) {
      this.indicatePoint.visible = false;
      this.indicateLine.visible = false;
    }

    if (start && realEnd) {
      this.updateIndicateLine(start, realEnd);
    }
  }

  updateIndicateLine(start: Vector3, end: Vector3) {
    if (!this.indicateLine.visible) {
      this.indicateLine.visible = true;
    }
    Line3d.update(this.indicateLine, [start.x, start.y, start.z, end.x, end.y, end.z]);
  }

  /**
   * 查找折线上可响应的点或线
   * @param ndcPoint
   * @returns
   */
  findClosestElement(ndcPoint: Vector3) {
    if (!this.editObject) return false;

    const camera = this.stage.currentSight!.camera;

    if (camera instanceof PerspectiveCamera) {
      _ray.origin.setFromMatrixPosition(camera.matrixWorld);
      _ray.direction.set(ndcPoint.x, ndcPoint.y, 0.5).unproject(camera).sub(_ray.origin).normalize();
    } else if (camera instanceof OrthographicCamera) {
      _ray.origin
        .set(ndcPoint.x, ndcPoint.y, (camera.near + camera.far) / (camera.near - camera.far))
        .unproject(camera);
      _ray.direction.set(0, 0, -1).transformDirection(camera.matrixWorld);
    }

    // 遍历折线所有端点，查找阈值内端点
    const vid = this.vectors.findIndex((v, i) => _ray.distanceSqToPoint(v) < MOVE_DETECTION);
    if (vid >= 0) {
      this.indicatePoint.position.copy(this.vectors[vid]);
      this.indicatePoint.visible = true;

      this.hoverLineId = -1;
      this.hoverPointId = vid;

      this.onPointerChange(this.vectors[vid]);
      return true;
    }

    // 遍历折线所有线段
    const lid = this.lines.findIndex(
      (line) => _ray.distanceSqToSegment(line.start, line.end, undefined, this.hoverLineClosest) < MOVE_DETECTION
    );
    if (lid >= 0) {
      this.indicatePoint.position.copy(this.hoverLineClosest);
      this.indicatePoint.visible = true;

      this.hoverLineId = lid;
      this.hoverPointId = -1;

      this.onPointerChange(this.hoverLineClosest);
      return true;
    }

    if (this.indicatePoint.visible) {
      this.hoverPointId = -1;
      this.hoverLineId = -1;
      this.indicatePoint.visible = false;

      this.onPointerChange(null);
    }
    return false;
  }

  isEndPoint(pointId: number) {
    return this.vectors.length > 0 && (pointId === 0 || pointId === this.vectors.length - 1);
  }

  /**
   * 取消引导线
   */
  disableIndicateLine() {
    if (this.editObject && this.indicateLine.visible) {
      // 取消 debounce 调用，避免在隐藏引导线后再次显示引导线
      this.debounceSetIndicate.cancel();
      this.indicateLine.visible = false;
      this.indicatePoint.visible = false;
      this.hoverPointId = -1;
      this.activePointId = -1;

      // 为了避免直接把类型为 Mesh<BoxGeometry, MeshBasicMaterial> 的点赋值给 this.editObject，
      // 引入的新类型 Mesh<BoxGeometry, MeshBasicMaterial> 需要在所有使用 Line3dHandler 的地方过滤，
      // 如 Caliper3dHandler,Spline3dHandler
      let transEditObject = undefined;

      // 线只剩一个点时，存为 point3d 标注物
      if (this.vectors.length === 1) {
        if (this.editObject.userData.type === 'line3d' || this.editObject.userData.type === 'spline3d') {
          this.editObject.parent?.remove(this.editObject);
          transEditObject = Point3d.create(this.vectors[0].toArray(), {
            color: this.editObject.userData.color,
            canvasSize: [this.stage.width, this.stage.height],
          });
          transEditObject.name = this.editObject.name;
          this.stage.currentSight?.addObjectFromTool('point3d', transEditObject);
        }
      }

      if (this.mode === 'create') {
        // 结束创建阶段
        this.mode = 'wait';

        if (!this.onObjectMount(transEditObject ?? this.editObject)) {
          this.editObject.parent?.remove(transEditObject ?? this.editObject);
        }
        // 清空当前编辑对象，便于重设标注物的编辑状态样式
        this.editObject = undefined;
        this.updateEditVectors();
      } else if (this.mode === 'add') {
        this.mode = 'wait';
        const obj = transEditObject ?? this.editObject;
        this.onObjectUpdate(obj);
        this.setEditObject(undefined);
        this.updateEditVectors();
        this.setEditObject(obj);
      }
    }
  }

  addEndPoint(pos: Vector3) {
    if (!this.editObject) return;

    const vid = this.vectors.findIndex((v) => pos.distanceToSquared(v) < MIN_POINT_RANGE);
    if (vid >= 0) {
      console.log('New point is too close to the existing point!');
      return;
    }

    const id = this.hoverPointId === this.vectors.length - 1 ? this.vectors.length : 0;
    // 处理引导线出现，点击在头部或尾部添加点事件
    this.Line.partialUpdate(this.editObject, pos.toArray(), {
      startIndex: id,
      mode: 'add',
    });

    this.addVector(new Vector3().copy(pos), id);

    // 更新下一个添加的点位置
    this.hoverPointId = id;
    this.activePointId = id;
  }

  dragPoint(ndcPoint: Vector3) {
    if (!this.editObject) return true;

    if (this.indicatePoint.visible) {
      this.indicatePoint.visible = false;
    }

    let target = this.pointPicker.findNearestPoint(ndcPoint);

    if (!target) {
      // 未找到可响应的点，根据当前平面计算出虚拟点
      target = this.pointPicker.getPointInPlane(ndcPoint, _point);
    }

    this.movePoint(this.dragPointId, target);
  }

  movePoint(index: number, target: Vector3 | null) {
    if (!this.editObject) return true;

    this.onPointerChange(target);
    if (target) {
      this.updateInfo(index, target);
      this.Line.partialUpdate(this.editObject, target.toArray(), { startIndex: index });
      this.updatePointsWidget(index, 'update');
    }
  }

  addVector(vec: Vector3, id: number) {
    if (id >= 0 && id <= this.vectors.length) {
      const changedVecs =
        id === 0
          ? [vec, this.vectors[0]]
          : id === this.vectors.length
          ? [this.vectors[this.vectors.length - 1], vec]
          : [this.vectors[id - 1], vec, this.vectors[id]];

      const newLines: Line3[] = [];
      for (let i = 1; i < changedVecs.length; i++) {
        newLines.push(new Line3(changedVecs[i - 1], changedVecs[i]));
      }
      this.lines = this.lines.slice(0, Math.max(0, id - 1)).concat(newLines, this.lines.slice(id));
      this.vectors = this.vectors.slice(0, id).concat([vec], this.vectors.slice(id));
      this.updatePointsPosition();
    }
  }
  /**
   * @param id
   * @returns nextHoverPointId 删除当前点后应该渲染的点ID
   */
  removeVector(id: number) {
    let nextHoverPointId = -1;
    if (id >= 0 && id < this.vectors.length) {
      let deleteLineIds: number[] = [];
      if (id === 0) {
        deleteLineIds = [id, id + 1];
        nextHoverPointId = 0;
      } else if (id === this.vectors.length - 1) {
        deleteLineIds = [id - 1, id];
        nextHoverPointId = id - 1;
      } else {
        deleteLineIds = [id - 1, id];
        // 更新线段的起点索引地址
        this.lines[id].start = this.vectors[id - 1];
      }

      this.lines = this.lines.slice(0, deleteLineIds[0]).concat(this.lines.slice(deleteLineIds[1]));
      this.vectors = this.vectors.slice(0, id).concat(this.vectors.slice(id + 1));
      this.updatePointsPosition();
    }
    // 处理删除所有点的情况
    nextHoverPointId = this.vectors.length === 0 ? -1 : nextHoverPointId;
    return nextHoverPointId;
  }

  destroy() {
    if (this.mode === 'create' && this.editObject) {
      this.editObject.parent?.remove(this.editObject);
    } else if (this.mode === 'add' && this.oldEditObject) {
      this.editObject?.parent?.remove(this.editObject);
      this.stage.currentSight?.addObjectFromTool(this.type, this.oldEditObject);
      AnnoFactory.setStatus(this.oldEditObject, 'default');
      this.oldEditObject = null;
    }
    this.transformControl.dispose();
    this.stage.currentSight?.scene.remove(this.indicatePoint, this.indicateLine, this.axisCtrlHelper);

    // TODO: add dispose
  }
}
