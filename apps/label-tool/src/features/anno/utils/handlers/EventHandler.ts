import { Object3D, Vector3 } from 'three';

import { IDescItem } from '@/components';
import type { WidgetObject } from '@/types';
import { AnnoFactory } from '@/utils/annos';
import type { Stage } from '@/utils/render';
import { TransformControlHandler } from '@/utils/render';
import type { AdditionHandlerType, AnnoHandlerType } from '@/utils/tool';

export interface HandlerOptions {
  editObject?: Object3D;
  allowNew?: boolean;
  extraHandle?: Record<string, any> | null;

  /**
   * 开始创建标注物的回调
   * @param isClearSelected 在创建标注物前，是否需要清除选中的标注物
   * @returns 是否允许：false 阻止创建，true 允许创建
   */
  onObjectBegin?: (isClearSelected?: boolean) => void | boolean;
  /**
   * 结束创建标注物的回调
   * @param obj 创建对象
   * @param opt
   * @returns
   */
  onObjectMount?: (
    obj?: Object3D,
    opt?: {
      // 是否来自 clone
      isClone?: boolean;
      // 是否保持编辑工具（如果设置为 true，则 mount 后会继续允许新增，没有修改属性和 widget 过程）
      allowKeepAdd?: boolean;
      // 引导线名称
      indicateLine?: string;
      // 分割点击坐标
      selectionPoints?: number[][];
    }
  ) => void | boolean;
  /**
   * 更新标注物的回调
   * @param obj 更新对象
   * @returns 是否成功更新数据
   */
  onObjectUpdate?: (obj?: Object3D) => void | boolean;

  /**
   * 删除标注物的回调
   * @param obj 删除的标注物
   * @returns 是否成功删除标注物
   */
  onObjectRemove?: (obj?: Object3D) => void | boolean;

  /**
   * 文件坐标系内点的更新回调，【不能改变传参的值】
   * @param point 鼠标对应点云坐标系中的三维点坐标
   * @param source 变更的来源：移动或点击，默认为移动
   * @returns
   */
  onPointerChange?: (point: Vector3 | null, source?: 'move' | 'click') => void;

  /**
   * 信息更新
   * @param type 信息类别
   * @param data 信息内容
   * @returns
   */
  onInfoChange?: (type: 'helper' | 'desc', data?: { type: string; items: Array<IDescItem> }) => void;
}

export type EventHandlerType = AnnoHandlerType | AdditionHandlerType;

export interface IInteractEvent {
  // TODO: 删除 onClick 事件，使用 onPointerDown + onPointerUp 替代
  onClick: (event: MouseEvent) => void;

  // 返回 true 表示已响应当前事件，false 表示无特殊响应（交给外部处理）
  onPointerDown: (event: PointerEvent) => void | boolean;
  onPointerMove: (event: PointerEvent) => void | boolean;
  onPointerUp: (event: PointerEvent) => void | boolean;

  onMouseEnter: (event: MouseEvent) => void | boolean;
  onMouseLeave: (event: MouseEvent) => void | boolean;

  onKeyDown: (event: KeyboardEvent) => void | boolean;
  onKeyUp: (event: KeyboardEvent) => void | boolean;
}

export abstract class EventHandler implements IInteractEvent {
  public type: EventHandlerType;
  public stage: Stage;

  public editObject?: Object3D;
  /** 移动控制器 */
  protected transformControl: TransformControlHandler;
  /** 当前的几何数据 */
  private currentWidget: WidgetObject | null;

  /**
   * 不同的类型，分别代表标注物不同的编辑状态
   * create：完全新建，在数据层也没有该数据
   * adjust：调整标注物已有的部分
   * add：数据层已有该数据，但是要给标注物增加部分内容，有引导线出现（仅适用于多步编辑的标注物，例如：折线、曲线等）
   * wait：无标注物，等待中
   */
  public mode: 'create' | 'adjust' | 'add' | 'wait';

  /**
   * 开始创建标注物的回调
   * @returns 是否允许：false 阻止创建，true 允许创建
   */
  public onObjectBegin: Required<HandlerOptions>['onObjectBegin'];
  /**
   * 结束创建标注物的回调
   * @param obj 创建对象
   * @param fromSelect 是否来自 selectedAnnos：true 来自所选，false 直接创建（默认值为 false）
   * @returns 对象是否符合要求：true 符合要求，false 不符合要求
   */
  public onObjectMount: Required<HandlerOptions>['onObjectMount'];
  /**
   * 更新标注物的回调
   * @param obj 更新对象
   * @returns 是否成功更新数据
   */
  public onObjectUpdate: Required<HandlerOptions>['onObjectUpdate'];

  /**
   * 删除标注物的回调
   * @param obj 删除的标注物
   * @returns 是否成功删除标注物
   */
  public onObjectRemove: Required<HandlerOptions>['onObjectRemove'];

  /**
   * 文件坐标系的点变更
   */
  public onPointerChange: Required<HandlerOptions>['onPointerChange'];

  public onInfoChange: Required<HandlerOptions>['onInfoChange'];

  constructor(type: EventHandlerType, stage: Stage, options: HandlerOptions) {
    const { onPointerChange, onObjectBegin, onObjectMount, onObjectUpdate, onObjectRemove, onInfoChange } = options;
    this.type = type;
    this.stage = stage;
    this.mode = 'wait';
    this.currentWidget = null;
    this.transformControl = new TransformControlHandler(stage);

    this.onObjectBegin = onObjectBegin ?? (() => true);
    this.onObjectMount = onObjectMount ?? (() => true);
    this.onObjectUpdate = onObjectUpdate ?? (() => true);
    this.onObjectRemove = onObjectRemove ?? (() => true);

    this.onPointerChange = onPointerChange ?? (() => {});
    this.onInfoChange = onInfoChange ?? (() => {});
  }

  onClick(event: MouseEvent) {}
  onPointerDown(event: PointerEvent | MouseEvent) {}
  onPointerMove(event: PointerEvent | MouseEvent) {}
  onPointerUp(event: PointerEvent | MouseEvent) {}

  onMouseEnter(event: MouseEvent) {}
  onMouseLeave(event: MouseEvent) {}

  onKeyDown(event: KeyboardEvent) {}
  onKeyUp(event: KeyboardEvent) {}

  abstract updateEditVectors(obj?: Object3D): void;

  /**
   * 是否禁止外部更新
   */
  get isUpdateForbidden() {
    return this.mode === 'create' || this.mode === 'add';
  }

  setWidget(data: WidgetObject | null) {
    this.currentWidget = data;
  }

  /**
   * 静默 / 解除静默：静默状态下不响应交互
   * @param status
   */
  muteContorls(status: boolean) {
    this.transformControl.mute(status);
  }

  /**
   * 废弃当前 handler
   */
  destroy() {}

  /**
   * 执行编辑对象更新时的副作用：更新向量、绑定控制器等
   * @param obj
   */
  setEditObjectEffects(obj?: Object3D) {}

  /**
   * 设置编辑对象
   * @param obj
   * @returns
   */
  setEditObject(obj?: Object3D) {
    if (this.isUpdateForbidden) return;

    if (!this.editObject && !obj) return;

    // 此处是为了 ctrl-z 撤销时，不会重复触发
    if (this.editObject === obj) {
      // 这个地方使用的位置判断
      if (this.editObject?.userData.widget !== this.currentWidget) {
        this.setEditObjectEffects(this.editObject);
      }
      return;
    }

    this.onPointerChange(null);

    if (this.editObject) {
      AnnoFactory.setStatus(this.editObject, 'default');
    }

    this.editObject = obj;
    this.mode = obj ? 'adjust' : 'wait';
    this.setWidget(obj?.userData.widget ?? null);
    // 进入 / 退出编辑态时，不同的 handlers 可能会有不同的处理，此处触发这些不同的处理
    this.setEditObjectEffects(this.editObject);

    if (this.editObject) {
      AnnoFactory.setStatus(this.editObject, 'adjust');
    }
  }

  /**
   * 更新轴移动控制器状态，若有 target，则绑定显示；否则解绑并隐藏
   * @param target
   * @param supportRotate 是否显示旋转（默认只有轴移动）
   * @returns
   */
  updateTransformControls(target?: Object3D, supportRotate = false) {
    if (!target) {
      this.onInfoChange('desc');
    }

    this.transformControl.update(target, supportRotate);
  }
}
