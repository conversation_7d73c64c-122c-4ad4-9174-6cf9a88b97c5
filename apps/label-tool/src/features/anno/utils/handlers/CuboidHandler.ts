import {
  Box3,
  <PERSON><PERSON>erG<PERSON><PERSON>,
  <PERSON>uler,
  Mesh,
  MeshBasicMaterial,
  Object3D,
  Quaternion,
  Raycaster,
  Vector2,
  Vector3,
} from 'three';

import { OBJECT_EDIT_YELLOW } from '@/config/color';
import { OBJECT_FACE_ALPHA, OBJECT_FACE_OPACITY } from '@/config/render';
import { selectionPoints } from '@/lib/three/SelectionPoints';
import { Cuboid, Point3d } from '@/utils/annos';
import { convexHull2, roundDecimals } from '@/utils/math';
import { PointPicker, projectMouseToPlane, type Stage, ThreeViews } from '@/utils/render';

import { DIRECTION_ROTATE_TIMES_MAP } from '../../config';
import { EventHandler, EventHandlerType, HandlerOptions } from './EventHandler';

const AXIS_Z = new Vector3(0, 0, 1);

const _startPoint = new Vector3();
const _endPoint = new Vector3();

/**
 * 根据拉框的第一点（startPoint）和第二点（endPoint），计算箭头方向
 * 箭头方向规则：指向和第一点相连的短边
 * @param startPoint
 * @param endPoint
 * @returns 方向
 */
const computeDirection = (startPoint: Vector3, endPoint: Vector3, width: number, height: number) => {
  if (height < width) {
    // y 是短边（左 or 右）
    if (startPoint.x < endPoint.x) {
      return 'left';
    } else {
      return 'right';
    }
  } else {
    // x 是短边（上 or 下）
    if (startPoint.y < endPoint.y) {
      return 'bottom';
    } else {
      return 'top';
    }
  }
};

// 此处绘制的内容都是 three 中的 object
export class CuboidHandler extends EventHandler {
  static preBox:
    | {
        width: number;
        height: number;
        length: number;
      }
    | undefined;

  private element: HTMLDivElement;
  private pointTopLeft: Vector2;
  private pointBottomRight: Vector2;
  private wrapper: HTMLDivElement;
  private withAnchor: boolean;
  private raycaster: Raycaster;
  private isAround: boolean;
  private pointPicker: PointPicker;
  private startPoint?: Vector2;

  /**
   * 初始化立体框工具，为绘制立体框做准备
   * @param type
   * @param stage 需确保当前 currentSight 不为空
   * @param options
   */
  constructor(type: EventHandlerType, stage: Stage, options: HandlerOptions) {
    super(type, stage, options);

    this.wrapper = this.stage.canvas.parentElement as HTMLDivElement;

    const color = OBJECT_EDIT_YELLOW;

    this.element = document.createElement('div');
    this.element.style.border = '1px solid ' + color;
    this.element.style.background = color + OBJECT_FACE_ALPHA;
    this.element.style.position = 'fixed';
    this.element.style.zIndex = '999999';
    this.element.style.pointerEvents = 'none';

    this.raycaster = new Raycaster();
    this.pointTopLeft = new Vector2();
    this.pointBottomRight = new Vector2();
    this.withAnchor = Boolean(this.stage.labelRenderer);
    this.isAround = false;
    CuboidHandler.preBox = undefined;
    this.pointPicker = new PointPicker(
      stage.currentSight!.rawObject,
      stage.currentSight!.camera,
      Point3d.POINT_SIZE * 1.1
    );

    const handleTransformControlChange = () => {
      // 轴移动时，实时更新三视图的相机
      const viewsHandler = this.stage?.currentSight?.subViews;
      if (viewsHandler instanceof ThreeViews) {
        viewsHandler.cameraFollowTarget();
      }
    };
    this.transformControl.onChange(handleTransformControlChange);
  }

  onPointerDown(event: PointerEvent) {
    if (event.button !== 0) return false;

    let needsUpdate = true;
    // 如果当前显示轴控制器且有响应，不创建
    if (this.transformControl.visible && (this.transformControl.hasAxis || event.altKey)) {
      this.mode = 'adjust';
      if (this.editObject) {
        this.editObject.userData.needsUpdate = needsUpdate;
      }
      return needsUpdate;
    }

    // 鼠标点在编辑中的标注物上，不创建
    if (this.editObject && this.isAround) return needsUpdate;

    // 开始绘制立体框：创建临时的矩形，设置 startPoint
    if (!this.onObjectBegin()) return false;

    this.mode = 'create';

    this.element.style.display = 'none';
    this.wrapper.appendChild(this.element);

    this.element.style.left = event.clientX + 'px';
    this.element.style.top = event.clientY + 'px';
    this.element.style.width = '0px';
    this.element.style.height = '0px';

    this.startPoint = new Vector2(event.clientX, event.clientY);

    _startPoint.set((event.offsetX / this.stage.width) * 2 - 1, -(event.offsetY / this.stage.height) * 2 + 1, 0.5);

    return needsUpdate;
  }
  onPointerMove(event: PointerEvent) {
    if (event.buttons > 0) {
      // 鼠标处于按下状态

      if (!this.startPoint) return false;

      // 绘制立体框过程：实时更新临时的矩形
      this.element.style.display = 'block';
      this.pointBottomRight.x = Math.max(this.startPoint.x, event.clientX);
      this.pointBottomRight.y = Math.max(this.startPoint.y, event.clientY);
      this.pointTopLeft.x = Math.min(this.startPoint.x, event.clientX);
      this.pointTopLeft.y = Math.min(this.startPoint.y, event.clientY);

      this.element.style.left = this.pointTopLeft.x + 'px';
      this.element.style.top = this.pointTopLeft.y + 'px';
      this.element.style.width = this.pointBottomRight.x - this.pointTopLeft.x + 'px';
      this.element.style.height = this.pointBottomRight.y - this.pointTopLeft.y + 'px';

      return true;
    } else {
      // 鼠标处于松开状态
      if (!this.editObject || !this.stage.currentSight) return false;

      this.pointTopLeft.set((event.offsetX / this.stage.width) * 2 - 1, -(event.offsetY / this.stage.height) * 2 + 1);
      this.raycaster.setFromCamera(this.pointTopLeft, this.stage.currentSight.camera);

      if (this.raycaster.intersectObject(this.editObject, false).length > 0) {
        this.isAround = true;
        return true;
      } else {
        this.isAround = false;
        return false;
      }
    }
  }
  onPointerUp(event: PointerEvent) {
    // 结束编辑，保存编辑的数据
    if (this.mode === 'adjust' && this.editObject?.userData.needsUpdate) {
      // 如果是 alt + 左键点击，更新正在编辑的标注物的位置
      if (event.altKey && event.button === 0 && this.stage.currentSight && this.editObject) {
        const x = (event.offsetX / this.stage.width) * 2 - 1;
        const y = -(event.offsetY / this.stage.height) * 2 + 1;
        const newPosition = projectMouseToPlane(this.stage.currentSight?.camera, x, y, this.editObject.position.z);
        newPosition && this.editObject.position.fromArray(newPosition);
      }
      this.onObjectUpdate(this.editObject);
      this.editObject.userData.needsUpdate = false;
      return true;
    }

    this.mode = 'wait';
    if (!this.startPoint) return false;

    // 完成绘制立体框：移除临时矩形，准备创建立体框
    this.wrapper.removeChild(this.element);
    this.startPoint = undefined;

    _endPoint.set((event.offsetX / this.stage.width) * 2 - 1, -(event.offsetY / this.stage.height) * 2 + 1, 0.5);

    this.createBox();

    return true;
  }

  getQuaternion() {
    // 通过欧拉角获取相机绕 z 轴旋转的角度
    const cameraEuler = new Euler().setFromQuaternion(this.stage.currentSight!.camera.quaternion, 'ZXY');

    // 确定箭头方向及对应的旋转角度
    const tmpRectWidth = this.pointBottomRight.x - this.pointTopLeft.x;
    const tmpRectHeight = this.pointBottomRight.y - this.pointTopLeft.y;
    const direction = computeDirection(_startPoint, _endPoint, tmpRectWidth, tmpRectHeight);
    const angleFromDirection = (Math.PI / 2) * DIRECTION_ROTATE_TIMES_MAP[direction];

    return new Quaternion().setFromAxisAngle(AXIS_Z, cameraEuler.z + angleFromDirection);
  }

  /**
   * 紧致框功能
   */
  createFixBox() {
    // 获取框选范围内的点云
    selectionPoints.updateCameraAndPointCloud(
      this.stage.currentSight!.camera,
      this.stage.currentSight!.getUsingRawObject().object
    );

    // 避免 _endPoint 被改变，传入它的副本
    const allSelected = selectionPoints.selectFromRectangle(_startPoint, new Vector3().copy(_endPoint));

    let lowZ = 0;
    let highZ = Number.MAX_SAFE_INTEGER;

    if (this.stage.currentSight) {
      const { groundZ, visibleZ } = this.stage.currentSight.getUsingRawObject().options;
      lowZ = Math.max(groundZ, visibleZ[0]);
      highZ = visibleZ[1];
    }

    const points: [number, number][] = [];
    let ground = Number.MAX_SAFE_INTEGER;
    // z 最小的值作为 ground
    for (let i = 2; i < allSelected.length; i = i + 3) {
      ground = Math.min(ground, allSelected[i]);
    }

    let minZ = Number.MAX_SAFE_INTEGER,
      maxZ = Number.MIN_SAFE_INTEGER;
    for (let i = 0; i < allSelected.length; i = i + 3) {
      const x = allSelected[i],
        y = allSelected[i + 1],
        z = allSelected[i + 2];
      // 过滤可视范围及地面高度（z 在范围内）
      if (z > lowZ && z < highZ) {
        points.push([x, y]); // x 和 y
        // 计算最高点
        minZ = Math.min(minZ, z);
        maxZ = Math.max(maxZ, z);
      }
    }

    // 至少有两个点才创建立体框

    if (points.length > 2) {
      // 计算点集的凸包用于贴合
      const hull = convexHull2(points) as [number, number][];
      // 给 hull 增加 z 坐标，确保覆盖 minZ 和 maxZ
      const criticalPoints = hull.map(([x, y], idx) => new Vector3(x, y, idx === 0 ? minZ : maxZ));

      const quaternion = this.getQuaternion();
      const editObject = Cuboid.createFromPoints(criticalPoints, quaternion, {
        color: OBJECT_EDIT_YELLOW,
        opacity: OBJECT_FACE_OPACITY,
        anchor: this.withAnchor,
        canvasSize: [this.stage.width, this.stage.height],
      });
      // 渲染对象需在 onObjectMount 前挂载，避免多次添加
      this.stage.currentSight?.addObjectFromTool(this.type, editObject);

      if (!this.onObjectMount(editObject)) {
        editObject.parent?.remove(editObject);
      }
    } else {
      this.onObjectMount(undefined);
    }
  }

  createBox() {
    const LTPoint = this.pointPicker.getPointInPlane(_startPoint);
    const RBPoint = this.pointPicker.getPointInPlane(_endPoint);
    if (!LTPoint || !RBPoint || LTPoint.distanceToSquared(RBPoint) < 1) {
      this.onObjectMount(undefined);
      return;
    }

    const { groundZ } = this.stage.currentSight!.getUsingRawObject().options;
    const fixedDepth = CuboidHandler.preBox?.height ?? 1.6;
    const quaternion = this.getQuaternion();
    const [minZ, maxZ] = [(groundZ + 1.6 - fixedDepth) / 2, (groundZ + 1.6 + fixedDepth) / 2];

    let cuboidPoints;
    if (CuboidHandler.preBox) {
      const { length, width } = CuboidHandler.preBox;
      const center = LTPoint.clone().add(RBPoint.clone().sub(LTPoint).multiplyScalar(0.5));
      const [halfLength, halfWidth] = [length / 2, width / 2];

      // 直接生成点集并限制范围
      cuboidPoints = [
        new Vector3(center.x - halfLength, center.y - halfWidth, minZ),
        new Vector3(center.x - halfLength, center.y + halfWidth, minZ),
        new Vector3(center.x + halfLength, center.y - halfWidth, maxZ),
        new Vector3(center.x + halfLength, center.y + halfWidth, maxZ),
      ];

      // 修正点集范围，确保旋转后宽度和长度符合设定值
      const inverseQuaternion = new Quaternion().copy(quaternion).invert();
      cuboidPoints = cuboidPoints.map((point) => point.clone().applyQuaternion(inverseQuaternion));
      const rotatedBox = new Box3().setFromPoints(cuboidPoints);
      const rotatedSize = rotatedBox.getSize(new Vector3());
      const rotatedCenter = rotatedBox.getCenter(new Vector3());

      cuboidPoints = cuboidPoints.map((point) => {
        point.x = rotatedCenter.x + (point.x - rotatedCenter.x) * (length / rotatedSize.x);
        point.y = rotatedCenter.y + (point.y - rotatedCenter.y) * (width / rotatedSize.y);
        return point.applyQuaternion(quaternion);
      });
    } else {
      const LBPoin = this.pointPicker.getPointInPlane(new Vector3(_startPoint.x, _endPoint.y, 0.5));
      const RTPoint = this.pointPicker.getPointInPlane(new Vector3(_endPoint.x, _startPoint.y, 0.5));

      cuboidPoints = [
        new Vector3(LTPoint.x, LTPoint.y, minZ),
        new Vector3(RTPoint!.x, RTPoint!.y, minZ),
        new Vector3(RBPoint.x, RBPoint.y, maxZ),
        new Vector3(LBPoin!.x, LBPoin!.y, maxZ),
      ];
    }

    const editObject = Cuboid.createFromPoints(cuboidPoints, quaternion, {
      color: OBJECT_EDIT_YELLOW,
      opacity: OBJECT_FACE_OPACITY,
      anchor: this.withAnchor,
      canvasSize: [this.stage.width, this.stage.height],
    });

    // 渲染对象需在 onObjectMount 前挂载，避免多次添加
    this.stage.currentSight?.addObjectFromTool(this.type, editObject);

    if (!this.onObjectMount(editObject)) {
      editObject.parent?.remove(editObject);
    }
  }

  onKeyUp(event: KeyboardEvent) {
    // 滚轮 + shift 表示旋转
    if (event.key === 'Shift' && this.editObject) {
      this.onObjectUpdate(this.editObject);
    }
  }

  updatePointsCount(editObj: Object3D) {
    Cuboid.updatePointCount(
      editObj as Mesh<BufferGeometry, MeshBasicMaterial>,
      this.stage.currentSight!.camera,
      this.stage.currentSight!.getCurrentUsingRawObject().object
    );
  }

  updateInfo(editObj?: Object3D) {
    if (!editObj) return;
    const { position, scale } = editObj;

    this.onInfoChange('desc', {
      type: 'cuboid',
      items: [
        { label: 'X', value: position.x },
        { label: 'Y', value: position.y },
        { label: 'Z', value: position.z },
        { label: '点数', value: editObj.userData.point_cnt },
        { label: '长', value: scale.x },
        { label: '宽', value: scale.y },
        { label: '高', value: scale.z },
      ].map(({ label, value }) => ({
        label,
        value: roundDecimals(value, 3),
      })),
    });
  }

  setEditObjectEffects(editObj?: Object3D) {
    // 进入编辑态显示轴移动，退出编辑态隐藏轴移动
    this.updateTransformControls(editObj, true);

    if (editObj) {
      // 三视图编辑后更新点的数量
      this.updatePointsCount(editObj);
      this.updateInfo(editObj);
    } else {
      selectionPoints.removeSelection(this.stage.currentSight!.getCurrentUsingRawObject().object);
    }
  }

  updateEditVectors() {}

  destroy() {
    this.transformControl.dispose();
    if (!this.startPoint) return false;
    this.wrapper.removeChild(this.element);
    this.startPoint = undefined;
  }
}
