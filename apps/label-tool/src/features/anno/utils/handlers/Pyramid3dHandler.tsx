import { DebouncedFunc, throttle } from 'lodash';
import { Mesh, MeshBasicMaterial, PolyhedronGeometry, Vector3 } from 'three';

import { OBJECT_EDIT_YELLOW } from '@/config/color';
import { Point3d, Pyramid3d } from '@/utils/annos';
import { PointPicker, Stage } from '@/utils/render';

import { CubeDot } from './CubeDot';
import type { EventHandlerType, HandlerOptions } from './EventHandler';
import { EventHandler } from './EventHandler';

const WAIT_TIME = 50;
const INDICATE_WHITE = '#FFFFFF';

export class Pyramid3dHandler extends EventHandler {
  public declare editObject?: Mesh<PolyhedronGeometry, MeshBasicMaterial>;

  private pointer3: Vector3;

  private pointPicker: PointPicker;

  private indicatePoint: Mesh;
  private debounceSetIndicate: DebouncedFunc<(end: Vector3) => void>;

  /**
   * 点工具
   * @param type
   * @param stage 需确保当前 currentSight 不为空
   * @param options
   */
  constructor(type: EventHandlerType, stage: Stage, options: HandlerOptions) {
    super(type, stage, options);

    this.pointer3 = new Vector3();
    this.pointPicker = new PointPicker(
      stage.currentSight!.rawObject,
      stage.currentSight!.camera,
      Point3d.POINT_SIZE * 1.1
    );

    // 创建引导点
    this.indicatePoint = CubeDot.create(0, 0, 0, INDICATE_WHITE);
    this.indicatePoint.scale.set(1.8, 1.8, 1.8);
    this.indicatePoint.visible = false;
    this.stage.currentSight?.scene.add(this.indicatePoint);
    this.debounceSetIndicate = throttle(this.setIndicate, WAIT_TIME);
  }

  onPointerDown(event: PointerEvent) {
    if (event.button !== 0) return false;

    this.debounceSetIndicate.cancel();
    this.updatePointer(event);

    // 可以新建点的条件：1.当前没有 editObject 或 2.有 editObject 时鼠标未点到当前 editObject 且鼠标点在其他有点云的地方
    if (!this.editObject) {
      this.setIndicate(this.pointer3);

      if (this.indicatePoint.visible) {
        this.mode = 'create';

        this.editObject = Pyramid3d.create(this.indicatePoint.position.toArray(), {
          color: OBJECT_EDIT_YELLOW,
          canvasSize: [this.stage.width, this.stage.height],
        });

        this.stage.currentSight?.addObjectFromTool(this.type, this.editObject);
        return true;
      }
    }

    return false;
  }

  onPointerMove(event: PointerEvent) {
    this.updatePointer(event);
    // 鼠标处于按下状态
    if (event.buttons > 0) return false;

    // 鼠标处于松开状态
    this.debounceSetIndicate(this.pointer3);

    return false;
  }

  onPointerUp(event: PointerEvent) {
    // 拖拽点后更新位置给标注物
    if (this.editObject) {
      if (this.mode === 'create') {
        // 每画完一个点就结束新建
        this.mode = 'wait';

        this.onObjectMount(this.editObject);

        this.editObject = undefined;
      }
      return true;
    }
    return false;
  }

  updateEditVectors() {}

  updatePointer(event: PointerEvent | MouseEvent) {
    this.pointer3.set((event.offsetX / this.stage.width) * 2 - 1, -(event.offsetY / this.stage.height) * 2 + 1, 0);
  }

  /**
   * 设置引导点坐标
   * @param end
   */
  setIndicate(end: Vector3) {
    let realEnd = this.pointPicker.findNearestPoint(end, undefined, true, false);

    this.onPointerChange(realEnd);

    // 更新引导点
    if (realEnd) {
      this.indicatePoint.position.copy(realEnd);
      if (!this.indicatePoint.visible) {
        this.indicatePoint.visible = true;
      }
    } else if (this.indicatePoint.visible) {
      this.indicatePoint.visible = false;
    }
  }

  destroy() {
    this.stage.currentSight?.scene.remove(this.indicatePoint);
  }
}
