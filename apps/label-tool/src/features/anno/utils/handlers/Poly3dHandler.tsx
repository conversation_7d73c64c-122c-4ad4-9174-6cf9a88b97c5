import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Vector3 } from 'three';

import { OBJECT_EDIT_YELLOW } from '@/config/color';
import { Line3d, Poly3d } from '@/utils/annos';
import { Stage } from '@/utils/render';

import { EventHandlerType, HandlerOptions } from './EventHandler';
import { Line3dHandler } from './Line3dHandler';

const INDICATE_WHITE = '#FFFFFF';
const MOVE_DETECTION = 0.06;

const _ray = new Ray();

export class Poly3dHandler extends Line3dHandler {
  constructor(type: EventHandlerType, stage: Stage, options: HandlerOptions) {
    super(type, stage, options, {
      canDelete: true,
      canAdjust: true,
      canAdd: false,
    });
    this.stage.currentSight?.scene.remove(this.indicateLine);
    this.indicateLine = Line3d.create([0, 0, 0, 0, 0, 0, 0, 0, 0], {
      color: INDICATE_WHITE,
      vertexColors: [1, 1, 1, 0.112, 0.125, 0.147, 0.1, 0.1, 0.1],
      canvasSize: [this.stage.width, this.stage.height],
    });
    this.indicateLine.visible = false;
    this.stage.currentSight?.scene.add(this.indicateLine);
    this.Line = Poly3d;
  }

  updateIndicateLine(start: Vector3, end: Vector3) {
    if (!this.indicateLine.visible) {
      this.indicateLine.visible = true;
    }
    // 更新引导线，如果点 >= 2个，就需要两段引导线，如果减少 < 2，则只有一段引导线
    const updatePosition = [start.x, start.y, start.z, end.x, end.y, end.z];
    if (this.vectors.length + 1 >= Poly3d.MIN_POINTS_COUNT) {
      Line3d.update(this.indicateLine, updatePosition.concat(this.vectors[0].toArray()));
      this.indicateLine.geometry.instanceCount = 2;
    } else {
      Line3d.update(this.indicateLine, updatePosition);
      this.indicateLine.geometry.instanceCount = 1;
    }
  }

  deletePoint(pointId: number) {
    if (!this.editObject) return false;

    if (!this.editObject.userData.isClose) {
      return super.deletePoint(pointId);
    }

    if (pointId >= 0 && pointId < this.vectors.length && this.vectors.length > Poly3d.MIN_POINTS_COUNT) {
      // 隐藏轴控制器
      this.updateTransformControls();
      // 删除当前选中点
      this.Line.partialUpdate(this.editObject, [], { startIndex: pointId, mode: 'remove' });

      this.removeVector(pointId);
      this.activePointId = -1;
      this.hoverPointId = -1;
      Line3d.changePointColor(this.editObject, this.activePointId, INDICATE_WHITE, OBJECT_EDIT_YELLOW);
      return true;
    }

    return false;
  }

  /**
   * 取消引导线
   */
  disableIndicateLine() {
    // 点数小于指定数量，点击 esc 就直接删除了
    if (this.vectors.length < Poly3d.MIN_POINTS_COUNT) {
      this.editObject?.parent?.remove(this.editObject);
      this.mode = 'wait';
      this.editObject = undefined;
      this.debounceSetIndicate.cancel();
      this.indicateLine.visible = false;
      this.indicatePoint.visible = false;
      this.updateEditVectors();
      return;
    }
    // 结束编辑的时候，需要把最后的点加上
    this.editObject && Poly3d.closeLine(this.editObject);
    return super.disableIndicateLine();
  }

  /**
   * 查找折线上可响应的点或线
   * @param ndcPoint
   * @returns
   */
  findClosestElement(ndcPoint: Vector3) {
    if (!this.editObject) return false;

    const camera = this.stage.currentSight!.camera;

    if (camera instanceof PerspectiveCamera) {
      _ray.origin.setFromMatrixPosition(camera.matrixWorld);
      _ray.direction.set(ndcPoint.x, ndcPoint.y, 0.5).unproject(camera).sub(_ray.origin).normalize();
    } else if (camera instanceof OrthographicCamera) {
      _ray.origin
        .set(ndcPoint.x, ndcPoint.y, (camera.near + camera.far) / (camera.near - camera.far))
        .unproject(camera);
      _ray.direction.set(0, 0, -1).transformDirection(camera.matrixWorld);
    }

    // 遍历折线所有端点，查找阈值内端点
    const vid = this.vectors.findIndex((v, i) => _ray.distanceSqToPoint(v) < MOVE_DETECTION);
    if (vid >= 0) {
      this.indicatePoint.position.copy(this.vectors[vid]);
      this.indicatePoint.visible = true;

      this.hoverLineId = -1;
      this.hoverPointId = vid;

      this.onPointerChange(this.vectors[vid]);
      return true;
    }

    // 遍历折线所有线段
    let lid = this.lines.findIndex(
      (line) => _ray.distanceSqToSegment(line.start, line.end, undefined, this.hoverLineClosest) < MOVE_DETECTION
    );
    // 只在 adjust 时加上检查自动画的一段线
    if (
      this.mode === 'adjust' &&
      lid === -1 &&
      _ray.distanceSqToSegment(
        this.vectors[this.vectors.length - 1],
        this.vectors[0],
        undefined,
        this.hoverLineClosest
      ) < MOVE_DETECTION
    ) {
      lid = this.lines.length;
    }

    if (lid >= 0) {
      this.indicatePoint.position.copy(this.hoverLineClosest);
      this.indicatePoint.visible = true;

      this.hoverLineId = lid;
      this.hoverPointId = -1;

      this.onPointerChange(this.hoverLineClosest);
      return true;
    }

    if (this.indicatePoint.visible) {
      this.hoverPointId = -1;
      this.hoverLineId = -1;
      this.indicatePoint.visible = false;

      this.onPointerChange(null);
    }
    return false;
  }

  // 拖拽后通过 three.js 中的标注物来更新 verctor
  updatePointVector(pointId: number) {
    this.vectors[pointId].fromArray(Poly3d.getVertexFromObject3D(this.editObject!), pointId * 3);
  }
}
