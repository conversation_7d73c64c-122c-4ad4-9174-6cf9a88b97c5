import { Vector3 } from 'three';

import { Line3d, Spline3d } from '@/utils/annos';
import { isSameVector3 } from '@/utils/math';
import { Stage } from '@/utils/render';

import { EventHandlerType, HandlerOptions } from './EventHandler';
import { Line3dHandler } from './Line3dHandler';

const INDICATE_WHITE = '#FFFFFF';

export class Spline3dHandler extends Line3dHandler {
  /**
   * 曲线工具
   * @param type
   * @param stage 需确保当前 currentSight 不为空
   * @param options
   */
  constructor(type: EventHandlerType, stage: Stage, options: HandlerOptions) {
    super(type, stage, options);
    this.stage.currentSight?.scene.remove(this.indicateLine);
    this.indicateLine = Line3d.create([], {
      color: INDICATE_WHITE,
      maxPoints: Spline3d.INTERPOLATION_COUNT + 2,
      canvasSize: [this.stage.width, this.stage.height],
    });
    this.indicateLine.visible = false;
    this.stage.currentSight?.scene.add(this.indicateLine);
    this.Line = Spline3d;
  }

  updateIndicateLine(start: Vector3, end: Vector3) {
    if (!this.indicateLine.visible) {
      this.indicateLine.visible = true;
    }

    // 只有一个点的时候，引导线是直线
    if (this.editObject?.userData.keyVectors.length < 2) {
      Line3d.update(this.indicateLine, [start.x, start.y, start.z, end.x, end.y, end.z]);
      // 如果从曲线变成直线，需要重新设置 instanceCount
      this.indicateLine.geometry.instanceCount = 1;
    } else {
      this.fittingIndicate(start, end);
    }
  }

  /**
   * 曲线响应引导线变化，需要计算曲线的插值
   * @param realEnd 引导结束点
   * @returns
   */
  fittingIndicate(start: Vector3, realEnd: Vector3) {
    if (!this.editObject) return;

    let pose: 'end' | 'start';

    // 在空白处打点时，两个向量有细微差别
    if (isSameVector3(start, this.editObject.userData.keyVectors[0], 1e-9)) {
      pose = 'start';
    } else if (
      isSameVector3(start, this.editObject.userData.keyVectors[this.editObject.userData.keyVectors.length - 1], 1e-9)
    ) {
      pose = 'end';
    } else {
      return;
    }

    const { newPositions, updatePositions, offset } = Spline3d.addKeyDot(this.editObject, realEnd, pose);

    Line3d.update(this.indicateLine, newPositions);

    // 此处不是因为添加关键点来调整 this.editObject，所以此处不能调用 Spline3d.update 来调整
    this.editObject.geometry.updatePositions(updatePositions, offset * 3);
  }

  // 拖拽后通过 three.js 中的标注物来更新 verctor
  updatePointVector(pointId: number) {
    this.vectors[pointId].fromArray(Spline3d.getVertexFromObject3D(this.editObject!), pointId * 3);
  }

  /**
   * 取消引导线
   */
  disableIndicateLine() {
    // 在曲线绘制中，最后一段的曲线会随着引导线变化，结束时需要更新最后一段的展示
    this.editObject && Spline3d.updateLine(this.editObject);
    return super.disableIndicateLine();
  }
}
