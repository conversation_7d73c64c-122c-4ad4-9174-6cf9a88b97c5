import { Line3, Mesh, MeshBasicMaterial, Object3D, ShapeGeometry, Vector3 } from 'three';

import { OBJECT_EDIT_YELLOW } from '@/config/color';
import { OBJECT_FACE_OPACITY } from '@/config/render';
import { Box2d } from '@/utils/annos';
import { Stage } from '@/utils/render';

import { EventHandler, HandlerOptions } from './EventHandler';

// 获取矩形点/边的相对编号
const getRelativeIndex = (index: number) => (index + 2 < 4 ? index + 2 : index - 2);

interface selectDownObject {
  type: 'none' | 'new' | 'point' | 'line' | 'rect';
  index: number;
  startX?: number;
  startY?: number;
  startRect?: [number, number, number, number]; // [x, y, w, h]
}
// 2D 矩形框编辑
export class Box2dHandler extends EventHandler {
  public declare editObject?: Mesh<ShapeGeometry, MeshBasicMaterial>;
  // 是否允许新建矩形
  public allowNew: boolean;

  private wrapper: HTMLDivElement;
  private text: HTMLSpanElement;

  private pointInSight: Vector3;

  // 矩形顶点从左上角顺时针存放
  private vertexArray: Array<Vector3>;
  // 矩形线段从上边顺时针存放
  private sideArray: Array<Line3>;
  // 选中的点
  private selectDown: selectDownObject;
  // 选取感应范围
  private senseRange: number;

  constructor(stage: Stage, options: HandlerOptions) {
    super('box2d', stage, options);
    const { allowNew = true } = options;

    this.wrapper = this.stage.canvas.parentElement as HTMLDivElement;

    this.text = document.createElement('span');
    this.text.style.position = 'absolute';
    this.text.style.pointerEvents = 'none';
    this.text.style.color = 'red';
    this.wrapper.appendChild(this.text);
    this.text.style.visibility = 'hidden';

    this.allowNew = allowNew;
    this.senseRange = 80;
    this.pointInSight = new Vector3();
    this.selectDown = {
      type: 'none',
      index: -1,
    };

    // 创建顶点集合
    this.vertexArray = new Array(4).fill(0).map(() => new Vector3());
    // 创建线段集合
    this.sideArray = [];
    for (let i = 0; i < this.vertexArray.length; i++) {
      if (i < 3) {
        this.sideArray.push(new Line3(this.vertexArray[i], this.vertexArray[i + 1]));
      } else {
        this.sideArray.push(new Line3(this.vertexArray[i], this.vertexArray[0]));
      }
    }
  }

  public onPointerMove(event: PointerEvent) {
    const { offsetX, offsetY } = event;
    this.transformPointInSight(offsetX, offsetY);

    // 选取阶段（未按下鼠标 或 按下鼠标但无响应）
    if (
      this.selectDown.startX === undefined ||
      this.selectDown.startY === undefined ||
      this.selectDown.type === 'none'
    ) {
      this.updateSelected();
      return false;
    }

    // 已按下鼠标且有响应事件
    const { type, index, startRect, startX, startY } = this.selectDown;

    switch (type) {
      case 'rect':
        if (this.editObject && startRect) {
          this.editObject.position.setX(startRect[0] + this.pointInSight.x - startX);
          this.editObject.position.setY(startRect[1] + this.pointInSight.y - startY);
        }
        break;

      case 'new':
      case 'point':
        if (this.editObject && startRect) {
          const newX = this.vertexArray[index].x + this.pointInSight.x - startX;
          const newY = this.vertexArray[index].y + this.pointInSight.y - startY;

          const rid = getRelativeIndex(index);

          Box2d.update(this.editObject, [
            (newX + this.vertexArray[rid].x) / 2,
            (newY + this.vertexArray[rid].y) / 2,
            Math.abs(newX - this.vertexArray[rid].x),
            Math.abs(newY - this.vertexArray[rid].y),
          ]);
        }
        break;

      case 'line':
        if (this.editObject && startRect) {
          const newX =
            index % 2 === 0 ? this.vertexArray[index].x : this.vertexArray[index].x + this.pointInSight.x - startX;
          const newY =
            index % 2 === 1 ? this.vertexArray[index].y : this.vertexArray[index].y + this.pointInSight.y - startY;

          const rid = getRelativeIndex(index);

          Box2d.update(this.editObject, [
            (newX + this.vertexArray[rid].x) / 2,
            (newY + this.vertexArray[rid].y) / 2,
            Math.abs(newX - this.vertexArray[rid].x),
            Math.abs(newY - this.vertexArray[rid].y),
          ]);
        }
        break;

      default:
        break;
    }
    return true;
  }

  public onPointerDown(event: PointerEvent) {
    if (!this.editObject && !this.allowNew) return false;

    this.updateSelected();
    this.selectDown.startX = this.pointInSight.x;
    this.selectDown.startY = this.pointInSight.y;

    // 已选中当前矩形某部分，预期进行编辑
    if (this.editObject && this.selectDown.type !== 'none') {
      // 保存编辑前的数据
      const { position, scale } = this.editObject;
      this.selectDown.startRect = [position.x, position.y, scale.x, scale.y];

      this.text.style.visibility = 'visible';

      if (this.selectDown.type === 'rect') {
        this.stage.setCursor('grabbing');
      }

      return true;
    }

    // 未选中矩形，且允许新建时
    if (this.allowNew) {
      // 新建矩形
      const editObject = Box2d.create([this.pointInSight.x, this.pointInSight.y, 0.1, 0.1], {
        color: OBJECT_EDIT_YELLOW,
        opacity: OBJECT_FACE_OPACITY,
      });
      this.stage.currentSight?.addObjectFromTool(this.type, editObject);
      this.setEditObject(editObject);

      // 将新建交互等同为拖拽矩形右下角的点进行缩放
      this.selectDown.type = 'new';
      this.selectDown.index = 2;
      this.selectDown.startRect = [this.pointInSight.x, this.pointInSight.y, 0.1, 0.1];

      return true;
    }

    return false;
  }

  public onPointerUp(event: PointerEvent) {
    if (this.selectDown.type === 'rect') {
      this.stage.setCursor('grab');
    } else if (this.selectDown.type === 'new') {
      this.allowNew = false;
    }

    this.updateEditVectors(this.editObject);
    this.selectDown = {
      type: 'none',
      index: -1,
    };
    this.text.style.visibility = 'hidden';
    return false;
  }

  private transformPointInSight(offsetX: number, offsetY: number) {
    this.pointInSight.set((offsetX / this.stage.width) * 2 - 1, -(offsetY / this.stage.height) * 2 + 1, 0);
    this.pointInSight.unproject(this.stage.currentSight?.camera!);

    this.pointInSight.setComponent(2, 0);
  }

  private updateSelected() {
    if (!this.editObject) return;

    // 选取顶点
    for (let i = 0; i < this.vertexArray.length; i++) {
      if (this.pointInSight.distanceToSquared(this.vertexArray[i]) <= this.senseRange) {
        if (i % 2 === 1) {
          this.stage.setCursor('nesw-resize');
        } else {
          this.stage.setCursor('nwse-resize');
        }
        this.selectDown.type = 'point';
        this.selectDown.index = i;
        return;
      }
    }

    // 选取边
    const target = new Vector3();
    for (let i = 0; i < this.sideArray.length; i++) {
      this.sideArray[i].closestPointToPoint(this.pointInSight, true, target);
      if (this.pointInSight.distanceToSquared(target) < this.senseRange) {
        // 左右两条边
        if (i % 2 === 1) {
          this.stage.setCursor('col-resize');
        } else {
          // 上下边
          this.stage.setCursor('row-resize');
        }
        this.selectDown.type = 'line';
        this.selectDown.index = i;
        return;
      }
    }

    // 选取矩形
    if (
      this.pointInSight.x > this.vertexArray[0].x &&
      this.pointInSight.x < this.vertexArray[1].x &&
      this.pointInSight.y > this.vertexArray[3].y &&
      this.pointInSight.y < this.vertexArray[0].y
    ) {
      this.stage.setCursor('grab');

      this.selectDown.type = 'rect';
      this.selectDown.index = 0;

      return;
    }

    this.stage.setCursor();

    this.selectDown.type = 'none';
    this.selectDown.index = 0;
  }

  public updateEditVectors(target?: Mesh) {
    if (target) {
      const { position, scale } = target;
      const delta = [
        [-1, 1],
        [1, 1],
        [1, -1],
        [-1, -1],
      ];

      // 更新顶点集合
      for (let i = 0; i < this.vertexArray.length; i++) {
        this.vertexArray[i].set(position.x + (scale.x * delta[i][0]) / 2, position.y + (scale.y * delta[i][1]) / 2, 0);
      }
    } else {
      for (let i = 0; i < this.vertexArray.length; i++) {
        this.vertexArray[i].set(0, 0, 0);
      }
    }
  }

  setEditObjectEffects(target?: Object3D) {
    if (target instanceof Mesh) {
      this.updateEditVectors(target);
    }
  }

  // TODO: 添加放大镜
  // private updateMagnifier(offsetX: number, offsetY: number) {
  //   // 拖动显示放大镜时把鼠标偏移量也计算进去
  //   const point = new Vector3(this.selectDown.relativeOffsetX, this.selectDown.relativeOffsetY, 0).add(
  //     this.pointInSight
  //   );

  //   this.drawMagnifier?.drawCanvasWithMagnifier({
  //     offsetX,
  //     offsetY,
  //     width: this.stage.canvas.width,
  //     height: this.stage.canvas.height,
  //     target: point.applyMatrix4(this.imageTransMatrix).toArray(),
  //   });
  // }
}
