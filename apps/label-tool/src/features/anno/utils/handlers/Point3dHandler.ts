import { DebouncedFunc, throttle } from 'lodash';
import {
  BoxGeometry,
  Line3,
  Mesh,
  MeshBasicMaterial,
  Object3D,
  OrthographicCamera,
  PerspectiveCamera,
  Ray,
  Vector3,
} from 'three';

import { OBJECT_EDIT_YELLOW } from '@/config/color';
import { AnnoFactory, Point3d } from '@/utils/annos';
import { roundDecimals } from '@/utils/math';
import { PointPicker, Stage } from '@/utils/render';

import { CubeDot } from './CubeDot';
import type { HandlerOptions } from './EventHandler';
import { EventHandler } from './EventHandler';

const WAIT_TIME = 50;
const INDICATE_WHITE = '#FFFFFF';
const MOVE_DETECTION = 0.1;

const _point = new Vector3();
const _ray = new Ray();
const _lineClosest = new Vector3();

type Point3dHandlerType = 'point3d' | 'series-point3d';

const point3dMountOpt = {
  point3d: {
    allowKeepAdd: false,
  },
  'series-point3d': {
    allowKeepAdd: true,
    indicateLine: '',
  },
};

export class Point3dHandler extends EventHandler {
  public declare editObject?: Mesh<BoxGeometry, MeshBasicMaterial>;

  private pointer3: Vector3;

  private pointPicker: PointPicker;

  private indicatePoint: Mesh;
  private debounceSetIndicate: DebouncedFunc<(end: Vector3) => void>;
  private debounceDragPoint: DebouncedFunc<(point: Vector3) => void>;

  // 当前 editObject 是否被选中，为 drag 做准备
  private selectDown: boolean;

  // 引导点的 hover 范围
  private indicateRange: { vectors: Vector3[]; lines: Line3[]; name: string } | null;

  /**
   * 点工具
   * @param type
   * @param stage 需确保当前 currentSight 不为空
   * @param options
   */
  constructor(type: Point3dHandlerType, stage: Stage, options: HandlerOptions) {
    super(type, stage, options);

    this.pointer3 = new Vector3();
    this.pointPicker = new PointPicker(
      stage.currentSight!.rawObject,
      stage.currentSight!.camera,
      Point3d.POINT_SIZE * 1.1
    );
    this.selectDown = false;

    // 创建引导点
    this.indicatePoint = CubeDot.create(0, 0, 0, INDICATE_WHITE);
    this.indicatePoint.scale.set(1.8, 1.8, 1.8);
    this.indicatePoint.visible = false;
    this.stage.currentSight?.scene.add(this.indicatePoint);
    this.debounceSetIndicate = throttle(this.setIndicate, WAIT_TIME);
    this.debounceDragPoint = throttle(this.dragPoint, WAIT_TIME);
    this.indicateRange = null;

    // 轴移动时，实时更新显示的 DescInfo
    const throttleUpdateInfo = throttle(() => {
      if (this.editObject) {
        this.updateInfo(this.editObject);
      }
    }, 100);
    this.transformControl.onChange(throttleUpdateInfo);
  }

  onPointerDown(event: PointerEvent) {
    if (event.button !== 0) return false;

    let needsUpdate = false;

    // 如果当前显示轴控制器且有响应，不创建
    if (this.transformControl?.visible && this.transformControl.hasAxis) {
      this.mode = 'adjust';
      needsUpdate = true;
      if (this.editObject) {
        this.editObject.userData.needsUpdate = needsUpdate;
      }
      return needsUpdate;
    }

    this.debounceSetIndicate.cancel();
    this.updatePointer(event);
    this.updateRay(this.pointer3);

    // 可以新建点的条件：1.当前没有 editObject 或 2.有 editObject 时鼠标未点到当前 editObject 且鼠标点在其他有点云的地方
    if (!this.editObject || (this.editObject && !this.isHovered())) {
      this.setIndicate(this.pointer3);

      if (this.indicatePoint.visible && this.onObjectBegin(this.type === 'point3d')) {
        this.mode = 'create';

        // 添加点增加 seriesPoint
        const indicateLine = this.indicateRange?.name ?? '';
        this.editObject = Point3d.create(this.indicatePoint.position.toArray(), {
          color: OBJECT_EDIT_YELLOW,
          canvasSize: [this.stage.width, this.stage.height],
          seriesPoint: this.type === 'series-point3d' ? { isAdsorb: true, indicateLine } : undefined,
        });
        this.selectDown = false;

        if (indicateLine) {
          // 将 indicateLine 传递给 onObjectMount
          point3dMountOpt['series-point3d'].indicateLine = indicateLine;
          // 创建时添加点加入到引导线中
          this.stage.currentSight?.addObjectFromTool(this.type, this.editObject, indicateLine);
        } else {
          this.stage.currentSight?.addObjectFromTool(this.type, this.editObject);
        }

        needsUpdate = true;
      }
    } else if (this.editObject) {
      // hover 到标注物上，准备拖动点
      if (this.isHovered()) {
        this.selectDown = true;
        needsUpdate = true;
      }
    }

    if (this.editObject) {
      this.editObject.userData.needsUpdate = needsUpdate;
    }

    return needsUpdate;
  }

  onPointerMove(event: PointerEvent) {
    // 拖动轴控制器时不创建
    if (this.transformControl?.dragging) return true;

    this.updatePointer(event);
    // 鼠标处于按下状态
    if (event.buttons > 0) {
      // 判断是否准备好拖动点
      if (this.selectDown) {
        this.debounceDragPoint(this.pointer3);
        return true;
      }
    } else {
      // 鼠标处于松开状态
      this.updateRay(this.pointer3);

      this.debounceSetIndicate(this.pointer3);
      if (!this.editObject) {
        return false;
      }

      return this.isHovered();
    }
  }

  isHovered() {
    if (!this.editObject) return false;

    const v = this.editObject.position;
    const found = _ray.distanceSqToPoint(v) < MOVE_DETECTION;
    if (found) {
      this.indicatePoint.position.copy(v);
      this.indicatePoint.visible = true;
      this.pointPicker.updateGroundByPoint(v);

      return true;
    }
    return false;
  }

  /**
   * 编辑状态更新添加点
   * @param isAdsorb
   * @param indicateLine
   */
  updateSeriesPoint(indicateLine?: string) {
    if (!this.editObject) return;
    this.stage.currentSight?.addObjectFromTool(this.type, this.editObject, indicateLine);
    this.updateTransformControls(this.editObject);
  }

  onPointerUp(event: PointerEvent) {
    // 拖拽点后更新位置给标注物
    if (this.editObject) {
      this.updateInfo(this.editObject);
      if (this.selectDown) {
        this.selectDown = false;
      }

      // 结束编辑，保存编辑的数据
      if (this.mode === 'adjust' && this.editObject.userData.needsUpdate) {
        if (this.editObject?.userData?.seriesPoint && this.indicateRange) {
          const { parent, userData, position } = this.editObject;
          const { indicateLine } = userData?.seriesPoint;
          const { vectors, lines } = this.indicateRange;
          const editPosition = Point3d.findClosestElement(
            this.stage.currentSight!.camera,
            position,
            vectors,
            lines,
            MOVE_DETECTION
          );
          if (editPosition && parent?.name !== indicateLine) {
            this.updateSeriesPoint(indicateLine);
          } else if (!editPosition && parent?.name !== 'container') {
            this.updateSeriesPoint();
          }
        }
        this.onObjectUpdate(this.editObject);
        this.editObject.userData.needsUpdate = false;
      }

      if (this.mode === 'create') {
        // 每画完一个点就结束新建
        this.mode = 'wait';

        if (!this.onObjectMount(this.editObject, point3dMountOpt[this.type as Point3dHandlerType])) {
          this.editObject.parent?.remove(this.editObject);
        }
        this.editObject = undefined;
      }
    }

    return true;
  }

  /**
   * 将顶点数据转换为 Vector3 对象和 Line3 对象
   * @param obj 3D对象
   * @returns { vectors, lines }
   */
  updateVectorsAndLines(obj: Object3D) {
    let vectors = [],
      lines = [];
    const positions = AnnoFactory.getVertexFromObject3D('line3d', obj);
    const len = Math.floor(positions.length / 3);
    for (let i = 0; i < len; i++) {
      vectors.push(new Vector3().fromArray(positions, i * 3));
      if (i > 0) {
        lines.push(new Line3(vectors[i - 1], vectors[i]));
      }
    }
    this.indicateRange = { vectors, lines, name: obj.name };
  }

  setEditObject(obj?: Object3D) {
    if (this.type === 'point3d') {
      super.setEditObject(obj);
      if (obj?.userData?.seriesPoint?.indicateLine) {
        const indicateLine = this.stage.currentSight?.getObjectByName(obj.userData.seriesPoint.indicateLine);
        indicateLine && this.updateVectorsAndLines(indicateLine);
      }
    } else if (this.type === 'series-point3d' && obj?.userData.type === 'line3d') {
      this.updateVectorsAndLines(obj);
    }
  }

  updateInfo(editObj: Mesh<BoxGeometry, MeshBasicMaterial>) {
    const { position } = editObj;

    this.onInfoChange('desc', {
      type: 'point',
      items: [
        { label: 'X', value: position.x },
        { label: 'Y', value: position.y },
        { label: 'Z', value: position.z },
      ].map(({ label, value }) => ({
        label,
        value: roundDecimals(value, 3),
      })),
    });
  }

  setEditObjectEffects(editObj?: Mesh<BoxGeometry, MeshBasicMaterial>) {
    this.updateTransformControls(editObj);
    if (editObj) {
      this.updateInfo(editObj);
    }
  }

  updateEditVectors() {}

  updatePointer(event: PointerEvent | MouseEvent) {
    this.pointer3.set((event.offsetX / this.stage.width) * 2 - 1, -(event.offsetY / this.stage.height) * 2 + 1, 0);
  }

  updateRay(ndcPoint: Vector3) {
    const camera = this.stage.currentSight!.camera;
    if (camera instanceof PerspectiveCamera) {
      _ray.origin.setFromMatrixPosition(camera.matrixWorld);
      _ray.direction.set(ndcPoint.x, ndcPoint.y, 0.5).unproject(camera).sub(_ray.origin).normalize();
    } else if (camera instanceof OrthographicCamera) {
      _ray.origin
        .set(ndcPoint.x, ndcPoint.y, (camera.near + camera.far) / (camera.near - camera.far))
        .unproject(camera);
      _ray.direction.set(0, 0, -1).transformDirection(camera.matrixWorld);
    }
  }

  /**
   * 设置引导点坐标
   * @param end
   */
  setIndicate(end: Vector3) {
    let realEnd = null;
    // 线上寻找
    if (this.indicateRange) {
      const vid = this.indicateRange.vectors.findIndex((v) => _ray.distanceSqToPoint(v) < MOVE_DETECTION);
      // 先找端点
      if (vid >= 0) {
        realEnd = this.indicateRange.vectors[vid];
      } else {
        // 遍历折线所有线段
        const lid = this.indicateRange.lines.findIndex(
          (line) => _ray.distanceSqToSegment(line.start, line.end, undefined, _lineClosest) < MOVE_DETECTION
        );
        if (lid >= 0) {
          realEnd = _lineClosest;
        }
      }
    } else {
      // 点云中寻找
      realEnd = this.pointPicker.findNearestPoint(end);
    }

    this.onPointerChange(realEnd);

    // 更新引导点
    if (realEnd) {
      this.indicatePoint.position.copy(realEnd);
      if (!this.indicatePoint.visible) {
        this.indicatePoint.visible = true;
      }
    } else if (this.indicatePoint.visible) {
      this.indicatePoint.visible = false;
    }
  }

  dragPoint(ndcPoint: Vector3) {
    if (!this.editObject) {
      return true;
    }

    if (this.indicatePoint.visible) {
      this.indicatePoint.visible = false;
    }
    let target = this.pointPicker.findNearestPoint(ndcPoint);

    if (!target) {
      target = this.pointPicker.getPointInPlane(ndcPoint, _point);
    }

    this.onPointerChange(target);

    target && Point3d.update(this.editObject, target.toArray());
    this.updateInfo(this.editObject);
  }

  destroy() {
    this.stage.currentSight?.scene.remove(this.indicatePoint);
    this.indicateRange = null;
    this.transformControl.dispose();
  }
}
