import { DebouncedFunc, throttle } from 'lodash';
import {
  BoxGeometry,
  EdgesGeometry,
  MathUtils,
  Mesh,
  MeshBasicMaterial,
  Object3D,
  Raycaster,
  Vector2,
  Vector3,
} from 'three';

import { FatLine, LineMaterial, LineSegmentsGeometry, LineSegmentsMesh } from '@/lib/three';
import { AnnoFactory, Cuboid, Line3d } from '@/utils/annos';
import { convexHull2 } from '@/utils/math';
import { projectMouseToPlane, Sight, type Stage } from '@/utils/render';
import { ToolType } from '@/utils/tool';
import { getMinBoxByRotatingCaliper } from '@/utils/transform';

import { HandlerOptions, IInteractEvent } from '..';

const WAIT_TIME = 10;
const BOX_MIN_SCALE = 1;
const INDICATE_WHITE = '#FFFFFF';
const WIDGET_CAN_MOVE = ['line3d'];
const _pointer = new Vector2();
const AXIS_Z = new Vector3(0, 0, 1);

// 计算 P1 和 P2 两个点连成的直线相对于水平直线的旋转角度
function getRotationAngle(P1: [number, number], P2: [number, number]) {
  const x = P1[0] - P2[0];
  const y = P1[1] - P2[1];
  const angle = Math.atan2(y, x);
  return angle;
}

// 调整 boundingBox 去包围 target
function fitBoundingBox(target: Object3D, boundingBox: Object3D) {
  const positions = AnnoFactory.getVertexFromObject3D(target.userData.type, target);
  if (!positions?.length) return;
  // 取各点的 x 和 y 值用于计算凸包
  const points: [number, number][] = [];
  let minZ = Number.MAX_SAFE_INTEGER;
  let maxZ = Number.MIN_SAFE_INTEGER;
  for (let i = 0; i < positions.length; i += 3) {
    points.push([positions[i], positions[i + 1]]);
    minZ = Math.min(minZ, positions[i + 2]);
    maxZ = Math.max(maxZ, positions[i + 2]);
  }

  // 计算凸包
  const hull = convexHull2([...points]);
  if (hull) {
    // 使用旋转卡壳法找最小面积矩形
    const { angle, width, height, center } = getMinBoxByRotatingCaliper(hull);
    boundingBox.quaternion.setFromAxisAngle(AXIS_Z, angle);
    boundingBox.scale.fromArray([width, height, maxZ - minZ]);
    boundingBox.position.fromArray([...center, (maxZ + minZ) / 2]);
  } else {
    // 不足 3 个点或者所有的点共线，根据两个点的连线方向决定选择框的方向
    const angle = getRotationAngle(points[0], points[points.length - 1]);
    boundingBox.quaternion.setFromAxisAngle(AXIS_Z, angle);

    // 计算指定旋转方向的最小面积矩形的 position 和 scale
    const { position, scale } = Cuboid.getMinCubeFromPoints(
      (hull || points).map(([x, y], idx) => new Vector3(x, y, idx === 0 ? minZ : maxZ)),
      boundingBox.quaternion
    );

    // 更新 boundingBox 的 scale 和 position
    boundingBox.scale.copy(scale);
    boundingBox.position.copy(position);
  }

  // 确保达到最小的宽度
  if (boundingBox.scale.x < BOX_MIN_SCALE) {
    boundingBox.scale.x = BOX_MIN_SCALE;
  }
  if (boundingBox.scale.y < BOX_MIN_SCALE) {
    boundingBox.scale.y = BOX_MIN_SCALE;
  }
}

/**
 * 为标注物创建一个包围盒
 * @param target
 * @param canvasSize
 * @returns Mesh 对象，包含了表示包围盒的线框网格和实体网格
 */
function createSelectionBox(target: Object3D, canvasSize: [number, number]): Mesh {
  // 创建选择框
  const selectionBox = new Mesh(
    new BoxGeometry(),
    new MeshBasicMaterial({ color: 0x000000, wireframe: false, opacity: 0, transparent: true })
  );
  selectionBox.name = 'selectionBox';
  fitBoundingBox(target, selectionBox);

  // 边框
  const edgesGeometry = new LineSegmentsGeometry();
  edgesGeometry.fromEdgesGeometry(new EdgesGeometry(selectionBox.geometry));
  const selectionBoxEdges = new LineSegmentsMesh(
    edgesGeometry,
    new LineMaterial({
      linewidth: 1.5,
      color: '#fff',
      opacity: 0.5,
      transparent: true,
      resolution: new Vector2(...canvasSize),
    })
  );
  selectionBox.add(selectionBoxEdges);
  return selectionBox;
}

export class MoveHandler implements IInteractEvent {
  public type: ToolType;
  public stage: Stage;

  private object?: Object3D;

  // 鼠标落下位置相对 objectCenter 的偏移量
  private displacement?: [number, number];
  private startPoint?: [number, number, number];

  // 引导线
  private indicateLine: FatLine;
  private selectionBox?: Mesh;

  private raycaster: Raycaster;

  private debounceMoveObject: DebouncedFunc<(x: number, y: number) => void>;

  /**
   * 结束创建标注物的回调
   * @param obj 创建对象
   * @param fromSelect 是否来自 selectedAnnos：true 来自所选，false 直接创建（默认值为 false）
   * @returns 对象是否符合要求：true 符合要求，false 不符合要求
   */
  public onObjectMount: Required<HandlerOptions>['onObjectMount'];
  /**
   * 更新标注物的回调
   * @param obj 更新对象
   * @returns 是否成功更新数据
   */
  public onObjectUpdate: Required<HandlerOptions>['onObjectUpdate'];

  constructor(stage: Stage, options: HandlerOptions) {
    this.type = 'select';
    const { onObjectUpdate, onObjectMount } = options;
    this.stage = stage;
    this.onObjectUpdate = onObjectUpdate ?? (() => true);
    this.onObjectMount = onObjectMount ?? (() => true);
    this.debounceMoveObject = throttle(this.moveObject, WAIT_TIME);
    this.raycaster = new Raycaster();

    // 创建引导线
    this.indicateLine = Line3d.create([0, 0, 0, 0, 0, 0], {
      color: INDICATE_WHITE,
      canvasSize: [this.stage.width, this.stage.height],
    });
    this.indicateLine.material.linewidth = 1;
    this.indicateLine.material.transparent = true;
    this.indicateLine.material.opacity = 0.7;
    this.indicateLine.visible = false;

    // 设置引导元素的图层
    const layer = Sight.ANNO_LAYER;
    this.indicateLine.layers.set(layer);
  }

  onClick(event: MouseEvent) {}
  onPointerDown(event: PointerEvent | MouseEvent) {
    if (!this.object) return;
    // alt 拖动复制，先原地 clone，然后移动 clone 出来的
    if (event.altKey) {
      this.cloneObject(this.object);
    }

    const camera = this.stage.currentSight?.camera;
    if (!camera) return;

    // 记录此时鼠标落下的点相对 objectCenter 的偏移
    const objectCenter = AnnoFactory.getAnnoCenter(this.object) || this.object.position;

    // 获取鼠标当前在指定 z 平面的三维坐标
    const x = (event.offsetX / this.stage.width) * 2 - 1;
    const y = -(event.offsetY / this.stage.height) * 2 + 1;
    const startPosition = projectMouseToPlane(camera, x, y, objectCenter.z);
    if (!startPosition) return;

    this.displacement = [startPosition[0] - objectCenter.x, startPosition[1] - objectCenter.y];
    // 显示引导线，startPosition 作为引导线的起始点
    this.updateIndicateLine(startPosition, startPosition);
    if (!this.indicateLine.visible) {
      this.indicateLine.visible = true;
    }
    this.startPoint = startPosition;
  }

  onPointerMove(event: PointerEvent | MouseEvent) {
    // 移动，更新位置
    this.debounceMoveObject(event.offsetX, event.offsetY);
  }

  private moveObject(x: number, y: number) {
    if (!this.object || !this.displacement) return;
    const camera = this.stage.currentSight?.camera;
    if (!camera) return;

    const z = (AnnoFactory.getAnnoCenter(this.object) || this.object.position).z;
    x = (x / this.stage.width) * 2 - 1;
    y = -(y / this.stage.height) * 2 + 1;
    const position = projectMouseToPlane(camera, x, y, z);
    if (position) {
      // 更新引导线
      if (this.startPoint) {
        this.updateIndicateLine(this.startPoint, position);
      }
      // 移动选择框
      this.updateSelectionBox(this.object, [position[0] - this.displacement[0], position[1] - this.displacement[1], z]);
      // 移动标注物
      AnnoFactory.changePosition(this.object, [
        position[0] - this.displacement[0],
        position[1] - this.displacement[1],
        z,
      ]);
    }
  }

  onPointerUp(event: PointerEvent | MouseEvent) {
    if (!this.object || !this.displacement) return;

    // 移除引导线
    this.indicateLine.visible = false;

    // 结束移动，更新数据
    this.onObjectUpdate(this.object);
  }

  onMouseEnter(event: MouseEvent) {}
  onMouseLeave(event: MouseEvent) {}

  onKeyDown(event: KeyboardEvent) {}
  onKeyUp(event: KeyboardEvent) {}

  /**
   * 设置移动的对象
   * @param obj
   * @returns
   */
  public setEditObject(obj?: Object3D) {
    if (obj && WIDGET_CAN_MOVE.includes(obj.userData.type)) {
      this.object = obj;

      if (!this.selectionBox) {
        // 创建选择框
        this.selectionBox = createSelectionBox(obj, [this.stage.width, this.stage.height]);
      } else {
        // 更新选择框位置，选中当前
        fitBoundingBox(obj, this.selectionBox);
      }

      this.stage.currentSight?.scene.add(this.selectionBox);
      this.stage.currentSight?.scene.add(this.indicateLine);
    } else {
      this.object = undefined;

      // 没有选中的，隐藏选择框
      if (this.selectionBox) {
        this.stage.currentSight?.scene.remove(this.selectionBox);
      }
      this.stage.currentSight?.scene.remove(this.indicateLine);
    }
  }

  public getObject() {
    return this.object;
  }

  /**
   * 克隆标注物到原始位置（用于拖动复制，不保存到剪切板）
   * @param obj
   */
  private cloneObject(obj: Object3D) {
    const uuid = MathUtils.generateUUID();
    const newObj = obj.clone();
    newObj.name = uuid;
    this.onObjectMount(newObj, { isClone: true });
  }

  private updateIndicateLine(start: [number, number, number], end: [number, number, number]) {
    Line3d.update(this.indicateLine, [...start, ...end]);
  }

  private updateSelectionBox(target: Object3D, position: [number, number, number]) {
    if (!this.selectionBox) return;
    const vertexData = AnnoFactory.getVertexFromObject3D(target.userData.type, target);
    const deltaX = this.selectionBox.position.x - vertexData[0];
    const deltaY = this.selectionBox.position.y - vertexData[1];
    this.selectionBox.position.setX(position[0] + deltaX);
    this.selectionBox.position.setY(position[1] + deltaY);
  }

  public intersectCurrentObject(x: number, y: number) {
    if (!this.stage.currentSight?.camera || !this.object || !this.selectionBox) return false;

    // 将鼠标位置归一化为-1到1的值
    _pointer.x = (x / this.stage.width) * 2 - 1;
    _pointer.y = -(y / this.stage.height) * 2 + 1;

    // 更新raycaster
    this.raycaster.setFromCamera(_pointer, this.stage.currentSight.camera);

    // 进行拾取检测
    const intersects = this.raycaster.intersectObject(this.selectionBox, false);

    if (intersects.length) {
      return true;
    }
    return false;
  }

  /**
   * 废弃当前 handler
   */
  destroy() {
    AnnoFactory.dispose(this.indicateLine);
    if (this.selectionBox) {
      AnnoFactory.dispose(this.selectionBox);
    }
  }
}
