import { Intersection, Raycaster, Vector2, Vector3 } from 'three';

import { CSS2DObject } from '@/lib/three';
import { HorizontalCaliper3d, Line3d, VerticalCaliper3d } from '@/utils/annos';
import { Sight, Stage } from '@/utils/render';

import { HandlerOptions } from './EventHandler';
import { Line3dHandler } from './Line3dHandler';

const INDICATE_WHITE = '#FFFFFF';
const COMPLETE_COLOR = '#6fc6f9';

const caliperMap = {
  'caliper-h': HorizontalCaliper3d,
  'caliper-v': VerticalCaliper3d,
};

interface CustomIntersection extends Intersection {
  pointOnLine?: Vector3;
}

export class DirectionCaliper3dHandler extends Line3dHandler {
  private label;
  private direction;
  private CaliperClass;
  private raycaster;

  /**
   * 量尺
   * @param type
   * @param stage 需确保当前 currentSight 不为空
   * @param options
   */
  constructor(type: 'caliper-h' | 'caliper-v', stage: Stage, options: HandlerOptions) {
    super(type, stage, options, {
      canDelete: false,
      canAdjust: false,
      canAdd: false,
    });
    this.direction = type;
    this.CaliperClass = caliperMap[type];
    this.raycaster = new Raycaster();

    const text = document.createElement('div');
    text.className = 'tool-caliper-label-cursor-div';
    this.label = new CSS2DObject(text);

    text.id = this.CaliperClass.getScopedId(this.label);

    this.stage.currentSight?.scene.remove(this.indicateLine);
    this.indicateLine = this.CaliperClass.create([0, 0, 0, 0, 0, 0], { color: INDICATE_WHITE });
    this.indicateLine.add(this.label);
    this.indicateLine.visible = false;

    // 设置引导元素的图层
    const layer = Sight.getLayerFromTool(type);
    this.indicatePoint.layers.set(layer);
    this.indicateLine.layers.set(layer);

    this.stage.currentSight?.scene.add(this.indicatePoint, this.indicateLine);
    this.Line = this.CaliperClass;
    this.stage.setCursor('ruler');
  }

  onPointerMove(event: PointerEvent) {
    this.updatePointer(event);

    // 鼠标处于按下状态
    if (event.buttons > 0) {
      return true;
    } else {
      // 鼠标处于松开状态

      // 尚未创建线条
      if (!this.editObject || this.vectors.length === 0) {
        this.debounceSetIndicate(undefined, this.pointer3);
        return false;
      }

      // 如果当前显示轴控制器且有响应
      if (this.transformControl.isActive) {
        return true;
      }

      if (this.isEndPoint(this.activePointId) && this.mode === 'create') {
        if (this.vectors.length < 2) {
          this.debounceSetIndicate(this.vectors[this.activePointId], this.pointer3);
        } else {
          this.disableIndicateLine();
        }
        return true;
      }

      return true;
    }
  }

  /**
   * 设置引导线点坐标
   * @param start
   * @param end
   */
  setIndicate(start: Vector3 | undefined, end: Vector3) {
    let realEnd;
    if (this.stage.currentSight) {
      const sight = this.stage.currentSight;
      this.raycaster.setFromCamera(new Vector2(end.x, end.y), sight.camera);
      const intersects = this.raycaster.intersectObjects(sight.container.children, false);
      if (intersects?.length) {
        realEnd = (intersects[0] as CustomIntersection).pointOnLine;
      }
    }

    if (!realEnd) {
      realEnd = this.pointPicker.findNearestPoint(end) ?? this.pointPicker.getPointInPlane(end);
    }
    this.onPointerChange(realEnd);

    // 更新引导点
    if (realEnd) {
      this.indicatePoint.position.copy(realEnd);
      if (!this.indicatePoint.visible) {
        this.indicatePoint.visible = true;
      }
    } else if (this.indicatePoint.visible || this.indicateLine.visible) {
      this.indicatePoint.visible = false;
      this.indicateLine.visible = false;
    }

    if (start && realEnd) {
      this.updateIndicateLine(start, realEnd);
    }
  }

  updateIndicateLine(start: Vector3, end: Vector3) {
    if (!this.indicateLine.visible) {
      this.indicateLine.visible = true;
    }
    Line3d.update(this.indicateLine, [start.x, start.y, start.z, end.x, end.y, end.z]);
    this.label.position.set(end.x, end.y, end.z);

    if (this.direction === 'caliper-h') {
      const distance = Math.sqrt(Math.pow(end.x - start.x, 2) + Math.pow(end.y - start.y, 2));
      this.label.element.innerText = `水平距离：${distance.toFixed(2)}m`;
    } else {
      const distance = Math.abs(end.z - start.z);
      this.label.element.innerText = `垂直距离：${distance.toFixed(2)}m`;
    }
  }

  /**
   * 取消引导线
   */
  disableIndicateLine() {
    if (this.editObject) {
      Line3d.changeColor(this.editObject, COMPLETE_COLOR);
    }
    super.disableIndicateLine();
  }

  destroy() {
    if (this.editObject) {
      this.CaliperClass.clear2DDom(this.editObject);
      this.editObject.parent?.remove(this.editObject);
    }
    this.CaliperClass.clear2DDom(this.indicateLine);
    this.stage.currentSight?.scene.remove(this.indicatePoint, this.indicateLine);
    this.stage.setCursor('default');
  }
}
