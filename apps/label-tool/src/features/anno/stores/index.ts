import { createContext, useContext } from 'react';
import { TemporalState } from 'zundo';
import { StoreApi } from 'zustand';
import { shallow } from 'zustand/shallow';
import { useStoreWithEqualityFn } from 'zustand/traditional';

import { frameAnnoStore } from './frameAnnoStore';
import { laneAnnoStore } from './laneAnnoStore';
import { TemporalAnnoState } from './utils';

export * from './frameAnnoStore';
export * from './laneAnnoStore';
export * from './selectors';

export type StoreType = typeof laneAnnoStore | typeof frameAnnoStore;

type UseContextStore<S extends StoreApi<unknown>> = {
  (): ExtractState<S>;
  <U>(selector: (state: ExtractState<S>) => U, equalityFn?: (a: U, b: U) => boolean): U;
};

type ExtractState<S> = S extends { getState: () => infer T } ? T : never;

export const StoreContext = createContext<StoreType | undefined>(undefined);
export const StoreProvider = StoreContext.Provider;

export const useContextStore: UseContextStore<StoreType> = <StateSlice = ExtractState<StoreType>>(
  selector?: (state: ExtractState<StoreType>) => StateSlice,
  equalityFn: (a: StateSlice, b: StateSlice) => boolean = shallow
) => {
  const store = useContext(StoreContext);
  if (!store) {
    throw new Error('Seems like you have not used StoreProvider as an ancestor.');
  }
  return useStoreWithEqualityFn(store, selector as (state: ExtractState<StoreType>) => StateSlice, equalityFn);
};

/** 撤销重做相关的数据类型 */
type TemporalStoreType = TemporalState<TemporalAnnoState<ExtractState<StoreType>>>;
/**
 * 使用 撤销 / 重做
 * @param selector
 * @param equality
 * @returns
 */
export const useTemporalStore = (
  selector: (state: TemporalStoreType) => TemporalStoreType,
  equality?: (a: TemporalStoreType, b: TemporalStoreType) => boolean
) => {
  const store = useContext(StoreContext);
  if (!store) {
    throw new Error('Seems like you have not used StoreProvider as an ancestor.');
  }
  return useStoreWithEqualityFn(store.temporal, selector, equality);
};

export const useStoreApi = () => {
  const store = useContext(StoreContext);
  if (!store) {
    throw new Error('Seems like you have not used StoreProvider as an ancestor.');
  }
  return store;
};
