import { ZundoOptions } from 'zundo';

import { AnnoFlowStore } from './slices';

export type TemporalAnnoState<S extends AnnoFlowStore> = Pick<S, 'currentAnno'>;

export const getTemporalOptions = <T extends AnnoFlowStore>(): ZundoOptions<T, TemporalAnnoState<T>> => {
  return {
    limit: 32,

    partialize: (state) => {
      const { currentAnno } = state;
      return { currentAnno };
    },

    diff: (pastState, currentState) => {
      if (pastState.currentAnno === currentState.currentAnno) return null;

      if (currentState.currentAnno === null) return null;

      return currentState;
    },
    undoFormat: (data) => {
      let result: Partial<T> = { ...data };
      if (!result.currentAnno) return data;

      const { currentAnno } = result;
      result.currentAnno = { ...currentAnno };
      result.currentAnno.source = 'undo';

      switch (currentAnno.opt) {
        case 'add':
          result.currentAnno.opt = 'remove';
          break;
        case 'remove':
          result.currentAnno.opt = 'add';

          break;

        case 'update':
          if (currentAnno.prevWidget) {
            result.currentAnno.widget = result.currentAnno.prevWidget;
            result.currentAnno.prevWidget = undefined;

            result.currentAnno.seriesPoints = result.currentAnno.prevSeriesPoints;
            result.currentAnno.prevSeriesPoints = undefined;
          } else {
            result.currentAnno = null;
          }
          break;
      }

      return result;
    },
    redoFormat: (data) => {
      let result: Partial<T> = { ...data };
      if (!result.currentAnno) return data;

      const { currentAnno } = result;
      result.currentAnno = { ...currentAnno };
      result.currentAnno.source = 'undo';
      return result;
    },
  };
};
