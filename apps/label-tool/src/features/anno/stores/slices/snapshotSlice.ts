import stringify from 'fast-json-stable-stringify';
import { omit } from 'lodash';
import xxhash from 'xxhash-wasm';
import { StateCreator } from 'zustand';

import { type ICache, type ISnapshot, type JobStore, type LotStore, type SegLabelStore } from '@/stores';
import { type Job, LabelDataSaveType, type Lot } from '@/types';
import { AnnoManager, annoTrackIdGenerator, IAnnoItem } from '@/utils/annos';
import { LRUCache } from '@/utils/cache';
import { type Segmentations, SseEditor } from '@/utils/render';

import { AnnoStore } from './annoSlice';
import type { CommentStore, CommentStoreObj } from './commentSlice';
import type { ElementStore } from './elementSlice';
import type { RenderStore } from './renderSlice';
import type { SegmentStore } from './segmentSlice';

export interface SnapshotSlice {
  annos?: Pick<ElementStore, 'elementAnnos' | 'elementAttrValues'>;
  comments: Pick<CommentStoreObj, 'resolveComments' | 'commentMap'>;
  segmentation3d?: Segmentations;
}

export interface SnapshotData extends ICache, SnapshotSlice {}

interface JobSnapshot extends SnapshotSlice {
  version: string;
}

/**
 * 对 ElementStore & CommentStore 生成快照，存储至 localStorage ，并用 LRU Cache 管理缓存
 */
export interface SnapshotStore extends ISnapshot<SnapshotData> {
  /**
   * 根据缓存初始化数据
   * @returns 是否初始化成功
   */
  initFromSnapshot: (job: Job, lot: Lot, userId: string) => boolean;

  createSnapshot: () => void;
  clearSnapshot: () => void;
  exportSnapshot: () => SnapshotSlice;

  getSnapshot: () => SnapshotData | null;

  snapshotHash: string;
  diffSnapshotHash: () => Promise<SnapshotSlice | undefined>;
}

const initialState = {
  snapshotId: '',
  snapshotCache: new LRUCache<string, SnapshotData>(),
  snapshotHash: '',
};

let hasherPromise: Promise<any> | null = null;

const hashLargeObject = async (obj: any): Promise<string> => {
  if (!hasherPromise) {
    hasherPromise = xxhash();
  }
  const hasher = await hasherPromise;
  return hasher.h64ToString(stringify(obj));
};

export const createSnapshotSlice: StateCreator<
  JobStore &
    LotStore &
    ElementStore &
    CommentStore &
    SnapshotStore &
    AnnoStore &
    Partial<SegmentStore & SegLabelStore> &
    RenderStore,
  [],
  [],
  SnapshotStore
> = (set, get) => ({
  ...initialState,

  initFromSnapshot: (job, lot, userId) => {
    const {
      clearSnapshot,
      initAnnotations,
      initComments,
      initElementsStateAndAttrs,
      setJobLabelDataInfo,
      setJobDraftTime,
      exportSnapshot,
    } = get();
    const snapshotId = `${lot.uid}_${job.uid}_${userId}`;
    set({ snapshotId });
    const initFromSnap = ({ annos, comments }: SnapshotData | JobSnapshot) => {
      if (annos) {
        const { elementAnnos } = annos;

        const onCreateAnno = (item: IAnnoItem | null) => {
          if (item) {
            const trackId = Number(item.trackId ?? 0);
            if (trackId > annoTrackIdGenerator.getCurrent()) {
              annoTrackIdGenerator.setCurrent(trackId);
            }
          }
        };

        for (const ele in elementAnnos) {
          const originData = elementAnnos[ele];
          elementAnnos[ele] = new AnnoManager({
            annotations: AnnoManager.transInAnnoClass(originData.annotations, onCreateAnno),
            labelAnnoMap: originData.labelAnnoMap,
            fileAnnoMap: originData.fileAnnoMap,
          });
        }
        set({
          elementAnnos,
          ...elementAnnos[0].getAnnotations(),
        });
      } else {
        // 根据标注数据初始化 trackId，必须保证在 AnnoInstanceItem 创建之前调用
        annoTrackIdGenerator.initTrackId(job.annotations);
        initAnnotations(job);
      }
      initElementsStateAndAttrs(job, lot, annos);
      initComments(job, lot, comments);
    };

    // 为了删除上一版本的本地保存功能的缓存数据。2024年8月22日之后可以删除这一行。
    window.localStorage.removeItem('elementAnno & comments-localStorage');

    const draftTime = (job?.snapshot as JobSnapshot)?.version ?? '';
    const serverTime = job?.last_modify ?? '';

    // 远程数据中的最新保存时间
    const latestRemoteTime = serverTime > draftTime ? serverTime : draftTime;

    const snapshotSlice = omit(job.snapshot as JobSnapshot, ['version']);
    const initSegmentFromSnap = (snapshot: SnapshotSlice | undefined, draftTime: string) => {
      if (!lot?.tool_cfg?.segmentation_3d_enabled || !snapshot) return;
      setTimeout(async () => {
        SseEditor.initElementSegments(snapshot?.segmentation3d);
        setJobDraftTime(draftTime);
        const snapshotHash = await hashLargeObject(exportSnapshot());
        set({ snapshotHash });
      }, 0);
    };

    // 无任何远程保存数据
    if (!latestRemoteTime) {
      setJobLabelDataInfo(LabelDataSaveType.NONE, '');
      return false;

      // 使用服务端缓存
    } else if (latestRemoteTime === draftTime) {
      setJobLabelDataInfo(LabelDataSaveType.DRAFT, latestRemoteTime);
      initFromSnap(job.snapshot as JobSnapshot);

      initSegmentFromSnap(snapshotSlice, latestRemoteTime);

      clearSnapshot();
      return true;

      // 不使用缓存
    } else {
      setJobLabelDataInfo(LabelDataSaveType.SERVER, latestRemoteTime);
      clearSnapshot();
      return false;
    }
  },

  exportSnapshot: () => {
    const { elementAnnos, elementAttrValues, resolveComments, commentMap, currentPhase } = get();

    let annos: SnapshotData['annos'] = undefined;
    let segmentation3d: Segmentations = [];
    // 如果有编辑权限，则允许保存标注物信息；无论何时。批注信息都是需要保存本地的
    if (!currentPhase || currentPhase.editable) {
      annos = { elementAnnos, elementAttrValues };
      for (const ele in elementAnnos) {
        const segmentation = SseEditor.segmentData[ele];
        if (!segmentation?.statistic.length) continue;
        const { category, instance_id, statistic } = segmentation;
        segmentation3d[ele] = {
          result: { category, instance_id },
          statistic,
        };
      }
    }

    return {
      annos,
      comments: {
        resolveComments,
        commentMap,
      },
      segmentation3d,
    };
  },

  createSnapshot: () => {
    // local storage空间有限，暂时comment掉以下代码，disable本地缓存标注结果的功能。
    // TODO：彻底去除本地缓存Snapshot相关的代码, 并把3D分割的定时自动保存功能应用于所有类型任务。
    // const { snapshotCache, snapshotId, exportSnapshot, setJobLabelDataInfo } = get();
    // const { annos, comments } = exportSnapshot();
    // snapshotCache.put(snapshotId, {
    //   annos,
    //   comments,
    //   saveTime: new Date().toISOString(),
    // });
    // // 只更新最新的存储位置
    // setJobLabelDataInfo(LabelDataSaveType.LOCAL);
    // set({
    //   snapshotCache,
    // });
  },

  getSnapshot() {
    const { snapshotId, snapshotCache } = get();
    return snapshotCache.get(snapshotId) ?? null;
  },

  clearSnapshot: () => {
    const { snapshotId, snapshotCache } = get();
    snapshotCache.delete(snapshotId);
    set({
      snapshotCache,
    });
  },

  diffSnapshotHash: async () => {
    const { snapshotHash: hash, exportSnapshot } = get();
    const snapshot = exportSnapshot();
    const snapshotHash = await hashLargeObject(snapshot);
    if (snapshotHash !== hash) {
      set({ snapshotHash });
      return snapshot;
    }
  },
});
