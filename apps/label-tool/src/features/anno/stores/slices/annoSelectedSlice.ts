import { StateCreator } from 'zustand';

import { IAnnoItem } from '@/utils/annos';

import { AnnoFlowStore } from './annoFlowSlice';
import { CommentMissedStore } from './commentMissedSlice';

export type SelectedSource = 'LabelCatalog' | 'HotKey' | 'LabelScene' | 'ImageScene' | 'CommentCatalog';

export interface IAnnoSelected {
  selectedAnnos: IAnnoItem[];
  selectedSource?: SelectedSource;
}

export interface AnnoSelectedStore extends IAnnoSelected {
  // 新增加一个选中的标注物
  addSelectedAnno: (anno: IAnnoItem) => void;
  // 移除一个选中的标注物
  removeSelectedAnno: (anno: IAnnoItem) => void;
  // 设置选中的标注物及来源
  setSelectedAnnos: (selectedAnnos: IAnnoItem[], source?: SelectedSource) => void;
  // 多选模式下更新单个标注的状态：选中/取消选中
  updateSelectedAnno: (anno: IAnnoItem) => void;
}

const initialState = {
  selectedAnnos: [],
};

export const createAnnoSelectedSlice: StateCreator<
  AnnoSelectedStore & AnnoFlowStore & CommentMissedStore,
  [],
  [],
  AnnoSelectedStore
> = (set, get) => ({
  ...initialState,
  addSelectedAnno: (anno) => {
    set((state) => {
      const { selectedAnnos } = state;
      if (selectedAnnos.includes(anno)) {
        return state;
      }
      return { selectedAnnos: [...selectedAnnos, anno], editAnno: null, selectedComment: null };
    });
  },
  removeSelectedAnno: (anno) => {
    set((state) => {
      const { selectedAnnos } = state;
      const index = selectedAnnos.findIndex((item) => item.name === anno.name);
      if (index === -1) {
        return state;
      }
      selectedAnnos.splice(index, 1);
      return { selectedAnnos: [...selectedAnnos] };
    });
  },
  setSelectedAnnos: (selectedAnnos, source?) => {
    const { selectedAnnos: oldSelectedAnnos } = get();

    if (selectedAnnos.length === 0) {
      set({
        selectedAnnos: oldSelectedAnnos.length === 0 ? oldSelectedAnnos : [],
        selectedSource: source,
      });
    } else {
      set({
        selectedAnnos,
        selectedSource: source,
        editAnno: null,
        selectedComment: null,
      });
    }
  },

  updateSelectedAnno: (anno) => {
    set((state) => {
      const { selectedAnnos } = state;
      const index = selectedAnnos.findIndex((item) => item.name === anno.name);
      if (index === -1) {
        return { selectedAnnos: [...selectedAnnos, anno], editAnno: null, selectedComment: null };
      } else {
        selectedAnnos.splice(index, 1);
        return { selectedAnnos: [...selectedAnnos] };
      }
    });
  },
});
