import { pick } from 'lodash';
import type { StateCreator } from 'zustand';

import { LabelStore } from '@/stores/labelSlice';
import { Label, Rawdata } from '@/types';
import { AnnoCompoundItem, AnnoInstanceItem, IAnnoItem, IAnnotation } from '@/utils/annos';

import { AnnoFlowStore } from './annoFlowSlice';
import { AnnoSelectedStore } from './annoSelectedSlice';
import { ElementStore } from './elementSlice';

/**
 * 单帧标注数据，值与 elementAnnos[dataElementIndex] 完全一致，主要用于触发组件变更
 */
export interface AnnoStore extends IAnnotation {
  getAnnoInstanceItem: (name?: AnnoInstanceItem['name']) => AnnoInstanceItem | null;
  getAnnosByFile: (fileName: Rawdata['name']) => Array<AnnoInstanceItem>;
  getAnnosByTrackId: (trackId: IAnnoItem['trackId']) => Record<Rawdata['name'], IAnnoItem> | undefined;
  // TODO: 迁移到 AnnoManager 中
  getPrevOrNextAnno: (type: 'next' | 'prev') => void;
  // TODO: 迁移到 AnnoManager 中 未去重，需手动去重
  getAnnoInstanceNames: (item: IAnnoItem) => Array<string>;

  getAnnotations: () => IAnnotation;
  setAnnotations: (annos?: IAnnotation | null) => void;

  addAnnoObject: (item: IAnnoItem, fileName: string) => void;
  updateAnnoObject: (item: IAnnoItem) => void;
  removeAnnoObject: (item: IAnnoItem, fileName: string) => void;
  updateAnnosInLabelMap: (
    annoNameList: Array<IAnnoItem['name']>,
    oldLabelName: Label['name'],
    newLabelName: Label['name']
  ) => void;
}

const initialState: IAnnotation = {
  annotations: {},
  labelAnnoMap: {},
  fileAnnoMap: {},
};

export const createAnnoSlice: StateCreator<
  AnnoStore & LabelStore & AnnoFlowStore & AnnoSelectedStore & ElementStore,
  [],
  [],
  AnnoStore
> = (set, get) => ({
  ...initialState,

  getAnnotations: () => pick(get(), ['annotations', 'labelAnnoMap', 'fileAnnoMap']),

  getAnnoInstanceItem: (name) => {
    const { elementAnnos, dataElementIndex } = get();
    return elementAnnos[dataElementIndex].getAnnoInstanceItem(name);
  },

  getAnnosByFile: (fileName) => {
    const { elementAnnos, dataElementIndex } = get();

    return elementAnnos[dataElementIndex].getAnnosByFile(fileName);
  },

  getPrevOrNextAnno: (type) => {
    const { editAnno, annotations, selectedAnnos, setEditAnno, setSelectedAnnos } = get();
    if (!editAnno && selectedAnnos.length !== 1) return '';
    const strTrackId = editAnno?.anno.trackId ?? selectedAnnos[0].trackId;
    let anno = null,
      distance = Infinity,
      trackId = Number(strTrackId);
    for (const name in annotations) {
      if (annotations[name] === null) continue;
      if (annotations[name]!.trackId === strTrackId) continue;
      const curTrackId = Number(annotations[name]!.trackId);
      if (type === 'next' && curTrackId > trackId && curTrackId - trackId < distance) {
        anno = annotations[name];
        distance = curTrackId - trackId;
      } else if (type === 'prev' && curTrackId < trackId && trackId - curTrackId < distance) {
        anno = annotations[name];
        distance = trackId - curTrackId;
      }
    }
    if (anno) {
      // 正在编辑或修改属性的标注物
      if (editAnno) {
        setEditAnno({
          anno,
          type: editAnno.type,
        });
      } else {
        // 选中的标注物
        setSelectedAnnos([anno], 'HotKey');
      }
    }
  },

  getAnnosByTrackId: (trackId) => {
    const { elementAnnos, dataElementIndex } = get();
    return elementAnnos[dataElementIndex].getAnnosByTrackId(trackId);
  },

  getAnnoInstanceNames: (item) => {
    if (item instanceof AnnoInstanceItem) return [item.name];

    const { annotations, getAnnoInstanceNames } = get();

    let names: Array<string> = [];
    if (item instanceof AnnoCompoundItem) {
      item.compound.forEach((com) => {
        if (annotations[com]) {
          names = names.concat(getAnnoInstanceNames(annotations[com]!));
        }
      });
    }
    return names;
  },

  setAnnotations: (annos) =>
    set({
      annotations: annos?.annotations ? { ...annos.annotations } : {},
      labelAnnoMap: annos?.labelAnnoMap ? { ...annos.labelAnnoMap } : {},
      fileAnnoMap: annos?.fileAnnoMap ? { ...annos.fileAnnoMap } : {},
    }),

  addAnnoObject: (obj: IAnnoItem, fileName: string) => {
    const { elementAnnos, dataElementIndex, setAnnotations } = get();
    elementAnnos[dataElementIndex].addAnnoItem(obj, fileName);

    setAnnotations(elementAnnos[dataElementIndex]);
  },

  updateAnnoObject: (obj: IAnnoItem) => {
    const { elementAnnos, dataElementIndex, setAnnotations } = get();
    elementAnnos[dataElementIndex].updateAnnoItem(obj);

    setAnnotations(elementAnnos[dataElementIndex]);
  },

  removeAnnoObject: (obj: IAnnoItem, fileName: string) => {
    const { elementAnnos, dataElementIndex, setAnnotations } = get();
    elementAnnos[dataElementIndex].removeAnnoItem(obj, fileName);

    setAnnotations(elementAnnos[dataElementIndex]);
  },

  updateAnnosInLabelMap: (annoNameList, oldLabelName, newLabelName) => {
    const { elementAnnos, dataElementIndex } = get();
    elementAnnos[dataElementIndex].updateAnnosInLabelMap(annoNameList, oldLabelName, newLabelName);

    set({ labelAnnoMap: { ...elementAnnos[dataElementIndex].labelAnnoMap } });
  },
});
