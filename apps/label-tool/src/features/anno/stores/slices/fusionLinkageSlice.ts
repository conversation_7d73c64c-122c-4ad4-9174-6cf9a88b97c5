import { Vector3 } from 'three';
import type { StateCreator } from 'zustand';

import { selectionPoints } from '@/lib/three/SelectionPoints';
import { JobStore, LotStore } from '@/stores';
import type { FusionParams, ILinkage, Job, Lot, Rawdata, WidgetObject, WidgetType } from '@/types';
import { AnnoFactory, AnnoInstanceItem, IAnnoItem, Point3d } from '@/utils/annos';
import { BaseCamera } from '@/utils/camera';
import { IKeyPoint } from '@/utils/render';
import { getFusionCameraParams, getFusionMatrix, getImageKey } from '@/utils/transform';

import { AnnoStore } from './annoSlice';
import { ElementStore } from './elementSlice';
import { RenderStore } from './renderSlice';

export type FusionParamsWithPartialCamera = Omit<FusionParams, 'camera'> & { camera?: BaseCamera };
/**
 * 映射点坐标系：原点为图片中心，x轴方向为图片从左到右，y轴方向为图片从下到上
 */
export interface MapPoint {
  /** 映射点x轴的坐标，取值范围为 [-0.5, 0.5] */
  offsetX: number;
  /** 映射点y轴的坐标，取值范围为 [-0.5, 0.5] */
  offsetY: number;
}

/**
 * 连续帧策略
 * 策略生效条件：当前任务数据为融合类数据，且第一帧配置了映射参数
 * 映射逻辑只对实际标注物生效，对虚拟标注物是不生效的
 */
export interface FusionLinkageStore extends ILinkage {
  /**
   * 包级别的相机参数
   */
  cameraParams: Map<string, Partial<FusionParams>>;
  /**
   * 3D 到 2D 的变换参数缓存
   * TODO: 未来可能需要添加上限及清除措施
   */
  transMatrix: Map<Rawdata['name'], FusionParamsWithPartialCamera>;
  /**
   * 观察点
   */
  keyPoints: Record<Rawdata['name'], Array<IKeyPoint>>;
  /**
   * 观察点射线范围
   */
  keyPointRanges: [number, number];

  /** 聚焦模式 */
  focusMode: {
    /** 聚焦模式是否开启 */
    isEnabled: boolean;
    /** 是否自动切帧 */
    autoSwitchFrame: boolean;
    /** 聚焦图片次序 */
    imageIndex: number;

    /** 鼠标最新位置 */
    mouse3d: Vector3;
    mouseMapPoint?: MapPoint;
  };

  init: (lot: Lot, job: Job) => void;
  reset: () => void;

  /**
   * 获取单张图片对应的转换参数，非图片文件直接返回 undefined
   * @param rawdata
   * @returns
   */
  getTransParams: (rawdata: Rawdata) => FusionParamsWithPartialCamera | undefined;
  /**
   * 判断当前标注对象是否支持映射
   * @param anno
   * @param rawdata
   * @returns
   */
  canMap: (anno: IAnnoItem, rawdata: Rawdata) => boolean;
  /**
   * 获取当前标注对象在文件列表中的映射框数据
   * @param type
   * @param vertexs
   * @param files
   * @returns
   */
  getMappedList: (
    type: WidgetType,
    vertexs: number[],
    files: Rawdata[]
  ) => Array<{ fileName: string; widget: WidgetObject }>;

  // compareMappedAnnos: (
  //   target: AnnoInstanceItem,
  //   currentItems: Record<Rawdata['name'], AnnoInstanceItem | undefined>,
  //   newRects: Array<{ data: number[]; fileName: string }>
  // ) => {
  //   toAdd: Array<{ item: AnnoInstanceItem; fileName: string }>;
  //   toRemove: Array<{ item: AnnoInstanceItem; fileName: string }>;
  // };

  //----------------------------- 观察点与探照灯 -----------------------------------
  /**
   * 【全量更新】某张图片对应的观察点，相对于文件坐标系
   * @param points
   * @param rawdata
   * @param activeKeyPoint
   * @returns
   */
  setKeyPoints: (
    points: Array<Pick<IKeyPoint, 'name' | 'coord2' | 'color'>>,
    rawdata: Rawdata,
    activeKeyPoint: Pick<IKeyPoint, 'name' | 'color'> | null
  ) => void;
  /**
   * 获取图片中的所有观察点
   * @param fileName
   * @returns
   */
  getKeyPointsByFile: (fileName: string) => Array<IKeyPoint>;
  /**
   * 删除所有的观察点
   * @returns
   */
  deleteAllKeyPoints: () => void;

  /**
   * 获取图片对应的 3D 扇形（逆时针旋转）区域，相对于点云坐标系
   * @param rawdata 图片
   * @returns [扇形区域的原点，扇形弧线的起点，扇形弧线的终点]
   */
  getLightAngleByFile: (rawdata: Rawdata) => [Vector3, Vector3, Vector3] | undefined;

  //------------------------------ 聚焦模式 ----------------------------------
  /**
   * 开启或关闭聚焦模式
   * @param isEnabled
   * @returns
   */
  setFocusMode: (isEnabled: boolean) => void;

  /**
   * 设置聚焦模式的图片序号，当前同 ImageEdit 组件的图片序号
   * @param index
   * @returns
   */
  setFocusImage: (index: number) => void;

  /**
   * 设置聚焦模式下是否需要自动切帧
   * @returns
   */
  toggleAutoSwitchFrame: () => void;
  /**
   * 根据传入的 3D 聚焦点更新映射点
   * @param pointer
   * @returns
   */
  updateFoucsPoint: (pointer?: Vector3 | null) => void;
}

const fusionLinkageConfig: ILinkage = {
  name: 'fusion',
  level: 'rawdata',
  effects: ['onAnnoAdd'],
  slices: ['ElementStore', 'JobStore'],
  isEnabled: false,
};

export const createFusionSlice: StateCreator<
  { fusionLinkage: FusionLinkageStore } & AnnoStore & JobStore & RenderStore & ElementStore & LotStore,
  [],
  [],
  { fusionLinkage: FusionLinkageStore }
> = (set, get) => ({
  fusionLinkage: {
    ...fusionLinkageConfig,

    cameraParams: new Map(),
    transMatrix: new Map(),
    keyPoints: {},
    keyPointRanges: [1, 50],
    focusMode: {
      isEnabled: false,
      imageIndex: -1,
      mouse3d: new Vector3(0, 0, 0),
      autoSwitchFrame: true,
    },

    init: (lot, job) =>
      set(({ fusionLinkage }) => {
        const { tool_cfg } = lot;

        const firstElementImages = job?.elements[0].datas.filter((rawdata) => rawdata.type === 'image');

        // 策略生效条件：当前任务数据为融合类数据，且有包参数或第一帧中所有图片数据均配置映射参数
        if (!job.cam_params && !firstElementImages?.every(({ transform }) => transform.length > 0)) return {};

        // if (tool_cfg?.redo_projection && job?.phase === 1 && job.elements.length) {
        //   // 任务配置需重新生成 2D 映射框且当前处于标注阶段

        //   for (let i = 0; i < job.elements.length; i++) {
        //     const manager = elementAnnos[i];
        //     if (!manager) continue;

        //     const { annotations, fileAnnoMap } = manager;
        //     const trackAnnoMap: Map<string, Record<Rawdata['name'], AnnoInstanceItem>> = new Map();

        //     const pcdFile = job.elements[i].datas.find((file) => file.type === 'pointcloud');
        //     const pcdAnnos: AnnoInstanceItem[] = [];

        //     for (const fileName in fileAnnoMap) {
        //       if (Array.isArray(fileAnnoMap[fileName])) {
        //         fileAnnoMap[fileName].forEach((annoName) => {
        //           const anno = annotations[annoName];
        //           if (anno instanceof AnnoInstanceItem) {
        //             const objs = trackAnnoMap.get(anno.trackId);
        //             if (objs) {
        //               objs[fileName] = anno;
        //             } else {
        //               trackAnnoMap.set(anno.trackId, { [fileName]: anno });
        //             }

        //             if (fileName === pcdFile?.name) {
        //               pcdAnnos.push(anno);
        //             }
        //           }
        //         });
        //       }
        //     }

        //     if (pcdFile && pcdAnnos.length > 0) {
        //       let allAdd: { fileName: string; item: AnnoInstanceItem }[] = [];
        //       let addRemove: { fileName: string; item: AnnoInstanceItem }[] = [];
        //       pcdAnnos.forEach((anno) => {
        //         const { toAdd, toRemove } = fusionLinkage.compareMappedAnnos(
        //           anno,
        //           trackAnnoMap.get(anno.trackId) ?? {},
        //           fusionLinkage.getMappedRects(anno, pcdFile, i)
        //         );
        //         allAdd = allAdd.concat(toAdd);
        //         addRemove = addRemove.concat(toRemove);
        //       });

        //       manager.addAnnoObjectList(allAdd);
        //       manager.removeAnnoObjectList(addRemove);
        //     }
        //   }
        // }

        if (tool_cfg?.ranges) {
          const keyPointRanges = fusionLinkage.keyPointRanges;
          tool_cfg.ranges.forEach(({ data }) => {
            data.forEach((len) => {
              if (len > keyPointRanges[1]) {
                keyPointRanges[1] = len;
              }
            });
          });
        }

        if (job.cam_params && firstElementImages?.length) {
          for (const key in job.cam_params) {
            const file = firstElementImages.find((img) => getImageKey(img) === key);
            file?.meta.image &&
              fusionLinkage.cameraParams.set(key, getFusionCameraParams(key, job.cam_params[key], file?.meta.image));
          }
        }

        return {
          fusionLinkage: {
            ...fusionLinkage,
            isEnabled: true,
          },
        };
      }),

    reset: () =>
      set(({ fusionLinkage }) => ({
        fusionLinkage: {
          ...fusionLinkage,
          isEnabled: false,
          cameraParams: new Map(),
          transMatrix: new Map(),
          keyPoints: {},
        },
      })),

    getTransParams: (rawdata) => {
      if (rawdata?.type !== 'image' || !rawdata.meta.image) return undefined;

      const {
        fusionLinkage: { cameraParams, transMatrix },
      } = get();
      const params = transMatrix.get(rawdata.name);

      if (params) {
        return params;
      } else {
        const { name } = rawdata;
        const { matrixs: camMats, camera: camModel } = cameraParams.get(getImageKey(rawdata)) ?? {};
        const params = getFusionMatrix(rawdata, !camModel);

        const paramsWithCamera: FusionParamsWithPartialCamera = {
          matrixs: camMats
            ? [params.matrixs[0].premultiply(camMats[0]), params.matrixs[1].multiply(camMats[1])]
            : params.matrixs,
          // 在相机内参无效的情况下，使用兜底的默认相机
          camera: camModel ?? params.camera,
        };
        transMatrix.set(name, paramsWithCamera);

        return paramsWithCamera;
      }
    },

    canMap: (anno, rawdata) => {
      return get().fusionLinkage.isEnabled && rawdata.type === 'pointcloud' && anno instanceof AnnoInstanceItem;
    },

    getMappedList: (type, vertexs, files) => {
      const { fusionLinkage } = get();

      if (!fusionLinkage.isEnabled) return [];

      const newMapItems: Array<{ fileName: string; widget: WidgetObject }> = [];

      for (let i = 0; i < files.length; i++) {
        const params = fusionLinkage.getTransParams(files[i]);

        if (!params || !params.camera) continue;

        const { name: fileName, meta } = files[i];
        const mapItem = AnnoFactory.getFusionMapped(type, vertexs, params.matrixs[0], params.camera, meta.image);

        mapItem && newMapItems.push({ fileName, widget: mapItem });
      }

      return newMapItems;
    },

    // compareMappedAnnos: (target, currentItems, newRects) => {
    //   const toAdd: { fileName: string; item: AnnoInstanceItem }[] = [];
    //   const toRemove: { fileName: string; item: AnnoInstanceItem }[] = [];
    //   const visited: Map<string, boolean> = new Map();

    //   newRects.forEach(({ fileName, data }) => {
    //     const rect = currentItems[fileName];

    //     // 判断对应的矩形是否存在
    //     if (rect) {
    //       // 如存在矩形，且保持着关联，则更新矩形数据
    //       if (rect.isMapped) {
    //         rect.widget.data = data;
    //       }
    //       // 将矩形标识为已遍历
    //       visited.set(rect.name, true);
    //     } else {
    //       // 如不存在，则新建矩形
    //       toAdd.push({
    //         fileName,
    //         item: new AnnoInstanceItem(
    //           MathUtils.generateUUID(),
    //           {
    //             name: 'box2d',
    //             data,
    //           },
    //           target.label,
    //           { trackId: target.trackId, attrs: target.attrs, isMapped: true, source: 'projection' }
    //         ),
    //       });
    //     }
    //   });

    //   for (const fileName in currentItems) {
    //     if (
    //       currentItems[fileName] &&
    //       currentItems[fileName]!.widget.name === 'box2d' &&
    //       !visited.get(currentItems[fileName]!.name)
    //     ) {
    //       // 删除未被遍历过的矩形
    //       toRemove.push({ fileName, item: currentItems[fileName]! });
    //     }
    //   }

    //   return { toAdd, toRemove };
    // },

    // onAnnoAdd: (anno, rawdata, elementIndex) => {
    //   const {
    //     addAnnoObjectList,
    //     fusionLinkage: { getMappedRects, compareMappedAnnos, isEnabled },
    //   } = get();

    //   if (isEnabled) {
    //     const { toAdd } = compareMappedAnnos(anno, {}, getMappedRects(anno, rawdata, elementIndex));
    //     addAnnoObjectList(toAdd);
    //   }
    // },

    // onAnnoUpdate: (anno, rawdata, elementIndex) => {
    //   const {
    //     getAnnosByTrackId,
    //     addAnnoObjectList,
    //     removeAnnoObjectList,
    //     fusionLinkage: { getMappedRects, compareMappedAnnos, isEnabled },
    //   } = get();
    //   if (isEnabled) {
    //     const { toAdd, toRemove } = compareMappedAnnos(
    //       anno,
    //       getAnnosByTrackId(anno.trackId) ?? {},
    //       getMappedRects(anno, rawdata, elementIndex)
    //     );

    //     addAnnoObjectList(toAdd);
    //     removeAnnoObjectList(toRemove);
    //   }
    // },

    // onAnnoRemove: (anno, rawdata, elementIndex) => {
    //   const {
    //     fusionLinkage: { compareMappedAnnos, isEnabled },
    //     getAnnosByTrackId,
    //     removeAnnoObjectList,
    //   } = get();

    //   if (isEnabled) {
    //     const { toRemove } = compareMappedAnnos(anno, getAnnosByTrackId(anno.trackId) ?? {}, []);
    //     removeAnnoObjectList(toRemove);
    //   }
    // },

    getKeyPointsByFile: (name) => get().fusionLinkage.keyPoints[name] ?? [],

    setKeyPoints: (points, image, active = null) => {
      const { fusionLinkage, labelStage } = get();
      const { keyPoints, getTransParams, keyPointRanges } = fusionLinkage;

      const params = getTransParams(image);

      if (!params || !params.camera) return;

      const { matrixs, camera } = params;

      if (labelStage && labelStage.currentSight) {
        selectionPoints.updateCameraAndPointCloud(
          labelStage.currentSight.camera,
          labelStage.currentSight.getUsingRawObject().object
        );
      }
      const pcdMatrixs = labelStage?.currentSight?.getMatrixs();

      // 计算图片关键点反投射线段数据
      let vec = new Vector3(),
        startPoint = new Vector3(),
        endPoint = new Vector3();
      const formatPoints = points.map(({ name, coord2, color }) => {
        const coord3: number[] = new Array(6);

        keyPointRanges.forEach((depth, i) => {
          vec.set(coord2[0] * depth, coord2[1] * depth, depth);
          camera
            .unproject(vec)
            .applyMatrix4(matrixs[1])
            .toArray(coord3, i * 3);
        });

        startPoint.fromArray(coord3, 0);
        endPoint.fromArray(coord3, 3);
        if (pcdMatrixs) {
          startPoint.applyMatrix4(pcdMatrixs[1]);
          endPoint.applyMatrix4(pcdMatrixs[1]);
        }
        let point = selectionPoints.getMinDistancePoint(startPoint, endPoint);
        if (point) {
          if (pcdMatrixs) {
            point = vec.fromArray(point).applyMatrix4(pcdMatrixs[0]).toArray();
          }
          coord3.push(...point);
        }

        return {
          name,
          coord2,
          coord3,
          color,
        };
      });

      const newKeyPoints = { ...keyPoints, [image.name]: formatPoints };

      // 更新主场景样式
      let list: Array<IKeyPoint> = [];
      for (const name in newKeyPoints) {
        if (Array.isArray(newKeyPoints[name]) && newKeyPoints[name].length > 0) {
          list = list.concat(newKeyPoints[name]);
        }
      }

      labelStage?.currentSight?.updateKeypointList(list, active);

      set({
        fusionLinkage: {
          ...fusionLinkage,
          keyPoints: newKeyPoints,
        },
      });
    },

    deleteAllKeyPoints: () =>
      set(({ labelStage, fusionLinkage, job, currentElementIndex, lot }) => {
        labelStage?.currentSight?.updateKeypointList([], null);
        const imageNames: string[] = [];
        if (lot?.data_type === 'fusion4d') {
          // 所有帧且有观察点的图片
          job?.elements.forEach((element) => {
            element.datas
              .filter((item) => item.type === 'image' && fusionLinkage.keyPoints[item.name])
              .forEach((image) => {
                imageNames.push(image.name);
              });
          });
        } else {
          // 当前帧的图片所有的观察点设置为空
          job?.elements[currentElementIndex]?.datas
            .filter((item) => item.type === 'image' && fusionLinkage.keyPoints[item.name])
            .forEach((image) => {
              imageNames.push(image.name);
            });
        }

        const newKeyPoints = imageNames.reduce(
          (pre, imageName) => ({
            ...pre,
            [imageName]: undefined,
          }),
          {}
        );

        return {
          fusionLinkage: {
            ...fusionLinkage,
            keyPoints: {
              ...fusionLinkage.keyPoints,
              ...newKeyPoints,
            },
          },
        };
      }),

    getLightAngleByFile: (image) => {
      const {
        fusionLinkage: { getTransParams },
      } = get();

      const params = getTransParams(image);
      if (!params || !params.camera) return undefined;

      const { matrixs, camera } = params;

      const { width, height } = image.meta.image!;
      const points = [
        [width - 1, height - 1],
        [1, 1],
      ];

      // 计算两个点投影出的直线角度与方程参数
      let [l1, l2] = points.map(([x, y]) => {
        const [v1, v2] = [1, 10].map((depth) => {
          return camera.unproject(new Vector3(x * depth, y * depth, depth)).applyMatrix4(matrixs[1]);
        });

        const point = new Vector3(v2.x - v1.x, v2.y - v1.y, 0);
        const k = point.y / point.x;
        const b = (v2.x * v1.y - v1.x * v2.y) / (v2.x - v1.x);

        return { k, b, point };
      });

      // 计算两直线交点作为原点
      const origin = new Vector3((l2.b - l1.b) / (l1.k - l2.k), (l2.b * l1.k - l1.b * l2.k) / (l1.k - l2.k), 0);

      return [origin, l1.point, l2.point];
    },

    setFocusMode: (isEnabled) =>
      set(({ fusionLinkage }) => ({
        fusionLinkage: {
          ...fusionLinkage,
          // 保证在融合策略开启的情况下才能启用聚焦模式
          focusMode: {
            isEnabled: fusionLinkage.isEnabled && isEnabled,
            mouse3d: new Vector3(),
            // 自动切帧由用户手动控制，聚焦模式的开关不影响自动切帧
            autoSwitchFrame: fusionLinkage.focusMode.autoSwitchFrame,
            imageIndex: fusionLinkage.focusMode.imageIndex,
          },
        },
      })),

    setFocusImage: (imageIndex) => {
      const { fusionLinkage } = get();
      if (imageIndex !== fusionLinkage.focusMode.imageIndex) {
        fusionLinkage.focusMode.imageIndex = imageIndex;
        fusionLinkage.updateFoucsPoint(fusionLinkage.focusMode.mouse3d.clone());
      }
    },

    toggleAutoSwitchFrame: () =>
      set(({ fusionLinkage }) => ({
        fusionLinkage: {
          ...fusionLinkage,
          focusMode: {
            ...fusionLinkage.focusMode,
            autoSwitchFrame: !fusionLinkage.focusMode.autoSwitchFrame,
          },
        },
      })),

    updateFoucsPoint: (pointer) => {
      const { job, currentElementIndex, fusionLinkage, labelStage, getClosestElementFromPointer } = get();

      if (!job || !fusionLinkage.focusMode.isEnabled || fusionLinkage.focusMode.imageIndex < 0) {
        return;
      }

      const renderMat = labelStage?.currentSight?.getMatrixs()[0];
      // 当前点为空值时重置聚焦视图
      if (!pointer || !renderMat) {
        set({
          fusionLinkage: {
            ...fusionLinkage,
            focusMode: {
              ...fusionLinkage.focusMode,
              mouseMapPoint: undefined,
            },
          },
        });
        return;
      }

      // 默认在当前帧搜索最佳匹配点
      let startIndex = currentElementIndex;
      let endIndex = currentElementIndex + 1;

      if (fusionLinkage.focusMode.autoSwitchFrame) {
        // // 若自动切帧开启，则根据车姿数据在鼠标附近搜索最近的帧
        endIndex = getClosestElementFromPointer(pointer);
        startIndex = Math.max(0, endIndex - 10);
        endIndex = Math.min(job.elements.length, startIndex + 20);
      }

      let bestElementIndex = currentElementIndex;
      let mouseMapPoint: MapPoint | undefined = undefined;

      let bestDistance = Number.MAX_SAFE_INTEGER;

      for (let ele = startIndex; ele < endIndex; ele++) {
        const file = job.elements[ele].datas[fusionLinkage.focusMode.imageIndex];

        const params = fusionLinkage.getTransParams(file);
        if (params?.camera) {
          fusionLinkage.focusMode.mouse3d.copy(pointer).applyMatrix4(renderMat);

          const { matrixs, camera } = params;

          Point3d.getVectorFusionMapped(fusionLinkage.focusMode.mouse3d, matrixs[0], camera);

          const { x: px, y: py } = fusionLinkage.focusMode.mouse3d;
          const { width, height } = file.meta.image!;

          if (px >= 0 && px <= width && py >= 0 && py <= height) {
            const xRatio = px / width;
            const yRatio = py / height;
            const distance = Math.abs(yRatio - 0.9);

            if (distance <= bestDistance) {
              bestDistance = distance;
              mouseMapPoint = {
                offsetX: xRatio - 0.5,
                offsetY: 0.5 - yRatio,
              };
              bestElementIndex = ele;
            }
          }
        }
      }

      set({
        currentElementIndex: bestElementIndex,
        fusionLinkage: {
          ...fusionLinkage,
          focusMode: {
            ...fusionLinkage.focusMode,
            mouseMapPoint,
            mouse3d: fusionLinkage.focusMode.mouse3d.copy(pointer),
          },
        },
      });
    },
  },
});
