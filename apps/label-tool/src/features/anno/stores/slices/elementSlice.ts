import { Vector3 } from 'three';
import type { StateCreator } from 'zustand';

import type { JobStore, LabelStore, LotStore } from '@/stores';
import type { Attr, Element, ElementAnno, ElementAttrValues, Job, JobAnno, Lot, RawdataAnnoObject } from '@/types';
import { AnnoInstanceItem, AnnoManager } from '@/utils/annos';
import { isNoCoordinate } from '@/utils/math';
import { SseEditor } from '@/utils/render';
import {
  transInAnno,
  transInAttrDefault,
  transInElementAttrs,
  transOutAnno,
  transOutElementAttrs,
} from '@/utils/service';

import type { AnnoStore } from './annoSlice';
import { OverlayStore } from './overlaySlice';
import type { RenderStore } from './renderSlice';
import { SegmentStore } from './segmentSlice';
import { SeriesLinkageStore } from './seriesLinkageSlice';
import { SnapshotStore } from './snapshotSlice';

const _pose = new Vector3();
const _getDistanceToSquared = (element: Element, point: Vector3) =>
  _pose.fromArray(element.relativePose ?? [0, 0, 0]).distanceToSquared(point);

export enum ElementState {
  NORMAL,
  KEY,
  INTERPOLATION,
  INVALID,
  INDEPENDENT,
}

export interface IElement {
  /** 页码所在的帧数 */
  currentElementIndex: number;
  /** 数据所在的帧数，
   * 4d 项目中时此处一直为 0，
   * 23d 项目中与 currentElementIndex 相同
   * */
  dataElementIndex: number;
  /** [Cache] 所有帧的标注数据 */
  elementAnnos: Record<number, AnnoManager>;
  /** 当前的帧操作条状态 */
  elementsState: ElementState[];
  /** 帧操作条状态默认值 */
  elementsStateDefaults: ElementState[];
  /** 帧属性配置数据 */
  elementAttrs: Attr[];
  /** [Cache] 存储所有帧的属性值 */
  elementAttrValues: ElementAttrValues[];
  /** 当前任务类型 */
  currentType: string;
}
export interface ExportOptions {
  needInterpolation?: boolean;
}
/**
 * 帧相关数据与所有帧的标注物数据存储，整体依赖 JobStore & RenderStore & LabelStore 数据
 */
export interface ElementStore extends IElement {
  /**
   * 初始化所有帧标注数据，且设置第一帧的标注数据
   * @param job
   * @param lot
   * @returns
   */
  initAnnotations: (job: Job) => void;
  /**
   * 初始化帧状态与帧属性数据
   * @param job
   * @param lot
   * @param localData
   * @returns
   */
  initElementsStateAndAttrs: (job: Job, lot: Lot, localData?: Pick<IElement, 'elementAttrValues'>) => void;

  /**
   * 设置当前帧序号
   * @param index 序号
   * @param replaceAnnos 是否更新标注数据
   * @returns
   */
  setElementIndex: (index: number, replaceAnnos?: boolean) => boolean;

  exportAnnoDatas: (opt?: ExportOptions) => JobAnno;

  formatJobElementsAttr: (
    job: Job,
    lot: Lot,
    frameDefaultValues: Record<string, string[]>
  ) => Pick<IElement, 'elementAttrValues'>;

  setElementsState: (newElementsState?: ElementState[]) => void;
  setElementAttrValues: (elementId: number, attrValues?: ElementAttrValues) => void;

  /**
   * 将接口返回的帧标注数据转换为由 AnnoInstanceItem 对象组成的 AnnoManager，依赖于 LabelStore 初始化完成
   * @param element
   * @param anno
   * @returns
   */
  formatAnnoData: (element: Element, anno?: ElementAnno) => AnnoManager;

  /**
   * 基于当前帧的附近帧查找距离点最近的一帧
   * 在回形点云的场景下无法保证返回结果为距离点最近的帧
   * @param point 点云中的点坐标
   * @returns 查找到的帧索引
   */
  getClosestElementFromPointer: (point?: Vector3 | null) => number;

  setCurrentType: (type: string) => void;
}

const initialState = {
  elementAnnos: {},
  dataElementIndex: 0,
  currentElementIndex: 0,
  elementsState: [],
  elementsStateDefaults: [],
  elementAttrs: [],
  elementAttrValues: [],
  currentType: 'detection',
};
export const createElementSlice: StateCreator<
  ElementStore &
    AnnoStore &
    JobStore &
    LabelStore &
    RenderStore &
    SnapshotStore &
    LotStore & { seriesLinkage: SeriesLinkageStore } & OverlayStore &
    Partial<SegmentStore>,
  [],
  [],
  ElementStore
> = (set, get) => {
  return {
    ...initialState,

    initAnnotations: (job) =>
      set(({ formatAnnoData }) => {
        const { elements, annotations } = job;

        const elementAnnos: Record<number, AnnoManager> = {};
        for (let i = 0; i < elements.length; i++) {
          elementAnnos[i] = formatAnnoData(elements[i], annotations[i]);
        }

        return { elementAnnos, ...elementAnnos[0].getAnnotations() };
      }),

    initElementsStateAndAttrs: (job, lot, localData) => {
      set(({ formatJobElementsAttr }) => {
        const attrs = lot.ontologies.attrs ?? {};
        const { required_v2 = [], optional_v2 = [] } = lot.ontologies.elem_attrs || {};
        const requiredLen = required_v2.length;
        const frameDefaultValues: Record<string, string[]> = {};

        const attrList: Attr[] = required_v2.concat(optional_v2).map(({ name, default: originDefault }, i) => {
          const attributions = attrs[name];
          const defaultValue = transInAttrDefault(attrs, name, originDefault);

          frameDefaultValues[name] = defaultValue;
          return {
            ...attributions,
            required: i < requiredLen,
            defaultValue,
          };
        });

        const elementsStateDefaults = new Array(job.elements.length).fill(ElementState.NORMAL);

        const { elementAttrValues } = localData ?? formatJobElementsAttr(job, lot, frameDefaultValues);

        return {
          elementAttrValues,
          elementsStateDefaults,
          elementsState: elementsStateDefaults,
          elementAttrs: attrList,
        };
      });
    },

    formatJobElementsAttr: (job, lot, frameDefaultValues) => {
      let elementAttrValues: ElementAttrValues[] = [];
      const { elements, annotations } = job;

      if (lot.data_type === 'fusion4d') {
        // 4d 情况没有帧属性，暂时用第一帧当作包属性
        elementAttrValues[0] = { ...frameDefaultValues, ...transInElementAttrs(elements[0], annotations[0]?.attrs) };
      } else {
        elementAttrValues = elements.map((_, i) => ({
          ...frameDefaultValues,
          ...transInElementAttrs(elements[i], annotations[i]?.attrs),
        }));
      }

      return { elementAttrValues };
    },

    setElementsState: (newElementsState) => {
      set(({ elementsStateDefaults }) => ({
        elementsState: newElementsState ?? elementsStateDefaults,
      }));
    },

    setElementIndex: (index, replaceAnnos = false) => {
      const { currentElementIndex, job, elementAnnos } = get();
      if (job && index >= 0 && index < job.elements.length && currentElementIndex !== index) {
        if (replaceAnnos) {
          // 更新标注数据所在帧数
          set({
            dataElementIndex: index,
            ...elementAnnos[index].getAnnotations(),
          });
        }
        set({
          currentElementIndex: index,
        });

        return true;
      }

      return false;
    },

    formatAnnoData: (element, anno) => {
      const manager = new AnnoManager();
      if (element && anno) {
        const { getLabelByName } = get();

        anno.rawdata_annos.forEach(({ name: fileName, objects }) => {
          objects.forEach((obj) => {
            const labelName = obj.label.name;
            const label = getLabelByName(labelName);

            // 校验标签信息存在
            if (label) {
              const annoItem = transInAnno(obj, label);

              // 判断标注对象是否转换成功
              if (annoItem) {
                manager.addAnnoItem(annoItem, fileName);
              }
            }
          });
        });
      }
      return manager;
    },

    exportAnnoDatas: (options = {}) => {
      const {
        elementAnnos,
        elementAttrValues,
        job,
        seriesLinkage: { getTrackIdWidgetByElementIndex },
      } = get();
      const { annotations, elements } = job!;
      const { needInterpolation = false } = options;

      // TODEL
      // if (lot?.data_type === 'fusion4d') {
      //   if (!elementAnnos[0]) {
      //     elementAnnos[0] = new AnnoManager();
      //   }

      //   const { fileAnnoMap: file0, annotations: annos0 } = elementAnnos[0];
      //   const list: Array<{ item: IAnnoItem; fileName: string }> = [];

      //   for (const fileName in file0) {
      //     file0[fileName].forEach((name) => {
      //       const item = annos0[name];
      //       if (item && (item instanceof AnnoCompoundItem || !AnnoFactory.isMappedType(item.widget.name))) {
      //         list.push({ item, fileName });
      //       }
      //     });
      //   }

      //   // 清除所有 3D 对象
      //   elementAnnos[0].removeAnnoObjectList(list);

      //   elementAnnos[0].importAnnotations(getAnnotations());
      // } else {
      //   elementAnnos[currentElementIndex] = new AnnoManager(getAnnotations());
      // }

      const filterInvalidParts = (objects: RawdataAnnoObject[], invalids: string[]) => {
        objects.forEach((annoObj) => {
          if (annoObj.compound?.parts) {
            annoObj.compound.parts = annoObj.compound.parts.filter((item) => !invalids.includes(item));
          }
        });
      };

      let allCnt = 0;
      const annoDatas: Array<ElementAnno> = elements.map((element, i) => {
        const { index, name, datas } = element;

        let insCnt = 0;
        let rawdataAnnos: ElementAnno['rawdata_annos'] = [];
        // 帧属性
        let elementAttrs: ElementAnno['attrs'] = transOutElementAttrs(elementAttrValues[i] ?? []);
        if (elementAnnos.hasOwnProperty(i)) {
          rawdataAnnos = datas.map((data) => {
            const { name } = data;

            const { fileAnnoMap, annotations } = elementAnnos[i].getAnnotations();
            const objects: RawdataAnnoObject[] = [];
            const invalids: string[] = [];
            if (Array.isArray(fileAnnoMap[name])) {
              fileAnnoMap[name].forEach((objName) => {
                const anno = annotations[objName];
                if (anno) {
                  // 如果是插值生成的标注对象，需要计算得到最准确的标注数据
                  if (anno.source === 'interpolation') {
                    const widget = getTrackIdWidgetByElementIndex(anno.trackId, i);
                    if (!widget) {
                      invalids.push(anno.name);
                      return;
                    }
                    (anno as AnnoInstanceItem).updateWidget(widget);
                  } else if (anno instanceof AnnoInstanceItem && isNoCoordinate(anno.widget.data)) {
                    invalids.push(anno.name);
                    return;
                  }
                  const data = transOutAnno(anno);
                  if (data) {
                    objects.push(data);
                  }
                }
              });
            }
            filterInvalidParts(objects, invalids);
            insCnt += objects.length;
            return {
              name,
              objects,
              attrs: [],
            };
          });
        } else if (annotations[i]) {
          const invalids: string[] = [];
          annotations[i].rawdata_annos.forEach(({ objects }) => {
            objects = objects.filter(({ label, uuid }) => {
              const widget = label.widget?.data;
              const isInvalid = widget && isNoCoordinate(widget);
              if (isInvalid) {
                invalids.push(uuid);
              }
              return !isInvalid;
            });
            filterInvalidParts(objects, invalids);
          });
          // 此处默认 annotations 数组长度与 elements 数组长度相同
          rawdataAnnos = annotations[i].rawdata_annos;
          insCnt = annotations[i].ins_cnt;
          elementAttrs = annotations[i].attrs;
        }

        let segmentation3d;
        const segmentation = SseEditor.segmentData[i];
        if (segmentation?.statistic.length) {
          const { category, instance_id, statistic } = segmentation;
          segmentation3d = {
            result: { category, instance_id },
            statistic,
          };
        }

        allCnt += insCnt;

        return {
          index,
          name,
          ins_cnt: insCnt,
          rawdata_annos: rawdataAnnos,
          attrs: elementAttrs,
          segmentation3d,
        };
      });

      return {
        element_annos: annoDatas,
        need_interpolation: needInterpolation,
        ins_cnt: allCnt,
      };
    },

    setElementAttrValues: (elementId, attrValues) => {
      if (attrValues) {
        const { elementAttrValues, createSnapshot, job } = get();
        elementAttrValues[elementId] = attrValues;
        job && createSnapshot();
      }
    },

    getClosestElementFromPointer: (point) => {
      const { lot, job, currentElementIndex } = get();
      if (lot?.data_type !== 'fusion4d' || !point || !job || job.elements.length < 2) return currentElementIndex;

      // 已确保当前数据类型为 fusion4d，故以下代码不考虑任意帧首个文件非点云的情况，90% 场景下查找速度为 O(1)

      let index = currentElementIndex;

      let curDistance = -1;
      let lastDistance = -1;
      let nextDistance = -1;

      while (index >= 0 && index < job.elements.length) {
        curDistance = curDistance >= 0 ? curDistance : _getDistanceToSquared(job.elements[index], point);

        const lastIndex = Math.max(index - 1, 0);
        lastDistance = lastIndex === index ? curDistance : _getDistanceToSquared(job.elements[lastIndex], point);

        const nextIndex = Math.min(index + 1, job.elements.length - 1);
        nextDistance = nextIndex === index ? curDistance : _getDistanceToSquared(job.elements[nextIndex], point);

        if (lastDistance >= curDistance && nextDistance >= curDistance) {
          break;
        } else if (lastDistance < curDistance) {
          index = lastIndex;
          curDistance = lastDistance;
        } else {
          index = nextIndex;
          curDistance = nextDistance;
        }
      }

      return index;
    },

    setCurrentType: (type) => set({ currentType: type }),
  };
};
