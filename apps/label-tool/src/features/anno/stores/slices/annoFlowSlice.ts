import { cloneDeep } from 'lodash';
import type { StateCreator } from 'zustand';

import { LabelStore } from '@/stores';
import type { ISeriesPoint, Label, WidgetObject } from '@/types';
import { AnnoFactory, AnnoInstanceItem, IAnnoItem } from '@/utils/annos';
import type { ToolType } from '@/utils/tool';

import type { AnnoEventStore } from './annoEventSlice';
import { AnnoSelectedStore } from './annoSelectedSlice';
import { AnnoStore } from './annoSlice';
import { CommentMissedStore } from './commentMissedSlice';
import { CommentStore } from './commentSlice';
import type { ElementStore } from './elementSlice';
import type { RenderStore } from './renderSlice';
import { SnapshotStore } from './snapshotSlice';

export type AnnoOpt = 'add' | 'update' | 'remove';
// 当前正在编辑标注物的宽高还是属性
export type AnnoEditType = 'widget' | 'attrs';
export interface CurrentAnnoType {
  anno: IAnnoItem;
  opt: AnnoOpt;
  source?: 'user' | 'undo';
  // 留存标注对象的几何数据，用于回溯
  widget?: AnnoInstanceItem['widget'];
  // 上一个状态的几何数据，用于回溯
  prevWidget?: AnnoInstanceItem['widget'];
  // 留存添加点的几何数据，用于回溯
  seriesPoints?: Record<string, WidgetObject> | ISeriesPoint;
  // 上一个状态的几何数据，用于回溯
  prevSeriesPoints?: Record<string, WidgetObject> | ISeriesPoint;
}
export interface EditAnnoType {
  anno: IAnnoItem;
  type: 'label' | 'widget';
}

/**
 * 标注物状态流
 */
export interface AnnoFlow {
  /** 当前工具 */
  currentTool: ToolType;
  /** 创建、更新、删除单个标注物 */
  currentAnno: CurrentAnnoType | null;
  /** 编辑中的标注对象 */
  editAnno: EditAnnoType | null;
  /** 删除确认弹窗中待删除的标注物 */
  deleteModalAnnos: IAnnoItem[];
}

export interface AnnoFlowStore extends AnnoFlow {
  setCurrentTool: (tool: ToolType) => void;
  setCurrentAnno: (newAnno: CurrentAnnoType | null) => void;
  setEditAnno: (anno: EditAnnoType | null) => void;

  setDeleteModalAnnos: (annos: IAnnoItem[]) => void;

  updateAnnoLabelAndAttrs: (label: Label, attrs?: Record<string, string[]>) => void;
  resetAnnoAndTool: () => void;

  onCurrentAnnoChange: (currentAnno: CurrentAnnoType) => void;
}

const initialState: AnnoFlow = {
  currentTool: 'select',
  currentAnno: null,
  editAnno: null,
  deleteModalAnnos: [],
};

export const createAnnoFlowSlice: StateCreator<
  AnnoFlowStore &
    AnnoStore &
    ElementStore &
    RenderStore &
    AnnoEventStore &
    SnapshotStore &
    AnnoSelectedStore &
    LabelStore &
    CommentStore &
    CommentMissedStore,
  [],
  [],
  AnnoFlowStore
> = (set, get) => ({
  ...initialState,
  setCurrentTool: (tool) => {
    set({ currentTool: tool });
    return;
  },
  setEditAnno: (anno) =>
    set(({ selectedAnnos }) => ({
      editAnno: anno,
      selectedComment: null,
      // 若原本是空的或者更新 anno 为 null，则不触发更新
      selectedAnnos: selectedAnnos.length === 0 || !anno ? selectedAnnos : [],
      // 进入编辑态切换对应工具，退出编辑态切换选择工具
      currentTool:
        anno?.type === 'widget' && anno.anno instanceof AnnoInstanceItem
          ? AnnoFactory.getWidgetType(anno.anno.widget)
          : 'select',
    })),

  setCurrentAnno: (newAnno) =>
    set(() => {
      if (!newAnno) return { currentAnno: null };

      return {
        currentAnno: {
          ...newAnno,
          source: 'user',
          widget: newAnno.widget ?? (newAnno.anno instanceof AnnoInstanceItem ? newAnno.anno.widget : undefined),
        },
      };
    }),

  setDeleteModalAnnos: (annos) =>
    set({
      deleteModalAnnos: annos,
    }),

  updateAnnoLabelAndAttrs: (label, attrs) => {
    const { editAnno, labelStage, createSnapshot, updateAnnosInLabelMap } = get();
    if (!editAnno) return;

    const anno = editAnno.anno;
    const oldLabelName = editAnno.anno.label;
    anno.updateLabelAndAttrs(label.name, attrs);

    // 需要更新标签
    if (oldLabelName !== label.name) {
      const obj = anno instanceof AnnoInstanceItem && labelStage?.currentSight?.getObjectByName(anno.name);

      if (obj) {
        if (editAnno.type === 'widget') {
          // 如当前对象处于编辑态，则仅改变存储的颜色值
          obj.userData.color = label.color;
        } else {
          AnnoFactory.changeColor(obj, label.color);
        }
      }

      updateAnnosInLabelMap([anno.name], oldLabelName, label.name);
    }

    createSnapshot();
  },

  resetAnnoAndTool: () => set({ currentAnno: null, currentTool: 'select', editAnno: null }),

  onCurrentAnnoChange: (current: CurrentAnnoType) => {
    const { anno, opt, widget, source } = current;
    const {
      labelStage,
      labelMap,
      currentElementIndex,
      editAnno,
      selectedAnnos,
      onAnnoAdd,
      onAnnoRemove,
      onAnnoUpdate,
      createSnapshot,
      updateCommentsWithDeleteAnno,
    } = get();

    if (labelStage?.currentSight) {
      const rawdata = labelStage.currentSight.getCurrentFile();
      const fileToThreeMat = labelStage.currentSight.getMatrixs()[1];

      switch (opt) {
        case 'add':
          if (anno instanceof AnnoInstanceItem) {
            const label = labelMap.get(anno.label);
            if (!labelStage.currentSight.getObjectByName(anno.name) && label) {
              const obj = AnnoFactory.createFromWidget(anno.name, anno.widget, fileToThreeMat, {
                color: label.color,
                anchor: Boolean(labelStage.labelRenderer),
                canvasSize: [labelStage.width, labelStage.height],
              });

              obj && labelStage.currentSight.addObjectFromTool(anno.widget.name, obj);
            }
          }

          onAnnoAdd(anno, rawdata, currentElementIndex);

          createSnapshot();

          break;
        case 'remove':
          updateCommentsWithDeleteAnno(anno.name);
          if (anno instanceof AnnoInstanceItem) {
            // 视图层移标注物
            labelStage.currentSight.removeObjectByName(anno.name);
          }

          onAnnoRemove(anno, rawdata, currentElementIndex);

          createSnapshot();

          break;
        case 'update':
          if (anno instanceof AnnoInstanceItem && widget && widget !== anno.widget) {
            const oldWidget = cloneDeep(anno.widget);
            anno.updateWidget(widget);

            const target = labelStage.currentSight.getObjectByName(anno.name);
            if (target && target?.userData.widget !== widget) {
              AnnoFactory.updateFromWidget(target, widget, fileToThreeMat);
            }

            onAnnoUpdate(anno, rawdata, currentElementIndex, oldWidget);

            createSnapshot();
          }
          break;
        default:
          break;
      }

      if (source === 'undo') {
        if (opt === 'remove') {
          set({
            // 清空编辑态或关闭属性面板
            editAnno: null,
            // 清空 selectedAnnos，若原本是空的，则不触发更新
            selectedAnnos: selectedAnnos.length === 0 ? selectedAnnos : [],
            currentTool: 'select',
          });
        } else if (opt === 'update' && editAnno?.type === 'widget' && editAnno?.anno.name === anno.name) {
          // 如果更新的是编辑中的标注物，需要触发 setEditObjectEffects
          set({
            editAnno: { ...editAnno },
          });
        } else {
          // 选中当前
          set({
            editAnno: null,
            selectedAnnos: [anno],
            currentTool: 'select',
          });
        }
      }
    }
  },
});
