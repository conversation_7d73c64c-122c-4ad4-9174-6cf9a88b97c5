import { StateCreator } from 'zustand';

import { Comment } from '@/types';
import { IAnnoItem } from '@/utils/annos';

/** 添加批注或者对批注进行操作 */
export interface CommentHover {
  commentHoverInfo: {
    /** 添加批注的标注物 */
    anno?: IAnnoItem;
    /** 展示具体信息批注 */
    comment?: Comment;
    position: Array<number | string>;
  } | null;
  commentHoverTimeoutId?: number;
}

export interface CommentHoverStore extends CommentHover {
  setCommentHoverInfo: (info: CommentHover['commentHoverInfo']) => void;
  createCommentHoverTimeoutId: () => void;
  clearCommentHoverTimeoutId: () => void;
}

const initialState: CommentHover = {
  commentHoverInfo: null,
};

export const createCommentHoverSlice: StateCreator<CommentHoverStore, [], [], CommentHoverStore> = (set, get) => ({
  ...initialState,

  setCommentHoverInfo: (info) => {
    set({ commentHoverInfo: info });
  },

  createCommentHoverTimeoutId: () => {
    const { commentHoverInfo, setCommentHoverInfo } = get();
    if (!commentHoverInfo) return;
    const commentHoverTimeoutId = setTimeout(() => {
      setCommentHoverInfo(null);
    }, 100);
    // @ts-ignore
    set({ commentHoverTimeoutId });
  },

  clearCommentHoverTimeoutId: () =>
    set(({ commentHoverTimeoutId }) => {
      if (commentHoverTimeoutId) {
        clearTimeout(commentHoverTimeoutId);
      }
      return { commentHoverTimeoutId: undefined };
    }),
});
