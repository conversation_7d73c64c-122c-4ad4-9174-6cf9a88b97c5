import { StateCreator } from 'zustand';

import { JobStore } from '@/stores';
import type { Rawdata } from '@/types';
import { RawPoints } from '@/utils/render';

import { ElementStore } from './elementSlice';
import { FusionLinkageStore } from './fusionLinkageSlice';
import { RenderStore } from './renderSlice';
import { SeriesLinkageStore } from './seriesLinkageSlice';

export interface IOverlay {
  overlayFrames: {
    startIndex: number;
    startFile: Rawdata;
    num: number;
  } | null;
}

export interface OverlayStore extends IOverlay {
  /**
   * 设置叠帧数量
   * @param newNumber
   * @returns
   */
  setOverlayNumber: (newNumber?: number) => Promise<void[] | undefined> | undefined;
  /**
   * 清空切帧
   * @returns
   */
  clearOverlay: () => void;

  /**
   * 判断当前帧是否为叠帧
   * @param elementIndex 帧页数
   * @returns
   */
  isInOverlay: (elementIndex: number) => boolean;
}

export const createOverlaySlice: StateCreator<
  OverlayStore & { seriesLinkage: SeriesLinkageStore } & { fusionLinkage: FusionLinkageStore } & ElementStore &
    JobStore &
    RenderStore,
  [],
  [],
  OverlayStore
> = (set, get) => ({
  overlayFrames: null,

  setOverlayNumber: (newNumber) => {
    const {
      job,
      currentElementIndex,
      seriesLinkage: { isEnabled },
      overlayFrames,
      labelStage,
      clearOverlay,
    } = get();

    if (!job || !isEnabled || !labelStage || newNumber === overlayFrames?.num) return;

    if (typeof newNumber !== 'number' || Number.isNaN(newNumber) || newNumber < 1) {
      clearOverlay();
      return;
    }

    const start = overlayFrames?.startIndex ?? currentElementIndex;
    const startFile = job.elements[start].datas[0];
    const baseSight = labelStage.sights[startFile.name];

    const oldNum = overlayFrames?.num ?? 0;
    // 格式化新值，确保合法
    const validNumber = Math.min(newNumber, job.elements.length - start - 1);
    // 计算新旧值之差
    const diff = validNumber - oldNum;

    if (diff === 0 || !baseSight) return;

    const baseIndex = start + oldNum;
    const newOverlays = { startIndex: start, startFile, num: validNumber };
    if (diff > 0) {
      // 添加叠帧
      const overlays = baseSight.overlays ?? baseSight.initOverlays();

      const list = [];
      for (let i = 1; i <= diff; i++) {
        list.push(overlays.pushRawdata(job.elements[baseIndex + i].datas[0]));
      }
      set({ overlayFrames: newOverlays });

      // 处理异步加载完成后的渲染
      return Promise.all(list);
    }

    // 删除叠帧
    const len = Math.abs(diff);
    for (let i = 0; i < len; i++) {
      baseSight.overlays?.hideRawdata(job.elements[baseIndex - i].datas[0].name);
      baseSight.overlays?.background?.scheduler?.removeRawdata(job.elements[baseIndex - i].datas[0].name);
    }

    set({ overlayFrames: newOverlays });
  },

  clearOverlay: () => {
    const { labelStage, overlayFrames, job, currentType } = get();
    if (!labelStage || !overlayFrames || !job) return;

    const baseSight = labelStage?.sights[overlayFrames.startFile.name];
    if (currentType === 'segment') {
      (baseSight.rawObject as RawPoints).scheduler?.updateAllRawdata();
    }

    baseSight?.destroyOverlays();
    set({ overlayFrames: null });
  },

  isInOverlay: (elementIndex) => {
    const { overlayFrames } = get();
    if (!overlayFrames) return false;

    return elementIndex >= overlayFrames.startIndex && elementIndex <= overlayFrames.startIndex + overlayFrames.num;
  },
});
