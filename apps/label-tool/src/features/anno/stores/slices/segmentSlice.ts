import { cloneDeep } from 'lodash';
import { StateCreator } from 'zustand';

import { SegLabelStore, SelectedSegment } from '@/stores/segLabelSlice';
import type { Statistic } from '@/types';
import type { SegModeType } from '@/utils/tool';

import { type ElementStore, type OverlayStore } from './index';

type SegId = Record<string, Record<string, number>>;
export interface InstanceStatus {
  isHide: number;
  isOnlyShow: number;
}
export type InstancesStatus = Record<number, InstanceStatus>;
type CategoryStatus = InstanceStatus & { instances: InstancesStatus };
export type SettingStatus = Record<string, CategoryStatus>;

/**
 * 分割标签数据存储
 */
export interface ISegment {
  // 标签的点云数量
  statistic: Record<string, Array<Statistic>>;
  ids: Record<string, SegId>;
  // 标签显隐
  settingStatus: SettingStatus;
  curSegMode: SegModeType;
  // 总点云数
  pointsNum: Record<number, number>;
}

const initialState: ISegment = {
  statistic: {},
  ids: {},
  settingStatus: {},
  curSegMode: 'add',
  pointsNum: {},
};

const availableTrackId = (label: SegId, currentFrame: number): number => {
  const usedIds = new Set<number>();
  // 遍历标签下的所有实例
  for (const instanceName in label) {
    const frames = label[instanceName];
    for (const frame in frames) {
      if (parseInt(frame) === currentFrame) {
        // 将实例ID标记为已使用
        usedIds.add(parseInt(instanceName));
      }
    }
  }
  // 寻找未使用的最小实例ID
  let trackId = 1;
  while (usedIds.has(trackId)) {
    trackId++;
  }
  return trackId;
};

export interface SegmentStore extends ISegment {
  /**
   * 初始化每帧的分割数据
   * @param segmentations
   * @returns
   */
  initSegments: () => void;
  updateIds: () => void;
  updateStatistic: (statistic: Array<Array<Statistic>>) => void;
  changeSettingStatus: (name: string, instanceId: number, value: number, property: string) => void;
  getOnlySetting: () => boolean;
  getSetting: (categoryName: string, instanceID?: number) => InstanceStatus | undefined;
  setSegMode: (segMode: SegModeType) => void;
  setPointsNum: (pointsNum: number, index?: number) => void;
  getSegSelectInfo: () => SelectedSegment | null;
}

export const createSegmentSlice: StateCreator<
  SegLabelStore & ElementStore & SegmentStore & OverlayStore,
  [],
  [],
  SegmentStore
> = (set, get) => ({
  ...initialState,
  async initSegments() {
    set(({ segLabels, settingStatus, ids, overlayFrames, updateIds }) => {
      if (!overlayFrames) {
        updateIds();
      }

      const defaultIsHide = 0;
      const newStatus: SettingStatus = {};

      segLabels?.forEach(({ name }) => {
        const currentStatus = settingStatus[name] ?? { isHide: defaultIsHide, isOnlyShow: 0, instances: {} };
        // 初始化新状态，但不包括instances
        newStatus[name] = { ...currentStatus, instances: {} };

        if (!overlayFrames) {
          // 遍历每个实例，应用现有或默认状态
          Object.keys(ids[name] ?? {}).forEach((instanceId) => {
            const instanceStatus = currentStatus.instances[Number(instanceId)] ?? {
              isHide: defaultIsHide,
              isOnlyShow: 0,
            };
            newStatus[name].instances[Number(instanceId)] = instanceStatus;
          });
        }
      });

      // 处理默认状态
      newStatus[''] = settingStatus[''] ?? { isHide: defaultIsHide, isOnlyShow: 0, instances: {} };

      return { settingStatus: newStatus };
    });
  },

  updateStatistic(statistics) {
    set(({ statistic: oldStatistic }) => {
      const statistic = cloneDeep(oldStatistic);
      statistics.forEach((newData, index) => {
        statistic[index] = newData;
      });
      return { statistic };
    });
  },

  updateIds() {
    set(({ statistic, ids: oldIds, currentElementIndex }) => {
      let ids = cloneDeep(oldIds);

      Object.keys(ids).forEach((groupId) => {
        const group = ids[groupId];
        const segmentGroup = statistic[currentElementIndex]?.find((item) => item.category_name === groupId);

        Object.keys(group).forEach((id) => {
          const idInfo = group[id];
          const instance = segmentGroup
            ? segmentGroup.instances?.find((item) => item.instance_id === Number(id))
            : undefined;

          // 如果ID在当前段中且没有对应的实例或实例数量为0，则删除
          if (idInfo[currentElementIndex] && (!instance || !instance.num)) {
            delete idInfo[currentElementIndex];
          }

          // 如果ID信息为空，则从组中删除ID
          if (!Object.keys(idInfo).length) {
            delete group[id];
          }
        });

        // 如果组为空，则从ID集合中删除组
        if (!Object.keys(group).length) {
          delete ids[groupId];
        }
      });

      statistic[currentElementIndex]?.forEach(({ instances, category_name }) => {
        const group = ids[category_name] || {};
        instances?.forEach(({ num, instance_id }) => {
          if (num) {
            group[instance_id] = group[instance_id] || {};
            group[instance_id][currentElementIndex] = 1;
          }
        });
        ids[category_name] = group;
      });

      return { ids };
    });
  },

  changeSettingStatus(name, instanceId, value, property) {
    set(({ settingStatus: oldStatus }) => {
      let settingStatus = cloneDeep(oldStatus);
      if (instanceId) {
        // 如果有 instanceId，更新特定实例的属性
        settingStatus[name].instances[instanceId][property as keyof InstanceStatus] = value;
      } else {
        // 否则，更新整个分类的属性
        settingStatus[name][property as keyof InstanceStatus] = value;
      }
      return { settingStatus };
    });
  },

  setSegMode: (segMode) => set({ curSegMode: segMode }),
  setPointsNum: (num, index) =>
    set(({ pointsNum, currentElementIndex }) => ({
      pointsNum: {
        ...pointsNum,
        [index !== undefined ? index : currentElementIndex]: num,
      },
    })),

  getOnlySetting: () => {
    const { settingStatus } = get();
    return Object.values(settingStatus).some(
      ({ instances, isOnlyShow }) =>
        isOnlyShow || Object.values(instances || {}).some((instance) => instance.isOnlyShow)
    );
  },
  getSetting: (categoryName, instanceID?) => {
    const { settingStatus } = get();
    return instanceID ? settingStatus[categoryName].instances?.[instanceID] : settingStatus[categoryName];
  },
  getSegSelectInfo: () => {
    const { currentElementIndex, selectedSegment, ids, curSegMode } = get();
    if (!selectedSegment) return null;

    const instanceId =
      curSegMode !== 'remove' && selectedSegment?.hasInstanceId
        ? availableTrackId(ids?.[selectedSegment.name] ?? {}, currentElementIndex)
        : selectedSegment.instanceId;
    return { ...selectedSegment, instanceId };
  },
});
