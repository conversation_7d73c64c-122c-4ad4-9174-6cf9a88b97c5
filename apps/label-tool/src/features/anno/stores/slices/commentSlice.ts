import type { StateCreator } from 'zustand';

import type { JobStore, LotStore } from '@/stores';
import type { Comment, Job, Lot } from '@/types';
import { commentTrackIdGenerator, IAnnoItem } from '@/utils/annos';

import { AnnoStore } from './annoSlice';
import { CommentMissedStore } from './commentMissedSlice';
import { ElementStore } from './elementSlice';
import { SnapshotStore } from './snapshotSlice';

const initAnnoCommentMap = (comments: Array<Comment | null>) => {
  const annoCommentMap: Record<IAnnoItem['name'], Comment> = {},
    elementCommentMap: Record<number, Comment> = {},
    missedCommentMap: Record<number, Comment[]> = {};
  comments.forEach((comment) => {
    if (!comment) return;
    if (comment.scope === 'unspecified') {
      // 漏标
      const comments = missedCommentMap[comment.elem_idx] || [];
      comment.trackId = commentTrackIdGenerator.createTrackId();
      comments.push(comment);
      missedCommentMap[comment.elem_idx] = comments;
    } else if (comment.scope === 'object') {
      comment.obj_uuids.forEach((obj_uuid) => {
        annoCommentMap[obj_uuid] = comment;
      });
    } else if (comment.scope === 'element') {
      elementCommentMap[comment.elem_idx] = comment;
    }
  });
  return { annoCommentMap, elementCommentMap, missedCommentMap };
};

export type CommentModalData =
  | {
    type: 'add';
    scope: 'element' | 'object';
    obj_uuids: string[];
    trackId?: string;
  }
  | {
    // 修改标注物批注
    type: 'edit';
    uuid: string;
    reasons: Comment['reasons'];
    content: Comment['content'];
  }
  | {
    // 漏标的批注
    type: 'missed';
    uuid: string; // 漏标图形的 uuid
    position: number[]; // 漏标图形的位置
    reasons?: Comment['reasons'];
    trackId?: string;
    labels?: string[];
    content?: Comment['content'];
  };

export interface CommentStoreObj {
  /** [Cache] 批注汇总，包含标注物批注、漏标、帧批注（不区分帧、状态） */
  commentMap: Record<Comment['uuid'], Comment | null>;

  /** [Cache] 已解决的批注列表，包含标注物批注、漏标、帧批注（不区分帧，只能是 resolve 的批注） */
  resolveComments: Array<Comment>;

  /**
   * 标注物批注的映射，只保留最近的批注
   * 对于是 refresh 的批注，需要从 resolveComments 中查找之前的批注
   * */
  annoCommentMap: Record<IAnnoItem['name'], Comment>;
  /**
   * 每一帧对应的帧批注，只保留最近的批注
   * 对于是 refresh 的批注，需要从 resolveComments 中查找之前的批注
   * */
  elementCommentMap: Record<number, Comment>;

  /** 批注弹窗 */
  commentModalData: CommentModalData | null;

  commentConfigDisplay: Record<
    string,
    {
      displayName: string;
      reasons: Record<string, string>;
    }
  >;
}

const initialState: CommentStoreObj = {
  resolveComments: [],
  commentMap: {},
  annoCommentMap: {},
  elementCommentMap: {},
  commentModalData: null,
  commentConfigDisplay: {},
};

export interface CommentStore extends CommentStoreObj {
  /**
   * 初始化批注数据
   * @param job
   * @param lot
   * @param localComments
   * @returns
   */
  initComments: (job: Job, lot: Lot, localComments?: Pick<CommentStoreObj, 'resolveComments' | 'commentMap'>) => void;
  /**
   * 格式化批注展示信息
   * @param lot
   * @returns
   */
  formatCommentDisplayName: (lot: Lot) => CommentStoreObj['commentConfigDisplay'];
  /**
   * 格式化接口返回的批注数据，生成 commentMap
   * @param job
   * @returns commentMap
   */
  formatJobComments: (job: Job) => Pick<CommentStoreObj, 'commentMap'>;

  addComment: (obj: Comment) => void;
  /**
   * 更新批注，只有漏标和标注物批注可以更新
   * @param newObj
   * @returns
   */
  updateComment: (newObj: Comment) => void;
  /**
   * 直接删除批注
   * @param uuid  批注的 uuid
   * @returns
   */
  deleteComment: (uuid: string) => void;
  addResolveComment: (uuids: Array<string | number>) => void;

  setCommentModal: (data: CommentModalData | null) => void;

  /**
   * 在删除标注物时，检查更新批注(处理漏标和普通批注)
   * @param annoName 标注物 name
   * @returns
   */
  updateCommentsWithDeleteAnno: (annoName: IAnnoItem['name']) => void;
}

export const createCommentSlice: StateCreator<
  CommentStore & JobStore & ElementStore & AnnoStore & SnapshotStore & LotStore & CommentMissedStore,
  [],
  [],
  CommentStore
> = (set, get) => ({
  ...initialState,

  // 从Claim接口生成批注列表
  initComments: (job, lot, localComments) => {
    const { formatCommentDisplayName, formatJobComments } = get();

    const commentData = localComments ?? formatJobComments(job);

    const { annoCommentMap, elementCommentMap, missedCommentMap } = initAnnoCommentMap(
      Object.values(commentData.commentMap)
    );

    commentTrackIdGenerator.initCommentTrackId(Object.values(commentData.commentMap));

    set({
      ...commentData,
      annoCommentMap,
      elementCommentMap,
      missedCommentMap,
      commentConfigDisplay: formatCommentDisplayName(lot),
    });
  },

  formatCommentDisplayName: (lot) => {
    const config: CommentStoreObj['commentConfigDisplay'] = {};
    lot.comment_reasons?.forEach((reasonClass) => {
      config[reasonClass.class.name] = {
        displayName: reasonClass.class.display_name,
        reasons: reasonClass.reasons.reduce((acc: Record<string, string>, reason) => {
          acc[reason.name] = reason.display_name;
          return acc;
        }, {}),
      };
    });

    return config;
  },

  formatJobComments: (job) => {
    const { comments: originComments, annotations } = job;
    const commentMap: Record<Comment['uuid'], Comment> = {};

    const getCommentStatus = (comment: Comment): Comment['status'] => {
      if (comment.resolve_phase > 0) {
        return comment.resolve_phase >= comment.add_phase ? 'resolved' : 'pending';
      }
      return 'unresolved';
    };

    originComments.forEach((comment) => {
      // 标注物
      switch (comment.scope) {
        case 'object':
          comment.obj_uuids.forEach((obj_uuid) => {
            if (annotations[comment.elem_idx].rawdata_annos[0].objects.find((obj) => obj.uuid === obj_uuid)) {
              commentMap[comment.uuid] = {
                ...comment,
                status: getCommentStatus(comment),
              };
            }
          });
          break;
        case 'unspecified':
          commentMap[comment.uuid] = {
            ...comment,
            status: getCommentStatus(comment),
          };
          break;
        case 'element':
          commentMap[comment.uuid] = {
            ...comment,
            status: getCommentStatus(comment), // ✅ 修复：使用统一的状态计算
          };
          break;
      }
    });

    return {
      commentMap,
    };
  },

  // 添加单个批注
  addComment: (newObj: Comment) =>
    set(({ commentMap, resolveComments, annoCommentMap, elementCommentMap, missedCommentMap }) => {
      let newResolveComments = resolveComments,
        newElementCommentMap = elementCommentMap,
        newMissedCommentMap = missedCommentMap,
        newAnnoCommentMap = annoCommentMap,
        status: Comment['status'] = 'unresolved';

      const newComment = { ...newObj, status };

      if (newComment.scope === 'unspecified') {
        const comments = missedCommentMap[newComment.elem_idx] || [];
        comments.push(newComment);
        newMissedCommentMap = {
          ...newMissedCommentMap,
          [newComment.elem_idx]: comments,
        };
      } else if (newComment.scope === 'object') {
        newComment.obj_uuids.forEach((obj_uuid) => {
          newAnnoCommentMap = {
            ...newAnnoCommentMap,
            [obj_uuid]: newComment,
          };
        });
      } else if (newComment.scope === 'element') {
        // 注意：帧批注没有 refresh 的状态，如果之前有 review 的批注，只有先 resolve 才能再 new
        newElementCommentMap = {
          ...newElementCommentMap,
          [newComment.elem_idx]: newComment,
        };
      }

      return {
        commentMap: {
          ...commentMap,
          [newComment.uuid]: newComment,
        },
        resolveComments: newResolveComments,
        elementCommentMap: newElementCommentMap,
        missedCommentMap: newMissedCommentMap,
        annoCommentMap: newAnnoCommentMap,
      };
    }),

  updateComment: (newObj: Comment) =>
    set(({ commentMap, annoCommentMap, missedCommentMap }) => {
      let newAnnoCommentMap = annoCommentMap,
        newMissedCommentMap = missedCommentMap;

      if (newObj.status !== 'unresolved') {
        newObj.status = 'unresolved';
      }

      if (newObj.scope === 'unspecified') {
        const comments = newMissedCommentMap[newObj.elem_idx],
          index = comments.findIndex((comment) => comment.uuid === newObj.uuid);
        if (index > -1) {
          comments[index] = newObj;
          newMissedCommentMap = {
            ...newMissedCommentMap,
            [newObj.elem_idx]: comments,
          };
        }
      } else {
        newObj.obj_uuids.forEach((obj_uuid) => {
          newAnnoCommentMap = {
            ...newAnnoCommentMap,
            [obj_uuid]: newObj,
          };
        });
      }

      return {
        commentMap: {
          ...commentMap,
          [newObj.uuid]: newObj,
        },
        annoCommentMap: newAnnoCommentMap,
        missedCommentMap: newMissedCommentMap,
      };
    }),
  deleteComment: (uuid: string) =>
    set(({ resolveComments, commentMap, annoCommentMap, elementCommentMap, missedCommentMap }) => {
      const comment = commentMap[uuid];
      if (!comment || !['unresolved', 'pending'].includes(commentMap[uuid]!.status!)) return {};

      let newResolveComments = resolveComments,
        newAnnoCommentMap = annoCommentMap,
        newElementCommentMap = elementCommentMap,
        newMissedCommentMap = missedCommentMap;

      if (comment.scope === 'object') {
        comment.obj_uuids.forEach((obj_uuid) => {
          Reflect.deleteProperty(newAnnoCommentMap, obj_uuid);
        });
        newAnnoCommentMap = { ...newAnnoCommentMap };
      } else if (comment.scope === 'unspecified') {
        const comments = newMissedCommentMap[comment.elem_idx];
        const index = comments.findIndex((comment) => comment.uuid === uuid);
        if (index > -1) {
          comments.splice(index, 1);
          newMissedCommentMap = {
            ...newMissedCommentMap,
            [comment.elem_idx]: comments,
          };
        }
      } else if (comment.scope === 'element') {
        Reflect.deleteProperty(newElementCommentMap, comment.elem_idx);
        newElementCommentMap = { ...newElementCommentMap };
      }

      return {
        resolveComments: newResolveComments,
        commentMap: {
          ...commentMap,
          [uuid]: null,
        },
        annoCommentMap: newAnnoCommentMap,
        elementCommentMap: newElementCommentMap,
        missedCommentMap: newMissedCommentMap,
      };
    }),

  // 已修改
  addResolveComment: (uuids: Array<string | number>) => {
    console.log('🔄 addResolveComment 开始执行:', { uuids, timestamp: Date.now() });
    set(({ resolveComments, commentMap, annoCommentMap, missedCommentMap, elementCommentMap }) => {
      const newResolveComments = [...resolveComments];
      let newAnnoCommentMap = annoCommentMap,
        newMissedCommentMap = missedCommentMap,
        newElementCommentMap = elementCommentMap;

      uuids.forEach((uuid) => {
        const oldComment = commentMap[uuid];
        console.log('📝 处理批注:', { uuid, oldStatus: oldComment?.status, timestamp: Date.now() });
        if (oldComment) {
          oldComment.status = 'pending';
          console.log('✅ 批注状态已更新为pending:', { uuid, newStatus: oldComment.status });
          newResolveComments.push(oldComment);
          // 分类更新
          if (oldComment.scope === 'element') {
            newElementCommentMap = { ...newElementCommentMap, [oldComment.elem_idx]: oldComment };
          } else if (oldComment.scope === 'unspecified') {
            const comments = newMissedCommentMap[oldComment.elem_idx];
            const index = comments.findIndex((comment) => comment.uuid === uuid);
            if (index > -1) {
              comments[index] = oldComment;
              newMissedCommentMap = {
                ...newMissedCommentMap,
                [oldComment.elem_idx]: comments,
              };
            }
          } else {
            oldComment.obj_uuids.forEach((obj_uuid) => {
              newAnnoCommentMap = {
                ...newAnnoCommentMap,
                [obj_uuid]: oldComment,
              };
            });
          }
        }
      });

      return {
        resolveComments: newResolveComments,
        commentMap: {
          ...commentMap,
        },
        annoCommentMap: newAnnoCommentMap,
        elementCommentMap: newElementCommentMap,
        missedCommentMap: newMissedCommentMap,
      };
    });
  },

  // 注：当 commentModalData 变化时，批注弹窗会打开
  setCommentModal: (commentModalData) => {
    // 漏标的批注，没有 track 就是新增的漏标，需要生成 trackId
    if (
      (commentModalData?.type === 'missed' || commentModalData?.type === 'add') &&
      commentModalData.trackId === undefined
    ) {
      commentModalData.trackId = commentTrackIdGenerator.createTrackId();
    }
    set({
      commentModalData,
    });
  },

  updateCommentsWithDeleteAnno: (annoName) => {
    const { annoCommentMap, deleteComment } = get();
    const comment = annoCommentMap[annoName];
    if (comment) {
      console.log('🗑️ 删除标注物关联的批注:', {
        annoName,
        commentUuid: comment.uuid,
        commentStatus: comment.status,
        timestamp: Date.now()
      });
      deleteComment(comment.uuid);
    }
  },
});
