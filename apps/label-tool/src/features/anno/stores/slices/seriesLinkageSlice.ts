import { Matrix4 } from 'three';
import type { StateCreator } from 'zustand';

import { JobStore } from '@/stores';
import type { ElementAnno, ILinkage, Job, Lot, Rawdata, WidgetObject } from '@/types';
import { SeriesOptScope } from '@/types';
import { AnnoCompoundItem, AnnoFactory, AnnoInstanceItem, IAnnoItem } from '@/utils/annos';
import { createInterpolatedAnnoItemList, Extrinsic } from '@/utils/math';

import { checkDeleteAnnos } from '../../utils';
import { AnnoFlowStore } from './annoFlowSlice';
import { AnnoSelectedStore } from './annoSelectedSlice';
import { AnnoStore } from './annoSlice';
import { CommentStore } from './commentSlice';
import { ElementState, ElementStore } from './elementSlice';
import { FusionLinkageStore } from './fusionLinkageSlice';
import { RenderStore } from './renderSlice';

export type TrackType = {
  label: string;
  // 标注的绘制工具名称
  widgetName?: WidgetObject['name'];
  // 构成组合标注物的 trackId
  compoundTrackIds?: string[];
};

/**
 * 连续帧策略数据，注意在此部分不会创建当前帧的标注物，所以需要优先在外部创建好
 */
export interface SeriesLinkageStore extends ILinkage {
  /** 此次连续帧的操作范围，影响标注物的创建、删除 */
  seriesOptScope: SeriesOptScope;
  /** 以 trackId 为索引组织当前工作包的所有标注框，不区分是否为插值框和关键框  */
  trackAnnoMap: Map<IAnnoItem['trackId'], Map<number, IAnnoItem['name']>>;
  /** 记录哪些帧是关键帧，哪些帧是插值帧 */
  trackElementsState: Map<IAnnoItem['trackId'], ElementState[]>;
  /** 记录每个 trackId 的“统一尺寸” */
  trackScale: Map<IAnnoItem['trackId'], [number, number, number]>;
  /** 记录 trackId 的标注物框类型, 标签名称，widget工具名称 */
  trackTypeMap: Map<IAnnoItem['trackId'], TrackType>;
  /** 记录每一帧 到世界坐标系 为相互变换矩阵，[世界坐标系转换到当前帧，当前帧转换到世界坐标系]  */
  elementsMatrixes: Array<[Matrix4, Matrix4]>;

  /** 当选中一个 trackId 时，在当前帧是否为空, 默认为 false */
  isEmptyWhenTrackIdSelected: boolean;

  init: (job: Job, lot: Lot, localAnno?: ElementAnno[]) => void;
  reset: () => void;

  // TODO: (xiaonan) 需要手动处理融合映射的关联
  /**
   * 只有修改对象的尺寸，位置时才能触发此方法。 修改对象后：
   * 如对象为插值框，则变更为非插值框，并将当前帧变为关键帧
   * 注意：因为插值框的尺寸，是在切换到具体帧的时候，进行计算, 所以此处不用更新插值框的尺寸
   * @param obj
   * @param rawdata
   * @param elementIndex
   */
  onAnnoUpdateWidget: (obj: AnnoInstanceItem, rawdata: Rawdata, elementIndex: number, oldWidget?: WidgetObject) => void;

  /**
   * 添加 trackId 的标注物
   * @param type 修改范围
   * @param trackId 新 trackId
   * @param elementIndex 当前帧索引
   * @param anno 标注物
   * @returns
   */
  onTrackIdAdd: (type: SeriesOptScope, trackId: string, elementIndex: number, anno: IAnnoItem) => void;

  /**
   * 删除 trackId 的标注物
   * @param type 修改范围
   * @param trackId 待删除的 trackId
   * @param elementIndex 当前帧索引
   * @param anno 标注物
   * @returns
   */
  onTrackIdRemove: (type: SeriesOptScope, trackId: string, elementIndex: number, anno: IAnnoItem) => void;

  /**
   * 判断当前标注物能否删除
   * @param trackIds 待检查的 trackId 列表
   * @param elementIndex 当前帧索引
   * @returns 返回无法删除的帧索引
   */
  canTrackIdsRemove: (trackIds: string[], elementIndex: number) => string | undefined;

  /**
   * 修改标注框的 trackId 为 newTrackId
   * @param type 修改范围
   * @param trackId 当前标注框的 trackId
   * @param elementIndex 当前标注框所在帧索引
   * @param newTrackId 新的 trackId
   * @returns
   */
  onTrackIdChange: (type: SeriesOptScope, trackId: string, elementIndex: number, newTrackId: string) => void;

  /**
   * 更新 trackId 的标注物标签和属性
   * @param type 修改范围
   * @param trackId 当前标注框的 trackId
   * @param elementIndex 当前标注框所在帧索引
   * @param label 新的标签
   * @param attrs 新的属性
   * @returns
   */
  onTrackIdLabelAndAttrUpdate: (
    type: SeriesOptScope,
    trackId: string,
    elementIndex: number,
    label?: string,
    attrs?: Record<string, string[]>
  ) => void;

  /**
   * 查找非插值框两侧最近的插值框以及所在帧索引
   * @param keyAnnoItem 非插值框
   * @param elementIndex
   * @returns [prevIndex, nextIndex, prevAnno, nextAnno]: [左侧最近的帧索引，右侧最近的帧索引，左侧最近的非插值框，右侧最近的非插值框]
   */
  findAroundKeyAnnoInstanceItem: (
    keyAnnoItem: AnnoInstanceItem,
    elementIndex: number
  ) => [number, number, AnnoInstanceItem | null, AnnoInstanceItem | null];

  /**
   * 获取 trackId 对应在 elementIndex 这帧下的标注位置信息
   * @param trackId 标注框的 trackId
   * @param elementIndex 帧索引
   * @returns
   */
  getTrackIdWidgetByElementIndex: (trackId: string, elementIndex: number) => WidgetObject | null;

  /**
   * 设置当前连续帧的操作范围
   */
  setSeriesOptScope: (scope: SeriesOptScope) => void;

  /**
   * 在连续帧切帧时，需要同步更新到当前帧的对应标注物
   */
  updateAnnosWhenChangeSight: () => void;

  /**
   * 在连续帧切帧的时候，需要得到 正在编辑 或者 选中 的标注物位置信息
   * @param fileName
   * @returns
   */
  getFocusedTrackIdWidgetByFileName: (fileName: string) => WidgetObject | null;
}

const seriesLinkageConfig: ILinkage = {
  name: 'series',
  level: 'element',
  effects: ['onAnnoAdd', 'onAnnoRemove', 'onAnnoUpdate'],
  slices: ['ElementStore', 'JobStore'],
  isEnabled: false,
};

const seriesLinkageInitialState = {
  seriesOptScope: SeriesOptScope.ALL,
  trackAnnoMap: new Map(),
  trackElementsState: new Map(),
  trackScale: new Map(),
  trackTypeMap: new Map(),
  elementsMatrixes: [],
  isEmptyWhenTrackIdSelected: false,
};

export const createSeriesSlice: StateCreator<
  { seriesLinkage: SeriesLinkageStore } & { fusionLinkage: FusionLinkageStore } & ElementStore &
  JobStore &
  RenderStore &
  AnnoStore &
  CommentStore &
  AnnoFlowStore &
  AnnoSelectedStore,
  [],
  [],
  { seriesLinkage: SeriesLinkageStore }
> = (set, get) => ({
  seriesLinkage: {
    ...seriesLinkageConfig,
    ...seriesLinkageInitialState,

    init: (job, lot) =>
      set(({ seriesLinkage, elementAnnos }) => {
        if (!lot.is_frame_series) {
          return {};
        }
        const { elements = [] } = job;
        const trackAnnoMap: Map<AnnoInstanceItem['trackId'], Map<number, AnnoInstanceItem['name']>> = new Map();
        const trackElementsState: Map<AnnoInstanceItem['trackId'], ElementState[]> = new Map();
        const trackScale: Map<AnnoInstanceItem['trackId'], [number, number, number]> = new Map();
        const trackTypeMap: Map<IAnnoItem['trackId'], TrackType> = new Map();
        const elementsMatrixes: Array<[Matrix4, Matrix4]> = [];

        for (let i = 0; i < elements.length; i++) {
          for (const name in elementAnnos[i].annotations) {
            const anno = elementAnnos[i].annotations[name];
            if (anno) {
              const elementsState =
                trackElementsState.get(anno.trackId) ?? Array(elements.length).fill(ElementState.NORMAL);

              if (anno instanceof AnnoInstanceItem && anno.isScaleIndependent) {
                // 单帧尺寸
                elementsState[i] = ElementState.INDEPENDENT;
              } else {
                // 关键帧
                if (anno.source === 'manual') {
                  elementsState[i] = ElementState.KEY;
                  // 记录统一的尺寸（ TOFIX 问题：如果没有任何的关键帧，这个数据似乎没有独立保存的地方。。）
                  if (anno instanceof AnnoInstanceItem) {
                    const [x, y, z] = anno.widget.data.slice(3, 6);
                    trackScale.set(anno.trackId, [x, y, z]);
                  }
                } else if (anno.source === 'interpolation') {
                  elementsState[i] = ElementState.INTERPOLATION;
                }
              }

              trackElementsState.set(anno.trackId, elementsState);

              const trackMap = trackAnnoMap.get(anno.trackId);
              if (!trackMap) {
                trackAnnoMap.set(anno.trackId, new Map([[i, name]]));
              } else {
                trackMap.set(i, name);
              }

              const typeMap = trackTypeMap.get(anno.trackId);
              if (!typeMap) {
                trackTypeMap.set(anno.trackId, {
                  label: anno.label,
                  widgetName: anno instanceof AnnoInstanceItem ? anno.widget.name : undefined,
                  compoundTrackIds:
                    anno instanceof AnnoCompoundItem
                      ? anno.compound
                        .map((name) => elementAnnos[i].annotations[name]?.trackId)
                        .filter((trackId): trackId is string => !!trackId)
                      : undefined,
                });
              }
            }
          }
          for (let i = 0; i < elements.length; i++) {
            const pose = elements[i].datas[0].meta.pcd?.pose;
            if (pose?.length) {
              const mat = Extrinsic.toMatrix4(pose);
              elementsMatrixes.push([mat.clone().invert(), mat]);
            } else {
              // 如果无车姿信息，则以每一帧的自车坐标系进行插值框的插值计算
              elementsMatrixes.push([new Matrix4(), new Matrix4()]);
            }
          }
        }

        return {
          seriesLinkage: {
            ...seriesLinkage,
            isEnabled: true,
            trackAnnoMap,
            trackElementsState,
            trackScale,
            trackTypeMap,
            elementsMatrixes,
          },
        };
      }),

    reset: () =>
      set(({ seriesLinkage }) => ({
        seriesLinkage: {
          ...seriesLinkage,
          isEnabled: false,
          ...seriesLinkageInitialState,
        },
      })),

    onAnnoUpdateWidget: (obj, rawdata, elementIndex, oldWidget) => {
      const {
        setElementsState,
        elementAnnos,
        seriesLinkage: { trackAnnoMap, trackElementsState, trackScale },
      } = get();

      const trackMap = trackAnnoMap.get(obj.trackId);
      const elementsState = trackElementsState.get(obj.trackId);
      if (!trackMap || !elementsState) {
        return;
      }

      // 判断是否要把当前对象变成关键帧
      const checkChangeToKey = () => {
        if (obj.source !== 'interpolation') return false;
        // 1. 统一尺寸，只要有任何修改，都变关键帧
        if (!obj.isScaleIndependent) return true;

        // 2. 单帧尺寸，如果修改了尺寸以外的东西，变关键帧
        const oldData = oldWidget?.data;
        if (!oldData) return false;
        const data = obj.widget.data;
        const indicesToCompare = [0, 1, 2, 6, 7, 8, 9];
        if (indicesToCompare.every((index) => oldData[index] === data[index])) {
          return false;
        } else {
          return true;
        }
      };

      if (checkChangeToKey()) {
        obj.setSource('manual');
      }

      elementsState[elementIndex] = obj.isScaleIndependent
        ? ElementState.INDEPENDENT
        : obj.source === 'manual'
          ? ElementState.KEY
          : ElementState.INTERPOLATION;

      // 修改了统一尺寸的，需要同时修改其他统一尺寸的关键帧
      if (!obj.isScaleIndependent && obj.source === 'manual') {
        trackMap.forEach((name, eleIndex) => {
          const anno = elementAnnos[eleIndex].annotations[name];
          if (anno?.source === 'manual' && anno instanceof AnnoInstanceItem && !anno.isScaleIndependent) {
            AnnoFactory.keepManualAnnoWidget(obj, anno);
          }
        });

        // 保存新的统一尺寸
        const [x, y, z] = obj.widget.data.slice(3, 6);
        trackScale.set(obj.trackId, [x, y, z]);
      }

      setElementsState([...elementsState]);
    },

    findAroundKeyAnnoInstanceItem: (keyAnnoItem, elementIndex) => {
      const {
        job,
        seriesLinkage: { trackAnnoMap },
        elementAnnos,
      } = get();

      const elementsLength = job?.elements.length || 0;

      // 判断有无当前对象的 trackId 的 Map 数据
      const trackMap = trackAnnoMap.get(keyAnnoItem.trackId);
      if (!trackMap) {
        return [0, elementsLength, null, null];
      }

      let prevIndex = elementIndex;
      let nextIndex = elementIndex;
      let prevAnno: AnnoInstanceItem | null = null;
      let nextAnno: AnnoInstanceItem | null = null;

      // 向前查找同 trackId 的非插值框
      while (prevIndex > 0) {
        prevIndex--;
        const anno = elementAnnos[prevIndex]?.getAnnoItem(trackMap.get(prevIndex));
        if (anno instanceof AnnoInstanceItem && anno.source === 'manual') {
          prevAnno = anno;
          break;
        }
      }

      // 向后查找同 trackId 的非插值框
      while (nextIndex < elementsLength - 1) {
        nextIndex++;
        const anno = elementAnnos[nextIndex]?.getAnnoItem(trackMap.get(nextIndex));
        if (anno instanceof AnnoInstanceItem && anno.source === 'manual') {
          nextAnno = anno;
          break;
        }
      }
      nextIndex = nextAnno ? nextIndex : elementsLength;

      return [prevIndex, nextIndex, prevAnno, nextAnno];
    },

    onTrackIdAdd(type, trackId, elementIndex, anno) {
      const {
        job,
        annotations,
        elementAnnos,
        seriesLinkage: { trackAnnoMap, trackElementsState, trackTypeMap },
        setElementsState,
      } = get();
      if (!job) return;
      const trackMap = new Map([[elementIndex, anno.name]]),
        elementsState = Array(job!.elements.length).fill(ElementState.NORMAL);

      trackAnnoMap.set(trackId, trackMap);
      trackElementsState.set(trackId, elementsState);
      trackTypeMap.set(trackId, {
        label: anno.label,
        widgetName: anno instanceof AnnoInstanceItem ? anno.widget.name : undefined,
        compoundTrackIds:
          anno instanceof AnnoCompoundItem
            ? anno.compound.map((name) => annotations[name]?.trackId).filter((trackId): trackId is string => !!trackId)
            : undefined,
      });

      let start = 0,
        end = job.elements.length - 1;

      elementsState[elementIndex] = ElementState.KEY;

      // 实际标注物只能是向后创建
      if (anno instanceof AnnoInstanceItem && type === SeriesOptScope.BACKWARD) {
        start = elementIndex + 1;
      } else if (anno instanceof AnnoCompoundItem) {
        switch (type) {
          case SeriesOptScope.CURRENT:
            start = elementIndex + 1;
            end = elementIndex - 1;
            break;
          case SeriesOptScope.FORWARD:
            end = elementIndex - 1;
            break;
          case SeriesOptScope.BACKWARD:
          case SeriesOptScope.ALL:
            start = elementIndex + 1;
            break;
        }
      }
      start = Math.min(Math.max(start, 0), job.elements.length - 1);
      end = Math.max(0, Math.min(end, job.elements.length - 1));
      if (start <= end) {
        createInterpolatedAnnoItemList(
          anno,
          start,
          end,
          trackAnnoMap,
          trackTypeMap.get(trackId)?.compoundTrackIds
        ).forEach((anno, eleIndex) => {
          if (elementAnnos[eleIndex]) {
            // 此处目前只处理点云的插值情况，所以数据都是位于 0
            const sightId = job!.elements[eleIndex].datas[0].name;
            elementAnnos[eleIndex].addAnnoItem(anno, sightId);
            trackMap.set(eleIndex, anno.name);
            if (anno instanceof AnnoInstanceItem) {
              elementsState[eleIndex] = ElementState.INTERPOLATION;
            } else {
              elementsState[eleIndex] = ElementState.KEY;
            }
          }
        });
      }
      setElementsState([...elementsState]);
    },

    onTrackIdRemove(type, trackId, elementIndex, anno) {
      const {
        job,
        elementAnnos,
        seriesLinkage: { trackAnnoMap, trackElementsState, getTrackIdWidgetByElementIndex, trackTypeMap },
        setElementsState,
      } = get();
      const trackMap = trackAnnoMap.get(trackId),
        elementsState = trackElementsState.get(trackId);
      if (!job || !trackMap || !elementsState) return;

      // 处理前后关键帧的逻辑
      if (anno instanceof AnnoInstanceItem) {
        // 处理当前帧的前一帧为关键帧
        if (type === SeriesOptScope.CURRENT || type === SeriesOptScope.BACKWARD) {
          const prevIndex = elementIndex - 1;
          const name = trackMap.get(prevIndex);
          const prevAnno = name && elementAnnos[prevIndex].annotations[name];
          if (prevAnno instanceof AnnoInstanceItem) {
            const widget = getTrackIdWidgetByElementIndex(prevAnno.trackId, prevIndex);
            widget && prevAnno.updateWidget(widget);
            if (prevAnno.source === 'interpolation') {
              prevAnno.setSource('manual');
              elementsState[prevIndex] = ElementState.KEY;
            }
          }
        }
        // 处理当前帧的后一帧为关键帧
        if (type === SeriesOptScope.CURRENT || type === SeriesOptScope.FORWARD) {
          const nextIndex = elementIndex + 1;
          const name = trackMap.get(nextIndex);
          const nextAnno = name && elementAnnos[nextIndex].annotations[name];
          if (nextAnno instanceof AnnoInstanceItem) {
            const widget = getTrackIdWidgetByElementIndex(nextAnno.trackId, nextIndex);
            widget && nextAnno.updateWidget(widget);
            if (nextAnno.source === 'interpolation') {
              nextAnno.setSource('manual');
              elementsState[nextIndex] = ElementState.KEY;
            }
          }
        }
      }

      trackMap.delete(elementIndex);
      elementsState[elementIndex] = ElementState.NORMAL;

      // 根据范围删除确定前后帧范围
      let start = 0,
        end = job.elements.length - 1;
      switch (type) {
        case SeriesOptScope.CURRENT:
          start = elementIndex + 1;
          end = elementIndex - 1;
          break;
        case SeriesOptScope.FORWARD:
          end = elementIndex - 1;
          break;
        case SeriesOptScope.BACKWARD:
          start = elementIndex + 1;
          break;
      }
      start = Math.min(Math.max(start, 0), job.elements.length - 1);
      end = Math.max(0, Math.min(end, job.elements.length - 1));

      // 删除范围内的标注物
      for (; start <= end; start++) {
        const name = trackMap.get(start);
        const anno = name && elementAnnos[start].annotations[name];
        if (anno) {
          elementAnnos[start].removeAnnoItem(anno, job.elements[start].datas[0].name);
          trackMap.delete(start);
          elementsState[start] = ElementState.NORMAL;
        }
      }

      // 删除 trackId
      if (trackMap.size === 0) {
        trackAnnoMap.delete(trackId);
        trackElementsState.delete(trackId);
        trackTypeMap.delete(trackId);
      }
      setElementsState([...elementsState]);
    },

    canTrackIdsRemove(trackIds, elementIndex) {
      const {
        job,
        elementAnnos,
        commentMap,
        seriesLinkage: { trackAnnoMap, seriesOptScope: type },
      } = get();
      if (!job) return;
      // 根据范围删除确定前后帧范围

      let start = 0,
        end = job.elements.length - 1;
      // 包含检查当前传入的帧
      switch (type) {
        case SeriesOptScope.CURRENT:
          start = elementIndex;
          end = elementIndex;
          break;
        case SeriesOptScope.FORWARD:
          end = elementIndex;
          break;
        case SeriesOptScope.BACKWARD:
          start = elementIndex;
          break;
      }
      const canNotRemove: number[] = [];

      for (; start <= end; start++) {
        const annotations = elementAnnos[start].annotations;
        const annos = trackIds
          // eslint-disable-next-line no-loop-func
          .map((trackId) => {
            const trackMap = trackAnnoMap.get(trackId);
            const name = trackMap && trackMap.get(start);
            return name && annotations[name];
          })
          .filter(Boolean) as IAnnoItem[];
        const errMsg = checkDeleteAnnos(annos, commentMap);
        if (errMsg) {
          canNotRemove.push(start + 1);
        }
      }
      return canNotRemove.length > 0 ? `删除失败，请先删除含有该标注物的组合 ${canNotRemove.join(', ')} ` : undefined;
    },

    onTrackIdLabelAndAttrUpdate(type, trackId, elementIndex, label, attrs) {
      const {
        job,
        elementAnnos,
        seriesLinkage: { trackAnnoMap, trackTypeMap },
      } = get();
      const trackMap = trackAnnoMap.get(trackId);
      if (!job || !trackMap || type === SeriesOptScope.CURRENT) return;

      // 根据范围删除确定前后帧范围
      let start = 0,
        end = job.elements.length - 1;
      switch (type) {
        case SeriesOptScope.FORWARD:
          end = elementIndex - 1;
          break;
        case SeriesOptScope.BACKWARD:
          start = elementIndex + 1;
          break;
      }
      start = Math.min(Math.max(start, 0), job.elements.length - 1);
      end = Math.max(0, Math.min(end, job.elements.length - 1));
      let hasLabelChange = false;

      for (; start <= end; start++) {
        if (start === elementIndex) continue;
        const name = trackMap.get(start);
        const anno = name && elementAnnos[start].annotations[name];
        if (anno) {
          const oldLabel = anno.label;
          anno.updateLabelAndAttrs(label, { ...attrs });
          if (label && oldLabel !== label) {
            elementAnnos[start].updateAnnosInLabelMap([name], oldLabel, label);
            hasLabelChange = true;
          }
        }
      }

      if (hasLabelChange && label) {
        const trackType = trackTypeMap.get(trackId);
        if (trackType) {
          trackType.label = label;
        }
      }
    },
    onTrackIdChange(type, trackId, elementIndex, newTrackId) {
      const {
        job,
        elementAnnos,
        seriesLinkage: { trackAnnoMap, trackElementsState, getTrackIdWidgetByElementIndex, trackTypeMap },
        setElementsState,
        labelStage,
      } = get();
      const oldTrackMap = trackAnnoMap.get(trackId),
        newTrackMap = trackAnnoMap.get(newTrackId);
      const oldElementsState = trackElementsState.get(trackId),
        newElementsState = trackElementsState.get(newTrackId);
      if (!job || !oldTrackMap || !oldElementsState) return;
      // 根据范围删除确定前后帧范围和修改新旧 ElementState
      if (newTrackMap && newElementsState) {
        let newTrackIdKeyAnno: AnnoInstanceItem | null = null;
        // 得到关键帧后，就可以根据这个更新新修改的标注物尺寸和方向
        newTrackMap.forEach((name, eleIndex) => {
          const anno = elementAnnos[eleIndex].annotations[name];
          if (anno?.source === 'manual' && anno instanceof AnnoInstanceItem) {
            newTrackIdKeyAnno = anno;
          }
        });

        const changeOldTrackIdToKey = (
          eleIndex: number,
          trackMap: Map<number, string>,
          elementState: ElementState[]
        ) => {
          if (eleIndex >= 0 && eleIndex < job.elements.length) {
            const name = trackMap.get(eleIndex);
            const anno = name && elementAnnos[eleIndex].annotations[name];
            if (anno && anno instanceof AnnoInstanceItem) {
              // 此处都是根据 oldTrackId 来计算的
              const widget = getTrackIdWidgetByElementIndex(trackId, eleIndex);
              widget && anno.updateWidget(widget);
              anno.setSource('manual');
              elementState[eleIndex] = anno.isScaleIndependent ? ElementState.INDEPENDENT : ElementState.KEY;
            }
          }
        };

        const changeNewTrackIdToKey = (
          eleIndex: number,
          trackMap: Map<number, string>,
          elementState: ElementState[]
        ) => {
          if (eleIndex >= 0 && eleIndex < job.elements.length) {
            const name = trackMap.get(eleIndex);
            const anno = name && elementAnnos[eleIndex].annotations[name];
            if (anno && anno instanceof AnnoInstanceItem && newTrackIdKeyAnno) {
              AnnoFactory.keepManualAnnoWidget(newTrackIdKeyAnno!, anno);
              anno.setSource('manual');
              elementState[eleIndex] = anno.isScaleIndependent ? ElementState.INDEPENDENT : ElementState.KEY;
              // 更新当前帧的渲染
              if (labelStage?.currentSight) {
                const object = labelStage?.currentSight?.getObjectByName(name);
                object && AnnoFactory.updateFromWidget(object, anno.widget, labelStage.currentSight.getMatrixs()[0]);
              }
            }
          }
        };

        newTrackMap.set(elementIndex, oldTrackMap.get(elementIndex)!);

        let start = 0,
          end = job.elements.length - 1;
        switch (type) {
          case SeriesOptScope.CURRENT:
            start = elementIndex + 1;
            end = elementIndex - 1;
            changeOldTrackIdToKey(start, oldTrackMap, oldElementsState);
            changeOldTrackIdToKey(end, oldTrackMap, oldElementsState);
            oldElementsState[elementIndex] = ElementState.NORMAL;
            changeNewTrackIdToKey(elementIndex, newTrackMap, newElementsState);
            break;
          case SeriesOptScope.FORWARD:
            end = elementIndex - 1;
            const next = elementIndex + 1;
            changeOldTrackIdToKey(next, oldTrackMap, oldElementsState);
            oldElementsState[elementIndex] = ElementState.NORMAL;
            changeNewTrackIdToKey(elementIndex, newTrackMap, newElementsState);
            break;
          case SeriesOptScope.BACKWARD:
            start = elementIndex + 1;
            const prev = elementIndex - 1;
            changeOldTrackIdToKey(prev, oldTrackMap, oldElementsState);
            oldElementsState[elementIndex] = ElementState.NORMAL;
            changeNewTrackIdToKey(elementIndex, newTrackMap, newElementsState);
            break;
          case SeriesOptScope.ALL:
            newElementsState[elementIndex] = oldElementsState[elementIndex];
            break;
        }

        oldTrackMap.delete(elementIndex);

        start = Math.min(Math.max(start, 0), job.elements.length - 1);
        end = Math.max(0, Math.min(end, job.elements.length - 1));

        // 集中更新把 oldTrackId 中需要更新成 newTrackId 的范围内的标注物
        for (; start <= end; start++) {
          if (start === elementIndex) continue;
          const name = oldTrackMap.get(start);
          const anno = name && elementAnnos[start].annotations[name];
          if (anno) {
            anno.trackId = newTrackId;
            elementAnnos[start].updateTrackIdInLabelMap(anno.label, trackId, newTrackId);
            oldTrackMap.delete(start);
            newTrackMap.set(start, name);
            newElementsState[start] = oldElementsState[start];
            oldElementsState[start] = ElementState.NORMAL;
            // 需要更新尺寸和方向
            if (newElementsState[start] === ElementState.KEY && anno instanceof AnnoInstanceItem && newTrackIdKeyAnno) {
              AnnoFactory.keepManualAnnoWidget(newTrackIdKeyAnno, anno);
            }
          }
        }

        // 删除 trackId
        if (oldTrackMap.size === 0) {
          trackAnnoMap.delete(trackId);
          trackElementsState.delete(trackId);
          trackTypeMap.delete(trackId);
        }

        setElementsState([...newElementsState]);
      } else {
        let start = 0,
          end = job.elements.length - 1,
          oriTrackMap = new Map(),
          trackMap = new Map();

        switch (type) {
          case SeriesOptScope.CURRENT:
            start = elementIndex + 1;
            end = elementIndex - 1;
            break;
          case SeriesOptScope.FORWARD:
            end = elementIndex - 1;
            break;
          case SeriesOptScope.BACKWARD:
            start = elementIndex + 1;
            break;
        }
        start = Math.min(Math.max(start, 0), job.elements.length - 1);
        end = Math.max(0, Math.min(end, job.elements.length - 1));

        // 处理 elementsState
        const elementsState = [...oldElementsState];
        if (type !== SeriesOptScope.ALL) {
          for (let i = 0; i < job.elements.length; i++) {
            const name = oldTrackMap.get(i);
            if ((i >= start && i <= end) || i === elementIndex) {
              const anno = name && elementAnnos[i].annotations[name];
              if (anno && anno instanceof AnnoInstanceItem) {
                anno.trackId = newTrackId;
                elementAnnos[i].updateTrackIdInLabelMap(anno.label, trackId, newTrackId);
                trackMap.set(i, name);
              }
              oldElementsState[i] = ElementState.NORMAL;
              if (i === elementIndex) {
                elementsState[i] = ElementState.KEY;
              }
            } else {
              oriTrackMap.set(i, name);
              elementsState[i] = ElementState.NORMAL;
            }
          }

          const syncKeyFrame = (index: number, oldElementsState: ElementState[]) => {
            const name = oldTrackMap.get(index);
            const anno = name && elementAnnos[index].annotations[name];
            if (anno instanceof AnnoInstanceItem) {
              const widget = getTrackIdWidgetByElementIndex(anno.trackId, index);
              widget && anno.updateWidget(widget);
              if (anno.source === 'interpolation') {
                anno.setSource('manual');
                oldElementsState[index] = ElementState.KEY;
              }
            }
          };

          // 向后同步，处理当前帧的前一帧为关键帧
          if (type === SeriesOptScope.BACKWARD) {
            syncKeyFrame(elementIndex - 1, oldElementsState);
          }

          // 向前同步，处理当前帧的后一帧为关键帧
          if (type === SeriesOptScope.FORWARD) {
            syncKeyFrame(elementIndex + 1, oldElementsState);
          }

          trackAnnoMap.set(trackId, oriTrackMap);
          trackAnnoMap.set(newTrackId, trackMap);
          trackElementsState.set(trackId, oldElementsState);
          trackElementsState.set(newTrackId, elementsState);
          setElementsState([...elementsState]);
          const trackType = trackTypeMap.get(trackId);
          trackType && trackTypeMap.set(newTrackId, trackType);
        } else {
          for (let i = 0; i < job.elements.length; i++) {
            const name = oldTrackMap.get(i);
            const anno = name && elementAnnos[i].annotations[name];
            if (anno && anno instanceof AnnoInstanceItem) {
              anno.trackId = newTrackId;
              elementAnnos[i].updateTrackIdInLabelMap(anno.label, trackId, newTrackId);
            }
          }
          trackAnnoMap.set(newTrackId, oldTrackMap);
          trackAnnoMap.delete(trackId);
          trackElementsState.set(newTrackId, oldElementsState);
          trackElementsState.delete(trackId);
          const trackType = trackTypeMap.get(trackId);
          trackType && trackTypeMap.set(newTrackId, trackType);
          trackTypeMap.delete(trackId);
        }
      }
    },

    getTrackIdWidgetByElementIndex: (trackId, elementIndex) => {
      const {
        job,
        elementAnnos,
        seriesLinkage: { trackAnnoMap, elementsMatrixes, findAroundKeyAnnoInstanceItem },
      } = get();
      const trackMap = trackAnnoMap.get(trackId);
      if (!job || !trackMap) return null;

      const name = trackMap.get(elementIndex);
      const anno = name && elementAnnos[elementIndex].annotations[name];
      if (anno instanceof AnnoInstanceItem) {
        if (anno.source === 'manual') {
          return anno.widget;
        } else {
          const [prevIndex, nextIndex, prevAnno, nextAnno] = findAroundKeyAnnoInstanceItem(anno, elementIndex);

          if (prevAnno) {
            // 都先转换到世界坐标系下
            const beginWidget = AnnoFactory.getTransformedWidget(prevAnno.widget, elementsMatrixes[prevIndex][1]),
              endWidget = nextAnno
                ? AnnoFactory.getTransformedWidget(nextAnno.widget, elementsMatrixes[nextIndex][1])
                : null;
            if (!beginWidget) return null;
            // 相同坐标系下进行插值计算
            const widget = AnnoFactory.getInterpolatedAnnoWidget(
              beginWidget,
              endWidget,
              elementIndex - prevIndex,
              nextIndex - prevIndex - 1
            );
            if (!widget) return null;
            // 转换到当前帧的坐标系下
            return AnnoFactory.getTransformedWidget(widget, elementsMatrixes[elementIndex][0]);
          } else {
            return null;
          }
        }
      }
      return null;
    },
    setSeriesOptScope: (scope) => {
      set(({ seriesLinkage }) => ({
        seriesLinkage: {
          ...seriesLinkage,
          seriesOptScope: scope,
        },
      }));
    },
    updateAnnosWhenChangeSight: () => {
      const {
        job,
        currentElementIndex,
        editAnno,
        selectedAnnos,
        seriesLinkage,
        setSelectedAnnos,
        setEditAnno,
        getAnnosByTrackId,
      } = get();

      let hasAnnoInCurrentElement: boolean = false;
      const fileName = job?.elements[currentElementIndex].datas[0].name,
        focusTrackId = editAnno?.anno.trackId ?? selectedAnnos[0]?.trackId;

      // 有选中的 trackId
      if (focusTrackId) {
        const annos = getAnnosByTrackId(focusTrackId);
        if (annos && fileName && annos[fileName]) {
          const anno = annos[fileName];
          // 当前帧有对应的标注物 anno
          if (anno) {
            hasAnnoInCurrentElement = true;
            if (editAnno) {
              setEditAnno({
                anno,
                type: editAnno.type,
              });
            } else {
              setSelectedAnnos([anno]);
            }
          }
        }
      }

      // 有选中的 trackId 但是当前帧没有 anno
      const isEmptyInCurrentElement = !!focusTrackId && !hasAnnoInCurrentElement;
      if (seriesLinkage.isEmptyWhenTrackIdSelected !== isEmptyInCurrentElement) {
        set({
          seriesLinkage: {
            ...seriesLinkage,
            isEmptyWhenTrackIdSelected: isEmptyInCurrentElement,
          },
        });
      }
    },

    getFocusedTrackIdWidgetByFileName: (fileName) => {
      const {
        editAnno,
        selectedAnnos,
        getElementIndexByFileName,
        seriesLinkage: { getTrackIdWidgetByElementIndex },
      } = get();
      const trackId = editAnno?.anno.trackId ?? (selectedAnnos.length === 1 && selectedAnnos[0].trackId);
      if (trackId) {
        const elementIndex = getElementIndexByFileName(fileName);
        return getTrackIdWidgetByElementIndex(trackId, elementIndex);
      }
      return null;
    },
  },
});
