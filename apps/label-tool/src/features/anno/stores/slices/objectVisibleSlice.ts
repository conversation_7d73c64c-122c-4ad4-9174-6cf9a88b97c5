import { StateCreator } from 'zustand';

import { LotStore } from '@/stores';
import { Comment } from '@/types';
import { AnnoCompoundItem, AnnoInstanceItem, IAnnoItem } from '@/utils/annos';

import { AnnoSelectedStore } from './annoSelectedSlice';
import { AnnoStore } from './annoSlice';
import { CommentStore } from './commentSlice';

export enum VisibleStatus {
  // 默认值，默认是显示状态
  Show = -1,
  // 自己设置的隐藏状态
  Hidden = 0,
  // 标注物的状态如果是 >= 1，就说明肯定有包含这个标注物的组合标注物是隐藏的
  DisableMin = 1,
}

export enum VisibleOpt {
  // 自己手动变成显示
  SelfToShow = -1,
  // 自己手动变成隐藏
  SelfToHidden = 1,
  // 上一级变成显示，导致标注物的显隐变化
  ParentToShow = -2,
  // 上一级变成隐藏，导致标注物的显隐变化
  ParentToHidden = +2,
}

// 每个标注物的显隐控制的结构，所有的值就是 -1、0、所有的正数（取决于有几个包含的组合标注物）
// number 默认是 -1（显示）、0（自己控制隐藏）、> 0 （组合标注物控制隐藏）
interface ObjectVisible {
  // 批注的显隐
  commentsVisibleFlag: Record<Comment['uuid'], number>;
  // 标注物的显隐
  trackIdsVisibleFlag: Record<IAnnoItem['trackId'], number>;
}
export interface ObjectVisibleStore extends ObjectVisible {
  /**
   * 批量修改漏标的显隐
   * @param list 待修改的漏标的 name 列表
   * @param isShow 预期修改成的样子，true 表示显示，false 表示隐藏
   * @returns
   */
  changeCommentListVisible: (list: Array<Comment['uuid']>, isShow: boolean) => void;

  /**
   * 批量修改 trackIds 的标注物显隐
   * @param list 待修改的标注物的 trackId 列表
   * @param isShow 预期修改成的样子，true 表示显示，false 表示隐藏
   * @returns
   */
  changeAnnoListVisible: (list: Array<IAnnoItem>, isShow: boolean) => void;
}

const initialState: ObjectVisible = {
  commentsVisibleFlag: {},
  trackIdsVisibleFlag: {},
};

export const createObjectVisibleSlice: StateCreator<
  ObjectVisibleStore & AnnoStore & AnnoSelectedStore & LotStore & CommentStore,
  [],
  [],
  ObjectVisibleStore
> = (set, get) => ({
  ...initialState,

  changeCommentListVisible: (list: Array<Comment['uuid']>, isShow: boolean) =>
    set(({ commentsVisibleFlag: oldCommentsVisibleFlag }) => {
      const commentsVisibleFlag = { ...oldCommentsVisibleFlag };
      for (let i = 0, l = list.length; i < l; i++) {
        const uuid = list[i];
        if (commentsVisibleFlag[uuid] === undefined) {
          commentsVisibleFlag[uuid] = VisibleStatus.Show;
        }
        const visibilityFlag = commentsVisibleFlag[uuid];

        // 状态没变
        if ((visibilityFlag === VisibleStatus.Show && isShow) || (visibilityFlag === VisibleStatus.Hidden && !isShow)) {
          continue;
        }
        commentsVisibleFlag[uuid] += isShow ? VisibleOpt.SelfToShow : VisibleOpt.SelfToHidden;
      }
      return { commentsVisibleFlag };
    }),
  changeAnnoListVisible: (list, isShow) =>
    set(({ trackIdsVisibleFlag: oldTrackIdsVisibleFlag, annotations }) => {
      const trackIdsVisibleFlag = { ...oldTrackIdsVisibleFlag };
      // 解决组合标注物多层级覆盖，无法全部控制展开或者隐藏的功能
      if (isShow) list.reverse();

      for (let i = 0, l = list.length; i < l; i++) {
        const anno = list[i],
          trackId = anno.trackId;
        if (trackIdsVisibleFlag[trackId] === undefined) {
          // 没有值的话，就是默认显示；
          trackIdsVisibleFlag[trackId] = VisibleStatus.Show;
        }
        const visibilityFlag = trackIdsVisibleFlag[trackId];

        // 没有权限控制显隐 或者 状态没变，不用往下
        if (
          visibilityFlag >= VisibleStatus.DisableMin ||
          (visibilityFlag === VisibleStatus.Show && isShow) ||
          (visibilityFlag === VisibleStatus.Hidden && !isShow)
        ) {
          continue;
        }

        trackIdsVisibleFlag[trackId] += isShow ? VisibleOpt.SelfToShow : VisibleOpt.SelfToHidden;

        if (anno instanceof AnnoCompoundItem) {
          getAllChildAnnos(anno, annotations).forEach(({ trackId }) => {
            if (typeof trackIdsVisibleFlag[trackId] === 'undefined') {
              trackIdsVisibleFlag[trackId] = VisibleStatus.Show;
            }
            trackIdsVisibleFlag[trackId] += isShow ? VisibleOpt.ParentToShow : VisibleOpt.ParentToHidden;
          });
        }
      }
      return {
        trackIdsVisibleFlag,
      };
    }),
});

// 得到组合标注物的所有子标注物
const getAllChildAnnos = (annoItem: AnnoCompoundItem, annotations: Record<IAnnoItem['name'], IAnnoItem | null>) => {
  const set = new Set<IAnnoItem>();

  const getChildObject = (anno: IAnnoItem | null, ans: Set<IAnnoItem>) => {
    if (!anno) return;

    if (anno !== annoItem) ans.add(anno);

    if (anno instanceof AnnoInstanceItem) return;

    const children = anno.compound;
    for (let i = 0, l = children.length; i < l; i++) {
      getChildObject(annotations[children[i]], ans);
    }
  };

  getChildObject(annoItem, set);
  return set;
};
