import type { StateCreator } from 'zustand';

import type { DisplayType } from '@/components';
import { JobStore, LabelStore } from '@/stores';
import type { Job, LoadStatus, LotRange, Rawdata } from '@/types';
import { SeriesOptScope } from '@/types';
import { AnnoInstanceItem } from '@/utils/annos';
import { isNoCoordinate } from '@/utils/math';
import { Stage } from '@/utils/render';
import { ToolType } from '@/utils/tool';

import { AnnoFlowStore } from './annoFlowSlice';
import { AnnoStore } from './annoSlice';
import { CommentMissedStore } from './commentMissedSlice';
import { CommentStore } from './commentSlice';
import { ElementStore } from './elementSlice';
import { ObjectVisibleStore } from './objectVisibleSlice';
import { SeriesLinkageStore } from './seriesLinkageSlice';

interface IPointsMeta {
  fileName: Rawdata['name'];
  zRange: [number, number];
  intensityRange?: [number, number];
}

export type PosePoints = Array<[number, number, number]>;

/**
 * 渲染数据、转换矩阵相关数据存储
 * 点云渲染策略：根据车姿 (viewpoint) 数据将点云移动到 three.js 坐标系中心点
 * 图片渲染策略：图片【左上角】与 three.js 坐标系【中心点】重合，渲染宽度阈值为 IMG_RENDER_WIDTH_MAX
 */

export interface IRender {
  /** 主场景绘制 */
  labelStage: Stage | null;

  /** Rawdata 文件加载后的元数据信息 */
  rawMeta: Partial<Record<Rawdata['type'], IPointsMeta>>;

  // 所有帧的车姿位置。相应位置对应 Job 的 Elements 相应位置的 relativePose 的结果。[[x1,y1,z1], [x2,y2,z2], ...] three.js 坐标系
  posePoints: PosePoints;
  ranges: Array<LotRange>;
  displayValues: Record<DisplayType, boolean>;

  // 展示加载进度
  loadingProgressInfo?: Record<string, string>;

  // for dev 需要展示的信息
  devMode: boolean;

  /** 控制 tools 是否允许使用，默认都是可以使用的，只有 false 是完全不支持使用的 */
  toolControls: Partial<Record<ToolType, boolean>>;
}

export interface RenderStore extends IRender {
  setLabelStage: (stage: Stage) => void;

  initPoseAndRange: (posePoints: PosePoints, ranges: Array<LotRange>) => void;

  /**
   * 更新 labelStage 当前 sight 所加载 rawdata 文件的元信息
   * @param type
   * @param value
   * @returns
   */
  updateRawMeta: (type: keyof IRender['rawMeta'], value: IPointsMeta) => void;
  /**
   * 更新场景展示
   * @param type
   * @returns
   */
  toggleSceneConfig: (type: DisplayType) => void;

  /**
   * 更新 dev mode 下的信息
   * @param info 更新的信息
   * @returns
   */
  toggleDevMode: () => void;
  /**
   * 更新第几帧的加载进度
   * @param name 当前帧的文件名
   * @param data 加载进度信息
   * @returns
   */
  updateLoadingProgressInfo: (name: string, data: LoadStatus) => void;

  /**
   * 根据 job 信息配置 toolControls
   * @param job
   * @returns
   */
  updateToolControls: (job: Job) => void;

  /**
   * 根据文件名获取当前文件已经更新好 widget 标注物
   * @param fileName
   * @returns
   */
  getRenderAnnosByFile: (fileName: Rawdata['name']) => Array<AnnoInstanceItem>;

  /**
   * 删除无效坐标的标注物
   * @param anno
   * @returns
   */
  removeInvalidAnno: (anno: AnnoInstanceItem) => void;

  /**
   * 渲染当前场景的标注与批注
   * @param sceneName
   * @returns
   */
  renderAnnosAndComments: () => void;

  /**
   * 通过 fileName 获取 elementIndex
   */
  getElementIndexByFileName: (fileName: Rawdata['name']) => number;
}

export const createRenderSlice: StateCreator<
  RenderStore &
  AnnoStore &
  AnnoFlowStore & { seriesLinkage: SeriesLinkageStore } & ElementStore &
  CommentStore &
  CommentMissedStore &
  LabelStore &
  JobStore &
  ObjectVisibleStore,
  [],
  [],
  RenderStore
> = (set, get) => ({
  labelStage: null,
  rawMeta: {},

  posePoints: [],
  ranges: [],

  displayValues: {
    /** 车姿显示 */
    poseVisible: false,
    /** 标注范围显示 */
    rangeVisible: false,
    /** 是否开启 bev 模式 */
    isBevModel: false,
  },

  toolControls: {},

  devMode: false,

  setLabelStage: (labelStage) => {
    set({ labelStage });
  },

  initPoseAndRange: (posePoints, ranges) =>
    set({
      posePoints,
      ranges,
      displayValues: {
        poseVisible: posePoints.length > 0,
        rangeVisible: ranges.length > 0,
        isBevModel: false,
      },
    }),

  updateRawMeta: (type, data) =>
    set(({ rawMeta }) => {
      return {
        rawMeta: {
          ...rawMeta,
          [type]: {
            ...rawMeta[type],
            ...data,
          },
        },
      };
    }),

  toggleSceneConfig: (type) => {
    const { displayValues } = get();

    if (typeof displayValues[type] === 'boolean') {
      set({
        displayValues: {
          ...displayValues,
          [type]: !displayValues[type],
        },
      });
    }
  },

  toggleDevMode: () =>
    set(({ devMode: oldDevMode }) => ({
      devMode: !oldDevMode,
    })),
  updateLoadingProgressInfo: (name, { info, progress }) => {
    const { loadingProgressInfo = {} } = get();
    const oldLoadingProgressInfo = loadingProgressInfo[name];

    if (oldLoadingProgressInfo === '初始化完成') return;

    let str = '';
    switch (info) {
      case 'downloading':
        str = '下载中 ' + (progress ?? 0) + '%';
        // 由于 onProgress 的调用被 throttle ，progress 的更新有延迟，当 loadingProgressInfo 已进入下一阶段时，不再更新回 downloading
        if (
          progress &&
          (oldLoadingProgressInfo === '加载中，请稍候' ||
            oldLoadingProgressInfo === '优化中' ||
            oldLoadingProgressInfo === '初始化完成')
        ) {
          return;
        }
        break;
      case 'loading':
        str = '加载中，请稍候';
        break;
      case 'computing':
        str = '优化中';
        break;
      case 'finish':
        str = '初始化完成';
        break;
      case 'error':
        str = '错误：' + progress;
        break;
    }
    if (oldLoadingProgressInfo === str) return;
    set({
      loadingProgressInfo: {
        ...loadingProgressInfo,
        [name]: str,
      },
    });
  },

  // TODO：后续需要根据 lot 来配置 toolControls
  updateToolControls: (job) => {
    const { phase } = job;
    // 标注阶段不允许使用批注工具
    if (phase <= 1) {
      set({
        toolControls: {
          pyramid: false,
        },
      });
    }
  },

  removeInvalidAnno: (anno: AnnoInstanceItem) => {
    const {
      dataElementIndex,
      seriesLinkage: { onTrackIdRemove },
      labelStage,
      removeAnnoObject,
      updateCommentsWithDeleteAnno,
      elementAnnos,
    } = get();
    const rawdataName = labelStage?.currentSight?.getCurrentFile()?.name;
    if (rawdataName) {
      // ✅ 修复：确保标注物确实存在才删除批注，保证原子性
      const annoExists = elementAnnos[dataElementIndex]?.annotations[anno.name];
      if (annoExists) {
        console.log('🗑️ 删除无效标注物和批注:', {
          annoName: anno.name,
          dataElementIndex,
          rawdataName,
          timestamp: Date.now()
        });
        updateCommentsWithDeleteAnno(anno.name);
        removeAnnoObject(anno, rawdataName);
        onTrackIdRemove(SeriesOptScope.CURRENT, anno.trackId, dataElementIndex, anno);
      } else {
        console.warn('⚠️ 标注物不存在，跳过删除:', {
          annoName: anno.name,
          dataElementIndex
        });
      }
    }
  },

  getRenderAnnosByFile: (fileName) => {
    const {
      getAnnosByFile,
      dataElementIndex,
      seriesLinkage: { isEnabled, getTrackIdWidgetByElementIndex },
      removeInvalidAnno,
    } = get();
    const annos = getAnnosByFile(fileName);
    return annos.reduce<AnnoInstanceItem[]>((result, anno) => {
      if (isEnabled && anno.source !== 'manual') {
        // 如果是插值生成的标注物，需要更新 widget
        const widget = getTrackIdWidgetByElementIndex(anno.trackId, dataElementIndex);
        if (widget) {
          anno.updateWidget(widget);
          result.push(anno);
        } else {
          // ✅ 修复：记录日志，避免误删连续帧插值标注物的批注
          console.warn('⚠️ 连续帧插值标注物widget无效:', {
            annoName: anno.name,
            trackId: anno.trackId,
            dataElementIndex
          });
          removeInvalidAnno(anno);
        }
      } else {
        if (isNoCoordinate(anno.widget.data)) {
          // ✅ 修复：记录日志，避免误删坐标无效标注物的批注
          console.warn('⚠️ 标注物坐标无效:', {
            annoName: anno.name,
            widget: anno.widget.data
          });
          removeInvalidAnno(anno);
        } else {
          result.push(anno);
        }
      }
      return result;
    }, []);
  },

  renderAnnosAndComments: () => {
    const {
      annotations,
      annoCommentMap,
      labelStage,
      labelMap,
      seriesLinkage,
      dataElementIndex,
      trackIdsVisibleFlag,
      currentType,
      getRenderAnnosByFile,
      getCurrentMissedComments,
      resetAnnoAndTool,
      getElementIndexByFileName,
    } = get();
    const current = labelStage?.currentSight;
    if (current) {
      const fileName = current.getCurrentFile().name;
      const elementIndex = getElementIndexByFileName(fileName);
      // 在快速切帧的时候, 可能出现 elementIndex 与 dataElementIndex 不一致的情况
      if (elementIndex !== dataElementIndex) return;
      const missed = getCurrentMissedComments();
      const annoObjects = getRenderAnnosByFile(fileName);
      const visibilityFlag =
        currentType === 'segment'
          ? annoObjects.reduce<Record<string, number>>((acc, anno) => {
            acc[anno.trackId] = 1;
            return acc;
          }, {})
          : trackIdsVisibleFlag;
      current.updateObjectList(annoObjects, labelMap, visibilityFlag, true);
      current.updateCommentObjects(missed);
      current.updateObjectsByComments(annoCommentMap, missed, annotations, labelMap);

      if (seriesLinkage.isEnabled) {
        // 连续帧选中物逻辑切帧保持
        seriesLinkage.updateAnnosWhenChangeSight();
      } else {
        // 离散帧切帧需要重置工具
        resetAnnoAndTool();
      }
    }
  },

  getElementIndexByFileName: (fileName) => {
    const { job } = get();
    if (!job) return 0;
    const { elements } = job,
      elementIndex = elements.findIndex((element) => {
        return element.datas.some((data) => data.name === fileName);
      });
    return elementIndex;
  },
});
