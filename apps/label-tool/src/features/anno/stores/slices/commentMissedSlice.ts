import { StateCreator } from 'zustand';

import { Comment } from '@/types';

import { AnnoFlowStore } from './annoFlowSlice';
import { AnnoSelectedStore } from './annoSelectedSlice';
import { CommentStore } from './commentSlice';
import { ElementStore } from './elementSlice';

interface CommentMissed {
  /** 每一帧对应的漏标 */
  missedCommentMap: Record<number, Comment[]>;
  /** 当前正在编辑的批注 */
  currentComment: Comment | null;
  /** 当前选中的批注 */
  selectedComment: Comment | null;
}

const initialState: CommentMissed = {
  missedCommentMap: {},
  currentComment: null,
  selectedComment: null,
};

export interface CommentMissedStore extends CommentMissed {
  /**
   * 设置正在编辑的漏标
   * @param comment 漏标的批注
   * @returns
   */
  setCurrentComment: (comment: Comment | null) => void;
  /**
   * 设置选中的漏标
   * @param comment 漏标的批注
   * @returns
   */
  setSelectedComment: (comment: Comment | null) => void;
  /**
   * 完成对当前标注物的修改，变成 resolve 状态
   * @returns
   */
  resolveCurrentMissedComment: () => void;
  /**
   * 取消对当前标注物的修改，不改变状态
   * @returns
   */
  cancelCurrentMissedComment: () => void;
  /**
   * 获取当前帧的所有漏标
   * @returns
   */
  getCurrentMissedComments: () => Comment[] | undefined;
}

export const createCommentMissedSlice: StateCreator<
  CommentMissedStore & AnnoSelectedStore & AnnoFlowStore & CommentStore & ElementStore,
  [],
  [],
  CommentMissedStore
> = (set, get) => ({
  ...initialState,
  setCurrentComment: (comment) => {
    set(() => {
      if (comment === null) {
        return {
          currentComment: null,
        };
      }
      return {
        currentComment: comment,
        selectedComment: null,
      };
    });
  },
  setSelectedComment: (comment) => {
    set(({ selectedAnnos, currentComment }) => {
      // 在 currentComment 有值的情况下，无法更新 selectedComment
      if (currentComment) return {};

      if (comment === null) {
        return {
          selectedComment: null,
        };
      }
      return {
        editAnno: null,
        selectedAnnos: selectedAnnos.length === 0 ? selectedAnnos : [],
        currentComment: null,
        selectedComment: comment,
      };
    });
  },

  resolveCurrentMissedComment: () => {
    const { currentComment, commentMap, resolveComments, missedCommentMap, editAnno, selectedAnnos } = get();
    if (!currentComment) return;

    let newResolveComments = resolveComments;
    // 也有可能是已经 resolve 的批注，但是又添加了标注物
    if (currentComment.status !== 'pending') {
      currentComment.status = 'pending';
      newResolveComments = resolveComments.concat(currentComment);
      const index = missedCommentMap[currentComment.elem_idx]?.findIndex(
        (comment) => comment.uuid === currentComment.uuid
      );
      if (index !== -1) {
        missedCommentMap[currentComment.elem_idx][index].status = 'pending';
      }
    }

    set({
      commentMap: {
        ...commentMap,
        [currentComment.uuid]: currentComment,
      },
      missedCommentMap: {
        ...missedCommentMap,
        [currentComment.elem_idx]: missedCommentMap[currentComment.elem_idx],
      },
      currentComment: null,
      resolveComments: newResolveComments,
      selectedComment: editAnno?.type === 'widget' || selectedAnnos.length > 0 ? null : currentComment,
    });
  },
  cancelCurrentMissedComment: () => {
    set(({ currentComment }) => ({
      currentComment: null,
      selectedComment: currentComment,
    }));
  },

  getCurrentMissedComments: () => {
    const { missedCommentMap, dataElementIndex } = get();

    return missedCommentMap[dataElementIndex];
  },
});
