import { pick } from 'lodash';
import { temporal } from 'zundo';
import { createStore } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';

import {
  createJobSlice,
  createLabelSlice,
  createLotSlice,
  getDEVOptions,
  getPersistDefaults,
  JobStore,
  LabelStore,
  LotStore,
} from '@/stores';
import type { Job, JobAnno, Lot } from '@/types';
import { annoTrackIdGenerator } from '@/utils/annos/GenerateTrackId';
import { RawPoints } from '@/utils/render';

import {
  AnnoEventStore,
  AnnoFlowStore,
  AnnoSelectedStore,
  AnnoStore,
  CommentHoverStore,
  CommentMissedStore,
  CommentStore,
  createAnnoFlowSlice,
  createAnnoSelectedSlice,
  createAnnoSlice,
  createCommentHoverSlice,
  createCommentMissedSlice,
  createCommentSlice,
  createElementSlice,
  createFusionSlice,
  createObjectVisibleSlice,
  createOverlaySlice,
  createRenderSlice,
  createSeriesSlice,
  createSnapshotSlice,
  ElementStore,
  FusionLinkageStore,
  ObjectVisibleStore,
  OverlayStore,
  RenderStore,
  SeriesLinkageStore,
  SnapshotData,
  SnapshotStore,
} from './slices';
import { getTemporalOptions } from './utils';

export type LaneAnnoStore = LotStore &
  JobStore &
  LabelStore &
  AnnoStore &
  AnnoFlowStore &
  AnnoEventStore &
  ElementStore &
  CommentStore &
  SnapshotStore &
  RenderStore & { fusionLinkage: FusionLinkageStore } & { seriesLinkage: SeriesLinkageStore } & {
    initJobData: (job: Job, lot: Lot, userId: string) => void;
    exportAnnotations: () => JobAnno;
  } & ObjectVisibleStore &
  AnnoSelectedStore &
  CommentHoverStore &
  CommentMissedStore &
  OverlayStore;

export const laneAnnoStore = createStore<LaneAnnoStore>()(
  devtools(
    subscribeWithSelector(
      persist(
        temporal(
          (set, get, store) => ({
            ...createLotSlice(set, get, store),
            ...createJobSlice(set, get, store),

            ...createLabelSlice(set, get, store),
            ...createAnnoSlice(set, get, store),
            ...createAnnoFlowSlice(set, get, store),
            ...createAnnoSelectedSlice(set, get, store),
            ...createElementSlice(set, get, store),
            ...createRenderSlice(set, get, store),
            ...createCommentSlice(set, get, store),

            ...createSeriesSlice(set, get, store),
            ...createFusionSlice(set, get, store),
            ...createSnapshotSlice(set, get, store),

            ...createObjectVisibleSlice(set, get, store),
            ...createCommentHoverSlice(set, get, store),
            ...createCommentMissedSlice(set, get, store),
            ...createOverlaySlice(set, get, store),

            initJobData: (job, lot, userId) => {
              const {
                fusionLinkage,
                setJob,
                setLot,
                importLabels,
                initAnnotations,
                initElementsStateAndAttrs,
                initComments,
                initFromSnapshot,
                initPoseAndRange,
                updateToolControls,
              } = get();

              // 初始化无依赖的数据切片
              setJob(job, lot);
              setLot(lot);
              importLabels(job.subtype, lot.ontologies);

              // 优先从本地缓存中初始化，若失败则从接口初始化
              if (!initFromSnapshot(job, lot, userId)) {
                // 根据标注数据初始化 trackId，必须保证在 AnnoInstanceItem 创建之前调用
                annoTrackIdGenerator.initTrackId(job.annotations);

                initAnnotations(job);
                initElementsStateAndAttrs(job, lot);
                initComments(job, lot);
              }

              // 融合策略需在标注数据初始化之后
              fusionLinkage.init(lot, job);
              const posePoints = job.elements
                .map((element) => {
                  return element.relativePose?.slice(0, 3);
                })
                .filter((pose): pose is [number, number, number] => Boolean(pose));

              initPoseAndRange(posePoints, lot?.tool_cfg?.ranges ?? []);
              // 配置工具是否允许使用
              updateToolControls(job);
              RawPoints.ALGOR_STRATEGY = 'intensityLinearSort';
            },

            exportAnnotations: () => {
              const { exportAnnoDatas } = get();

              return exportAnnoDatas({
                needInterpolation: false,
              });
            },

            // TODEL: 迁移到 AnnoFlow 的 onCurrentAnnoChange
            onAnnoAdd: (anno, rawdata) => {
              const { job, labelStage, addAnnoObject } = get();
              // 确保添加事件在初始化完成后
              if (!job || !labelStage) return;

              addAnnoObject(anno, rawdata.name);
            },
            onAnnoRemove: (anno, rawdata) => {
              const { job, labelStage, removeAnnoObject } = get();
              // 确保添加事件在初始化完成后
              if (!job || !labelStage) return;

              removeAnnoObject(anno, rawdata.name);
            },
            onAnnoUpdate: () => {},
          }),
          getTemporalOptions()
        ),
        getPersistDefaults<SnapshotData, LaneAnnoStore>()
      )
    ),
    getDEVOptions('LaneAnnoStore')
  )
);

laneAnnoStore.subscribe(
  (state) => pick(state, ['currentAnno', 'onCurrentAnnoChange']),
  ({ currentAnno, onCurrentAnnoChange }) => {
    currentAnno && onCurrentAnnoChange(currentAnno);
  },
  {
    equalityFn: (a, b) => a.currentAnno === b.currentAnno,
  }
);
