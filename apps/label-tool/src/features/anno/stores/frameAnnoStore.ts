import { pick } from 'lodash';
import { temporal } from 'zundo';
import { createStore } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';

import {
  createJobSlice,
  createLabelSlice,
  createLotSlice,
  createSegLabelSlice,
  getDEVOptions,
  getPersistDefaults,
  JobStore,
  LabelStore,
  LotStore,
  SegLabelStore,
} from '@/stores';
import { type Job, type JobAnno, type Lot, SeriesOptScope } from '@/types';
import { AnnoInstanceItem } from '@/utils/annos';
import { annoTrackIdGenerator } from '@/utils/annos/GenerateTrackId';
import { RawPoints, SseEditor } from '@/utils/render';

import { labelTemplate } from '../utils';
import {
  AnnoEventStore,
  AnnoFlowStore,
  AnnoSelectedStore,
  AnnoStore,
  CommentHoverStore,
  CommentMissedStore,
  CommentStore,
  createAnnoFlowSlice,
  createAnnoSelectedSlice,
  createAnnoSlice,
  createCommentHoverSlice,
  createCommentMissedSlice,
  createCommentSlice,
  createElementSlice,
  createFusionSlice,
  createObjectVisibleSlice,
  createOverlaySlice,
  createRenderSlice,
  createSegmentSlice,
  createSeriesSlice,
  createSnapshotSlice,
  ElementStore,
  FusionLinkageStore,
  ObjectVisibleStore,
  OverlayStore,
  RenderStore,
  SegmentStore,
  SeriesLinkageStore,
  SnapshotData,
  SnapshotStore,
} from './slices';
import { getTemporalOptions } from './utils';

export type FrameAnnoStore = LotStore &
  JobStore &
  LabelStore &
  AnnoStore &
  AnnoFlowStore &
  AnnoEventStore &
  ElementStore &
  CommentStore &
  SnapshotStore &
  OverlayStore &
  RenderStore & { fusionLinkage: FusionLinkageStore } & { seriesLinkage: SeriesLinkageStore } & {
    initJobData: (job: Job, lot: Lot, userId: string) => void;
    exportAnnotations: () => JobAnno;
  } & ObjectVisibleStore &
  AnnoSelectedStore &
  CommentHoverStore &
  CommentMissedStore &
  SegLabelStore &
  SegmentStore;

export const frameAnnoStore = createStore<FrameAnnoStore>()(
  devtools(
    subscribeWithSelector(
      persist(
        temporal(
          (set, get, store) => ({
            ...createLotSlice(set, get, store),
            ...createJobSlice(set, get, store),

            ...createLabelSlice(set, get, store),
            ...createSegLabelSlice(set, get, store),
            ...createSegmentSlice(set, get, store),
            ...createAnnoSlice(set, get, store),
            ...createAnnoFlowSlice(set, get, store),
            ...createAnnoSelectedSlice(set, get, store),
            ...createElementSlice(set, get, store),
            ...createRenderSlice(set, get, store),
            ...createCommentSlice(set, get, store),

            ...createSeriesSlice(set, get, store),
            ...createFusionSlice(set, get, store),
            ...createSnapshotSlice(set, get, store),

            ...createObjectVisibleSlice(set, get, store),
            ...createCommentHoverSlice(set, get, store),
            ...createCommentMissedSlice(set, get, store),
            ...createOverlaySlice(set, get, store),

            initJobData: (job, lot, userId) => {
              const {
                fusionLinkage,
                seriesLinkage,
                setJob,
                setLot,
                importLabels,
                initAnnotations,
                initComments,
                initFromSnapshot,
                initElementsStateAndAttrs,
                initPoseAndRange,
                updateToolControls,
                initSegLabels,
              } = get();

              // 初始化无依赖的数据切片
              setJob(job, lot);
              setLot(lot);

              importLabels(job.subtype, lot.ontologies);
              // 初始化标签模版
              labelTemplate.initTemplateFromLot(lot.uid);

              if (lot?.tool_cfg?.segmentation_3d_enabled) {
                initSegLabels(job.subtype, lot.ontologies);
              }

              // 优先从本地缓存中初始化，若失败则从接口初始化
              if (!initFromSnapshot(job, lot, userId)) {
                // 根据标注数据初始化 trackId，必须保证在 AnnoInstanceItem 创建之前调用
                annoTrackIdGenerator.initTrackId(job.annotations);

                initAnnotations(job);

                initElementsStateAndAttrs(job, lot);
                initComments(job, lot);

                if (lot?.tool_cfg?.segmentation_3d_enabled) {
                  SseEditor.initElementSegments(job.annotations);
                }
              }

              // 连续帧策略
              seriesLinkage.init(job, lot);
              // 融合策略需在标注数据初始化之后
              fusionLinkage.init(lot, job);

              lot.is_frame_series && initSubscribeForElementsState();

              initPoseAndRange([], lot?.tool_cfg?.ranges ?? []);
              // 设置配置工具是否允许使用
              updateToolControls(job);
              // 3D 标注点云智能着色方案采用聚类策略
              RawPoints.ALGOR_STRATEGY = 'intensityCluster';
            },
            exportAnnotations: () => {
              const { exportAnnoDatas } = get();

              return exportAnnoDatas({
                needInterpolation: false,
              });
            },

            onAnnoAdd: (anno, rawdata, elementIndex) => {
              const { labelStage, seriesLinkage, addAnnoObject } = get();
              if (anno && labelStage) {
                addAnnoObject(anno, rawdata.name);
                if (seriesLinkage.isEnabled) {
                  if (anno instanceof AnnoInstanceItem) {
                    // 实体标注物只能是向后添加
                    seriesLinkage.onTrackIdAdd(SeriesOptScope.BACKWARD, anno.trackId, elementIndex, anno);
                  } else {
                    seriesLinkage.onTrackIdAdd(seriesLinkage.seriesOptScope, anno.trackId, elementIndex, anno);
                  }
                }
              }
            },
            onAnnoRemove: (anno, rawdata, elementIndex) => {
              const { labelStage, removeAnnoObject, seriesLinkage } = get();
              if (anno && labelStage) {
                removeAnnoObject(anno, rawdata.name);
                if (seriesLinkage.isEnabled) {
                  seriesLinkage.onTrackIdRemove(seriesLinkage.seriesOptScope, anno.trackId, elementIndex, anno);
                }
              }
            },
            onAnnoUpdate: (anno, rawdata, elementIndex, oldWidget) => {
              const { seriesLinkage } = get();
              if (anno && anno instanceof AnnoInstanceItem && seriesLinkage.isEnabled) {
                seriesLinkage.onAnnoUpdateWidget(anno, rawdata, elementIndex, oldWidget);
              }
            },
          }),
          getTemporalOptions(),
        ),
        getPersistDefaults<SnapshotData, FrameAnnoStore>(),
      ),
    ),
    getDEVOptions('FrameAnnoStore'),
  ),
);

frameAnnoStore.subscribe(
  (state) => pick(state, ['currentAnno', 'onCurrentAnnoChange']),
  ({ currentAnno, onCurrentAnnoChange }) => {
    currentAnno && onCurrentAnnoChange(currentAnno);
  },
  {
    equalityFn: (a, b) => a.currentAnno === b.currentAnno,
  },
);

function initSubscribeForElementsState() {
  frameAnnoStore.subscribe(
    (state) => pick(state, ['editAnno', 'setElementsState', 'seriesLinkage', 'selectedAnnos']),
    ({ editAnno, setElementsState, seriesLinkage, selectedAnnos }) => {
      if (editAnno || selectedAnnos.length === 1) {
        const elementsState = seriesLinkage.trackElementsState.get(editAnno?.anno.trackId ?? selectedAnnos[0].trackId);
        if (elementsState) {
          setElementsState(elementsState);
        }
      } else {
        setElementsState();
      }
    },
    {
      equalityFn: (a, b) => {
        const trackIdA = a.editAnno?.anno.trackId ?? (a.selectedAnnos.length === 1 && a.selectedAnnos[0].trackId);
        const trackIdB = b.editAnno?.anno.trackId ?? (b.selectedAnnos.length === 1 && b.selectedAnnos[0].trackId);
        return trackIdA === trackIdB;
      },
    },
  );
}
