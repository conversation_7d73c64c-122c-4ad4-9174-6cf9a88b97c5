import type { FrameAnnoStore } from './frameAnnoStore';
import type { LaneAnnoStore } from './laneAnnoStore';

export const formatFrameAnnoState = (state: FrameAnnoStore | LaneAnnoStore) => {
  const {
    job,
    jobLabelDataInfo,
    currentPhase,
    currentElementIndex,
    lot,
    labelStage,
    elementAttrs,
    overlayFrames,
    isInOverlay,
    initJobData,
    setElementIndex,
    setCurrentAnno,
    setOverlayNumber,
    renderAnnosAndComments,
  } = state;

  return {
    lot,
    job,
    jobLabelDataInfo,
    currentPhase,
    currentElementIndex,
    dataList: job?.elements[currentElementIndex]?.datas ?? [],
    labelStage,
    elementAttrs,
    overlayFrames,
    isInOverlay,
    initJobData,
    setElementIndex,
    setCurrentAnno,
    setOverlayNumber,
    renderAnnosAndComments,
  };
};

export const formatLaneAnnoState = (state: LaneAnnoStore) => {
  const {
    job,
    jobLabelDataInfo,
    currentPhase,
    currentElementIndex,
    lot,
    labelStage,
    displayValues,
    posePoints,
    rawMeta,
    elementAttrs,
    selectedComment,
    initJobData,
    setElementIndex,
    setCurrentAnno,
    setSelectedComment,
  } = state;

  return {
    lot,
    job,
    jobLabelDataInfo,
    currentPhase,
    currentElementIndex,
    dataList: job?.elements[currentElementIndex]?.datas ?? [],
    labelStage,
    poseVisible: displayValues.poseVisible,
    posePoints,
    elementAttrs,
    rawMeta,
    selectedComment,
    initJobData,
    setElementIndex,
    setCurrentAnno,
    setSelectedComment,
  };
};
