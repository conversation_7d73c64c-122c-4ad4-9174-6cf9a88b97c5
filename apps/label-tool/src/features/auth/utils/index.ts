import { GlobalLoader } from '@/components';
import { initReactQueryAuth } from '@/lib/auth';

import { getMe as loadUser } from '../api';
import type { User } from '../types';

const config = {
  loadUser,
  loadErrorHandler: (error: any) => {
    if (error.status < 500) {
      return '';
    }
    return error.message;
  },
  LoaderComponent: GlobalLoader,
};

export const { AuthProvider, useAuth } = initReactQueryAuth<User | undefined, Error>(config);
