import { from, map } from 'rxjs';

import type {
  iam_v1_LoginRequest as LoginRequest,
  iam_v1_SendAuthCodeReply as SendAuthCodeReply,
  iam_v1_SendAuthCodeRequest as SendAuthCodeRequest,
} from '@/service/iam/v1';
import { Configs as ConfigService, Users as UserService } from '@/service/iam/v1';

export type { LoginRequest, SendAuthCodeReply };

export const getErrors = () => from(ConfigService.configsListErrors());

export const getMe = () => from(UserService.usersGetMe());
export const login = ({ params }: { params: LoginRequest }) => {
  return from(UserService.usersLogin({ requestBody: params })).pipe(map(({ user }) => user));
};

export const sendVerifyCode = (phoneNumber: string) => {
  const requestBody: SendAuthCodeRequest = {
    purpose: 'login',
    channel: 'sms',
    locale: 'zh-<PERSON>',
    receiver: phoneNumber,
  };
  return from(UserService.usersSendAuthCode({ requestBody }));
};
