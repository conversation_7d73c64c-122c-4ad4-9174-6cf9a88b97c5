import { Button, Input, InputRef, Typography } from 'antd';
import { CSSProperties, useRef, useState } from 'react';
import { Matrix4 } from 'three';

import { Extrinsic } from '@/utils/math';

const { Title, Paragraph } = Typography;

/** 转向矩阵 */
const TRANS_MAT = new Matrix4().set(0, -1, 0, 0, 0, 0, -1, 0, 1, 0, 0, 0, 0, 0, 0, 1);

const INSTRUCTS = `使用说明：

1. 输入：

  1.1 请填入7个数作为外参，不限定分隔符，肉眼能分辨出是【7】个数即可
  1.2 顺序为先平移，后旋转 (translation_x translation_y translation_z rotation_x rotation_y rotation_z rotation_w)

2. 解析失败 => 检查输入

3. 解析成功 =>

  3.1 检查输出的【原始外参】是否符合预期，如不符合预期 => 检查数值是否被正确分隔

  3.2 优先使用【逆矩阵】结果作为外参矩阵，如不符合预期 =>

    3.2.1 映射框在真实对象【附近】 => 添加 / 删除畸变参数
    3.2.2 映射框在真实对象【对面】 => 尝试使用【逆矩阵 + 转向处理】结果
    
`;

export const Math = () => {
  const [results, setResults] = useState<
    Array<{
      label?: string;
      value: string;
      copyable?: boolean;
      style?: CSSProperties;
    }>
  >([]);

  const inputRef = useRef<InputRef>(null);

  const onClickTrans = () => {
    if (!inputRef.current?.input?.value) {
      setResults([{ value: '非法输入' }]);
      return;
    }

    const str = inputRef.current?.input?.value;
    const floatReg = /(-?\d+)(\.\d+)?/g;

    const ext = str.match(floatReg);
    if (ext?.length !== 7) {
      setResults([{ label: '解析错误 😭 请检查输入，匹配结果如下：', value: ext ? ext.join(' ') : '未匹配到数字' }]);
      return;
    }

    const exts = ext.map((str) => parseFloat(str));
    const mat = Extrinsic.toMatrix4(exts);

    // 矩阵取逆，并将 [4, 4] 的位置置 1.0
    const invertMat = mat.clone().invert();
    invertMat.elements[15] = 1.0;

    setResults([
      { value: '解析成功 🎉 以下矩阵数据均为行优先：' },
      {
        label: '原始外参：',
        value: ext.join(', '),
        copyable: true,
        style: { opacity: 0.6 },
      },
      {
        label: '原始矩阵：',
        value: mat.clone().transpose().elements.join(', '),
        copyable: true,
        style: { opacity: 0.6 },
      },
      {
        label: '逆矩阵：',
        value: invertMat.clone().transpose().elements.join(', '),
        copyable: true,
      },

      {
        label: '逆矩阵 + 转向处理：',
        value: invertMat.clone().premultiply(TRANS_MAT).transpose().elements.join(', '),
        copyable: true,
      },
    ]);
    return;
  };

  return (
    <Typography style={{ padding: '28px 24px', height: '100vh', overflow: 'scroll' }}>
      <Title>外参 ={'>'} 矩阵</Title>
      <div>
        <div>
          <Input ref={inputRef} placeholder="Basic usage" style={{ width: 800, marginRight: 8 }} />
          <Button onClick={onClickTrans}>转换</Button>
        </div>

        <div
          style={{
            width: 800,
            padding: 12,
            borderRadius: 4,
            background: '#171F26',
            whiteSpace: 'pre-line',
            display: results.length === 0 ? 'none' : 'block',
          }}
        >
          {results.map(({ label, value, copyable, style }) => (
            <Paragraph copyable={copyable ? { text: value } : false} style={{ fontSize: 14, ...style }}>
              {label ? label + '\n' : ''}
              {value}
            </Paragraph>
          ))}
        </div>

        <pre style={{ width: 800, padding: 12, color: '#F5F7FA' }}>{INSTRUCTS}</pre>
      </div>
    </Typography>
  );
};
