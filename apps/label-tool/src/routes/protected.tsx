import { lazyImport } from '@forest/utils';
import { Navigate, Outlet } from 'react-router-dom';

const { AnnoRoutes } = lazyImport(() => import('@/features/anno'), 'AnnoRoutes');
const { Tools } = lazyImport(() => import('@/features/tools'), 'Tools');

export const protectedRoutes = [
  {
    path: '/annotate/*',
    element: <Outlet />,
    children: [{ path: '*', element: <AnnoRoutes /> }],
  },
  {
    path: '/tools/*',
    element: <Outlet />,
    children: [{ path: '*', element: <Tools /> }],
  },
  { path: '*', element: <Navigate to="/annotate/frame" /> },
];
