import { useConstant } from '@forest/hooks';
import { MessageInstance } from 'antd/es/message/interface';
import { useCallback, useEffect, useState } from 'react';
import { catchError, EMPTY, map, Observable, Subject, switchMap, tap, timeout as timeoutController } from 'rxjs';

import { useError, useMessage } from '@/provider';

interface RequestOptions<T, R, E extends Error> {
  params?: T;
  timeout?: number;
  enabled?: boolean;
  meta?: Record<string, unknown>;
  onSuccess?: (data: R, options: RequestOptions<T, R, E>, messageApi: MessageInstance | null) => void;
  onError?: (error: E, options: RequestOptions<T, R, E>, messageApi: MessageInstance | null) => void;
}

export const useRequest = <T, R, E extends Error = Error>(
  fetchFunc: ((params: T) => Observable<R>) | (() => Observable<R>),
  options: RequestOptions<T, R, E> = {}
) => {
  const [result, setResult] = useState<R | null>(null);
  const [error, setError] = useState<E | null>(null);
  const [isLoading, setLoading] = useState<boolean>(false);

  const event$ = useConstant<Subject<RequestOptions<T, R, E>>>(() => new Subject());

  const { errorList, lang } = useError();

  const messageApi = useMessage();

  // TODO: 改为 useLayoutEffect
  useEffect(() => {
    const eventSub = event$
      .pipe(
        map((opt) => ({
          ...options,
          ...opt,
        })),
        tap(() => setLoading(true)),
        switchMap((opt) => {
          const { params = {}, timeout = 300000, onError } = opt;
          // console.log(`Request ${fetchFunc.name} Begin: `, params);
          return (fetchFunc.length === 0 ? (fetchFunc as () => Observable<R>)() : fetchFunc(params as T)).pipe(
            timeoutController({
              each: timeout,
              with: () => {
                throw new Error('网络超时，请检查网络连接或稍后再试');
              },
            }),
            tap((data) => {
              setLoading(false);
              const { onSuccess } = opt;
              if (onSuccess) {
                onSuccess(data, opt, messageApi);
              }
              setResult(data);
            }),
            catchError((error) => {
              // 把 error.message 根据 errorList 显示为中文
              const { reason = '' } = error.body || {};
              const content = errorList[reason]?.langs[lang] || reason || error.message;
              error.message = content;
              if (onError) {
                onError(error, opt, messageApi);
              } else {
                messageApi?.error({ content });
              }
              setLoading(false);
              setError(error);
              return EMPTY;
            })
          );
        })
      )
      .subscribe({
        next: (data) => {
          console.log(`Request ${fetchFunc.name} End: `, data);
        },
        error: () => {},
        complete: () => {},
      });
    if (options.enabled) {
      event$.next(options);
    }
    return () => {
      eventSub.unsubscribe();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const refetch = useCallback((opt?: RequestOptions<T, R, E>) => event$.next(opt || {}), []);

  return {
    data: result,
    error: error,
    isLoading: isLoading,
    refetch,
  };
};
