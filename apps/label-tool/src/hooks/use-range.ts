import { useEffect, useRef } from 'react';
import { Group, Object3D } from 'three';

import { Job, LotRange } from '@/types';
import { Box2d, Circle3d } from '@/utils/annos';
import { Stage } from '@/utils/render';

// 标注范围高度设定为稳定值。圆有面时需要与其他有透明度的对象 z 值保持不同。
export const RANGE_Z = 0.1;

const CENTER_DEFAULT: [number, number, number] = [0, 0, 0];

export const useRange = (
  job: Job | null,
  currentElementIndex: number,
  stage: Stage | null,
  ranges: Array<LotRange>,
  rangeVisible: boolean,
  sightId?: string,
  center = CENTER_DEFAULT,
) => {
  const rangeRef = useRef<Object3D>();

  useEffect(() => {
    if (!stage?.currentSight || ranges.length === 0) return;

    if (!rangeRef.current) {
      const rangesContainer = new Group();
      ranges.forEach((range) => {
        if (range.shape === 'circle') {
          const circle = Circle3d.createLine([0, 0, 0, range.data[0]], {
            color: '#27CD9D',
          });
          rangesContainer.add(circle);
        }
        if (range.shape === 'rectangle') {
          const box = Box2d.create([0, 0, range.data[0], range.data[1]], {
            color: '#27CD9D',
            opacity: 0,
          });
          rangesContainer.add(box);
        }
      });
      rangesContainer.visible = rangeVisible;
      rangeRef.current = rangesContainer;
    }

    if (rangeRef.current) {
      rangeRef.current.position.set(center[0], center[1], RANGE_Z);
      // 每次更新场景 scene 时重新添加标注范围
      stage.currentSight.addMarker(rangeRef.current);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [stage, ranges, sightId]);

  useEffect(() => {
    if (rangeRef.current) {
      rangeRef.current.visible = rangeVisible;
    }
  }, [rangeVisible]);

  useEffect(() => {
    if (rangeRef.current) {
      rangeRef.current.position.set(center[0], center[1], RANGE_Z);
      const currentElement = job?.elements[currentElementIndex];
      const relativePose = currentElement?.relativePose;
      if (relativePose?.length) {
        rangeRef.current.quaternion.set(relativePose[3], relativePose[4], relativePose[5], relativePose[6]);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [center]);
};
