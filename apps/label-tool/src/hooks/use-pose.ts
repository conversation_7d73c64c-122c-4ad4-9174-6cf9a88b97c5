import { useConstant } from '@forest/hooks';
import { useEffect, useRef } from 'react';
import { Group } from 'three';

import { CSS2DObject } from '@/lib/three';
import { Stage } from '@/utils/render';

/**
 * 设置车姿点选中/非选中的样式
 * @param group
 * @param index
 * @param selected
 */
const setPoseSelected = (group: Group, index: number, selected: boolean) => {
  if (index >= 0 && index < group.children.length) {
    const target = group.children[index];

    if (target instanceof CSS2DObject) {
      // 车姿选中态为黄色，默认态为绿色
      target.element.style.color = selected ? '#ffd400' : '#27cd9d';

      // 修改车姿背景图的透明度
      const path = target.element.querySelector('path');
      path && path.setAttribute('fill-opacity', selected ? '90%' : '50%');
    }
  }
};

export const usePose = (
  stage: Stage | null,
  posePoints: Array<[number, number, number]>,
  poseVisible: boolean,
  sightLoaded: boolean,
  selectedPose: number,
  onSelected: (index: number) => void
) => {
  const poseGroup = useRef<Group>();

  const currentSelected = useRef(-1);

  const poseGroupDiv = useConstant(() => document.createElement('div'));

  useEffect(() => {
    // 判断是否符合初始化车姿点的条件，当前车姿点全局只初始化一次
    if (
      posePoints.length === 0 ||
      poseGroup.current ||
      !stage?.labelRenderer ||
      stage.labelRenderer.manualMaxDepth < 2 ||
      !stage.currentSight
    )
      return;

    // 将双击响应车姿变化事件挂载到集合 div 元素上
    poseGroupDiv.addEventListener(
      'dblclick',
      (evt) => {
        if (evt.target instanceof HTMLElement) {
          const { index } = evt.target.dataset;
          index && onSelected(parseInt(index));
        }
      },
      false
    );

    // 禁止触摸板缩放
    poseGroupDiv.addEventListener(
      'wheel',
      function (event) {
        event.preventDefault();
        event.stopPropagation();
      },
      { passive: false }
    );

    const group = new Group();
    posePoints?.forEach((position, i) => {
      const pose = new CSS2DObject();

      pose.element.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24"  fill="currentColor" style="pointer-events: none;">
        <path fill="currentColor" fill-opacity="50%" fill-rule="evenodd" d="m2 10 3-7h14l3 7h2v2h-2v9a1 1 0 0 1-1 1h-1a1 1 0 0 1-1-1v-1H5v1a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-9H0v-2h2Z" clip-rule="evenodd" />
        <text x="12" y="13" font-size="11" fill="white" style="dominant-baseline:middle;text-anchor:middle;">${
          i + 1
        }</text></svg>`;
      // 设置节点对应序号
      pose.element.dataset.index = i.toString();
      // 车姿底色默认为绿色
      pose.element.style.color = '#27cd9d';
      // 设置鼠标可响应
      pose.element.style.pointerEvents = 'auto';
      // 行高设为0
      pose.element.style.lineHeight = '0';
      // 手动构造 dom 层级，确保渲染后 poseGroup 为所有车姿点的父级
      poseGroupDiv.appendChild(pose.element);

      pose.position.fromArray(position);
      group.add(pose);
    });

    // 待所有车姿点处理完毕后，将车姿集合的 div 手动添加到 labelRenderer 的根节点中
    stage.labelRenderer.domElement.appendChild(poseGroupDiv);

    poseGroup.current = group;
    poseGroup.current.visible = poseVisible;

    // 更新选中帧
    setPoseSelected(poseGroup.current, selectedPose, true);
    currentSelected.current = selectedPose;

    stage.currentSight?.addMarker(poseGroup.current);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [stage, posePoints, sightLoaded]);

  useEffect(() => {
    if (poseGroup.current) {
      poseGroup.current.visible = poseVisible;
    }
  }, [poseVisible]);

  useEffect(() => {
    if (!poseGroup.current || currentSelected.current === selectedPose) return;

    setPoseSelected(poseGroup.current, currentSelected.current, false);
    setPoseSelected(poseGroup.current, selectedPose, true);
    currentSelected.current = selectedPose;
  }, [selectedPose]);
};
