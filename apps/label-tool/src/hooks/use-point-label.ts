/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from 'react';
import { Mesh, Vector3 } from 'three';

import { CSS2DObject } from '@/lib/three';
import { Label } from '@/types';
import { AnnoFactory, IAnnoItem } from '@/utils/annos';
import { Stage } from '@/utils/render';

// 创建一个新的canvas元素和context，用于度量文字宽度
const canvas = document.createElement('canvas');
const _context = canvas.getContext('2d');
if (_context) {
  _context.font = '11px serif';
}

export const usePointLabel = (
  stage: Stage | null,
  editAnno: IAnnoItem[],
  annotations: Record<string, IAnnoItem | null>,
  labelMap: Map<Label['name'], Label>,
  labelDomParent?: HTMLElement,
  sightId?: string
) => {
  const [pointLabels, setPointLabels] = useState<Map<string, CSS2DObject>>();

  useEffect(() => {
    if (!stage || !labelDomParent) return;

    // 当前 stage 支持渲染 dom 节点时，才初始化标签
    setPointLabels(new Map());
  }, [stage, labelDomParent]);

  useEffect(() => {
    if (!pointLabels || !labelDomParent || !stage) return;

    if (!sightId && pointLabels.size > 0) {
      // 清除标签展示
      if (pointLabels.size > 0) {
        pointLabels.forEach((pointLabel) => pointLabel.parent?.remove(pointLabel));
        pointLabels.clear();
      }
      return;
    }

    Object.entries(annotations).forEach(([key, annoItem]) => {
      if (!annoItem) {
        // 清除多余的标签
        const pointLabel = pointLabels.get(key);
        if (pointLabel) {
          pointLabel.parent?.remove(pointLabel);
          AnnoFactory.dispose(pointLabel);
          pointLabels.delete(key);
        }
        return;
      }
      const { name } = annoItem;
      const obj = stage.currentSight?.getObjectByName(name);
      if (!obj || !['point3d', 'poly3d', 'line3d'].includes(obj?.userData?.type)) return;

      // 检查 pointLabels 中是否已经有该标签
      let pointLabel = pointLabels.get(name);
      const anno = annotations[name];
      if (!anno) return;
      const label = labelMap.get(anno.label);
      if (!label) return;
      if (pointLabel) {
        if (obj.userData.type !== 'point3d') {
          const center = new Vector3();
          (obj as Mesh).geometry.boundingBox?.getCenter(center);
          center && pointLabel.position.copy(center);
        }
      } else {
        pointLabel = new CSS2DObject(document.createElement('div'));
        pointLabel.name = pointLabel.uuid;
        pointLabel.element.id = name;

        if (obj.userData.type !== 'point3d') {
          const geometry = (obj as Mesh).geometry;
          geometry.computeBoundingBox();
          const center = new Vector3();
          geometry.boundingBox?.getCenter(center);
          center && pointLabel.position.copy(center);
        }
        pointLabel.position.z += 0.05;
        pointLabel.center.set(0.5, 1);

        // 手动插入 dom 节点
        labelDomParent.appendChild(pointLabel.element);

        pointLabels.set(obj.name, pointLabel);
        obj.add(pointLabel);
      }
    });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sightId, annotations, editAnno]);
};
