/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from 'react';

import { CSS2DObject } from '@/lib/three';
import { AnnoFactory, AnnoInstanceItem, IAnnoItem } from '@/utils/annos';
import { Stage } from '@/utils/render';

const WIDGET_SHOW_DOT = ['line3d', 'poly3d'];

const createDot2DObject = (index: number) => {
  const div = document.createElement('div');
  div.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
    <path d="M0 2C0 0.89543 0.895431 0 2 0H14C15.1046 0 16 0.895431 16 2V12C16 13.1046 15.1046 14 14 14H10L8 16L6 14H2C0.89543 14 0 13.1046 0 12V2Z" fill="currentColor"/>
    <text x="8" y="8" font-size="11" fill="white" style="dominant-baseline:middle;text-anchor:middle;">${
      index + 1
    }</text>
  </svg>`;
  return new CSS2DObject(div);
};

const createDotGroup = (vertexs: number[] | Float32Array, dotParent: HTMLElement, color: string) => {
  const dotGroup = new CSS2DObject();
  dotGroup.name = dotGroup.uuid;
  // 手动插入 dom 节点
  dotParent.appendChild(dotGroup.element);

  // 遍历 vertexs 创建新的 dotObj
  for (let i = 0; i < vertexs.length; i += 3) {
    const dotObj = createDot2DObject(i / 3);
    dotObj.position.fromArray(vertexs, i);
    dotObj.position.z += 0.05;
    dotObj.center.set(0.5, 1);
    dotObj.element.style.color = color;
    dotGroup.add(dotObj);

    // 手动插入 dom 节点
    dotParent.appendChild(dotObj.element);
  }

  // 更新 dotGroup 的顶点数据
  dotGroup.userData.vertexs = vertexs;

  return dotGroup;
};

export const useLineDot = (
  stage: Stage | null,
  selectedAnnos: IAnnoItem[],
  dotParent?: HTMLElement,
  sightId?: string,
  currentAnno?: any
) => {
  const [dots, setDots] = useState<Map<string, CSS2DObject>>();

  useEffect(() => {
    if (!stage || !dotParent) return;

    // 当前 stage 支持渲染 dom 节点时，才初始化序号数据
    setDots(new Map());
  }, [stage, dotParent]);

  useEffect(() => {
    if (!dots || !dotParent || !stage) return;

    if (!sightId && dots.size > 0) {
      // 清除序号展示
      if (dots.size > 0) {
        dots.forEach((dotGroup) => {
          dotGroup.parent?.remove(dotGroup);
          AnnoFactory.dispose(dotGroup);
        });
        dots.clear();
      }
      return;
    }

    let selected: Record<string, boolean> = {};

    selectedAnnos.forEach((anno) => {
      if (anno instanceof AnnoInstanceItem && WIDGET_SHOW_DOT.includes(anno.widget.name)) {
        const line = stage.currentSight?.getObjectByName(anno.name);
        if (!line) return;

        const vertexs = AnnoFactory.getVertexFromObject3D(line.userData.type, line);
        let dotGroup = dots.get(anno.name);

        // 无序号标签或序号数小于顶点数
        if (!dotGroup || dotGroup.children.length * 3 < vertexs.length) {
          if (dotGroup) {
            // 序号数小于顶点数，清除当前序号标签
            dotGroup.parent?.remove(dotGroup);
            AnnoFactory.dispose(dotGroup);
            dots.delete(anno.name);
          }
          dotGroup = createDotGroup(vertexs, dotParent, line.userData.color);

          dots.set(anno.name, dotGroup);
          line.add(dotGroup);
        }
        selected[anno.name] = true;
      }
    });

    // 清除多余的序号点
    dots.forEach((dotGroup, name) => {
      if (!selected[name]) {
        dotGroup.parent?.remove(dotGroup);
        AnnoFactory.dispose(dotGroup);
        dots.delete(name);
      }
    });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sightId, selectedAnnos]);

  // 撤销时，可能需要更新点的位置
  useEffect(() => {
    if (!dots || !dotParent || !stage) return;
    if (selectedAnnos.length !== 1) return;
    const anno = selectedAnnos[0];
    const line = stage.currentSight?.getObjectByName(anno.name);
    if (!line) return;
    const vertexs = AnnoFactory.getVertexFromObject3D(line.userData.type, line);
    let dotGroup = dots.get(anno.name);
    if (!dotGroup) return;

    // 检查顶点是否发生变化
    const hasVertexChanged =
      dotGroup.children.length * 3 !== vertexs.length || vertexs.some((v, i) => v !== dotGroup!.userData.vertexs[i]);

    if (!hasVertexChanged) return; // 如果顶点没变化,直接返回

    // 移除旧的 dotGroup
    if (dotGroup) {
      dotGroup.parent?.remove(dotGroup);
      AnnoFactory.dispose(dotGroup);
      dots.delete(anno.name);
    }
    // 创建新的 dotGroup
    dotGroup = createDotGroup(vertexs, dotParent, line.userData.color);

    // 添加新的 dotGroup 到场景
    dots.set(anno.name, dotGroup);
    line.add(dotGroup);
  }, [currentAnno]);
};
