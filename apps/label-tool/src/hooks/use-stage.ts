import { throttle } from 'lodash';
import { RefObject, useEffect, useRef } from 'react';

import { Stage, StageOptions } from '@/utils/render';

export const useStage = (
  options: StageOptions,
  onInitStage: (stage: Stage) => void,
  onWrapResize?: (width: number, height: number) => void
): [RefObject<HTMLDivElement>, RefObject<HTMLCanvasElement>] => {
  const canvasWrapRef = useRef<HTMLDivElement>(null);
  const canvasElementRef = useRef<HTMLCanvasElement>(null);

  const updateSizeHandler = useRef<Function | null>(null);

  // 添加页面 resize 监听，初始化 three.js 引擎
  useEffect(() => {
    if (canvasWrapRef.current && canvasElementRef.current) {
      const { width, height } = canvasWrapRef.current.getBoundingClientRect();

      const stage = new Stage(width, height, canvasElementRef.current, {
        backgroundColor: 0x1a202c,
        ...options,
      });
      updateSizeHandler.current = throttle((width, height) => {
        stage.updateStageSize(width, height);
        onWrapResize?.(width, height);
      }, 300);
      onInitStage?.(stage);

      const observer = new ResizeObserver((entries) => {
        const { inlineSize, blockSize } = entries[0].borderBoxSize[0];
        if (inlineSize > 0 && blockSize > 0) {
          updateSizeHandler.current?.(inlineSize, blockSize);
        }
      });
      observer.observe(canvasWrapRef.current);

      return () => observer.disconnect();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return [canvasWrapRef, canvasElementRef];
};
