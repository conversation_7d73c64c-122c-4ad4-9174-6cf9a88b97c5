/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type anno_v1_ToolConfig_Ruler = {
  /**
   * a friendly name for the ruler
   */
  name: string;
  /**
   * shape of the ruler
   */
  type: 'unspecified' | 'circle' | 'rectangle';
  /**
   * the params of the shape; specific to shape type.
   * rectangle: [width, height], the unit is pixel;
   * circle: [radius], the unit is pixel;
   */
  data: Array<number>;
  /**
   * which rawdata types the ruler is applicable to
   */
  rawdata_types?: Array<'unspecified' | 'image' | 'pointcloud'>;
};
