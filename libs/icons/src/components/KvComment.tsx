export function KvComment(props: JSX.IntrinsicAttributes & React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      stroke="currentColor"
      fill="currentColor"
      strokeWidth={0}
      viewBox="0 0 24 24"
      height="1em"
      width="1em"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19 6.5L6.5 19H2V14.5L14.5 2L19 6.5ZM14.5 5L4 15.5V17H5.5L16 6.5L14.5 5ZM22 22V20H2V22H22ZM22 12V14H16V12H22ZM22 16H12V18H22V16Z"
      />
    </svg>
  );
}
